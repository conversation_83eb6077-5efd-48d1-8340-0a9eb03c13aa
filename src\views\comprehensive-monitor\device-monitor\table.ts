export class Columns {
  get columns1() {
    return [
      // {
      //   title: "",
      //   align: "center",
      //   type: "selection",
      //   width: 60,
      // },
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "传感器编号",
        key: "cgqbm",
        align: "center",
      },
      {
        title: "传感器类型",
        key: "cgqTypeLabel",
        align: "center",
      },
      {
        title: "所属设备",
        key: "sssbmc",
        align: "center",
      },
      {
        title: "厂商信息",
        key: "csxx",
        align: "center",
      },
      {
        title: "设备状态",
        key: "sbzt",
        slot: "sbzt",
        align: "center",
      },
      {
        title: "操作",
        slot: "operations",
        align: "center",
        width: 160,
      },
    ];
  }
  // 监测数据
  get columns2() {
    return [
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "监测项代码",
        key: "jcxbm",
        align: "center",
      },
      {
        title: "监测项",
        key: "jcxmc",
        align: "center",
      },
      {
        title: "最新监测项值",
        key: "zxjcxz",
        align: "center",
      },
      {
        title: "监测上报时间",
        key: "jcsbsj",
        align: "center",
      },
      {
        title: "历史数据",
        slot: "operations",
        align: "center",
        width: 160,
      },
    ];
  }
  // 监测数据-历史数据
  get columns2_1() {
    return [
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "监测项",
        key: "jcxmc",
        align: "center",
      },
      {
        title: "监测项值",
        key: "jcxz",
        align: "center",
      },
      {
        title: "监测上报时间",
        key: "jcsbsj",
        align: "center",
      }
    ];
  }
  // 报警数据
  get columns3() {
    return [
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "监测项代码",
        key: "jcxbm",
        align: "center",
      },
      {
        title: "监测项",
        key: "jcxmc",
        align: "center",
      },
      {
        title: "当前报警值",
        key: "dqbjz",
        align: "center",
      },
      {
        title: "当前报警时间",
        key: "dqbjsj",
        align: "center",
      },
      {
        title: "报警状态",
        key: "bjztbm",
        slot: "bjztbm",
        align: "center",
        width: 160,
      },
      {
        title: "操作",
        slot: "operations",
        align: "center",
        width: 160,
      },
    ];
  }
  // 报警数据-历史报警数据
  get columns3_1() {
    return [
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "监测项",
        key: "jcxmc",
        align: "center",
      },
      {
        title: "报警值",
        key: "bjz",
        align: "center",
      },
      {
        title: "报警时间",
        key: "bjsj",
        align: "center",
      },
      {
        title: "报警状态",
        key: "bjztbm",
        slot: "bjztbm",
        align: "center",
      },
      {
        title: "操作",
        slot: "operations",
        align: "center",
        width: 160,
      },
    ]
  }
}
