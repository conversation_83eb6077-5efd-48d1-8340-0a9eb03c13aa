<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">场景名称:</el-col>
        <el-col :span="8">{{ data.sceneName }}</el-col>
        <el-col :span="4" class="text-right">最密人数:</el-col>
        <el-col :span="8">{{ data.maxPeople || '-' }}人</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">时间设定:</el-col>
        <el-col :span="20">{{ data.timeMin }}至{{ data.timeMax }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">室外温度:</el-col>
        <el-col :span="8">{{ data.outTempMin }}℃至{{ data.outTempMax }}℃</el-col>
        <el-col :span="4" class="text-right">风速:</el-col>
        <el-col :span="8">{{ data.windSpeedMin }}m/s至{{ data.windSpeedMax }}m/s</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">人流范围:</el-col>
        <el-col :span="8">{{ data.peopleRangeMin }}人至{{ data.peopleRangeMax }}人</el-col>
        <el-col :span="4" class="text-right">月份:</el-col>
        <el-col :span="8">{{ data.monthMin }}月至{{ data.monthMax }}月</el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      {
      }
    },
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
