<template>
  <el-dialog v-model="isVisible" width="960" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>查看</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="knowledge-base-title">{{ props.data.title }}</div>
      <div class="knowledge-base-sub-title">发布时间：{{ props.data.releaseTime }}</div>
      <div class="knowledge-base-sub-title knowledge-base-type">{{ props.data.typeName }}</div>
      <div class="knowledge-base-sub-title">{{ props.data.description }}</div>
      <el-divider />
      <!-- <div class="knowledge-base-content" v-html="props.data.content"></div> -->
      <div class="ql-container ql-snow">
        <div class="ql-editor" v-html="props.data.content"/>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";
import { getDict } from "@/api/common";
import "@vueup/vue-quill/dist/vue-quill.snow.css"

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      {
      }
    },
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};

// 挂载
onMounted(async () => {
  fun_getDicts();//获取字典
})

// 获取字典
const fun_getDicts = async () => {
  // const {data:typeOptionData} = await getDict('type');//获取知识库类型
  // typeOption.value = typeOptionData.list;
}

// 类型
const typeOption = ref([]);

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
    max-height: 800px;
    overflow-y: auto;
    .knowledge-base-title {
      font-size: 20px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 20px;
    }
    .knowledge-base-sub-title {
      font-size: 16px;
      text-align: center;
      margin-bottom: 10px;
    }
    .knowledge-base-type {
      color: #169BD5;
    }
    .ql-container.ql-snow {
      border: none;
    }
  }
}
</style>
