import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';

// 知识库-新增
export function add(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/knowledgeBase/addKnowledgeBase',
    method: 'post',
    data: data
  })
}

// 知识库-修改
export function update(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/knowledgeBase/editKnowledgeBase',
    method: 'post',
    data: data
  })
}

// 知识库-删除
export function del(delIds: any) {
  return request({
    url: baseRouter + apiPrefix + '/knowledgeBase/deleteByIds',
    method: 'delete',
    params: { ids: delIds },
  })
}

// 知识库-列表
export function getList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/knowledgeBase/byPage',
    method: 'post',
    data: {...query}
  })
}

// 更新状态
export function updateStatus(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/knowledgeBase/updateInfoStatus',
    method: 'get',
    params: {
      gid: data.gid,
      status: data.status
    }
  })
}
