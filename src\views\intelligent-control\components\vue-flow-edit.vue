<template>
  <div class="pointManage font-16">
    <div class="height-all bg-color-fff border-radius-5 padd-20">
      <div class="posit-relat width-all height-all over-auto">
        <div class="width-all height-all over-auto" style="background-color: #07162C;">
          <VueFlowEditor :data="flowData" :panelList="flowPanelList" :editable="flowIsEdit" @nodeClick="fun_handleClick" @nodeMouseEnter="fun_handleMouseEnter" @nodeMouseLeave="fun_handleMouseLeave" @fun_backSaveData="fun_backSaveData" />
        </div>
        <div class="posit-absol top-0 padd-0_20" style="background-color:rgba(129, 255, 210, 0.8);left: 30%;">
          <el-tabs v-model="tabs_activeCode" @tab-change="fun_tabsChange">
            <el-tab-pane label="冷源" name="1937014595413544961"></el-tab-pane>
            <el-tab-pane label="热源" name="-1"></el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
const router = useRouter();


import { getDict } from "@/api/common";
import {
  getPanelDeviceList,
  getFlowChart,
  addFlowChart,
  updateFlowChart,
} from "@/api/intelligent-control/index";

/* VueFlow功能 */
import VueFlowEditor from '@/components/VueFlow/VueFlowEditor.vue';


// 获取字典
const fun_getDicts = async () => {
  // const {data:cycleTypeOptionData} = await getDict('cycle_type');//获取巡检周期
  // cycleTypeOption.value = cycleTypeOptionData.list;
  // console.log('巡检周期cycleTypeOption:',cycleTypeOption.value);
}

// tabs
const tabs_activeCode = ref('');
const fun_tabsChange = () => {
  console.log('tab:',tabs_activeCode.value);
  fun_getList();
};

// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  // const {name:routeName} = router.currentRoute.value;
  // console.log('当前路由:',routeName);
  // fun_getList(routeName);
});

const flowData = ref();
const flowIsEdit = ref(true);//false:查看模式,true:编辑模式
//编辑模式下的面板列表
const flowPanelList = ref([
  // {id:1,imgUrl:'/static/images/emergency.png',style:{width:'74px'},label:'节点名称1'},
  // {id:2,imgUrl:'/static/images/dialog-arrow.png',style:{width:'18px'},label:'节点名称2'},
  // {id:3,imgUrl:'/static/images/dialog-bg1.png',style:{width:'120px'},label:'节点名称3'},
]);

const fun_getList = async () => {
  // if(tabs_activeCode.value=='Cold-source'){}
  // else if(tabs_activeCode.value=='Hot-source'){}
  //面板列表
  const { data:{list} } = await getPanelDeviceList(tabs_activeCode.value);
  console.log('面板设备列表:',list);
  if(list){
    flowPanelList.value= list.map(v => {
      return {
        id:v.gid,
        imgUrl:v.filePath,
        style:{width:(v.equipmentWide||100)+'px',height:v.equipmentHigh?v.equipmentHigh+'px':''},
        label:v.sbmc,
      }
    });
  }else{
    flowPanelList.value=[];
  }

  //流程数据
  const { data } = await getFlowChart(tabs_activeCode.value);
  console.log('流程数据:',data);
  flowData.value = data.procddata?JSON.parse(data.procddata):{};
  // flowData.value = {
  //   "nodes":[
  //     {"id":"999","type":"custom","initialized":false,"position":{"x":291.9375,"y":63.71875},"data":{"id":"999","label":"节点名称999","imgUrl":"/static/images/emergency.png","style":{"width":"74px"},"description":"这是一个自定义节点","type":"custom"}},
  //     {"id":"2","type":"custom","initialized":false,"position":{"x":187.4375,"y":242.21875},"data":{"id":"2","label":"节点名称2","imgUrl":"/static/images/dialog-arrow.png","style":{"width":"18px"},"description":"这是一个自定义节点","type":"custom"}},
  //     {"id":"3","type":"custom","initialized":false,"position":{"x":452.9375,"y":376.71875},"data":{"id":"3","label":"节点名称3","imgUrl":"/static/images/dialog-bg1.png","style":{"width":"120px"},"description":"这是一个自定义节点","type":"custom"}}
  //   ],
  //   "edges":[
  //     {"id":"vueflow__edge-999source-0-2source-5","type":"custom-edge","source":"999","target":"2","sourceHandle":"source-0","targetHandle":"source-5","data":{},"label":"","sourceX":286.9375,"sourceY":84.109375,"targetX":241.296875,"targetY":265.53125},
  //     {"id":"vueflow__edge-999source-11-3source-0","type":"custom-edge","source":"999","target":"3","sourceHandle":"source-11","targetHandle":"source-0","data":{"customColor":"#f00"},"label":"","sourceX":350.34375,"sourceY":170.71875,"targetX":447.9375,"targetY":395.515625},
  //     {"id":"vueflow__edge-2source-10-3source-9","type":"custom-edge","source":"2","target":"3","sourceHandle":"source-10","targetHandle":"source-9","data":{"customColor":"#0f0"},"label":"","sourceX":211.859375,"sourceY":284.21875,"targetX":477.328125,"targetY":475.734375},
  //     {"id":"vueflow__edge-2source-11-3source-7","type":"custom-edge","source":"2","target":"3","sourceHandle":"source-11","targetHandle":"source-7","data":{},"label":"","sourceX":221.421875,"sourceY":284.21875,"targetX":513.9375,"targetY":371.71875}
  //   ],
  //   "position":[0,0],
  //   "zoom":1,
  //   "viewport":{"x":0,"y":0,"zoom":1}
  // }


};

// 鼠标移入
const fun_handleMouseEnter = async (v_id) => {
  console.log('鼠标移入:',v_id);
  // const { data } = await getInfo(v_id);
  // dataInfo.value=dataList.value[0];
  // dataInfoVisibility.value=true;
}
// 鼠标移出
const fun_handleMouseLeave = (v_id) => {
  console.log('鼠标移出:',v_id);
  // dataInfoVisibility.value=false;
}
// 节点点击
const fun_handleClick = (v_id) => {
  console.log('点击:',v_id);
  // const { data } = await getInfo(v_id);
  // dataInfo.value=dataList.value[0];
  // dataControlVisibility.value=true;
}


//保存绘制数据
const fun_backSaveData = async (jsonData) => {
  if(!tabs_activeCode.value){
    ElMessage.warning('请先选择类型');
    return;
  }
  if(tabs_activeCode.value=='-1'){
    ElMessage.warning('热源功能暂未对接');
    return;
    // const res = await addFlowChart({procddata:JSON.stringify(jsonData)});//第一次走新增接口,后面走更新接口
  }
  const res = await updateFlowChart({gid:tabs_activeCode.value,procddata:JSON.stringify(jsonData)});
  ElMessage({
    message: res.message,
    type: "success",
  });
  flowData.value={};
  // fun_getList();
}

</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}

</style>
