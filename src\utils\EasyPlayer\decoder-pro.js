!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";var e,t=(e="undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("decoder-pro.js",document.baseURI).href,function(t){var r,i;(t=void 0!==(t=t||{})?t:{}).ready=new Promise((function(e,t){r=e,i=t})),(t=void 0!==t?t:{}).locateFile=function(e){return"decoder-pro.wasm"==e&&"undefined"!=typeof EASYPLAYER_PRO_WASM_URL&&""!=EASYPLAYER_PRO_WASM_URL?EASYPLAYER_PRO_WASM_URL:e};var n,s,o,a,l,d,u=Object.assign({},t),c="./this.program",f="object"==typeof window,h="function"==typeof importScripts,p="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,m="";p?(m=h?require("path").dirname(m)+"/":__dirname+"/",d=()=>{l||(a=require("fs"),l=require("path"))},n=function(e,t){return d(),e=l.normalize(e),a.readFileSync(e,t?void 0:"utf8")},o=e=>{var t=n(e,!0);return t.buffer||(t=new Uint8Array(t)),t},s=(e,t,r)=>{d(),e=l.normalize(e),a.readFile(e,(function(e,i){e?r(e):t(i.buffer)}))},process.argv.length>1&&(c=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("uncaughtException",(function(e){if(!(e instanceof ee))throw e})),process.on("unhandledRejection",(function(e){throw e})),t.inspect=function(){return"[Emscripten Module object]"}):(f||h)&&(h?m=self.location.href:"undefined"!=typeof document&&document.currentScript&&(m=document.currentScript.src),e&&(m=e),m=0!==m.indexOf("blob:")?m.substr(0,m.replace(/[?#].*/,"").lastIndexOf("/")+1):"",n=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},h&&(o=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),s=(e,t,r)=>{var i=new XMLHttpRequest;i.open("GET",e,!0),i.responseType="arraybuffer",i.onload=()=>{200==i.status||0==i.status&&i.response?t(i.response):r()},i.onerror=r,i.send(null)});var _=t.print||console.log.bind(console),y=t.printErr||console.warn.bind(console);Object.assign(t,u),u=null,t.arguments&&t.arguments,t.thisProgram&&(c=t.thisProgram),t.quit&&t.quit;var g,v,b=4;t.wasmBinary&&(g=t.wasmBinary),t.noExitRuntime,"object"!=typeof WebAssembly&&j("no native wasm support detected");var w=!1;function E(e,t){e||j(t)}var S,U,x,A,k,T,B,C,D,F,P="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function I(e,t,r){for(var i=t+r,n=t;e[n]&&!(n>=i);)++n;if(n-t>16&&e.buffer&&P)return P.decode(e.subarray(t,n));for(var s="";t<n;){var o=e[t++];if(128&o){var a=63&e[t++];if(192!=(224&o)){var l=63&e[t++];if((o=224==(240&o)?(15&o)<<12|a<<6|l:(7&o)<<18|a<<12|l<<6|63&e[t++])<65536)s+=String.fromCharCode(o);else{var d=o-65536;s+=String.fromCharCode(55296|d>>10,56320|1023&d)}}else s+=String.fromCharCode((31&o)<<6|a)}else s+=String.fromCharCode(o)}return s}function L(e,t){return e?I(x,e,t):""}function R(e,t,r,i){if(!(i>0))return 0;for(var n=r,s=r+i-1,o=0;o<e.length;++o){var a=e.charCodeAt(o);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++o)),a<=127){if(r>=s)break;t[r++]=a}else if(a<=2047){if(r+1>=s)break;t[r++]=192|a>>6,t[r++]=128|63&a}else if(a<=65535){if(r+2>=s)break;t[r++]=224|a>>12,t[r++]=128|a>>6&63,t[r++]=128|63&a}else{if(r+3>=s)break;t[r++]=240|a>>18,t[r++]=128|a>>12&63,t[r++]=128|a>>6&63,t[r++]=128|63&a}}return t[r]=0,r-n}function M(e){for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);i<=127?t++:i<=2047?t+=2:i>=55296&&i<=57343?(t+=4,++r):t+=3}return t}t.INITIAL_MEMORY;var z=[],N=[],O=[],$=0,G=null;function H(e){$++,t.monitorRunDependencies&&t.monitorRunDependencies($)}function V(e){if($--,t.monitorRunDependencies&&t.monitorRunDependencies($),0==$&&G){var r=G;G=null,r()}}function j(e){t.onAbort&&t.onAbort(e),y(e="Aborted("+e+")"),w=!0,e+=". Build with -sASSERTIONS for more info.";var r=new WebAssembly.RuntimeError(e);throw i(r),r}var W,Y,q,K,X="data:application/octet-stream;base64,";function Z(e){return e.startsWith(X)}function J(e){return e.startsWith("file://")}function Q(e){try{if(e==W&&g)return new Uint8Array(g);if(o)return o(e);throw"both async and sync fetching of the wasm failed"}catch(e){j(e)}}function ee(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function te(e){for(;e.length>0;)e.shift()(t)}function re(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){B[this.ptr+4>>2]=e},this.get_type=function(){return B[this.ptr+4>>2]},this.set_destructor=function(e){B[this.ptr+8>>2]=e},this.get_destructor=function(){return B[this.ptr+8>>2]},this.set_refcount=function(e){T[this.ptr>>2]=e},this.set_caught=function(e){e=e?1:0,U[this.ptr+12|0]=e},this.get_caught=function(){return 0!=U[this.ptr+12|0]},this.set_rethrown=function(e){e=e?1:0,U[this.ptr+13|0]=e},this.get_rethrown=function(){return 0!=U[this.ptr+13|0]},this.init=function(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=T[this.ptr>>2];T[this.ptr>>2]=e+1},this.release_ref=function(){var e=T[this.ptr>>2];return T[this.ptr>>2]=e-1,1===e},this.set_adjusted_ptr=function(e){B[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return B[this.ptr+16>>2]},this.get_exception_ptr=function(){if(Xt(this.get_type()))return B[this.excPtr>>2];var e=this.get_adjusted_ptr();return 0!==e?e:this.excPtr}}t.locateFile?Z(W="decoder-pro.wasm")||(Y=W,W=t.locateFile?t.locateFile(Y,m):m+Y):W=new URL("decoder-pro.wasm","undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("decoder-pro.js",document.baseURI).href).toString();var ie={isAbs:e=>"/"===e.charAt(0),splitPath:e=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1),normalizeArray:(e,t)=>{for(var r=0,i=e.length-1;i>=0;i--){var n=e[i];"."===n?e.splice(i,1):".."===n?(e.splice(i,1),r++):r&&(e.splice(i,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize:e=>{var t=ie.isAbs(e),r="/"===e.substr(-1);return(e=ie.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||t||(e="."),e&&r&&(e+="/"),(t?"/":"")+e},dirname:e=>{var t=ie.splitPath(e),r=t[0],i=t[1];return r||i?(i&&(i=i.substr(0,i.length-1)),r+i):"."},basename:e=>{if("/"===e)return"/";var t=(e=(e=ie.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===t?e:e.substr(t+1)},join:function(){var e=Array.prototype.slice.call(arguments,0);return ie.normalize(e.join("/"))},join2:(e,t)=>ie.normalize(e+"/"+t)},ne={resolve:function(){for(var e="",t=!1,r=arguments.length-1;r>=-1&&!t;r--){var i=r>=0?arguments[r]:de.cwd();if("string"!=typeof i)throw new TypeError("Arguments to path.resolve must be strings");if(!i)return"";e=i+"/"+e,t=ie.isAbs(i)}return(t?"/":"")+(e=ie.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||"."},relative:(e,t)=>{function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=ne.resolve(e).substr(1),t=ne.resolve(t).substr(1);for(var i=r(e.split("/")),n=r(t.split("/")),s=Math.min(i.length,n.length),o=s,a=0;a<s;a++)if(i[a]!==n[a]){o=a;break}var l=[];for(a=o;a<i.length;a++)l.push("..");return(l=l.concat(n.slice(o))).join("/")}};function se(e,t,r){var i=r>0?r:M(e)+1,n=new Array(i),s=R(e,n,0,n.length);return t&&(n.length=s),n}var oe={ttys:[],init:function(){},shutdown:function(){},register:function(e,t){oe.ttys[e]={input:[],output:[],ops:t},de.registerDevice(e,oe.stream_ops)},stream_ops:{open:function(e){var t=oe.ttys[e.node.rdev];if(!t)throw new de.ErrnoError(43);e.tty=t,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,t,r,i,n){if(!e.tty||!e.tty.ops.get_char)throw new de.ErrnoError(60);for(var s=0,o=0;o<i;o++){var a;try{a=e.tty.ops.get_char(e.tty)}catch(e){throw new de.ErrnoError(29)}if(void 0===a&&0===s)throw new de.ErrnoError(6);if(null==a)break;s++,t[r+o]=a}return s&&(e.node.timestamp=Date.now()),s},write:function(e,t,r,i,n){if(!e.tty||!e.tty.ops.put_char)throw new de.ErrnoError(60);try{for(var s=0;s<i;s++)e.tty.ops.put_char(e.tty,t[r+s])}catch(e){throw new de.ErrnoError(29)}return i&&(e.node.timestamp=Date.now()),s}},default_tty_ops:{get_char:function(e){if(!e.input.length){var t=null;if(p){var r=Buffer.alloc(256),i=0;try{i=a.readSync(process.stdin.fd,r,0,256,-1)}catch(e){if(!e.toString().includes("EOF"))throw e;i=0}t=i>0?r.slice(0,i).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(t=window.prompt("Input: "))&&(t+="\n"):"function"==typeof readline&&null!==(t=readline())&&(t+="\n");if(!t)return null;e.input=se(t,!0)}return e.input.shift()},put_char:function(e,t){null===t||10===t?(_(I(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(_(I(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,t){null===t||10===t?(y(I(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(y(I(e.output,0)),e.output=[])}}};function ae(e){e=function(e,t){return Math.ceil(e/t)*t}(e,65536);var t=Kt(65536,e);return t?(function(e,t){x.fill(0,e,e+t)}(t,e),t):0}var le={ops_table:null,mount:function(e){return le.createNode(null,"/",16895,0)},createNode:function(e,t,r,i){if(de.isBlkdev(r)||de.isFIFO(r))throw new de.ErrnoError(63);le.ops_table||(le.ops_table={dir:{node:{getattr:le.node_ops.getattr,setattr:le.node_ops.setattr,lookup:le.node_ops.lookup,mknod:le.node_ops.mknod,rename:le.node_ops.rename,unlink:le.node_ops.unlink,rmdir:le.node_ops.rmdir,readdir:le.node_ops.readdir,symlink:le.node_ops.symlink},stream:{llseek:le.stream_ops.llseek}},file:{node:{getattr:le.node_ops.getattr,setattr:le.node_ops.setattr},stream:{llseek:le.stream_ops.llseek,read:le.stream_ops.read,write:le.stream_ops.write,allocate:le.stream_ops.allocate,mmap:le.stream_ops.mmap,msync:le.stream_ops.msync}},link:{node:{getattr:le.node_ops.getattr,setattr:le.node_ops.setattr,readlink:le.node_ops.readlink},stream:{}},chrdev:{node:{getattr:le.node_ops.getattr,setattr:le.node_ops.setattr},stream:de.chrdev_stream_ops}});var n=de.createNode(e,t,r,i);return de.isDir(n.mode)?(n.node_ops=le.ops_table.dir.node,n.stream_ops=le.ops_table.dir.stream,n.contents={}):de.isFile(n.mode)?(n.node_ops=le.ops_table.file.node,n.stream_ops=le.ops_table.file.stream,n.usedBytes=0,n.contents=null):de.isLink(n.mode)?(n.node_ops=le.ops_table.link.node,n.stream_ops=le.ops_table.link.stream):de.isChrdev(n.mode)&&(n.node_ops=le.ops_table.chrdev.node,n.stream_ops=le.ops_table.chrdev.stream),n.timestamp=Date.now(),e&&(e.contents[t]=n,e.timestamp=n.timestamp),n},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,t){var r=e.contents?e.contents.length:0;if(!(r>=t)){t=Math.max(t,r*(r<1048576?2:1.125)>>>0),0!=r&&(t=Math.max(t,256));var i=e.contents;e.contents=new Uint8Array(t),e.usedBytes>0&&e.contents.set(i.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,t){if(e.usedBytes!=t)if(0==t)e.contents=null,e.usedBytes=0;else{var r=e.contents;e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t}},node_ops:{getattr:function(e){var t={};return t.dev=de.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,de.isDir(e.mode)?t.size=4096:de.isFile(e.mode)?t.size=e.usedBytes:de.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr:function(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&le.resizeFileStorage(e,t.size)},lookup:function(e,t){throw de.genericErrors[44]},mknod:function(e,t,r,i){return le.createNode(e,t,r,i)},rename:function(e,t,r){if(de.isDir(e.mode)){var i;try{i=de.lookupNode(t,r)}catch(e){}if(i)for(var n in i.contents)throw new de.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink:function(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir:function(e,t){var r=de.lookupNode(e,t);for(var i in r.contents)throw new de.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir:function(e){var t=[".",".."];for(var r in e.contents)e.contents.hasOwnProperty(r)&&t.push(r);return t},symlink:function(e,t,r){var i=le.createNode(e,t,41471,0);return i.link=r,i},readlink:function(e){if(!de.isLink(e.mode))throw new de.ErrnoError(28);return e.link}},stream_ops:{read:function(e,t,r,i,n){var s=e.node.contents;if(n>=e.node.usedBytes)return 0;var o=Math.min(e.node.usedBytes-n,i);if(o>8&&s.subarray)t.set(s.subarray(n,n+o),r);else for(var a=0;a<o;a++)t[r+a]=s[n+a];return o},write:function(e,t,r,i,n,s){if(!i)return 0;var o=e.node;if(o.timestamp=Date.now(),t.subarray&&(!o.contents||o.contents.subarray)){if(s)return o.contents=t.subarray(r,r+i),o.usedBytes=i,i;if(0===o.usedBytes&&0===n)return o.contents=t.slice(r,r+i),o.usedBytes=i,i;if(n+i<=o.usedBytes)return o.contents.set(t.subarray(r,r+i),n),i}if(le.expandFileStorage(o,n+i),o.contents.subarray&&t.subarray)o.contents.set(t.subarray(r,r+i),n);else for(var a=0;a<i;a++)o.contents[n+a]=t[r+a];return o.usedBytes=Math.max(o.usedBytes,n+i),i},llseek:function(e,t,r){var i=t;if(1===r?i+=e.position:2===r&&de.isFile(e.node.mode)&&(i+=e.node.usedBytes),i<0)throw new de.ErrnoError(28);return i},allocate:function(e,t,r){le.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap:function(e,t,r,i,n){if(!de.isFile(e.node.mode))throw new de.ErrnoError(43);var s,o,a=e.node.contents;if(2&n||a.buffer!==S){if((r>0||r+t<a.length)&&(a=a.subarray?a.subarray(r,r+t):Array.prototype.slice.call(a,r,r+t)),o=!0,!(s=ae(t)))throw new de.ErrnoError(48);U.set(a,s)}else o=!1,s=a.byteOffset;return{ptr:s,allocated:o}},msync:function(e,t,r,i,n){if(!de.isFile(e.node.mode))throw new de.ErrnoError(43);return 2&n||le.stream_ops.write(e,t,0,i,r,!1),0}}},de={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:(e,t={})=>{if(!(e=ne.resolve(de.cwd(),e)))return{path:"",node:null};if((t=Object.assign({follow_mount:!0,recurse_count:0},t)).recurse_count>8)throw new de.ErrnoError(32);for(var r=ie.normalizeArray(e.split("/").filter((e=>!!e)),!1),i=de.root,n="/",s=0;s<r.length;s++){var o=s===r.length-1;if(o&&t.parent)break;if(i=de.lookupNode(i,r[s]),n=ie.join2(n,r[s]),de.isMountpoint(i)&&(!o||o&&t.follow_mount)&&(i=i.mounted.root),!o||t.follow)for(var a=0;de.isLink(i.mode);){var l=de.readlink(n);if(n=ne.resolve(ie.dirname(n),l),i=de.lookupPath(n,{recurse_count:t.recurse_count+1}).node,a++>40)throw new de.ErrnoError(32)}}return{path:n,node:i}},getPath:e=>{for(var t;;){if(de.isRoot(e)){var r=e.mount.mountpoint;return t?"/"!==r[r.length-1]?r+"/"+t:r+t:r}t=t?e.name+"/"+t:e.name,e=e.parent}},hashName:(e,t)=>{for(var r=0,i=0;i<t.length;i++)r=(r<<5)-r+t.charCodeAt(i)|0;return(e+r>>>0)%de.nameTable.length},hashAddNode:e=>{var t=de.hashName(e.parent.id,e.name);e.name_next=de.nameTable[t],de.nameTable[t]=e},hashRemoveNode:e=>{var t=de.hashName(e.parent.id,e.name);if(de.nameTable[t]===e)de.nameTable[t]=e.name_next;else for(var r=de.nameTable[t];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:(e,t)=>{var r=de.mayLookup(e);if(r)throw new de.ErrnoError(r,e);for(var i=de.hashName(e.id,t),n=de.nameTable[i];n;n=n.name_next){var s=n.name;if(n.parent.id===e.id&&s===t)return n}return de.lookup(e,t)},createNode:(e,t,r,i)=>{var n=new de.FSNode(e,t,r,i);return de.hashAddNode(n),n},destroyNode:e=>{de.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>32768==(61440&e),isDir:e=>16384==(61440&e),isLink:e=>40960==(61440&e),isChrdev:e=>8192==(61440&e),isBlkdev:e=>24576==(61440&e),isFIFO:e=>4096==(61440&e),isSocket:e=>!(49152&~e),flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:e=>{var t=de.flagModes[e];if(void 0===t)throw new Error("Unknown file open mode: "+e);return t},flagsToPermissionString:e=>{var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},nodePermissions:(e,t)=>de.ignorePermissions||(!t.includes("r")||292&e.mode)&&(!t.includes("w")||146&e.mode)&&(!t.includes("x")||73&e.mode)?0:2,mayLookup:e=>{var t=de.nodePermissions(e,"x");return t||(e.node_ops.lookup?0:2)},mayCreate:(e,t)=>{try{return de.lookupNode(e,t),20}catch(e){}return de.nodePermissions(e,"wx")},mayDelete:(e,t,r)=>{var i;try{i=de.lookupNode(e,t)}catch(e){return e.errno}var n=de.nodePermissions(e,"wx");if(n)return n;if(r){if(!de.isDir(i.mode))return 54;if(de.isRoot(i)||de.getPath(i)===de.cwd())return 10}else if(de.isDir(i.mode))return 31;return 0},mayOpen:(e,t)=>e?de.isLink(e.mode)?32:de.isDir(e.mode)&&("r"!==de.flagsToPermissionString(t)||512&t)?31:de.nodePermissions(e,de.flagsToPermissionString(t)):44,MAX_OPEN_FDS:4096,nextfd:(e=0,t=de.MAX_OPEN_FDS)=>{for(var r=e;r<=t;r++)if(!de.streams[r])return r;throw new de.ErrnoError(33)},getStream:e=>de.streams[e],createStream:(e,t,r)=>{de.FSStream||(de.FSStream=function(){this.shared={}},de.FSStream.prototype={},Object.defineProperties(de.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return!!(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}},flags:{get:function(){return this.shared.flags},set:function(e){this.shared.flags=e}},position:{get:function(){return this.shared.position},set:function(e){this.shared.position=e}}})),e=Object.assign(new de.FSStream,e);var i=de.nextfd(t,r);return e.fd=i,de.streams[i]=e,e},closeStream:e=>{de.streams[e]=null},chrdev_stream_ops:{open:e=>{var t=de.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:()=>{throw new de.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,t)=>e<<8|t,registerDevice:(e,t)=>{de.devices[e]={stream_ops:t}},getDevice:e=>de.devices[e],getMounts:e=>{for(var t=[],r=[e];r.length;){var i=r.pop();t.push(i),r.push.apply(r,i.mounts)}return t},syncfs:(e,t)=>{"function"==typeof e&&(t=e,e=!1),de.syncFSRequests++,de.syncFSRequests>1&&y("warning: "+de.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var r=de.getMounts(de.root.mount),i=0;function n(e){return de.syncFSRequests--,t(e)}function s(e){if(e)return s.errored?void 0:(s.errored=!0,n(e));++i>=r.length&&n(null)}r.forEach((t=>{if(!t.type.syncfs)return s(null);t.type.syncfs(t,e,s)}))},mount:(e,t,r)=>{var i,n="/"===r,s=!r;if(n&&de.root)throw new de.ErrnoError(10);if(!n&&!s){var o=de.lookupPath(r,{follow_mount:!1});if(r=o.path,i=o.node,de.isMountpoint(i))throw new de.ErrnoError(10);if(!de.isDir(i.mode))throw new de.ErrnoError(54)}var a={type:e,opts:t,mountpoint:r,mounts:[]},l=e.mount(a);return l.mount=a,a.root=l,n?de.root=l:i&&(i.mounted=a,i.mount&&i.mount.mounts.push(a)),l},unmount:e=>{var t=de.lookupPath(e,{follow_mount:!1});if(!de.isMountpoint(t.node))throw new de.ErrnoError(28);var r=t.node,i=r.mounted,n=de.getMounts(i);Object.keys(de.nameTable).forEach((e=>{for(var t=de.nameTable[e];t;){var r=t.name_next;n.includes(t.mount)&&de.destroyNode(t),t=r}})),r.mounted=null;var s=r.mount.mounts.indexOf(i);r.mount.mounts.splice(s,1)},lookup:(e,t)=>e.node_ops.lookup(e,t),mknod:(e,t,r)=>{var i=de.lookupPath(e,{parent:!0}).node,n=ie.basename(e);if(!n||"."===n||".."===n)throw new de.ErrnoError(28);var s=de.mayCreate(i,n);if(s)throw new de.ErrnoError(s);if(!i.node_ops.mknod)throw new de.ErrnoError(63);return i.node_ops.mknod(i,n,t,r)},create:(e,t)=>(t=void 0!==t?t:438,t&=4095,t|=32768,de.mknod(e,t,0)),mkdir:(e,t)=>(t=void 0!==t?t:511,t&=1023,t|=16384,de.mknod(e,t,0)),mkdirTree:(e,t)=>{for(var r=e.split("/"),i="",n=0;n<r.length;++n)if(r[n]){i+="/"+r[n];try{de.mkdir(i,t)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,t,r)=>(void 0===r&&(r=t,t=438),t|=8192,de.mknod(e,t,r)),symlink:(e,t)=>{if(!ne.resolve(e))throw new de.ErrnoError(44);var r=de.lookupPath(t,{parent:!0}).node;if(!r)throw new de.ErrnoError(44);var i=ie.basename(t),n=de.mayCreate(r,i);if(n)throw new de.ErrnoError(n);if(!r.node_ops.symlink)throw new de.ErrnoError(63);return r.node_ops.symlink(r,i,e)},rename:(e,t)=>{var r,i,n=ie.dirname(e),s=ie.dirname(t),o=ie.basename(e),a=ie.basename(t);if(r=de.lookupPath(e,{parent:!0}).node,i=de.lookupPath(t,{parent:!0}).node,!r||!i)throw new de.ErrnoError(44);if(r.mount!==i.mount)throw new de.ErrnoError(75);var l,d=de.lookupNode(r,o),u=ne.relative(e,s);if("."!==u.charAt(0))throw new de.ErrnoError(28);if("."!==(u=ne.relative(t,n)).charAt(0))throw new de.ErrnoError(55);try{l=de.lookupNode(i,a)}catch(e){}if(d!==l){var c=de.isDir(d.mode),f=de.mayDelete(r,o,c);if(f)throw new de.ErrnoError(f);if(f=l?de.mayDelete(i,a,c):de.mayCreate(i,a))throw new de.ErrnoError(f);if(!r.node_ops.rename)throw new de.ErrnoError(63);if(de.isMountpoint(d)||l&&de.isMountpoint(l))throw new de.ErrnoError(10);if(i!==r&&(f=de.nodePermissions(r,"w")))throw new de.ErrnoError(f);de.hashRemoveNode(d);try{r.node_ops.rename(d,i,a)}catch(e){throw e}finally{de.hashAddNode(d)}}},rmdir:e=>{var t=de.lookupPath(e,{parent:!0}).node,r=ie.basename(e),i=de.lookupNode(t,r),n=de.mayDelete(t,r,!0);if(n)throw new de.ErrnoError(n);if(!t.node_ops.rmdir)throw new de.ErrnoError(63);if(de.isMountpoint(i))throw new de.ErrnoError(10);t.node_ops.rmdir(t,r),de.destroyNode(i)},readdir:e=>{var t=de.lookupPath(e,{follow:!0}).node;if(!t.node_ops.readdir)throw new de.ErrnoError(54);return t.node_ops.readdir(t)},unlink:e=>{var t=de.lookupPath(e,{parent:!0}).node;if(!t)throw new de.ErrnoError(44);var r=ie.basename(e),i=de.lookupNode(t,r),n=de.mayDelete(t,r,!1);if(n)throw new de.ErrnoError(n);if(!t.node_ops.unlink)throw new de.ErrnoError(63);if(de.isMountpoint(i))throw new de.ErrnoError(10);t.node_ops.unlink(t,r),de.destroyNode(i)},readlink:e=>{var t=de.lookupPath(e).node;if(!t)throw new de.ErrnoError(44);if(!t.node_ops.readlink)throw new de.ErrnoError(28);return ne.resolve(de.getPath(t.parent),t.node_ops.readlink(t))},stat:(e,t)=>{var r=de.lookupPath(e,{follow:!t}).node;if(!r)throw new de.ErrnoError(44);if(!r.node_ops.getattr)throw new de.ErrnoError(63);return r.node_ops.getattr(r)},lstat:e=>de.stat(e,!0),chmod:(e,t,r)=>{var i;if(!(i="string"==typeof e?de.lookupPath(e,{follow:!r}).node:e).node_ops.setattr)throw new de.ErrnoError(63);i.node_ops.setattr(i,{mode:4095&t|-4096&i.mode,timestamp:Date.now()})},lchmod:(e,t)=>{de.chmod(e,t,!0)},fchmod:(e,t)=>{var r=de.getStream(e);if(!r)throw new de.ErrnoError(8);de.chmod(r.node,t)},chown:(e,t,r,i)=>{var n;if(!(n="string"==typeof e?de.lookupPath(e,{follow:!i}).node:e).node_ops.setattr)throw new de.ErrnoError(63);n.node_ops.setattr(n,{timestamp:Date.now()})},lchown:(e,t,r)=>{de.chown(e,t,r,!0)},fchown:(e,t,r)=>{var i=de.getStream(e);if(!i)throw new de.ErrnoError(8);de.chown(i.node,t,r)},truncate:(e,t)=>{if(t<0)throw new de.ErrnoError(28);var r;if(!(r="string"==typeof e?de.lookupPath(e,{follow:!0}).node:e).node_ops.setattr)throw new de.ErrnoError(63);if(de.isDir(r.mode))throw new de.ErrnoError(31);if(!de.isFile(r.mode))throw new de.ErrnoError(28);var i=de.nodePermissions(r,"w");if(i)throw new de.ErrnoError(i);r.node_ops.setattr(r,{size:t,timestamp:Date.now()})},ftruncate:(e,t)=>{var r=de.getStream(e);if(!r)throw new de.ErrnoError(8);if(!(2097155&r.flags))throw new de.ErrnoError(28);de.truncate(r.node,t)},utime:(e,t,r)=>{var i=de.lookupPath(e,{follow:!0}).node;i.node_ops.setattr(i,{timestamp:Math.max(t,r)})},open:(e,r,i)=>{if(""===e)throw new de.ErrnoError(44);var n;if(i=void 0===i?438:i,i=64&(r="string"==typeof r?de.modeStringToFlags(r):r)?4095&i|32768:0,"object"==typeof e)n=e;else{e=ie.normalize(e);try{n=de.lookupPath(e,{follow:!(131072&r)}).node}catch(e){}}var s=!1;if(64&r)if(n){if(128&r)throw new de.ErrnoError(20)}else n=de.mknod(e,i,0),s=!0;if(!n)throw new de.ErrnoError(44);if(de.isChrdev(n.mode)&&(r&=-513),65536&r&&!de.isDir(n.mode))throw new de.ErrnoError(54);if(!s){var o=de.mayOpen(n,r);if(o)throw new de.ErrnoError(o)}512&r&&!s&&de.truncate(n,0),r&=-131713;var a=de.createStream({node:n,path:de.getPath(n),flags:r,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return a.stream_ops.open&&a.stream_ops.open(a),!t.logReadFiles||1&r||(de.readFiles||(de.readFiles={}),e in de.readFiles||(de.readFiles[e]=1)),a},close:e=>{if(de.isClosed(e))throw new de.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{de.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek:(e,t,r)=>{if(de.isClosed(e))throw new de.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new de.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new de.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read:(e,t,r,i,n)=>{if(i<0||n<0)throw new de.ErrnoError(28);if(de.isClosed(e))throw new de.ErrnoError(8);if(1==(2097155&e.flags))throw new de.ErrnoError(8);if(de.isDir(e.node.mode))throw new de.ErrnoError(31);if(!e.stream_ops.read)throw new de.ErrnoError(28);var s=void 0!==n;if(s){if(!e.seekable)throw new de.ErrnoError(70)}else n=e.position;var o=e.stream_ops.read(e,t,r,i,n);return s||(e.position+=o),o},write:(e,t,r,i,n,s)=>{if(i<0||n<0)throw new de.ErrnoError(28);if(de.isClosed(e))throw new de.ErrnoError(8);if(!(2097155&e.flags))throw new de.ErrnoError(8);if(de.isDir(e.node.mode))throw new de.ErrnoError(31);if(!e.stream_ops.write)throw new de.ErrnoError(28);e.seekable&&1024&e.flags&&de.llseek(e,0,2);var o=void 0!==n;if(o){if(!e.seekable)throw new de.ErrnoError(70)}else n=e.position;var a=e.stream_ops.write(e,t,r,i,n,s);return o||(e.position+=a),a},allocate:(e,t,r)=>{if(de.isClosed(e))throw new de.ErrnoError(8);if(t<0||r<=0)throw new de.ErrnoError(28);if(!(2097155&e.flags))throw new de.ErrnoError(8);if(!de.isFile(e.node.mode)&&!de.isDir(e.node.mode))throw new de.ErrnoError(43);if(!e.stream_ops.allocate)throw new de.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap:(e,t,r,i,n)=>{if(2&i&&!(2&n)&&2!=(2097155&e.flags))throw new de.ErrnoError(2);if(1==(2097155&e.flags))throw new de.ErrnoError(2);if(!e.stream_ops.mmap)throw new de.ErrnoError(43);return e.stream_ops.mmap(e,t,r,i,n)},msync:(e,t,r,i,n)=>e&&e.stream_ops.msync?e.stream_ops.msync(e,t,r,i,n):0,munmap:e=>0,ioctl:(e,t,r)=>{if(!e.stream_ops.ioctl)throw new de.ErrnoError(59);return e.stream_ops.ioctl(e,t,r)},readFile:(e,t={})=>{if(t.flags=t.flags||0,t.encoding=t.encoding||"binary","utf8"!==t.encoding&&"binary"!==t.encoding)throw new Error('Invalid encoding type "'+t.encoding+'"');var r,i=de.open(e,t.flags),n=de.stat(e).size,s=new Uint8Array(n);return de.read(i,s,0,n,0),"utf8"===t.encoding?r=I(s,0):"binary"===t.encoding&&(r=s),de.close(i),r},writeFile:(e,t,r={})=>{r.flags=r.flags||577;var i=de.open(e,r.flags,r.mode);if("string"==typeof t){var n=new Uint8Array(M(t)+1),s=R(t,n,0,n.length);de.write(i,n,0,s,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(t))throw new Error("Unsupported data type");de.write(i,t,0,t.byteLength,void 0,r.canOwn)}de.close(i)},cwd:()=>de.currentPath,chdir:e=>{var t=de.lookupPath(e,{follow:!0});if(null===t.node)throw new de.ErrnoError(44);if(!de.isDir(t.node.mode))throw new de.ErrnoError(54);var r=de.nodePermissions(t.node,"x");if(r)throw new de.ErrnoError(r);de.currentPath=t.path},createDefaultDirectories:()=>{de.mkdir("/tmp"),de.mkdir("/home"),de.mkdir("/home/<USER>")},createDefaultDevices:()=>{de.mkdir("/dev"),de.registerDevice(de.makedev(1,3),{read:()=>0,write:(e,t,r,i,n)=>i}),de.mkdev("/dev/null",de.makedev(1,3)),oe.register(de.makedev(5,0),oe.default_tty_ops),oe.register(de.makedev(6,0),oe.default_tty1_ops),de.mkdev("/dev/tty",de.makedev(5,0)),de.mkdev("/dev/tty1",de.makedev(6,0));var e=function(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return()=>(crypto.getRandomValues(e),e[0])}if(p)try{var t=require("crypto");return()=>t.randomBytes(1)[0]}catch(e){}return()=>j("randomDevice")}();de.createDevice("/dev","random",e),de.createDevice("/dev","urandom",e),de.mkdir("/dev/shm"),de.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{de.mkdir("/proc");var e=de.mkdir("/proc/self");de.mkdir("/proc/self/fd"),de.mount({mount:()=>{var t=de.createNode(e,"fd",16895,73);return t.node_ops={lookup:(e,t)=>{var r=+t,i=de.getStream(r);if(!i)throw new de.ErrnoError(8);var n={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>i.path}};return n.parent=n,n}},t}},{},"/proc/self/fd")},createStandardStreams:()=>{t.stdin?de.createDevice("/dev","stdin",t.stdin):de.symlink("/dev/tty","/dev/stdin"),t.stdout?de.createDevice("/dev","stdout",null,t.stdout):de.symlink("/dev/tty","/dev/stdout"),t.stderr?de.createDevice("/dev","stderr",null,t.stderr):de.symlink("/dev/tty1","/dev/stderr"),de.open("/dev/stdin",0),de.open("/dev/stdout",1),de.open("/dev/stderr",1)},ensureErrnoError:()=>{de.ErrnoError||(de.ErrnoError=function(e,t){this.node=t,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},de.ErrnoError.prototype=new Error,de.ErrnoError.prototype.constructor=de.ErrnoError,[44].forEach((e=>{de.genericErrors[e]=new de.ErrnoError(e),de.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:()=>{de.ensureErrnoError(),de.nameTable=new Array(4096),de.mount(le,{},"/"),de.createDefaultDirectories(),de.createDefaultDevices(),de.createSpecialDirectories(),de.filesystems={MEMFS:le}},init:(e,r,i)=>{de.init.initialized=!0,de.ensureErrnoError(),t.stdin=e||t.stdin,t.stdout=r||t.stdout,t.stderr=i||t.stderr,de.createStandardStreams()},quit:()=>{de.init.initialized=!1;for(var e=0;e<de.streams.length;e++){var t=de.streams[e];t&&de.close(t)}},getMode:(e,t)=>{var r=0;return e&&(r|=365),t&&(r|=146),r},findObject:(e,t)=>{var r=de.analyzePath(e,t);return r.exists?r.object:null},analyzePath:(e,t)=>{try{e=(i=de.lookupPath(e,{follow:!t})).path}catch(e){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var i=de.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=i.path,r.parentObject=i.node,r.name=ie.basename(e),i=de.lookupPath(e,{follow:!t}),r.exists=!0,r.path=i.path,r.object=i.node,r.name=i.node.name,r.isRoot="/"===i.path}catch(e){r.error=e.errno}return r},createPath:(e,t,r,i)=>{e="string"==typeof e?e:de.getPath(e);for(var n=t.split("/").reverse();n.length;){var s=n.pop();if(s){var o=ie.join2(e,s);try{de.mkdir(o)}catch(e){}e=o}}return o},createFile:(e,t,r,i,n)=>{var s=ie.join2("string"==typeof e?e:de.getPath(e),t),o=de.getMode(i,n);return de.create(s,o)},createDataFile:(e,t,r,i,n,s)=>{var o=t;e&&(e="string"==typeof e?e:de.getPath(e),o=t?ie.join2(e,t):e);var a=de.getMode(i,n),l=de.create(o,a);if(r){if("string"==typeof r){for(var d=new Array(r.length),u=0,c=r.length;u<c;++u)d[u]=r.charCodeAt(u);r=d}de.chmod(l,146|a);var f=de.open(l,577);de.write(f,r,0,r.length,0,s),de.close(f),de.chmod(l,a)}return l},createDevice:(e,t,r,i)=>{var n=ie.join2("string"==typeof e?e:de.getPath(e),t),s=de.getMode(!!r,!!i);de.createDevice.major||(de.createDevice.major=64);var o=de.makedev(de.createDevice.major++,0);return de.registerDevice(o,{open:e=>{e.seekable=!1},close:e=>{i&&i.buffer&&i.buffer.length&&i(10)},read:(e,t,i,n,s)=>{for(var o=0,a=0;a<n;a++){var l;try{l=r()}catch(e){throw new de.ErrnoError(29)}if(void 0===l&&0===o)throw new de.ErrnoError(6);if(null==l)break;o++,t[i+a]=l}return o&&(e.node.timestamp=Date.now()),o},write:(e,t,r,n,s)=>{for(var o=0;o<n;o++)try{i(t[r+o])}catch(e){throw new de.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),o}}),de.mkdev(n,s,o)},forceLoadFile:e=>{if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!n)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=se(n(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new de.ErrnoError(29)}},createLazyFile:(e,t,r,i,n)=>{function s(){this.lengthKnown=!1,this.chunks=[]}if(s.prototype.get=function(e){if(!(e>this.length-1||e<0)){var t=e%this.chunkSize,r=e/this.chunkSize|0;return this.getter(r)[t]}},s.prototype.setDataGetter=function(e){this.getter=e},s.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",r,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+r+". Status: "+e.status);var t,i=Number(e.getResponseHeader("Content-length")),n=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t,s=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t,o=1048576;n||(o=i);var a=this;a.setDataGetter((e=>{var t=e*o,n=(e+1)*o-1;if(n=Math.min(n,i-1),void 0===a.chunks[e]&&(a.chunks[e]=((e,t)=>{if(e>t)throw new Error("invalid range ("+e+", "+t+") or no bytes requested!");if(t>i-1)throw new Error("only "+i+" bytes available! programmer error!");var n=new XMLHttpRequest;if(n.open("GET",r,!1),i!==o&&n.setRequestHeader("Range","bytes="+e+"-"+t),n.responseType="arraybuffer",n.overrideMimeType&&n.overrideMimeType("text/plain; charset=x-user-defined"),n.send(null),!(n.status>=200&&n.status<300||304===n.status))throw new Error("Couldn't load "+r+". Status: "+n.status);return void 0!==n.response?new Uint8Array(n.response||[]):se(n.responseText||"",!0)})(t,n)),void 0===a.chunks[e])throw new Error("doXHR failed!");return a.chunks[e]})),!s&&i||(o=i=1,i=this.getter(0).length,o=i,_("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=i,this._chunkSize=o,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!h)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var o=new s;Object.defineProperties(o,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var a={isDevice:!1,contents:o}}else a={isDevice:!1,url:r};var l=de.createFile(e,t,a,i,n);a.contents?l.contents=a.contents:a.url&&(l.contents=null,l.url=a.url),Object.defineProperties(l,{usedBytes:{get:function(){return this.contents.length}}});var d={};function u(e,t,r,i,n){var s=e.node.contents;if(n>=s.length)return 0;var o=Math.min(s.length-n,i);if(s.slice)for(var a=0;a<o;a++)t[r+a]=s[n+a];else for(a=0;a<o;a++)t[r+a]=s.get(n+a);return o}return Object.keys(l.stream_ops).forEach((e=>{var t=l.stream_ops[e];d[e]=function(){return de.forceLoadFile(l),t.apply(null,arguments)}})),d.read=(e,t,r,i,n)=>(de.forceLoadFile(l),u(e,t,r,i,n)),d.mmap=(e,t,r,i,n)=>{de.forceLoadFile(l);var s=ae(t);if(!s)throw new de.ErrnoError(48);return u(e,U,s,t,r),{ptr:s,allocated:!0}},l.stream_ops=d,l},createPreloadedFile:(e,t,r,i,n,o,a,l,d,u)=>{var c=t?ne.resolve(ie.join2(e,t)):e;function f(r){function s(r){u&&u(),l||de.createDataFile(e,t,r,i,n,d),o&&o(),V()}Browser.handledByPreloadPlugin(r,c,s,(()=>{a&&a(),V()}))||s(r)}H(),"string"==typeof r?function(e,t,r,i){var n=i?"":"al "+e;s(e,(r=>{E(r,'Loading data file "'+e+'" failed (no arrayBuffer).'),t(new Uint8Array(r)),n&&V()}),(t=>{if(!r)throw'Loading data file "'+e+'" failed.';r()})),n&&H()}(r,(e=>f(e)),a):f(r)},indexedDB:()=>window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB,DB_NAME:()=>"EM_FS_"+window.location.pathname,DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var i=de.indexedDB();try{var n=i.open(de.DB_NAME(),de.DB_VERSION)}catch(e){return r(e)}n.onupgradeneeded=()=>{_("creating db"),n.result.createObjectStore(de.DB_STORE_NAME)},n.onsuccess=()=>{var i=n.result.transaction([de.DB_STORE_NAME],"readwrite"),s=i.objectStore(de.DB_STORE_NAME),o=0,a=0,l=e.length;function d(){0==a?t():r()}e.forEach((e=>{var t=s.put(de.analyzePath(e).object.contents,e);t.onsuccess=()=>{++o+a==l&&d()},t.onerror=()=>{a++,o+a==l&&d()}})),i.onerror=r},n.onerror=r},loadFilesFromDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var i=de.indexedDB();try{var n=i.open(de.DB_NAME(),de.DB_VERSION)}catch(e){return r(e)}n.onupgradeneeded=r,n.onsuccess=()=>{var i=n.result;try{var s=i.transaction([de.DB_STORE_NAME],"readonly")}catch(e){return void r(e)}var o=s.objectStore(de.DB_STORE_NAME),a=0,l=0,d=e.length;function u(){0==l?t():r()}e.forEach((e=>{var t=o.get(e);t.onsuccess=()=>{de.analyzePath(e).exists&&de.unlink(e),de.createDataFile(ie.dirname(e),ie.basename(e),t.result,!0,!0,!0),++a+l==d&&u()},t.onerror=()=>{l++,a+l==d&&u()}})),s.onerror=r},n.onerror=r}},ue={DEFAULT_POLLMASK:5,calculateAt:function(e,t,r){if(ie.isAbs(t))return t;var i;if(-100===e)i=de.cwd();else{var n=de.getStream(e);if(!n)throw new de.ErrnoError(8);i=n.path}if(0==t.length){if(!r)throw new de.ErrnoError(44);return i}return ie.join2(i,t)},doStat:function(e,t,r){try{var i=e(t)}catch(e){if(e&&e.node&&ie.normalize(t)!==ie.normalize(de.getPath(e.node)))return-54;throw e}return T[r>>2]=i.dev,T[r+4>>2]=0,T[r+8>>2]=i.ino,T[r+12>>2]=i.mode,T[r+16>>2]=i.nlink,T[r+20>>2]=i.uid,T[r+24>>2]=i.gid,T[r+28>>2]=i.rdev,T[r+32>>2]=0,K=[i.size>>>0,(q=i.size,+Math.abs(q)>=1?q>0?(0|Math.min(+Math.floor(q/4294967296),4294967295))>>>0:~~+Math.ceil((q-+(~~q>>>0))/4294967296)>>>0:0)],T[r+40>>2]=K[0],T[r+44>>2]=K[1],T[r+48>>2]=4096,T[r+52>>2]=i.blocks,K=[Math.floor(i.atime.getTime()/1e3)>>>0,(q=Math.floor(i.atime.getTime()/1e3),+Math.abs(q)>=1?q>0?(0|Math.min(+Math.floor(q/4294967296),4294967295))>>>0:~~+Math.ceil((q-+(~~q>>>0))/4294967296)>>>0:0)],T[r+56>>2]=K[0],T[r+60>>2]=K[1],T[r+64>>2]=0,K=[Math.floor(i.mtime.getTime()/1e3)>>>0,(q=Math.floor(i.mtime.getTime()/1e3),+Math.abs(q)>=1?q>0?(0|Math.min(+Math.floor(q/4294967296),4294967295))>>>0:~~+Math.ceil((q-+(~~q>>>0))/4294967296)>>>0:0)],T[r+72>>2]=K[0],T[r+76>>2]=K[1],T[r+80>>2]=0,K=[Math.floor(i.ctime.getTime()/1e3)>>>0,(q=Math.floor(i.ctime.getTime()/1e3),+Math.abs(q)>=1?q>0?(0|Math.min(+Math.floor(q/4294967296),4294967295))>>>0:~~+Math.ceil((q-+(~~q>>>0))/4294967296)>>>0:0)],T[r+88>>2]=K[0],T[r+92>>2]=K[1],T[r+96>>2]=0,K=[i.ino>>>0,(q=i.ino,+Math.abs(q)>=1?q>0?(0|Math.min(+Math.floor(q/4294967296),4294967295))>>>0:~~+Math.ceil((q-+(~~q>>>0))/4294967296)>>>0:0)],T[r+104>>2]=K[0],T[r+108>>2]=K[1],0},doMsync:function(e,t,r,i,n){var s=x.slice(e,e+r);de.msync(t,s,n,r,i)},varargs:void 0,get:function(){return ue.varargs+=4,T[ue.varargs-4>>2]},getStr:function(e){return L(e)},getStreamFromFD:function(e){var t=de.getStream(e);if(!t)throw new de.ErrnoError(8);return t}};function ce(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var fe=void 0;function he(e){for(var t="",r=e;x[r];)t+=fe[x[r++]];return t}var pe={},me={},_e={},ye=48,ge=57;function ve(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=ye&&t<=ge?"_"+e:e}function be(e,t){return e=ve(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(t)}function we(e,t){var r=be(t,(function(e){this.name=t,this.message=e;var r=new Error(e).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))}));return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var Ee=void 0;function Se(e){throw new Ee(e)}var Ue=void 0;function xe(e){throw new Ue(e)}function Ae(e,t,r){function i(t){var i=r(t);i.length!==e.length&&xe("Mismatched type converter count");for(var n=0;n<e.length;++n)ke(e[n],i[n])}e.forEach((function(e){_e[e]=t}));var n=new Array(t.length),s=[],o=0;t.forEach(((e,t)=>{me.hasOwnProperty(e)?n[t]=me[e]:(s.push(e),pe.hasOwnProperty(e)||(pe[e]=[]),pe[e].push((()=>{n[t]=me[e],++o===s.length&&i(n)})))})),0===s.length&&i(n)}function ke(e,t,r={}){if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var i=t.name;if(e||Se('type "'+i+'" must have a positive integer typeid pointer'),me.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;Se("Cannot register type '"+i+"' twice")}if(me[e]=t,delete _e[e],pe.hasOwnProperty(e)){var n=pe[e];delete pe[e],n.forEach((e=>e()))}}function Te(e){if(!(this instanceof Xe))return!1;if(!(e instanceof Xe))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,i=e.$$.ptrType.registeredClass,n=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;i.baseClass;)n=i.upcast(n),i=i.baseClass;return t===i&&r===n}function Be(e){Se(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Ce=!1;function De(e){}function Fe(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function Pe(e,t,r){if(t===r)return e;if(void 0===r.baseClass)return null;var i=Pe(e,t,r.baseClass);return null===i?null:r.downcast(i)}var Ie={};function Le(){return Object.keys($e).length}function Re(){var e=[];for(var t in $e)$e.hasOwnProperty(t)&&e.push($e[t]);return e}var Me=[];function ze(){for(;Me.length;){var e=Me.pop();e.$$.deleteScheduled=!1,e.delete()}}var Ne=void 0;function Oe(e){Ne=e,Me.length&&Ne&&Ne(ze)}var $e={};function Ge(e,t){return t=function(e,t){for(void 0===t&&Se("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),$e[t]}function He(e,t){return t.ptrType&&t.ptr||xe("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!=!!t.smartPtr&&xe("Both smartPtrType and smartPtr must be specified"),t.count={value:1},je(Object.create(e,{$$:{value:t}}))}function Ve(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var r=Ge(this.registeredClass,t);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();var i=r.clone();return this.destructor(e),i}function n(){return this.isSmartPointer?He(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):He(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var s,o=this.registeredClass.getActualType(t),a=Ie[o];if(!a)return n.call(this);s=this.isConst?a.constPointerType:a.pointerType;var l=Pe(t,this.registeredClass,s.registeredClass);return null===l?n.call(this):this.isSmartPointer?He(s.registeredClass.instancePrototype,{ptrType:s,ptr:l,smartPtrType:this,smartPtr:e}):He(s.registeredClass.instancePrototype,{ptrType:s,ptr:l})}function je(e){return"undefined"==typeof FinalizationRegistry?(je=e=>e,e):(Ce=new FinalizationRegistry((e=>{Fe(e.$$)})),je=e=>{var t=e.$$;if(t.smartPtr){var r={$$:t};Ce.register(e,r,e)}return e},De=e=>Ce.unregister(e),je(e))}function We(){if(this.$$.ptr||Be(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=je(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t}function Ye(){this.$$.ptr||Be(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Se("Object already scheduled for deletion"),De(this),Fe(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function qe(){return!this.$$.ptr}function Ke(){return this.$$.ptr||Be(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Se("Object already scheduled for deletion"),Me.push(this),1===Me.length&&Ne&&Ne(ze),this.$$.deleteScheduled=!0,this}function Xe(){}function Ze(e,t,r){if(void 0===e[t].overloadTable){var i=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||Se("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[i.argCount]=i}}function Je(e,t,r,i,n,s,o,a){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=i,this.baseClass=n,this.getActualType=s,this.upcast=o,this.downcast=a,this.pureVirtualFunctions=[]}function Qe(e,t,r){for(;t!==r;)t.upcast||Se("Expected null or instance of "+r.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function et(e,t){if(null===t)return this.isReference&&Se("null is not a valid "+this.name),0;t.$$||Se('Cannot pass "'+xt(t)+'" as a '+this.name),t.$$.ptr||Se("Cannot pass deleted object as a pointer of type "+this.name);var r=t.$$.ptrType.registeredClass;return Qe(t.$$.ptr,r,this.registeredClass)}function tt(e,t){var r;if(null===t)return this.isReference&&Se("null is not a valid "+this.name),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t.$$||Se('Cannot pass "'+xt(t)+'" as a '+this.name),t.$$.ptr||Se("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&Se("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var i=t.$$.ptrType.registeredClass;if(r=Qe(t.$$.ptr,i,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&Se("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:Se("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var n=t.clone();r=this.rawShare(r,Ut.toHandle((function(){n.delete()}))),null!==e&&e.push(this.rawDestructor,r)}break;default:Se("Unsupporting sharing policy")}return r}function rt(e,t){if(null===t)return this.isReference&&Se("null is not a valid "+this.name),0;t.$$||Se('Cannot pass "'+xt(t)+'" as a '+this.name),t.$$.ptr||Se("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&Se("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var r=t.$$.ptrType.registeredClass;return Qe(t.$$.ptr,r,this.registeredClass)}function it(e){return this.fromWireType(T[e>>2])}function nt(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function st(e){this.rawDestructor&&this.rawDestructor(e)}function ot(e){null!==e&&e.delete()}function at(e,t,r,i,n,s,o,a,l,d,u){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=i,this.isSmartPointer=n,this.pointeeType=s,this.sharingPolicy=o,this.rawGetPointee=a,this.rawConstructor=l,this.rawShare=d,this.rawDestructor=u,n||void 0!==t.baseClass?this.toWireType=tt:i?(this.toWireType=et,this.destructorFunction=null):(this.toWireType=rt,this.destructorFunction=null)}var lt=[];function dt(e){var t=lt[e];return t||(e>=lt.length&&(lt.length=e+1),lt[e]=t=F.get(e)),t}function ut(e,r,i){return e.includes("j")?function(e,r,i){var n=t["dynCall_"+e];return i&&i.length?n.apply(null,[r].concat(i)):n.call(null,r)}(e,r,i):dt(r).apply(null,i)}function ct(e,t){var r,i,n,s=(e=he(e)).includes("j")?(r=e,i=t,n=[],function(){return n.length=0,Object.assign(n,arguments),ut(r,i,n)}):dt(t);return"function"!=typeof s&&Se("unknown function pointer with signature "+e+": "+t),s}var ft=void 0;function ht(e){var t=Yt(e),r=he(t);return Vt(t),r}function pt(e,t){var r=[],i={};throw t.forEach((function e(t){i[t]||me[t]||(_e[t]?_e[t].forEach(e):(r.push(t),i[t]=!0))})),new ft(e+": "+r.map(ht).join([", "]))}function mt(e,t){for(var r=[],i=0;i<e;i++)r.push(B[t+4*i>>2]);return r}function _t(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function yt(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var r=be(e.name||"unknownFunctionName",(function(){}));r.prototype=e.prototype;var i=new r,n=e.apply(i,t);return n instanceof Object?n:i}function gt(e,t,r,i,n){var s=t.length;s<2&&Se("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var o=null!==t[1]&&null!==r,a=!1,l=1;l<t.length;++l)if(null!==t[l]&&void 0===t[l].destructorFunction){a=!0;break}var d="void"!==t[0].name,u="",c="";for(l=0;l<s-2;++l)u+=(0!==l?", ":"")+"arg"+l,c+=(0!==l?", ":"")+"arg"+l+"Wired";var f="return function "+ve(e)+"("+u+") {\nif (arguments.length !== "+(s-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(s-2)+" args!');\n}\n";a&&(f+="var destructors = [];\n");var h=a?"destructors":"null",p=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],m=[Se,i,n,_t,t[0],t[1]];for(o&&(f+="var thisWired = classParam.toWireType("+h+", this);\n"),l=0;l<s-2;++l)f+="var arg"+l+"Wired = argType"+l+".toWireType("+h+", arg"+l+"); // "+t[l+2].name+"\n",p.push("argType"+l),m.push(t[l+2]);if(o&&(c="thisWired"+(c.length>0?", ":"")+c),f+=(d?"var rv = ":"")+"invoker(fn"+(c.length>0?", ":"")+c+");\n",a)f+="runDestructors(destructors);\n";else for(l=o?1:2;l<t.length;++l){var _=1===l?"thisWired":"arg"+(l-2)+"Wired";null!==t[l].destructorFunction&&(f+=_+"_dtor("+_+"); // "+t[l].name+"\n",p.push(_+"_dtor"),m.push(t[l].destructorFunction))}return d&&(f+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),f+="}\n",p.push(f),yt(Function,p).apply(null,m)}var vt=[],bt=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function wt(e){e>4&&0==--bt[e].refcount&&(bt[e]=void 0,vt.push(e))}function Et(){for(var e=0,t=5;t<bt.length;++t)void 0!==bt[t]&&++e;return e}function St(){for(var e=5;e<bt.length;++e)if(void 0!==bt[e])return bt[e];return null}var Ut={toValue:e=>(e||Se("Cannot use deleted val. handle = "+e),bt[e].value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=vt.length?vt.pop():bt.length;return bt[t]={refcount:1,value:e},t}}};function xt(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function At(e,t){switch(t){case 2:return function(e){return this.fromWireType(C[e>>2])};case 3:return function(e){return this.fromWireType(D[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function kt(e,t,r){switch(t){case 0:return r?function(e){return U[e]}:function(e){return x[e]};case 1:return r?function(e){return A[e>>1]}:function(e){return k[e>>1]};case 2:return r?function(e){return T[e>>2]}:function(e){return B[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var Tt="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function Bt(e,t){for(var r=e,i=r>>1,n=i+t/2;!(i>=n)&&k[i];)++i;if((r=i<<1)-e>32&&Tt)return Tt.decode(x.subarray(e,r));for(var s="",o=0;!(o>=t/2);++o){var a=A[e+2*o>>1];if(0==a)break;s+=String.fromCharCode(a)}return s}function Ct(e,t,r){if(void 0===r&&(r=2147483647),r<2)return 0;for(var i=t,n=(r-=2)<2*e.length?r/2:e.length,s=0;s<n;++s){var o=e.charCodeAt(s);A[t>>1]=o,t+=2}return A[t>>1]=0,t-i}function Dt(e){return 2*e.length}function Ft(e,t){for(var r=0,i="";!(r>=t/4);){var n=T[e+4*r>>2];if(0==n)break;if(++r,n>=65536){var s=n-65536;i+=String.fromCharCode(55296|s>>10,56320|1023&s)}else i+=String.fromCharCode(n)}return i}function Pt(e,t,r){if(void 0===r&&(r=2147483647),r<4)return 0;for(var i=t,n=i+r-4,s=0;s<e.length;++s){var o=e.charCodeAt(s);if(o>=55296&&o<=57343&&(o=65536+((1023&o)<<10)|1023&e.charCodeAt(++s)),T[t>>2]=o,(t+=4)+4>n)break}return T[t>>2]=0,t-i}function It(e){for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);i>=55296&&i<=57343&&++r,t+=4}return t}var Lt={},Rt=[],Mt=[],zt={};function Nt(){if(!Nt.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:c||"./this.program"};for(var t in zt)void 0===zt[t]?delete e[t]:e[t]=zt[t];var r=[];for(var t in e)r.push(t+"="+e[t]);Nt.strings=r}return Nt.strings}var Ot=function(e,t,r,i){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=de.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=i},$t=365,Gt=146;Object.defineProperties(Ot.prototype,{read:{get:function(){return(this.mode&$t)===$t},set:function(e){e?this.mode|=$t:this.mode&=-366}},write:{get:function(){return(this.mode&Gt)===Gt},set:function(e){e?this.mode|=Gt:this.mode&=-147}},isFolder:{get:function(){return de.isDir(this.mode)}},isDevice:{get:function(){return de.isChrdev(this.mode)}}}),de.FSNode=Ot,de.staticInit(),function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);fe=e}(),Ee=t.BindingError=we(Error,"BindingError"),Ue=t.InternalError=we(Error,"InternalError"),Xe.prototype.isAliasOf=Te,Xe.prototype.clone=We,Xe.prototype.delete=Ye,Xe.prototype.isDeleted=qe,Xe.prototype.deleteLater=Ke,t.getInheritedInstanceCount=Le,t.getLiveInheritedInstances=Re,t.flushPendingDeletes=ze,t.setDelayFunction=Oe,at.prototype.getPointee=nt,at.prototype.destructor=st,at.prototype.argPackAdvance=8,at.prototype.readValueFromPointer=it,at.prototype.deleteObject=ot,at.prototype.fromWireType=Ve,ft=t.UnboundTypeError=we(Error,"UnboundTypeError"),t.count_emval_handles=Et,t.get_first_emval=St;var Ht={p:function(e){return jt(e+24)+24},o:function(e,t,r){throw new re(e).init(t,r),e},C:function(e,t,r){ue.varargs=r;try{var i=ue.getStreamFromFD(e);switch(t){case 0:return(n=ue.get())<0?-28:de.createStream(i,n).fd;case 1:case 2:case 6:case 7:return 0;case 3:return i.flags;case 4:var n=ue.get();return i.flags|=n,0;case 5:return n=ue.get(),A[n+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return s=28,T[Wt()>>2]=s,-1}}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return-e.errno}var s},w:function(e,t,r,i){ue.varargs=i;try{t=ue.getStr(t),t=ue.calculateAt(e,t);var n=i?ue.get():0;return de.open(t,r,n).fd}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return-e.errno}},t:function(e,t,r,i,n){},E:function(e,t,r,i,n){var s=ce(r);ke(e,{name:t=he(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?i:n},argPackAdvance:8,readValueFromPointer:function(e){var i;if(1===r)i=U;else if(2===r)i=A;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+t);i=T}return this.fromWireType(i[e>>s])},destructorFunction:null})},s:function(e,r,i,n,s,o,a,l,d,u,c,f,h){c=he(c),o=ct(s,o),l&&(l=ct(a,l)),u&&(u=ct(d,u)),h=ct(f,h);var p=ve(c);!function(e,r,i){t.hasOwnProperty(e)?((void 0===i||void 0!==t[e].overloadTable&&void 0!==t[e].overloadTable[i])&&Se("Cannot register public name '"+e+"' twice"),Ze(t,e,e),t.hasOwnProperty(i)&&Se("Cannot register multiple overloads of a function with the same number of arguments ("+i+")!"),t[e].overloadTable[i]=r):(t[e]=r,void 0!==i&&(t[e].numArguments=i))}(p,(function(){pt("Cannot construct "+c+" due to unbound types",[n])})),Ae([e,r,i],n?[n]:[],(function(r){var i,s;r=r[0],s=n?(i=r.registeredClass).instancePrototype:Xe.prototype;var a=be(p,(function(){if(Object.getPrototypeOf(this)!==d)throw new Ee("Use 'new' to construct "+c);if(void 0===f.constructor_body)throw new Ee(c+" has no accessible constructor");var e=f.constructor_body[arguments.length];if(void 0===e)throw new Ee("Tried to invoke ctor of "+c+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(f.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),d=Object.create(s,{constructor:{value:a}});a.prototype=d;var f=new Je(c,a,d,h,i,o,l,u),m=new at(c,f,!0,!1,!1),_=new at(c+"*",f,!1,!1,!1),y=new at(c+" const*",f,!1,!0,!1);return Ie[e]={pointerType:_,constPointerType:y},function(e,r,i){t.hasOwnProperty(e)||xe("Replacing nonexistant public symbol"),void 0!==t[e].overloadTable&&void 0!==i?t[e].overloadTable[i]=r:(t[e]=r,t[e].argCount=i)}(p,a),[m,_,y]}))},q:function(e,t,r,i,n,s){E(t>0);var o=mt(t,r);n=ct(i,n),Ae([],[e],(function(e){var r="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new Ee("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[t-1]=()=>{pt("Cannot construct "+e.name+" due to unbound types",o)},Ae([],o,(function(i){return i.splice(1,0,null),e.registeredClass.constructor_body[t-1]=gt(r,i,null,n,s),[]})),[]}))},d:function(e,t,r,i,n,s,o,a){var l=mt(r,i);t=he(t),s=ct(n,s),Ae([],[e],(function(e){var i=(e=e[0]).name+"."+t;function n(){pt("Cannot call "+i+" due to unbound types",l)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]),a&&e.registeredClass.pureVirtualFunctions.push(t);var d=e.registeredClass.instancePrototype,u=d[t];return void 0===u||void 0===u.overloadTable&&u.className!==e.name&&u.argCount===r-2?(n.argCount=r-2,n.className=e.name,d[t]=n):(Ze(d,t,i),d[t].overloadTable[r-2]=n),Ae([],l,(function(n){var a=gt(i,n,e,s,o);return void 0===d[t].overloadTable?(a.argCount=r-2,d[t]=a):d[t].overloadTable[r-2]=a,[]})),[]}))},D:function(e,t){ke(e,{name:t=he(t),fromWireType:function(e){var t=Ut.toValue(e);return wt(e),t},toWireType:function(e,t){return Ut.toHandle(t)},argPackAdvance:8,readValueFromPointer:it,destructorFunction:null})},l:function(e,t,r){var i=ce(r);ke(e,{name:t=he(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:At(t,i),destructorFunction:null})},c:function(e,t,r,i,n){t=he(t);var s=ce(r),o=e=>e;if(0===i){var a=32-8*r;o=e=>e<<a>>>a}var l=t.includes("unsigned");ke(e,{name:t,fromWireType:o,toWireType:l?function(e,t){return this.name,t>>>0}:function(e,t){return this.name,t},argPackAdvance:8,readValueFromPointer:kt(t,s,0!==i),destructorFunction:null})},b:function(e,t,r){var i=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function n(e){var t=B,r=t[e>>=2],n=t[e+1];return new i(S,n,r)}ke(e,{name:r=he(r),fromWireType:n,argPackAdvance:8,readValueFromPointer:n},{ignoreDuplicateRegistrations:!0})},k:function(e,t){var r="std::string"===(t=he(t));ke(e,{name:t,fromWireType:function(e){var t,i=B[e>>2],n=e+4;if(r)for(var s=n,o=0;o<=i;++o){var a=n+o;if(o==i||0==x[a]){var l=L(s,a-s);void 0===t?t=l:(t+=String.fromCharCode(0),t+=l),s=a+1}}else{var d=new Array(i);for(o=0;o<i;++o)d[o]=String.fromCharCode(x[n+o]);t=d.join("")}return Vt(e),t},toWireType:function(e,t){var i;t instanceof ArrayBuffer&&(t=new Uint8Array(t));var n="string"==typeof t;n||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||Se("Cannot pass non-string to std::string"),i=r&&n?M(t):t.length;var s=jt(4+i+1),o=s+4;if(B[s>>2]=i,r&&n)R(t,x,o,i+1);else if(n)for(var a=0;a<i;++a){var l=t.charCodeAt(a);l>255&&(Vt(o),Se("String has UTF-16 code units that do not fit in 8 bits")),x[o+a]=l}else for(a=0;a<i;++a)x[o+a]=t[a];return null!==e&&e.push(Vt,s),s},argPackAdvance:8,readValueFromPointer:it,destructorFunction:function(e){Vt(e)}})},f:function(e,t,r){var i,n,s,o,a;r=he(r),2===t?(i=Bt,n=Ct,o=Dt,s=()=>k,a=1):4===t&&(i=Ft,n=Pt,o=It,s=()=>B,a=2),ke(e,{name:r,fromWireType:function(e){for(var r,n=B[e>>2],o=s(),l=e+4,d=0;d<=n;++d){var u=e+4+d*t;if(d==n||0==o[u>>a]){var c=i(l,u-l);void 0===r?r=c:(r+=String.fromCharCode(0),r+=c),l=u+t}}return Vt(e),r},toWireType:function(e,i){"string"!=typeof i&&Se("Cannot pass non-string to C++ string type "+r);var s=o(i),l=jt(4+s+t);return B[l>>2]=s>>a,n(i,l+4,s+t),null!==e&&e.push(Vt,l),l},argPackAdvance:8,readValueFromPointer:it,destructorFunction:function(e){Vt(e)}})},n:function(e,t){ke(e,{isVoid:!0,name:t=he(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},e:function(){return Date.now()},i:function(e,t,r,i){var n,s;(e=Rt[e])(t=Ut.toValue(t),r=void 0===(s=Lt[n=r])?he(n):s,null,i)},g:wt,m:function(e,t){var r=function(e,t){for(var r,i,n,s=new Array(e),o=0;o<e;++o)s[o]=(r=B[t+o*b>>2],i="parameter "+o,n=void 0,void 0===(n=me[r])&&Se(i+" has unknown type "+ht(r)),n);return s}(e,t),i=r[0],n=i.name+"_$"+r.slice(1).map((function(e){return e.name})).join("_")+"$",s=Mt[n];if(void 0!==s)return s;for(var o=["retType"],a=[i],l="",d=0;d<e-1;++d)l+=(0!==d?", ":"")+"arg"+d,o.push("argType"+d),a.push(r[1+d]);var u="return function "+ve("methodCaller_"+n)+"(handle, name, destructors, args) {\n",c=0;for(d=0;d<e-1;++d)u+="    var arg"+d+" = argType"+d+".readValueFromPointer(args"+(c?"+"+c:"")+");\n",c+=r[d+1].argPackAdvance;for(u+="    var rv = handle[name]("+l+");\n",d=0;d<e-1;++d)r[d+1].deleteObject&&(u+="    argType"+d+".deleteObject(arg"+d+");\n");i.isVoid||(u+="    return retType.toWireType(destructors, rv);\n"),u+="};\n",o.push(u);var f,h,p=yt(Function,o).apply(null,a);return f=p,h=Rt.length,Rt.push(f),s=h,Mt[n]=s,s},a:function(){j("")},A:function(e,t,r){x.copyWithin(e,t,t+r)},v:function(e){x.length,j("OOM")},y:function(e,t){var r=0;return Nt().forEach((function(i,n){var s=t+r;B[e+4*n>>2]=s,function(e,t,r){for(var i=0;i<e.length;++i)U[0|t++]=e.charCodeAt(i);r||(U[0|t]=0)}(i,s),r+=i.length+1})),0},z:function(e,t){var r=Nt();B[e>>2]=r.length;var i=0;return r.forEach((function(e){i+=e.length+1})),B[t>>2]=i,0},j:function(e){try{var t=ue.getStreamFromFD(e);return de.close(t),0}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return e.errno}},x:function(e,t){try{var r=ue.getStreamFromFD(e),i=r.tty?2:de.isDir(r.mode)?3:de.isLink(r.mode)?7:4;return U[0|t]=i,0}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return e.errno}},B:function(e,t,r,i){try{var n=function(e,t,r,i){for(var n=0,s=0;s<r;s++){var o=B[t>>2],a=B[t+4>>2];t+=8;var l=de.read(e,U,o,a,i);if(l<0)return-1;if(n+=l,l<a)break}return n}(ue.getStreamFromFD(e),t,r);return T[i>>2]=n,0}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return e.errno}},r:function(e,t,r,i,n){try{var s=(l=r)+2097152>>>0<4194305-!!(a=t)?(a>>>0)+4294967296*l:NaN;if(isNaN(s))return 61;var o=ue.getStreamFromFD(e);return de.llseek(o,s,i),K=[o.position>>>0,(q=o.position,+Math.abs(q)>=1?q>0?(0|Math.min(+Math.floor(q/4294967296),4294967295))>>>0:~~+Math.ceil((q-+(~~q>>>0))/4294967296)>>>0:0)],T[n>>2]=K[0],T[n+4>>2]=K[1],o.getdents&&0===s&&0===i&&(o.getdents=null),0}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return e.errno}var a,l},h:function(e,t,r,i){try{var n=function(e,t,r,i){for(var n=0,s=0;s<r;s++){var o=B[t>>2],a=B[t+4>>2];t+=8;var l=de.write(e,U,o,a,i);if(l<0)return-1;n+=l}return n}(ue.getStreamFromFD(e),t,r);return B[i>>2]=n,0}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return e.errno}},u:function(e){}};!function(){var e={a:Ht};function r(e,r){var i,n,s=e.exports;t.asm=s,v=t.asm.F,i=v.buffer,S=i,t.HEAP8=U=new Int8Array(i),t.HEAP16=A=new Int16Array(i),t.HEAP32=T=new Int32Array(i),t.HEAPU8=x=new Uint8Array(i),t.HEAPU16=k=new Uint16Array(i),t.HEAPU32=B=new Uint32Array(i),t.HEAPF32=C=new Float32Array(i),t.HEAPF64=D=new Float64Array(i),F=t.asm.J,n=t.asm.G,N.unshift(n),V()}function n(e){r(e.instance)}function o(t){return function(){if(!g&&(f||h)){if("function"==typeof fetch&&!J(W))return fetch(W,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+W+"'";return e.arrayBuffer()})).catch((function(){return Q(W)}));if(s)return new Promise((function(e,t){s(W,(function(t){e(new Uint8Array(t))}),t)}))}return Promise.resolve().then((function(){return Q(W)}))}().then((function(t){return WebAssembly.instantiate(t,e)})).then((function(e){return e})).then(t,(function(e){y("failed to asynchronously prepare wasm: "+e),j(e)}))}if(H(),t.instantiateWasm)try{return t.instantiateWasm(e,r)}catch(e){return y("Module.instantiateWasm callback failed with error: "+e),!1}(g||"function"!=typeof WebAssembly.instantiateStreaming||Z(W)||J(W)||p||"function"!=typeof fetch?o(n):fetch(W,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(n,(function(e){return y("wasm streaming compile failed: "+e),y("falling back to ArrayBuffer instantiation"),o(n)}))}))).catch(i)}(),t.___wasm_call_ctors=function(){return(t.___wasm_call_ctors=t.asm.G).apply(null,arguments)};var Vt=t._free=function(){return(Vt=t._free=t.asm.H).apply(null,arguments)},jt=t._malloc=function(){return(jt=t._malloc=t.asm.I).apply(null,arguments)},Wt=t.___errno_location=function(){return(Wt=t.___errno_location=t.asm.K).apply(null,arguments)},Yt=t.___getTypeName=function(){return(Yt=t.___getTypeName=t.asm.L).apply(null,arguments)};t.___embind_register_native_and_builtin_types=function(){return(t.___embind_register_native_and_builtin_types=t.asm.M).apply(null,arguments)};var qt,Kt=t._emscripten_builtin_memalign=function(){return(Kt=t._emscripten_builtin_memalign=t.asm.N).apply(null,arguments)},Xt=t.___cxa_is_pointer_type=function(){return(Xt=t.___cxa_is_pointer_type=t.asm.O).apply(null,arguments)};function Zt(e){function i(){qt||(qt=!0,t.calledRun=!0,w||(t.noFSInit||de.init.initialized||de.init(),de.ignorePermissions=!1,te(N),r(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),function(){if(t.postRun)for("function"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;)e=t.postRun.shift(),O.unshift(e);var e;te(O)}()))}$>0||(function(){if(t.preRun)for("function"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)e=t.preRun.shift(),z.unshift(e);var e;te(z)}(),$>0||(t.setStatus?(t.setStatus("Running..."),setTimeout((function(){setTimeout((function(){t.setStatus("")}),1),i()}),1)):i()))}if(t.dynCall_jiji=function(){return(t.dynCall_jiji=t.asm.P).apply(null,arguments)},t._ff_h264_cabac_tables=74748,G=function e(){qt||Zt(),qt||(G=e)},t.preInit)for("function"==typeof t.preInit&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();return Zt(),t.ready}),r=(()=>{var e="undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("decoder-pro.js",document.baseURI).href;return function(t){var r,i;(t=void 0!==(t=t||{})?t:{}).ready=new Promise((function(e,t){r=e,i=t})),(t=void 0!==t?t:{}).locateFile=function(e){return"decoder-pro-audio.wasm"==e&&"undefined"!=typeof EASYPLAYER_PRO_AUDIO_WASM_URL&&""!=EASYPLAYER_PRO_AUDIO_WASM_URL?EASYPLAYER_PRO_AUDIO_WASM_URL:e};var n,s,o,a,l,d,u=Object.assign({},t),c="./this.program",f="object"==typeof window,h="function"==typeof importScripts,p="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,m="";p?(m=h?require("path").dirname(m)+"/":__dirname+"/",d=()=>{l||(a=require("fs"),l=require("path"))},n=function(e,t){return d(),e=l.normalize(e),a.readFileSync(e,t?void 0:"utf8")},o=e=>{var t=n(e,!0);return t.buffer||(t=new Uint8Array(t)),t},s=(e,t,r)=>{d(),e=l.normalize(e),a.readFile(e,(function(e,i){e?r(e):t(i.buffer)}))},process.argv.length>1&&(c=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("uncaughtException",(function(e){if(!(e instanceof ee))throw e})),process.on("unhandledRejection",(function(e){throw e})),t.inspect=function(){return"[Emscripten Module object]"}):(f||h)&&(h?m=self.location.href:"undefined"!=typeof document&&document.currentScript&&(m=document.currentScript.src),e&&(m=e),m=0!==m.indexOf("blob:")?m.substr(0,m.replace(/[?#].*/,"").lastIndexOf("/")+1):"",n=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},h&&(o=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),s=(e,t,r)=>{var i=new XMLHttpRequest;i.open("GET",e,!0),i.responseType="arraybuffer",i.onload=()=>{200==i.status||0==i.status&&i.response?t(i.response):r()},i.onerror=r,i.send(null)});var _=t.print||console.log.bind(console),y=t.printErr||console.warn.bind(console);Object.assign(t,u),u=null,t.arguments&&t.arguments,t.thisProgram&&(c=t.thisProgram),t.quit&&t.quit;var g,v,b=4;t.wasmBinary&&(g=t.wasmBinary),t.noExitRuntime,"object"!=typeof WebAssembly&&j("no native wasm support detected");var w=!1;function E(e,t){e||j(t)}var S,U,x,A,k,T,B,C,D,F,P="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function I(e,t,r){for(var i=t+r,n=t;e[n]&&!(n>=i);)++n;if(n-t>16&&e.buffer&&P)return P.decode(e.subarray(t,n));for(var s="";t<n;){var o=e[t++];if(128&o){var a=63&e[t++];if(192!=(224&o)){var l=63&e[t++];if((o=224==(240&o)?(15&o)<<12|a<<6|l:(7&o)<<18|a<<12|l<<6|63&e[t++])<65536)s+=String.fromCharCode(o);else{var d=o-65536;s+=String.fromCharCode(55296|d>>10,56320|1023&d)}}else s+=String.fromCharCode((31&o)<<6|a)}else s+=String.fromCharCode(o)}return s}function L(e,t){return e?I(x,e,t):""}function R(e,t,r,i){if(!(i>0))return 0;for(var n=r,s=r+i-1,o=0;o<e.length;++o){var a=e.charCodeAt(o);if(a>=55296&&a<=57343)a=65536+((1023&a)<<10)|1023&e.charCodeAt(++o);if(a<=127){if(r>=s)break;t[r++]=a}else if(a<=2047){if(r+1>=s)break;t[r++]=192|a>>6,t[r++]=128|63&a}else if(a<=65535){if(r+2>=s)break;t[r++]=224|a>>12,t[r++]=128|a>>6&63,t[r++]=128|63&a}else{if(r+3>=s)break;t[r++]=240|a>>18,t[r++]=128|a>>12&63,t[r++]=128|a>>6&63,t[r++]=128|63&a}}return t[r]=0,r-n}function M(e){for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);i<=127?t++:i<=2047?t+=2:i>=55296&&i<=57343?(t+=4,++r):t+=3}return t}t.INITIAL_MEMORY;var z=[],N=[],O=[];var $=0,G=null;function H(e){$++,t.monitorRunDependencies&&t.monitorRunDependencies($)}function V(e){if($--,t.monitorRunDependencies&&t.monitorRunDependencies($),0==$&&G){var r=G;G=null,r()}}function j(e){t.onAbort&&t.onAbort(e),y(e="Aborted("+e+")"),w=!0,e+=". Build with -sASSERTIONS for more info.";var r=new WebAssembly.RuntimeError(e);throw i(r),r}var W,Y,q,K,X="data:application/octet-stream;base64,";function Z(e){return e.startsWith(X)}function J(e){return e.startsWith("file://")}function Q(e){try{if(e==W&&g)return new Uint8Array(g);if(o)return o(e);throw"both async and sync fetching of the wasm failed"}catch(e){j(e)}}function ee(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function te(e){for(;e.length>0;)e.shift()(t)}function re(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){B[this.ptr+4>>2]=e},this.get_type=function(){return B[this.ptr+4>>2]},this.set_destructor=function(e){B[this.ptr+8>>2]=e},this.get_destructor=function(){return B[this.ptr+8>>2]},this.set_refcount=function(e){T[this.ptr>>2]=e},this.set_caught=function(e){e=e?1:0,U[this.ptr+12|0]=e},this.get_caught=function(){return 0!=U[this.ptr+12|0]},this.set_rethrown=function(e){e=e?1:0,U[this.ptr+13|0]=e},this.get_rethrown=function(){return 0!=U[this.ptr+13|0]},this.init=function(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=T[this.ptr>>2];T[this.ptr>>2]=e+1},this.release_ref=function(){var e=T[this.ptr>>2];return T[this.ptr>>2]=e-1,1===e},this.set_adjusted_ptr=function(e){B[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return B[this.ptr+16>>2]},this.get_exception_ptr=function(){if(Xt(this.get_type()))return B[this.excPtr>>2];var e=this.get_adjusted_ptr();return 0!==e?e:this.excPtr}}t.locateFile?Z(W="decoder-pro-audio.wasm")||(Y=W,W=t.locateFile?t.locateFile(Y,m):m+Y):W=new URL("decoder-pro-audio.wasm","undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("decoder-pro.js",document.baseURI).href).toString();var ie={isAbs:e=>"/"===e.charAt(0),splitPath:e=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1),normalizeArray:(e,t)=>{for(var r=0,i=e.length-1;i>=0;i--){var n=e[i];"."===n?e.splice(i,1):".."===n?(e.splice(i,1),r++):r&&(e.splice(i,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize:e=>{var t=ie.isAbs(e),r="/"===e.substr(-1);return(e=ie.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||t||(e="."),e&&r&&(e+="/"),(t?"/":"")+e},dirname:e=>{var t=ie.splitPath(e),r=t[0],i=t[1];return r||i?(i&&(i=i.substr(0,i.length-1)),r+i):"."},basename:e=>{if("/"===e)return"/";var t=(e=(e=ie.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===t?e:e.substr(t+1)},join:function(){var e=Array.prototype.slice.call(arguments,0);return ie.normalize(e.join("/"))},join2:(e,t)=>ie.normalize(e+"/"+t)};var ne={resolve:function(){for(var e="",t=!1,r=arguments.length-1;r>=-1&&!t;r--){var i=r>=0?arguments[r]:de.cwd();if("string"!=typeof i)throw new TypeError("Arguments to path.resolve must be strings");if(!i)return"";e=i+"/"+e,t=ie.isAbs(i)}return(t?"/":"")+(e=ie.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||"."},relative:(e,t)=>{function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=ne.resolve(e).substr(1),t=ne.resolve(t).substr(1);for(var i=r(e.split("/")),n=r(t.split("/")),s=Math.min(i.length,n.length),o=s,a=0;a<s;a++)if(i[a]!==n[a]){o=a;break}var l=[];for(a=o;a<i.length;a++)l.push("..");return(l=l.concat(n.slice(o))).join("/")}};function se(e,t,r){var i=r>0?r:M(e)+1,n=new Array(i),s=R(e,n,0,n.length);return t&&(n.length=s),n}var oe={ttys:[],init:function(){},shutdown:function(){},register:function(e,t){oe.ttys[e]={input:[],output:[],ops:t},de.registerDevice(e,oe.stream_ops)},stream_ops:{open:function(e){var t=oe.ttys[e.node.rdev];if(!t)throw new de.ErrnoError(43);e.tty=t,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,t,r,i,n){if(!e.tty||!e.tty.ops.get_char)throw new de.ErrnoError(60);for(var s=0,o=0;o<i;o++){var a;try{a=e.tty.ops.get_char(e.tty)}catch(e){throw new de.ErrnoError(29)}if(void 0===a&&0===s)throw new de.ErrnoError(6);if(null==a)break;s++,t[r+o]=a}return s&&(e.node.timestamp=Date.now()),s},write:function(e,t,r,i,n){if(!e.tty||!e.tty.ops.put_char)throw new de.ErrnoError(60);try{for(var s=0;s<i;s++)e.tty.ops.put_char(e.tty,t[r+s])}catch(e){throw new de.ErrnoError(29)}return i&&(e.node.timestamp=Date.now()),s}},default_tty_ops:{get_char:function(e){if(!e.input.length){var t=null;if(p){var r=Buffer.alloc(256),i=0;try{i=a.readSync(process.stdin.fd,r,0,256,-1)}catch(e){if(!e.toString().includes("EOF"))throw e;i=0}t=i>0?r.slice(0,i).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(t=window.prompt("Input: "))&&(t+="\n"):"function"==typeof readline&&null!==(t=readline())&&(t+="\n");if(!t)return null;e.input=se(t,!0)}return e.input.shift()},put_char:function(e,t){null===t||10===t?(_(I(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(_(I(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,t){null===t||10===t?(y(I(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(y(I(e.output,0)),e.output=[])}}};function ae(e){e=function(e,t){return Math.ceil(e/t)*t}(e,65536);var t=Kt(65536,e);return t?(function(e,t){x.fill(0,e,e+t)}(t,e),t):0}var le={ops_table:null,mount:function(e){return le.createNode(null,"/",16895,0)},createNode:function(e,t,r,i){if(de.isBlkdev(r)||de.isFIFO(r))throw new de.ErrnoError(63);le.ops_table||(le.ops_table={dir:{node:{getattr:le.node_ops.getattr,setattr:le.node_ops.setattr,lookup:le.node_ops.lookup,mknod:le.node_ops.mknod,rename:le.node_ops.rename,unlink:le.node_ops.unlink,rmdir:le.node_ops.rmdir,readdir:le.node_ops.readdir,symlink:le.node_ops.symlink},stream:{llseek:le.stream_ops.llseek}},file:{node:{getattr:le.node_ops.getattr,setattr:le.node_ops.setattr},stream:{llseek:le.stream_ops.llseek,read:le.stream_ops.read,write:le.stream_ops.write,allocate:le.stream_ops.allocate,mmap:le.stream_ops.mmap,msync:le.stream_ops.msync}},link:{node:{getattr:le.node_ops.getattr,setattr:le.node_ops.setattr,readlink:le.node_ops.readlink},stream:{}},chrdev:{node:{getattr:le.node_ops.getattr,setattr:le.node_ops.setattr},stream:de.chrdev_stream_ops}});var n=de.createNode(e,t,r,i);return de.isDir(n.mode)?(n.node_ops=le.ops_table.dir.node,n.stream_ops=le.ops_table.dir.stream,n.contents={}):de.isFile(n.mode)?(n.node_ops=le.ops_table.file.node,n.stream_ops=le.ops_table.file.stream,n.usedBytes=0,n.contents=null):de.isLink(n.mode)?(n.node_ops=le.ops_table.link.node,n.stream_ops=le.ops_table.link.stream):de.isChrdev(n.mode)&&(n.node_ops=le.ops_table.chrdev.node,n.stream_ops=le.ops_table.chrdev.stream),n.timestamp=Date.now(),e&&(e.contents[t]=n,e.timestamp=n.timestamp),n},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,t){var r=e.contents?e.contents.length:0;if(!(r>=t)){t=Math.max(t,r*(r<1048576?2:1.125)>>>0),0!=r&&(t=Math.max(t,256));var i=e.contents;e.contents=new Uint8Array(t),e.usedBytes>0&&e.contents.set(i.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,t){if(e.usedBytes!=t)if(0==t)e.contents=null,e.usedBytes=0;else{var r=e.contents;e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t}},node_ops:{getattr:function(e){var t={};return t.dev=de.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,de.isDir(e.mode)?t.size=4096:de.isFile(e.mode)?t.size=e.usedBytes:de.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr:function(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&le.resizeFileStorage(e,t.size)},lookup:function(e,t){throw de.genericErrors[44]},mknod:function(e,t,r,i){return le.createNode(e,t,r,i)},rename:function(e,t,r){if(de.isDir(e.mode)){var i;try{i=de.lookupNode(t,r)}catch(e){}if(i)for(var n in i.contents)throw new de.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink:function(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir:function(e,t){var r=de.lookupNode(e,t);for(var i in r.contents)throw new de.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir:function(e){var t=[".",".."];for(var r in e.contents)e.contents.hasOwnProperty(r)&&t.push(r);return t},symlink:function(e,t,r){var i=le.createNode(e,t,41471,0);return i.link=r,i},readlink:function(e){if(!de.isLink(e.mode))throw new de.ErrnoError(28);return e.link}},stream_ops:{read:function(e,t,r,i,n){var s=e.node.contents;if(n>=e.node.usedBytes)return 0;var o=Math.min(e.node.usedBytes-n,i);if(o>8&&s.subarray)t.set(s.subarray(n,n+o),r);else for(var a=0;a<o;a++)t[r+a]=s[n+a];return o},write:function(e,t,r,i,n,s){if(!i)return 0;var o=e.node;if(o.timestamp=Date.now(),t.subarray&&(!o.contents||o.contents.subarray)){if(s)return o.contents=t.subarray(r,r+i),o.usedBytes=i,i;if(0===o.usedBytes&&0===n)return o.contents=t.slice(r,r+i),o.usedBytes=i,i;if(n+i<=o.usedBytes)return o.contents.set(t.subarray(r,r+i),n),i}if(le.expandFileStorage(o,n+i),o.contents.subarray&&t.subarray)o.contents.set(t.subarray(r,r+i),n);else for(var a=0;a<i;a++)o.contents[n+a]=t[r+a];return o.usedBytes=Math.max(o.usedBytes,n+i),i},llseek:function(e,t,r){var i=t;if(1===r?i+=e.position:2===r&&de.isFile(e.node.mode)&&(i+=e.node.usedBytes),i<0)throw new de.ErrnoError(28);return i},allocate:function(e,t,r){le.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap:function(e,t,r,i,n){if(!de.isFile(e.node.mode))throw new de.ErrnoError(43);var s,o,a=e.node.contents;if(2&n||a.buffer!==S){if((r>0||r+t<a.length)&&(a=a.subarray?a.subarray(r,r+t):Array.prototype.slice.call(a,r,r+t)),o=!0,!(s=ae(t)))throw new de.ErrnoError(48);U.set(a,s)}else o=!1,s=a.byteOffset;return{ptr:s,allocated:o}},msync:function(e,t,r,i,n){if(!de.isFile(e.node.mode))throw new de.ErrnoError(43);return 2&n||le.stream_ops.write(e,t,0,i,r,!1),0}}};var de={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:(e,t={})=>{if(!(e=ne.resolve(de.cwd(),e)))return{path:"",node:null};if((t=Object.assign({follow_mount:!0,recurse_count:0},t)).recurse_count>8)throw new de.ErrnoError(32);for(var r=ie.normalizeArray(e.split("/").filter((e=>!!e)),!1),i=de.root,n="/",s=0;s<r.length;s++){var o=s===r.length-1;if(o&&t.parent)break;if(i=de.lookupNode(i,r[s]),n=ie.join2(n,r[s]),de.isMountpoint(i)&&(!o||o&&t.follow_mount)&&(i=i.mounted.root),!o||t.follow)for(var a=0;de.isLink(i.mode);){var l=de.readlink(n);if(n=ne.resolve(ie.dirname(n),l),i=de.lookupPath(n,{recurse_count:t.recurse_count+1}).node,a++>40)throw new de.ErrnoError(32)}}return{path:n,node:i}},getPath:e=>{for(var t;;){if(de.isRoot(e)){var r=e.mount.mountpoint;return t?"/"!==r[r.length-1]?r+"/"+t:r+t:r}t=t?e.name+"/"+t:e.name,e=e.parent}},hashName:(e,t)=>{for(var r=0,i=0;i<t.length;i++)r=(r<<5)-r+t.charCodeAt(i)|0;return(e+r>>>0)%de.nameTable.length},hashAddNode:e=>{var t=de.hashName(e.parent.id,e.name);e.name_next=de.nameTable[t],de.nameTable[t]=e},hashRemoveNode:e=>{var t=de.hashName(e.parent.id,e.name);if(de.nameTable[t]===e)de.nameTable[t]=e.name_next;else for(var r=de.nameTable[t];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:(e,t)=>{var r=de.mayLookup(e);if(r)throw new de.ErrnoError(r,e);for(var i=de.hashName(e.id,t),n=de.nameTable[i];n;n=n.name_next){var s=n.name;if(n.parent.id===e.id&&s===t)return n}return de.lookup(e,t)},createNode:(e,t,r,i)=>{var n=new de.FSNode(e,t,r,i);return de.hashAddNode(n),n},destroyNode:e=>{de.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>32768==(61440&e),isDir:e=>16384==(61440&e),isLink:e=>40960==(61440&e),isChrdev:e=>8192==(61440&e),isBlkdev:e=>24576==(61440&e),isFIFO:e=>4096==(61440&e),isSocket:e=>!(49152&~e),flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:e=>{var t=de.flagModes[e];if(void 0===t)throw new Error("Unknown file open mode: "+e);return t},flagsToPermissionString:e=>{var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},nodePermissions:(e,t)=>de.ignorePermissions||(!t.includes("r")||292&e.mode)&&(!t.includes("w")||146&e.mode)&&(!t.includes("x")||73&e.mode)?0:2,mayLookup:e=>{var t=de.nodePermissions(e,"x");return t||(e.node_ops.lookup?0:2)},mayCreate:(e,t)=>{try{de.lookupNode(e,t);return 20}catch(e){}return de.nodePermissions(e,"wx")},mayDelete:(e,t,r)=>{var i;try{i=de.lookupNode(e,t)}catch(e){return e.errno}var n=de.nodePermissions(e,"wx");if(n)return n;if(r){if(!de.isDir(i.mode))return 54;if(de.isRoot(i)||de.getPath(i)===de.cwd())return 10}else if(de.isDir(i.mode))return 31;return 0},mayOpen:(e,t)=>e?de.isLink(e.mode)?32:de.isDir(e.mode)&&("r"!==de.flagsToPermissionString(t)||512&t)?31:de.nodePermissions(e,de.flagsToPermissionString(t)):44,MAX_OPEN_FDS:4096,nextfd:(e=0,t=de.MAX_OPEN_FDS)=>{for(var r=e;r<=t;r++)if(!de.streams[r])return r;throw new de.ErrnoError(33)},getStream:e=>de.streams[e],createStream:(e,t,r)=>{de.FSStream||(de.FSStream=function(){this.shared={}},de.FSStream.prototype={},Object.defineProperties(de.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return!!(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}},flags:{get:function(){return this.shared.flags},set:function(e){this.shared.flags=e}},position:{get:function(){return this.shared.position},set:function(e){this.shared.position=e}}})),e=Object.assign(new de.FSStream,e);var i=de.nextfd(t,r);return e.fd=i,de.streams[i]=e,e},closeStream:e=>{de.streams[e]=null},chrdev_stream_ops:{open:e=>{var t=de.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:()=>{throw new de.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,t)=>e<<8|t,registerDevice:(e,t)=>{de.devices[e]={stream_ops:t}},getDevice:e=>de.devices[e],getMounts:e=>{for(var t=[],r=[e];r.length;){var i=r.pop();t.push(i),r.push.apply(r,i.mounts)}return t},syncfs:(e,t)=>{"function"==typeof e&&(t=e,e=!1),de.syncFSRequests++,de.syncFSRequests>1&&y("warning: "+de.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var r=de.getMounts(de.root.mount),i=0;function n(e){return de.syncFSRequests--,t(e)}function s(e){if(e)return s.errored?void 0:(s.errored=!0,n(e));++i>=r.length&&n(null)}r.forEach((t=>{if(!t.type.syncfs)return s(null);t.type.syncfs(t,e,s)}))},mount:(e,t,r)=>{var i,n="/"===r,s=!r;if(n&&de.root)throw new de.ErrnoError(10);if(!n&&!s){var o=de.lookupPath(r,{follow_mount:!1});if(r=o.path,i=o.node,de.isMountpoint(i))throw new de.ErrnoError(10);if(!de.isDir(i.mode))throw new de.ErrnoError(54)}var a={type:e,opts:t,mountpoint:r,mounts:[]},l=e.mount(a);return l.mount=a,a.root=l,n?de.root=l:i&&(i.mounted=a,i.mount&&i.mount.mounts.push(a)),l},unmount:e=>{var t=de.lookupPath(e,{follow_mount:!1});if(!de.isMountpoint(t.node))throw new de.ErrnoError(28);var r=t.node,i=r.mounted,n=de.getMounts(i);Object.keys(de.nameTable).forEach((e=>{for(var t=de.nameTable[e];t;){var r=t.name_next;n.includes(t.mount)&&de.destroyNode(t),t=r}})),r.mounted=null;var s=r.mount.mounts.indexOf(i);r.mount.mounts.splice(s,1)},lookup:(e,t)=>e.node_ops.lookup(e,t),mknod:(e,t,r)=>{var i=de.lookupPath(e,{parent:!0}).node,n=ie.basename(e);if(!n||"."===n||".."===n)throw new de.ErrnoError(28);var s=de.mayCreate(i,n);if(s)throw new de.ErrnoError(s);if(!i.node_ops.mknod)throw new de.ErrnoError(63);return i.node_ops.mknod(i,n,t,r)},create:(e,t)=>(t=void 0!==t?t:438,t&=4095,t|=32768,de.mknod(e,t,0)),mkdir:(e,t)=>(t=void 0!==t?t:511,t&=1023,t|=16384,de.mknod(e,t,0)),mkdirTree:(e,t)=>{for(var r=e.split("/"),i="",n=0;n<r.length;++n)if(r[n]){i+="/"+r[n];try{de.mkdir(i,t)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,t,r)=>(void 0===r&&(r=t,t=438),t|=8192,de.mknod(e,t,r)),symlink:(e,t)=>{if(!ne.resolve(e))throw new de.ErrnoError(44);var r=de.lookupPath(t,{parent:!0}).node;if(!r)throw new de.ErrnoError(44);var i=ie.basename(t),n=de.mayCreate(r,i);if(n)throw new de.ErrnoError(n);if(!r.node_ops.symlink)throw new de.ErrnoError(63);return r.node_ops.symlink(r,i,e)},rename:(e,t)=>{var r,i,n=ie.dirname(e),s=ie.dirname(t),o=ie.basename(e),a=ie.basename(t);if(r=de.lookupPath(e,{parent:!0}).node,i=de.lookupPath(t,{parent:!0}).node,!r||!i)throw new de.ErrnoError(44);if(r.mount!==i.mount)throw new de.ErrnoError(75);var l,d=de.lookupNode(r,o),u=ne.relative(e,s);if("."!==u.charAt(0))throw new de.ErrnoError(28);if("."!==(u=ne.relative(t,n)).charAt(0))throw new de.ErrnoError(55);try{l=de.lookupNode(i,a)}catch(e){}if(d!==l){var c=de.isDir(d.mode),f=de.mayDelete(r,o,c);if(f)throw new de.ErrnoError(f);if(f=l?de.mayDelete(i,a,c):de.mayCreate(i,a))throw new de.ErrnoError(f);if(!r.node_ops.rename)throw new de.ErrnoError(63);if(de.isMountpoint(d)||l&&de.isMountpoint(l))throw new de.ErrnoError(10);if(i!==r&&(f=de.nodePermissions(r,"w")))throw new de.ErrnoError(f);de.hashRemoveNode(d);try{r.node_ops.rename(d,i,a)}catch(e){throw e}finally{de.hashAddNode(d)}}},rmdir:e=>{var t=de.lookupPath(e,{parent:!0}).node,r=ie.basename(e),i=de.lookupNode(t,r),n=de.mayDelete(t,r,!0);if(n)throw new de.ErrnoError(n);if(!t.node_ops.rmdir)throw new de.ErrnoError(63);if(de.isMountpoint(i))throw new de.ErrnoError(10);t.node_ops.rmdir(t,r),de.destroyNode(i)},readdir:e=>{var t=de.lookupPath(e,{follow:!0}).node;if(!t.node_ops.readdir)throw new de.ErrnoError(54);return t.node_ops.readdir(t)},unlink:e=>{var t=de.lookupPath(e,{parent:!0}).node;if(!t)throw new de.ErrnoError(44);var r=ie.basename(e),i=de.lookupNode(t,r),n=de.mayDelete(t,r,!1);if(n)throw new de.ErrnoError(n);if(!t.node_ops.unlink)throw new de.ErrnoError(63);if(de.isMountpoint(i))throw new de.ErrnoError(10);t.node_ops.unlink(t,r),de.destroyNode(i)},readlink:e=>{var t=de.lookupPath(e).node;if(!t)throw new de.ErrnoError(44);if(!t.node_ops.readlink)throw new de.ErrnoError(28);return ne.resolve(de.getPath(t.parent),t.node_ops.readlink(t))},stat:(e,t)=>{var r=de.lookupPath(e,{follow:!t}).node;if(!r)throw new de.ErrnoError(44);if(!r.node_ops.getattr)throw new de.ErrnoError(63);return r.node_ops.getattr(r)},lstat:e=>de.stat(e,!0),chmod:(e,t,r)=>{var i;"string"==typeof e?i=de.lookupPath(e,{follow:!r}).node:i=e;if(!i.node_ops.setattr)throw new de.ErrnoError(63);i.node_ops.setattr(i,{mode:4095&t|-4096&i.mode,timestamp:Date.now()})},lchmod:(e,t)=>{de.chmod(e,t,!0)},fchmod:(e,t)=>{var r=de.getStream(e);if(!r)throw new de.ErrnoError(8);de.chmod(r.node,t)},chown:(e,t,r,i)=>{var n;"string"==typeof e?n=de.lookupPath(e,{follow:!i}).node:n=e;if(!n.node_ops.setattr)throw new de.ErrnoError(63);n.node_ops.setattr(n,{timestamp:Date.now()})},lchown:(e,t,r)=>{de.chown(e,t,r,!0)},fchown:(e,t,r)=>{var i=de.getStream(e);if(!i)throw new de.ErrnoError(8);de.chown(i.node,t,r)},truncate:(e,t)=>{if(t<0)throw new de.ErrnoError(28);var r;"string"==typeof e?r=de.lookupPath(e,{follow:!0}).node:r=e;if(!r.node_ops.setattr)throw new de.ErrnoError(63);if(de.isDir(r.mode))throw new de.ErrnoError(31);if(!de.isFile(r.mode))throw new de.ErrnoError(28);var i=de.nodePermissions(r,"w");if(i)throw new de.ErrnoError(i);r.node_ops.setattr(r,{size:t,timestamp:Date.now()})},ftruncate:(e,t)=>{var r=de.getStream(e);if(!r)throw new de.ErrnoError(8);if(!(2097155&r.flags))throw new de.ErrnoError(28);de.truncate(r.node,t)},utime:(e,t,r)=>{var i=de.lookupPath(e,{follow:!0}).node;i.node_ops.setattr(i,{timestamp:Math.max(t,r)})},open:(e,r,i)=>{if(""===e)throw new de.ErrnoError(44);var n;if(i=void 0===i?438:i,i=64&(r="string"==typeof r?de.modeStringToFlags(r):r)?4095&i|32768:0,"object"==typeof e)n=e;else{e=ie.normalize(e);try{n=de.lookupPath(e,{follow:!(131072&r)}).node}catch(e){}}var s=!1;if(64&r)if(n){if(128&r)throw new de.ErrnoError(20)}else n=de.mknod(e,i,0),s=!0;if(!n)throw new de.ErrnoError(44);if(de.isChrdev(n.mode)&&(r&=-513),65536&r&&!de.isDir(n.mode))throw new de.ErrnoError(54);if(!s){var o=de.mayOpen(n,r);if(o)throw new de.ErrnoError(o)}512&r&&!s&&de.truncate(n,0),r&=-131713;var a=de.createStream({node:n,path:de.getPath(n),flags:r,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return a.stream_ops.open&&a.stream_ops.open(a),!t.logReadFiles||1&r||(de.readFiles||(de.readFiles={}),e in de.readFiles||(de.readFiles[e]=1)),a},close:e=>{if(de.isClosed(e))throw new de.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{de.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek:(e,t,r)=>{if(de.isClosed(e))throw new de.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new de.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new de.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read:(e,t,r,i,n)=>{if(i<0||n<0)throw new de.ErrnoError(28);if(de.isClosed(e))throw new de.ErrnoError(8);if(1==(2097155&e.flags))throw new de.ErrnoError(8);if(de.isDir(e.node.mode))throw new de.ErrnoError(31);if(!e.stream_ops.read)throw new de.ErrnoError(28);var s=void 0!==n;if(s){if(!e.seekable)throw new de.ErrnoError(70)}else n=e.position;var o=e.stream_ops.read(e,t,r,i,n);return s||(e.position+=o),o},write:(e,t,r,i,n,s)=>{if(i<0||n<0)throw new de.ErrnoError(28);if(de.isClosed(e))throw new de.ErrnoError(8);if(!(2097155&e.flags))throw new de.ErrnoError(8);if(de.isDir(e.node.mode))throw new de.ErrnoError(31);if(!e.stream_ops.write)throw new de.ErrnoError(28);e.seekable&&1024&e.flags&&de.llseek(e,0,2);var o=void 0!==n;if(o){if(!e.seekable)throw new de.ErrnoError(70)}else n=e.position;var a=e.stream_ops.write(e,t,r,i,n,s);return o||(e.position+=a),a},allocate:(e,t,r)=>{if(de.isClosed(e))throw new de.ErrnoError(8);if(t<0||r<=0)throw new de.ErrnoError(28);if(!(2097155&e.flags))throw new de.ErrnoError(8);if(!de.isFile(e.node.mode)&&!de.isDir(e.node.mode))throw new de.ErrnoError(43);if(!e.stream_ops.allocate)throw new de.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap:(e,t,r,i,n)=>{if(2&i&&!(2&n)&&2!=(2097155&e.flags))throw new de.ErrnoError(2);if(1==(2097155&e.flags))throw new de.ErrnoError(2);if(!e.stream_ops.mmap)throw new de.ErrnoError(43);return e.stream_ops.mmap(e,t,r,i,n)},msync:(e,t,r,i,n)=>e&&e.stream_ops.msync?e.stream_ops.msync(e,t,r,i,n):0,munmap:e=>0,ioctl:(e,t,r)=>{if(!e.stream_ops.ioctl)throw new de.ErrnoError(59);return e.stream_ops.ioctl(e,t,r)},readFile:(e,t={})=>{if(t.flags=t.flags||0,t.encoding=t.encoding||"binary","utf8"!==t.encoding&&"binary"!==t.encoding)throw new Error('Invalid encoding type "'+t.encoding+'"');var r,i=de.open(e,t.flags),n=de.stat(e).size,s=new Uint8Array(n);return de.read(i,s,0,n,0),"utf8"===t.encoding?r=I(s,0):"binary"===t.encoding&&(r=s),de.close(i),r},writeFile:(e,t,r={})=>{r.flags=r.flags||577;var i=de.open(e,r.flags,r.mode);if("string"==typeof t){var n=new Uint8Array(M(t)+1),s=R(t,n,0,n.length);de.write(i,n,0,s,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(t))throw new Error("Unsupported data type");de.write(i,t,0,t.byteLength,void 0,r.canOwn)}de.close(i)},cwd:()=>de.currentPath,chdir:e=>{var t=de.lookupPath(e,{follow:!0});if(null===t.node)throw new de.ErrnoError(44);if(!de.isDir(t.node.mode))throw new de.ErrnoError(54);var r=de.nodePermissions(t.node,"x");if(r)throw new de.ErrnoError(r);de.currentPath=t.path},createDefaultDirectories:()=>{de.mkdir("/tmp"),de.mkdir("/home"),de.mkdir("/home/<USER>")},createDefaultDevices:()=>{de.mkdir("/dev"),de.registerDevice(de.makedev(1,3),{read:()=>0,write:(e,t,r,i,n)=>i}),de.mkdev("/dev/null",de.makedev(1,3)),oe.register(de.makedev(5,0),oe.default_tty_ops),oe.register(de.makedev(6,0),oe.default_tty1_ops),de.mkdev("/dev/tty",de.makedev(5,0)),de.mkdev("/dev/tty1",de.makedev(6,0));var e=function(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return()=>(crypto.getRandomValues(e),e[0])}if(p)try{var t=require("crypto");return()=>t.randomBytes(1)[0]}catch(e){}return()=>j("randomDevice")}();de.createDevice("/dev","random",e),de.createDevice("/dev","urandom",e),de.mkdir("/dev/shm"),de.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{de.mkdir("/proc");var e=de.mkdir("/proc/self");de.mkdir("/proc/self/fd"),de.mount({mount:()=>{var t=de.createNode(e,"fd",16895,73);return t.node_ops={lookup:(e,t)=>{var r=+t,i=de.getStream(r);if(!i)throw new de.ErrnoError(8);var n={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>i.path}};return n.parent=n,n}},t}},{},"/proc/self/fd")},createStandardStreams:()=>{t.stdin?de.createDevice("/dev","stdin",t.stdin):de.symlink("/dev/tty","/dev/stdin"),t.stdout?de.createDevice("/dev","stdout",null,t.stdout):de.symlink("/dev/tty","/dev/stdout"),t.stderr?de.createDevice("/dev","stderr",null,t.stderr):de.symlink("/dev/tty1","/dev/stderr"),de.open("/dev/stdin",0),de.open("/dev/stdout",1),de.open("/dev/stderr",1)},ensureErrnoError:()=>{de.ErrnoError||(de.ErrnoError=function(e,t){this.node=t,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},de.ErrnoError.prototype=new Error,de.ErrnoError.prototype.constructor=de.ErrnoError,[44].forEach((e=>{de.genericErrors[e]=new de.ErrnoError(e),de.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:()=>{de.ensureErrnoError(),de.nameTable=new Array(4096),de.mount(le,{},"/"),de.createDefaultDirectories(),de.createDefaultDevices(),de.createSpecialDirectories(),de.filesystems={MEMFS:le}},init:(e,r,i)=>{de.init.initialized=!0,de.ensureErrnoError(),t.stdin=e||t.stdin,t.stdout=r||t.stdout,t.stderr=i||t.stderr,de.createStandardStreams()},quit:()=>{de.init.initialized=!1;for(var e=0;e<de.streams.length;e++){var t=de.streams[e];t&&de.close(t)}},getMode:(e,t)=>{var r=0;return e&&(r|=365),t&&(r|=146),r},findObject:(e,t)=>{var r=de.analyzePath(e,t);return r.exists?r.object:null},analyzePath:(e,t)=>{try{e=(i=de.lookupPath(e,{follow:!t})).path}catch(e){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var i=de.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=i.path,r.parentObject=i.node,r.name=ie.basename(e),i=de.lookupPath(e,{follow:!t}),r.exists=!0,r.path=i.path,r.object=i.node,r.name=i.node.name,r.isRoot="/"===i.path}catch(e){r.error=e.errno}return r},createPath:(e,t,r,i)=>{e="string"==typeof e?e:de.getPath(e);for(var n=t.split("/").reverse();n.length;){var s=n.pop();if(s){var o=ie.join2(e,s);try{de.mkdir(o)}catch(e){}e=o}}return o},createFile:(e,t,r,i,n)=>{var s=ie.join2("string"==typeof e?e:de.getPath(e),t),o=de.getMode(i,n);return de.create(s,o)},createDataFile:(e,t,r,i,n,s)=>{var o=t;e&&(e="string"==typeof e?e:de.getPath(e),o=t?ie.join2(e,t):e);var a=de.getMode(i,n),l=de.create(o,a);if(r){if("string"==typeof r){for(var d=new Array(r.length),u=0,c=r.length;u<c;++u)d[u]=r.charCodeAt(u);r=d}de.chmod(l,146|a);var f=de.open(l,577);de.write(f,r,0,r.length,0,s),de.close(f),de.chmod(l,a)}return l},createDevice:(e,t,r,i)=>{var n=ie.join2("string"==typeof e?e:de.getPath(e),t),s=de.getMode(!!r,!!i);de.createDevice.major||(de.createDevice.major=64);var o=de.makedev(de.createDevice.major++,0);return de.registerDevice(o,{open:e=>{e.seekable=!1},close:e=>{i&&i.buffer&&i.buffer.length&&i(10)},read:(e,t,i,n,s)=>{for(var o=0,a=0;a<n;a++){var l;try{l=r()}catch(e){throw new de.ErrnoError(29)}if(void 0===l&&0===o)throw new de.ErrnoError(6);if(null==l)break;o++,t[i+a]=l}return o&&(e.node.timestamp=Date.now()),o},write:(e,t,r,n,s)=>{for(var o=0;o<n;o++)try{i(t[r+o])}catch(e){throw new de.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),o}}),de.mkdev(n,s,o)},forceLoadFile:e=>{if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!n)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=se(n(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new de.ErrnoError(29)}},createLazyFile:(e,t,r,i,n)=>{function s(){this.lengthKnown=!1,this.chunks=[]}if(s.prototype.get=function(e){if(!(e>this.length-1||e<0)){var t=e%this.chunkSize,r=e/this.chunkSize|0;return this.getter(r)[t]}},s.prototype.setDataGetter=function(e){this.getter=e},s.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",r,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+r+". Status: "+e.status);var t,i=Number(e.getResponseHeader("Content-length")),n=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t,s=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t,o=1048576;n||(o=i);var a=this;a.setDataGetter((e=>{var t=e*o,n=(e+1)*o-1;if(n=Math.min(n,i-1),void 0===a.chunks[e]&&(a.chunks[e]=((e,t)=>{if(e>t)throw new Error("invalid range ("+e+", "+t+") or no bytes requested!");if(t>i-1)throw new Error("only "+i+" bytes available! programmer error!");var n=new XMLHttpRequest;if(n.open("GET",r,!1),i!==o&&n.setRequestHeader("Range","bytes="+e+"-"+t),n.responseType="arraybuffer",n.overrideMimeType&&n.overrideMimeType("text/plain; charset=x-user-defined"),n.send(null),!(n.status>=200&&n.status<300||304===n.status))throw new Error("Couldn't load "+r+". Status: "+n.status);return void 0!==n.response?new Uint8Array(n.response||[]):se(n.responseText||"",!0)})(t,n)),void 0===a.chunks[e])throw new Error("doXHR failed!");return a.chunks[e]})),!s&&i||(o=i=1,i=this.getter(0).length,o=i,_("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=i,this._chunkSize=o,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!h)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var o=new s;Object.defineProperties(o,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var a={isDevice:!1,contents:o}}else a={isDevice:!1,url:r};var l=de.createFile(e,t,a,i,n);a.contents?l.contents=a.contents:a.url&&(l.contents=null,l.url=a.url),Object.defineProperties(l,{usedBytes:{get:function(){return this.contents.length}}});var d={};function u(e,t,r,i,n){var s=e.node.contents;if(n>=s.length)return 0;var o=Math.min(s.length-n,i);if(s.slice)for(var a=0;a<o;a++)t[r+a]=s[n+a];else for(a=0;a<o;a++)t[r+a]=s.get(n+a);return o}return Object.keys(l.stream_ops).forEach((e=>{var t=l.stream_ops[e];d[e]=function(){return de.forceLoadFile(l),t.apply(null,arguments)}})),d.read=(e,t,r,i,n)=>(de.forceLoadFile(l),u(e,t,r,i,n)),d.mmap=(e,t,r,i,n)=>{de.forceLoadFile(l);var s=ae(t);if(!s)throw new de.ErrnoError(48);return u(e,U,s,t,r),{ptr:s,allocated:!0}},l.stream_ops=d,l},createPreloadedFile:(e,t,r,i,n,o,a,l,d,u)=>{var c=t?ne.resolve(ie.join2(e,t)):e;function f(r){function s(r){u&&u(),l||de.createDataFile(e,t,r,i,n,d),o&&o(),V()}Browser.handledByPreloadPlugin(r,c,s,(()=>{a&&a(),V()}))||s(r)}H(),"string"==typeof r?function(e,t,r,i){var n=i?"":"al "+e;s(e,(r=>{E(r,'Loading data file "'+e+'" failed (no arrayBuffer).'),t(new Uint8Array(r)),n&&V()}),(t=>{if(!r)throw'Loading data file "'+e+'" failed.';r()})),n&&H()}(r,(e=>f(e)),a):f(r)},indexedDB:()=>window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB,DB_NAME:()=>"EM_FS_"+window.location.pathname,DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var i=de.indexedDB();try{var n=i.open(de.DB_NAME(),de.DB_VERSION)}catch(e){return r(e)}n.onupgradeneeded=()=>{_("creating db"),n.result.createObjectStore(de.DB_STORE_NAME)},n.onsuccess=()=>{var i=n.result.transaction([de.DB_STORE_NAME],"readwrite"),s=i.objectStore(de.DB_STORE_NAME),o=0,a=0,l=e.length;function d(){0==a?t():r()}e.forEach((e=>{var t=s.put(de.analyzePath(e).object.contents,e);t.onsuccess=()=>{++o+a==l&&d()},t.onerror=()=>{a++,o+a==l&&d()}})),i.onerror=r},n.onerror=r},loadFilesFromDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var i=de.indexedDB();try{var n=i.open(de.DB_NAME(),de.DB_VERSION)}catch(e){return r(e)}n.onupgradeneeded=r,n.onsuccess=()=>{var i=n.result;try{var s=i.transaction([de.DB_STORE_NAME],"readonly")}catch(e){return void r(e)}var o=s.objectStore(de.DB_STORE_NAME),a=0,l=0,d=e.length;function u(){0==l?t():r()}e.forEach((e=>{var t=o.get(e);t.onsuccess=()=>{de.analyzePath(e).exists&&de.unlink(e),de.createDataFile(ie.dirname(e),ie.basename(e),t.result,!0,!0,!0),++a+l==d&&u()},t.onerror=()=>{l++,a+l==d&&u()}})),s.onerror=r},n.onerror=r}},ue={DEFAULT_POLLMASK:5,calculateAt:function(e,t,r){if(ie.isAbs(t))return t;var i;if(-100===e)i=de.cwd();else{var n=de.getStream(e);if(!n)throw new de.ErrnoError(8);i=n.path}if(0==t.length){if(!r)throw new de.ErrnoError(44);return i}return ie.join2(i,t)},doStat:function(e,t,r){try{var i=e(t)}catch(e){if(e&&e.node&&ie.normalize(t)!==ie.normalize(de.getPath(e.node)))return-54;throw e}return T[r>>2]=i.dev,T[r+4>>2]=0,T[r+8>>2]=i.ino,T[r+12>>2]=i.mode,T[r+16>>2]=i.nlink,T[r+20>>2]=i.uid,T[r+24>>2]=i.gid,T[r+28>>2]=i.rdev,T[r+32>>2]=0,K=[i.size>>>0,(q=i.size,+Math.abs(q)>=1?q>0?(0|Math.min(+Math.floor(q/4294967296),4294967295))>>>0:~~+Math.ceil((q-+(~~q>>>0))/4294967296)>>>0:0)],T[r+40>>2]=K[0],T[r+44>>2]=K[1],T[r+48>>2]=4096,T[r+52>>2]=i.blocks,K=[Math.floor(i.atime.getTime()/1e3)>>>0,(q=Math.floor(i.atime.getTime()/1e3),+Math.abs(q)>=1?q>0?(0|Math.min(+Math.floor(q/4294967296),4294967295))>>>0:~~+Math.ceil((q-+(~~q>>>0))/4294967296)>>>0:0)],T[r+56>>2]=K[0],T[r+60>>2]=K[1],T[r+64>>2]=0,K=[Math.floor(i.mtime.getTime()/1e3)>>>0,(q=Math.floor(i.mtime.getTime()/1e3),+Math.abs(q)>=1?q>0?(0|Math.min(+Math.floor(q/4294967296),4294967295))>>>0:~~+Math.ceil((q-+(~~q>>>0))/4294967296)>>>0:0)],T[r+72>>2]=K[0],T[r+76>>2]=K[1],T[r+80>>2]=0,K=[Math.floor(i.ctime.getTime()/1e3)>>>0,(q=Math.floor(i.ctime.getTime()/1e3),+Math.abs(q)>=1?q>0?(0|Math.min(+Math.floor(q/4294967296),4294967295))>>>0:~~+Math.ceil((q-+(~~q>>>0))/4294967296)>>>0:0)],T[r+88>>2]=K[0],T[r+92>>2]=K[1],T[r+96>>2]=0,K=[i.ino>>>0,(q=i.ino,+Math.abs(q)>=1?q>0?(0|Math.min(+Math.floor(q/4294967296),4294967295))>>>0:~~+Math.ceil((q-+(~~q>>>0))/4294967296)>>>0:0)],T[r+104>>2]=K[0],T[r+108>>2]=K[1],0},doMsync:function(e,t,r,i,n){var s=x.slice(e,e+r);de.msync(t,s,n,r,i)},varargs:void 0,get:function(){return ue.varargs+=4,T[ue.varargs-4>>2]},getStr:function(e){return L(e)},getStreamFromFD:function(e){var t=de.getStream(e);if(!t)throw new de.ErrnoError(8);return t}};function ce(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var fe=void 0;function he(e){for(var t="",r=e;x[r];)t+=fe[x[r++]];return t}var pe={},me={},_e={},ye=48,ge=57;function ve(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=ye&&t<=ge?"_"+e:e}function be(e,t){return e=ve(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(t)}function we(e,t){var r=be(t,(function(e){this.name=t,this.message=e;var r=new Error(e).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))}));return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var Ee=void 0;function Se(e){throw new Ee(e)}var Ue=void 0;function xe(e){throw new Ue(e)}function Ae(e,t,r){function i(t){var i=r(t);i.length!==e.length&&xe("Mismatched type converter count");for(var n=0;n<e.length;++n)ke(e[n],i[n])}e.forEach((function(e){_e[e]=t}));var n=new Array(t.length),s=[],o=0;t.forEach(((e,t)=>{me.hasOwnProperty(e)?n[t]=me[e]:(s.push(e),pe.hasOwnProperty(e)||(pe[e]=[]),pe[e].push((()=>{n[t]=me[e],++o===s.length&&i(n)})))})),0===s.length&&i(n)}function ke(e,t,r={}){if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var i=t.name;if(e||Se('type "'+i+'" must have a positive integer typeid pointer'),me.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;Se("Cannot register type '"+i+"' twice")}if(me[e]=t,delete _e[e],pe.hasOwnProperty(e)){var n=pe[e];delete pe[e],n.forEach((e=>e()))}}function Te(e){if(!(this instanceof Xe))return!1;if(!(e instanceof Xe))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,i=e.$$.ptrType.registeredClass,n=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;i.baseClass;)n=i.upcast(n),i=i.baseClass;return t===i&&r===n}function Be(e){Se(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Ce=!1;function De(e){}function Fe(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function Pe(e,t,r){if(t===r)return e;if(void 0===r.baseClass)return null;var i=Pe(e,t,r.baseClass);return null===i?null:r.downcast(i)}var Ie={};function Le(){return Object.keys($e).length}function Re(){var e=[];for(var t in $e)$e.hasOwnProperty(t)&&e.push($e[t]);return e}var Me=[];function ze(){for(;Me.length;){var e=Me.pop();e.$$.deleteScheduled=!1,e.delete()}}var Ne=void 0;function Oe(e){Ne=e,Me.length&&Ne&&Ne(ze)}var $e={};function Ge(e,t){return t=function(e,t){for(void 0===t&&Se("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),$e[t]}function He(e,t){return t.ptrType&&t.ptr||xe("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!==!!t.smartPtr&&xe("Both smartPtrType and smartPtr must be specified"),t.count={value:1},je(Object.create(e,{$$:{value:t}}))}function Ve(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var r=Ge(this.registeredClass,t);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();var i=r.clone();return this.destructor(e),i}function n(){return this.isSmartPointer?He(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):He(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var s,o=this.registeredClass.getActualType(t),a=Ie[o];if(!a)return n.call(this);s=this.isConst?a.constPointerType:a.pointerType;var l=Pe(t,this.registeredClass,s.registeredClass);return null===l?n.call(this):this.isSmartPointer?He(s.registeredClass.instancePrototype,{ptrType:s,ptr:l,smartPtrType:this,smartPtr:e}):He(s.registeredClass.instancePrototype,{ptrType:s,ptr:l})}function je(e){return"undefined"==typeof FinalizationRegistry?(je=e=>e,e):(Ce=new FinalizationRegistry((e=>{Fe(e.$$)})),je=e=>{var t=e.$$;if(!!t.smartPtr){var r={$$:t};Ce.register(e,r,e)}return e},De=e=>Ce.unregister(e),je(e))}function We(){if(this.$$.ptr||Be(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=je(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t}function Ye(){this.$$.ptr||Be(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Se("Object already scheduled for deletion"),De(this),Fe(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function qe(){return!this.$$.ptr}function Ke(){return this.$$.ptr||Be(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Se("Object already scheduled for deletion"),Me.push(this),1===Me.length&&Ne&&Ne(ze),this.$$.deleteScheduled=!0,this}function Xe(){}function Ze(e,t,r){if(void 0===e[t].overloadTable){var i=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||Se("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[i.argCount]=i}}function Je(e,t,r,i,n,s,o,a){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=i,this.baseClass=n,this.getActualType=s,this.upcast=o,this.downcast=a,this.pureVirtualFunctions=[]}function Qe(e,t,r){for(;t!==r;)t.upcast||Se("Expected null or instance of "+r.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function et(e,t){if(null===t)return this.isReference&&Se("null is not a valid "+this.name),0;t.$$||Se('Cannot pass "'+xt(t)+'" as a '+this.name),t.$$.ptr||Se("Cannot pass deleted object as a pointer of type "+this.name);var r=t.$$.ptrType.registeredClass;return Qe(t.$$.ptr,r,this.registeredClass)}function tt(e,t){var r;if(null===t)return this.isReference&&Se("null is not a valid "+this.name),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t.$$||Se('Cannot pass "'+xt(t)+'" as a '+this.name),t.$$.ptr||Se("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&Se("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var i=t.$$.ptrType.registeredClass;if(r=Qe(t.$$.ptr,i,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&Se("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:Se("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var n=t.clone();r=this.rawShare(r,Ut.toHandle((function(){n.delete()}))),null!==e&&e.push(this.rawDestructor,r)}break;default:Se("Unsupporting sharing policy")}return r}function rt(e,t){if(null===t)return this.isReference&&Se("null is not a valid "+this.name),0;t.$$||Se('Cannot pass "'+xt(t)+'" as a '+this.name),t.$$.ptr||Se("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&Se("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var r=t.$$.ptrType.registeredClass;return Qe(t.$$.ptr,r,this.registeredClass)}function it(e){return this.fromWireType(T[e>>2])}function nt(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function st(e){this.rawDestructor&&this.rawDestructor(e)}function ot(e){null!==e&&e.delete()}function at(e,t,r,i,n,s,o,a,l,d,u){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=i,this.isSmartPointer=n,this.pointeeType=s,this.sharingPolicy=o,this.rawGetPointee=a,this.rawConstructor=l,this.rawShare=d,this.rawDestructor=u,n||void 0!==t.baseClass?this.toWireType=tt:i?(this.toWireType=et,this.destructorFunction=null):(this.toWireType=rt,this.destructorFunction=null)}var lt=[];function dt(e){var t=lt[e];return t||(e>=lt.length&&(lt.length=e+1),lt[e]=t=F.get(e)),t}function ut(e,r,i){return e.includes("j")?function(e,r,i){var n=t["dynCall_"+e];return i&&i.length?n.apply(null,[r].concat(i)):n.call(null,r)}(e,r,i):dt(r).apply(null,i)}function ct(e,t){var r,i,n,s=(e=he(e)).includes("j")?(r=e,i=t,n=[],function(){return n.length=0,Object.assign(n,arguments),ut(r,i,n)}):dt(t);return"function"!=typeof s&&Se("unknown function pointer with signature "+e+": "+t),s}var ft=void 0;function ht(e){var t=Wt(e),r=he(t);return Vt(t),r}function pt(e,t){var r=[],i={};throw t.forEach((function e(t){i[t]||me[t]||(_e[t]?_e[t].forEach(e):(r.push(t),i[t]=!0))})),new ft(e+": "+r.map(ht).join([", "]))}function mt(e,t){for(var r=[],i=0;i<e;i++)r.push(B[t+4*i>>2]);return r}function _t(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function yt(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var r=be(e.name||"unknownFunctionName",(function(){}));r.prototype=e.prototype;var i=new r,n=e.apply(i,t);return n instanceof Object?n:i}function gt(e,t,r,i,n){var s=t.length;s<2&&Se("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var o=null!==t[1]&&null!==r,a=!1,l=1;l<t.length;++l)if(null!==t[l]&&void 0===t[l].destructorFunction){a=!0;break}var d="void"!==t[0].name,u="",c="";for(l=0;l<s-2;++l)u+=(0!==l?", ":"")+"arg"+l,c+=(0!==l?", ":"")+"arg"+l+"Wired";var f="return function "+ve(e)+"("+u+") {\nif (arguments.length !== "+(s-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(s-2)+" args!');\n}\n";a&&(f+="var destructors = [];\n");var h=a?"destructors":"null",p=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],m=[Se,i,n,_t,t[0],t[1]];o&&(f+="var thisWired = classParam.toWireType("+h+", this);\n");for(l=0;l<s-2;++l)f+="var arg"+l+"Wired = argType"+l+".toWireType("+h+", arg"+l+"); // "+t[l+2].name+"\n",p.push("argType"+l),m.push(t[l+2]);if(o&&(c="thisWired"+(c.length>0?", ":"")+c),f+=(d?"var rv = ":"")+"invoker(fn"+(c.length>0?", ":"")+c+");\n",a)f+="runDestructors(destructors);\n";else for(l=o?1:2;l<t.length;++l){var _=1===l?"thisWired":"arg"+(l-2)+"Wired";null!==t[l].destructorFunction&&(f+=_+"_dtor("+_+"); // "+t[l].name+"\n",p.push(_+"_dtor"),m.push(t[l].destructorFunction))}return d&&(f+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),f+="}\n",p.push(f),yt(Function,p).apply(null,m)}var vt=[],bt=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function wt(e){e>4&&0==--bt[e].refcount&&(bt[e]=void 0,vt.push(e))}function Et(){for(var e=0,t=5;t<bt.length;++t)void 0!==bt[t]&&++e;return e}function St(){for(var e=5;e<bt.length;++e)if(void 0!==bt[e])return bt[e];return null}var Ut={toValue:e=>(e||Se("Cannot use deleted val. handle = "+e),bt[e].value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=vt.length?vt.pop():bt.length;return bt[t]={refcount:1,value:e},t}}};function xt(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function At(e,t){switch(t){case 2:return function(e){return this.fromWireType(C[e>>2])};case 3:return function(e){return this.fromWireType(D[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function kt(e,t,r){switch(t){case 0:return r?function(e){return U[e]}:function(e){return x[e]};case 1:return r?function(e){return A[e>>1]}:function(e){return k[e>>1]};case 2:return r?function(e){return T[e>>2]}:function(e){return B[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var Tt="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function Bt(e,t){for(var r=e,i=r>>1,n=i+t/2;!(i>=n)&&k[i];)++i;if((r=i<<1)-e>32&&Tt)return Tt.decode(x.subarray(e,r));for(var s="",o=0;!(o>=t/2);++o){var a=A[e+2*o>>1];if(0==a)break;s+=String.fromCharCode(a)}return s}function Ct(e,t,r){if(void 0===r&&(r=2147483647),r<2)return 0;for(var i=t,n=(r-=2)<2*e.length?r/2:e.length,s=0;s<n;++s){var o=e.charCodeAt(s);A[t>>1]=o,t+=2}return A[t>>1]=0,t-i}function Dt(e){return 2*e.length}function Ft(e,t){for(var r=0,i="";!(r>=t/4);){var n=T[e+4*r>>2];if(0==n)break;if(++r,n>=65536){var s=n-65536;i+=String.fromCharCode(55296|s>>10,56320|1023&s)}else i+=String.fromCharCode(n)}return i}function Pt(e,t,r){if(void 0===r&&(r=2147483647),r<4)return 0;for(var i=t,n=i+r-4,s=0;s<e.length;++s){var o=e.charCodeAt(s);if(o>=55296&&o<=57343)o=65536+((1023&o)<<10)|1023&e.charCodeAt(++s);if(T[t>>2]=o,(t+=4)+4>n)break}return T[t>>2]=0,t-i}function It(e){for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);i>=55296&&i<=57343&&++r,t+=4}return t}var Lt={};var Rt=[];var Mt=[];var zt={};function Nt(){if(!Nt.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:c||"./this.program"};for(var t in zt)void 0===zt[t]?delete e[t]:e[t]=zt[t];var r=[];for(var t in e)r.push(t+"="+e[t]);Nt.strings=r}return Nt.strings}var Ot=function(e,t,r,i){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=de.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=i},$t=365,Gt=146;Object.defineProperties(Ot.prototype,{read:{get:function(){return(this.mode&$t)===$t},set:function(e){e?this.mode|=$t:this.mode&=-366}},write:{get:function(){return(this.mode&Gt)===Gt},set:function(e){e?this.mode|=Gt:this.mode&=-147}},isFolder:{get:function(){return de.isDir(this.mode)}},isDevice:{get:function(){return de.isChrdev(this.mode)}}}),de.FSNode=Ot,de.staticInit(),function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);fe=e}(),Ee=t.BindingError=we(Error,"BindingError"),Ue=t.InternalError=we(Error,"InternalError"),Xe.prototype.isAliasOf=Te,Xe.prototype.clone=We,Xe.prototype.delete=Ye,Xe.prototype.isDeleted=qe,Xe.prototype.deleteLater=Ke,t.getInheritedInstanceCount=Le,t.getLiveInheritedInstances=Re,t.flushPendingDeletes=ze,t.setDelayFunction=Oe,at.prototype.getPointee=nt,at.prototype.destructor=st,at.prototype.argPackAdvance=8,at.prototype.readValueFromPointer=it,at.prototype.deleteObject=ot,at.prototype.fromWireType=Ve,ft=t.UnboundTypeError=we(Error,"UnboundTypeError"),t.count_emval_handles=Et,t.get_first_emval=St;var Ht={q:function(e){return qt(e+24)+24},p:function(e,t,r){throw new re(e).init(t,r),e},C:function(e,t,r){ue.varargs=r;try{var i=ue.getStreamFromFD(e);switch(t){case 0:return(n=ue.get())<0?-28:de.createStream(i,n).fd;case 1:case 2:case 6:case 7:return 0;case 3:return i.flags;case 4:var n=ue.get();return i.flags|=n,0;case 5:n=ue.get();return A[n+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return s=28,T[jt()>>2]=s,-1}}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return-e.errno}var s},w:function(e,t,r,i){ue.varargs=i;try{t=ue.getStr(t),t=ue.calculateAt(e,t);var n=i?ue.get():0;return de.open(t,r,n).fd}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return-e.errno}},u:function(e,t,r,i,n){},E:function(e,t,r,i,n){var s=ce(r);ke(e,{name:t=he(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?i:n},argPackAdvance:8,readValueFromPointer:function(e){var i;if(1===r)i=U;else if(2===r)i=A;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+t);i=T}return this.fromWireType(i[e>>s])},destructorFunction:null})},t:function(e,r,i,n,s,o,a,l,d,u,c,f,h){c=he(c),o=ct(s,o),l&&(l=ct(a,l)),u&&(u=ct(d,u)),h=ct(f,h);var p=ve(c);!function(e,r,i){t.hasOwnProperty(e)?((void 0===i||void 0!==t[e].overloadTable&&void 0!==t[e].overloadTable[i])&&Se("Cannot register public name '"+e+"' twice"),Ze(t,e,e),t.hasOwnProperty(i)&&Se("Cannot register multiple overloads of a function with the same number of arguments ("+i+")!"),t[e].overloadTable[i]=r):(t[e]=r,void 0!==i&&(t[e].numArguments=i))}(p,(function(){pt("Cannot construct "+c+" due to unbound types",[n])})),Ae([e,r,i],n?[n]:[],(function(r){var i,s;r=r[0],s=n?(i=r.registeredClass).instancePrototype:Xe.prototype;var a=be(p,(function(){if(Object.getPrototypeOf(this)!==d)throw new Ee("Use 'new' to construct "+c);if(void 0===f.constructor_body)throw new Ee(c+" has no accessible constructor");var e=f.constructor_body[arguments.length];if(void 0===e)throw new Ee("Tried to invoke ctor of "+c+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(f.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),d=Object.create(s,{constructor:{value:a}});a.prototype=d;var f=new Je(c,a,d,h,i,o,l,u),m=new at(c,f,!0,!1,!1),_=new at(c+"*",f,!1,!1,!1),y=new at(c+" const*",f,!1,!0,!1);return Ie[e]={pointerType:_,constPointerType:y},function(e,r,i){t.hasOwnProperty(e)||xe("Replacing nonexistant public symbol"),void 0!==t[e].overloadTable&&void 0!==i?t[e].overloadTable[i]=r:(t[e]=r,t[e].argCount=i)}(p,a),[m,_,y]}))},r:function(e,t,r,i,n,s){E(t>0);var o=mt(t,r);n=ct(i,n),Ae([],[e],(function(e){var r="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new Ee("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[t-1]=()=>{pt("Cannot construct "+e.name+" due to unbound types",o)},Ae([],o,(function(i){return i.splice(1,0,null),e.registeredClass.constructor_body[t-1]=gt(r,i,null,n,s),[]})),[]}))},d:function(e,t,r,i,n,s,o,a){var l=mt(r,i);t=he(t),s=ct(n,s),Ae([],[e],(function(e){var i=(e=e[0]).name+"."+t;function n(){pt("Cannot call "+i+" due to unbound types",l)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]),a&&e.registeredClass.pureVirtualFunctions.push(t);var d=e.registeredClass.instancePrototype,u=d[t];return void 0===u||void 0===u.overloadTable&&u.className!==e.name&&u.argCount===r-2?(n.argCount=r-2,n.className=e.name,d[t]=n):(Ze(d,t,i),d[t].overloadTable[r-2]=n),Ae([],l,(function(n){var a=gt(i,n,e,s,o);return void 0===d[t].overloadTable?(a.argCount=r-2,d[t]=a):d[t].overloadTable[r-2]=a,[]})),[]}))},D:function(e,t){ke(e,{name:t=he(t),fromWireType:function(e){var t=Ut.toValue(e);return wt(e),t},toWireType:function(e,t){return Ut.toHandle(t)},argPackAdvance:8,readValueFromPointer:it,destructorFunction:null})},n:function(e,t,r){var i=ce(r);ke(e,{name:t=he(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:At(t,i),destructorFunction:null})},c:function(e,t,r,i,n){t=he(t);var s=ce(r),o=e=>e;if(0===i){var a=32-8*r;o=e=>e<<a>>>a}var l=t.includes("unsigned");ke(e,{name:t,fromWireType:o,toWireType:l?function(e,t){return this.name,t>>>0}:function(e,t){return this.name,t},argPackAdvance:8,readValueFromPointer:kt(t,s,0!==i),destructorFunction:null})},b:function(e,t,r){var i=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function n(e){var t=B,r=t[e>>=2],n=t[e+1];return new i(S,n,r)}ke(e,{name:r=he(r),fromWireType:n,argPackAdvance:8,readValueFromPointer:n},{ignoreDuplicateRegistrations:!0})},m:function(e,t){var r="std::string"===(t=he(t));ke(e,{name:t,fromWireType:function(e){var t,i=B[e>>2],n=e+4;if(r)for(var s=n,o=0;o<=i;++o){var a=n+o;if(o==i||0==x[a]){var l=L(s,a-s);void 0===t?t=l:(t+=String.fromCharCode(0),t+=l),s=a+1}}else{var d=new Array(i);for(o=0;o<i;++o)d[o]=String.fromCharCode(x[n+o]);t=d.join("")}return Vt(e),t},toWireType:function(e,t){var i;t instanceof ArrayBuffer&&(t=new Uint8Array(t));var n="string"==typeof t;n||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||Se("Cannot pass non-string to std::string"),i=r&&n?M(t):t.length;var s=qt(4+i+1),o=s+4;if(B[s>>2]=i,r&&n)R(t,x,o,i+1);else if(n)for(var a=0;a<i;++a){var l=t.charCodeAt(a);l>255&&(Vt(o),Se("String has UTF-16 code units that do not fit in 8 bits")),x[o+a]=l}else for(a=0;a<i;++a)x[o+a]=t[a];return null!==e&&e.push(Vt,s),s},argPackAdvance:8,readValueFromPointer:it,destructorFunction:function(e){Vt(e)}})},h:function(e,t,r){var i,n,s,o,a;r=he(r),2===t?(i=Bt,n=Ct,o=Dt,s=()=>k,a=1):4===t&&(i=Ft,n=Pt,o=It,s=()=>B,a=2),ke(e,{name:r,fromWireType:function(e){for(var r,n=B[e>>2],o=s(),l=e+4,d=0;d<=n;++d){var u=e+4+d*t;if(d==n||0==o[u>>a]){var c=i(l,u-l);void 0===r?r=c:(r+=String.fromCharCode(0),r+=c),l=u+t}}return Vt(e),r},toWireType:function(e,i){"string"!=typeof i&&Se("Cannot pass non-string to C++ string type "+r);var s=o(i),l=qt(4+s+t);return B[l>>2]=s>>a,n(i,l+4,s+t),null!==e&&e.push(Vt,l),l},argPackAdvance:8,readValueFromPointer:it,destructorFunction:function(e){Vt(e)}})},o:function(e,t){ke(e,{isVoid:!0,name:t=he(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},f:function(){return Date.now()},g:function(e,t,r,i){var n,s;(e=Rt[e])(t=Ut.toValue(t),r=void 0===(s=Lt[n=r])?he(n):s,null,i)},j:wt,i:function(e,t){var r=function(e,t){for(var r,i,n,s=new Array(e),o=0;o<e;++o)s[o]=(r=B[t+o*b>>2],i="parameter "+o,n=void 0,void 0===(n=me[r])&&Se(i+" has unknown type "+ht(r)),n);return s}(e,t),i=r[0],n=i.name+"_$"+r.slice(1).map((function(e){return e.name})).join("_")+"$",s=Mt[n];if(void 0!==s)return s;for(var o=["retType"],a=[i],l="",d=0;d<e-1;++d)l+=(0!==d?", ":"")+"arg"+d,o.push("argType"+d),a.push(r[1+d]);var u="return function "+ve("methodCaller_"+n)+"(handle, name, destructors, args) {\n",c=0;for(d=0;d<e-1;++d)u+="    var arg"+d+" = argType"+d+".readValueFromPointer(args"+(c?"+"+c:"")+");\n",c+=r[d+1].argPackAdvance;for(u+="    var rv = handle[name]("+l+");\n",d=0;d<e-1;++d)r[d+1].deleteObject&&(u+="    argType"+d+".deleteObject(arg"+d+");\n");i.isVoid||(u+="    return retType.toWireType(destructors, rv);\n"),u+="};\n",o.push(u);var f,h,p=yt(Function,o).apply(null,a);return f=p,h=Rt.length,Rt.push(f),s=h,Mt[n]=s,s},a:function(){j("")},A:function(e,t,r){x.copyWithin(e,t,t+r)},v:function(e){x.length,j("OOM")},y:function(e,t){var r=0;return Nt().forEach((function(i,n){var s=t+r;B[e+4*n>>2]=s,function(e,t,r){for(var i=0;i<e.length;++i)U[0|t++]=e.charCodeAt(i);r||(U[0|t]=0)}(i,s),r+=i.length+1})),0},z:function(e,t){var r=Nt();B[e>>2]=r.length;var i=0;return r.forEach((function(e){i+=e.length+1})),B[t>>2]=i,0},l:function(e){try{var t=ue.getStreamFromFD(e);return de.close(t),0}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return e.errno}},x:function(e,t){try{var r=ue.getStreamFromFD(e),i=r.tty?2:de.isDir(r.mode)?3:de.isLink(r.mode)?7:4;return U[t|0]=i,0}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return e.errno}},B:function(e,t,r,i){try{var n=function(e,t,r,i){for(var n=0,s=0;s<r;s++){var o=B[t>>2],a=B[t+4>>2];t+=8;var l=de.read(e,U,o,a,i);if(l<0)return-1;if(n+=l,l<a)break}return n}(ue.getStreamFromFD(e),t,r);return T[i>>2]=n,0}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return e.errno}},s:function(e,t,r,i,n){try{var s=(l=r)+2097152>>>0<4194305-!!(a=t)?(a>>>0)+4294967296*l:NaN;if(isNaN(s))return 61;var o=ue.getStreamFromFD(e);return de.llseek(o,s,i),K=[o.position>>>0,(q=o.position,+Math.abs(q)>=1?q>0?(0|Math.min(+Math.floor(q/4294967296),4294967295))>>>0:~~+Math.ceil((q-+(~~q>>>0))/4294967296)>>>0:0)],T[n>>2]=K[0],T[n+4>>2]=K[1],o.getdents&&0===s&&0===i&&(o.getdents=null),0}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return e.errno}var a,l},k:function(e,t,r,i){try{var n=function(e,t,r,i){for(var n=0,s=0;s<r;s++){var o=B[t>>2],a=B[t+4>>2];t+=8;var l=de.write(e,U,o,a,i);if(l<0)return-1;n+=l}return n}(ue.getStreamFromFD(e),t,r);return B[i>>2]=n,0}catch(e){if(void 0===de||!(e instanceof de.ErrnoError))throw e;return e.errno}},e:function(e){}};!function(){var e={a:Ht};function r(e,r){var i,n,s=e.exports;t.asm=s,v=t.asm.F,i=v.buffer,S=i,t.HEAP8=U=new Int8Array(i),t.HEAP16=A=new Int16Array(i),t.HEAP32=T=new Int32Array(i),t.HEAPU8=x=new Uint8Array(i),t.HEAPU16=k=new Uint16Array(i),t.HEAPU32=B=new Uint32Array(i),t.HEAPF32=C=new Float32Array(i),t.HEAPF64=D=new Float64Array(i),F=t.asm.I,n=t.asm.G,N.unshift(n),V()}function n(e){r(e.instance)}function o(t){return function(){if(!g&&(f||h)){if("function"==typeof fetch&&!J(W))return fetch(W,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+W+"'";return e.arrayBuffer()})).catch((function(){return Q(W)}));if(s)return new Promise((function(e,t){s(W,(function(t){e(new Uint8Array(t))}),t)}))}return Promise.resolve().then((function(){return Q(W)}))}().then((function(t){return WebAssembly.instantiate(t,e)})).then((function(e){return e})).then(t,(function(e){y("failed to asynchronously prepare wasm: "+e),j(e)}))}if(H(),t.instantiateWasm)try{return t.instantiateWasm(e,r)}catch(e){return y("Module.instantiateWasm callback failed with error: "+e),!1}(g||"function"!=typeof WebAssembly.instantiateStreaming||Z(W)||J(W)||p||"function"!=typeof fetch?o(n):fetch(W,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(n,(function(e){return y("wasm streaming compile failed: "+e),y("falling back to ArrayBuffer instantiation"),o(n)}))}))).catch(i)}(),t.___wasm_call_ctors=function(){return(t.___wasm_call_ctors=t.asm.G).apply(null,arguments)};var Vt=t._free=function(){return(Vt=t._free=t.asm.H).apply(null,arguments)},jt=t.___errno_location=function(){return(jt=t.___errno_location=t.asm.J).apply(null,arguments)},Wt=t.___getTypeName=function(){return(Wt=t.___getTypeName=t.asm.K).apply(null,arguments)};t.___embind_register_native_and_builtin_types=function(){return(t.___embind_register_native_and_builtin_types=t.asm.L).apply(null,arguments)};var Yt,qt=t._malloc=function(){return(qt=t._malloc=t.asm.M).apply(null,arguments)},Kt=t._emscripten_builtin_memalign=function(){return(Kt=t._emscripten_builtin_memalign=t.asm.N).apply(null,arguments)},Xt=t.___cxa_is_pointer_type=function(){return(Xt=t.___cxa_is_pointer_type=t.asm.O).apply(null,arguments)};function Zt(e){function i(){Yt||(Yt=!0,t.calledRun=!0,w||(t.noFSInit||de.init.initialized||de.init(),de.ignorePermissions=!1,te(N),r(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),function(){if(t.postRun)for("function"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;)e=t.postRun.shift(),O.unshift(e);var e;te(O)}()))}$>0||(!function(){if(t.preRun)for("function"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)e=t.preRun.shift(),z.unshift(e);var e;te(z)}(),$>0||(t.setStatus?(t.setStatus("Running..."),setTimeout((function(){setTimeout((function(){t.setStatus("")}),1),i()}),1)):i()))}if(t.dynCall_viiijj=function(){return(t.dynCall_viiijj=t.asm.P).apply(null,arguments)},t.dynCall_jij=function(){return(t.dynCall_jij=t.asm.Q).apply(null,arguments)},t.dynCall_jii=function(){return(t.dynCall_jii=t.asm.R).apply(null,arguments)},t.dynCall_jiji=function(){return(t.dynCall_jiji=t.asm.S).apply(null,arguments)},G=function e(){Yt||Zt(),Yt||(G=e)},t.preInit)for("function"==typeof t.preInit&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();return Zt(),t.ready}})(),i=1e-6,n="undefined"!=typeof Float32Array?Float32Array:Array;function s(){var e=new n(16);return n!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0),e[0]=1,e[5]=1,e[10]=1,e[15]=1,e}function o(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=1,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=1,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e}Math.hypot||(Math.hypot=function(){for(var e=0,t=arguments.length;t--;)e+=arguments[t]*arguments[t];return Math.sqrt(e)});var a,l=function(e,t,r,i,n,s,o){var a=1/(t-r),l=1/(i-n),d=1/(s-o);return e[0]=-2*a,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=-2*l,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=2*d,e[11]=0,e[12]=(t+r)*a,e[13]=(n+i)*l,e[14]=(o+s)*d,e[15]=1,e};function d(e,t,r){var i=new n(3);return i[0]=e,i[1]=t,i[2]=r,i}a=new n(3),n!=Float32Array&&(a[0]=0,a[1]=0,a[2]=0);var u=(e,t)=>{t&&e.pixelStorei(e.UNPACK_ALIGNMENT,1);const r=function(){const t=m(e.VERTEX_SHADER,"\n            attribute vec4 aVertexPosition;\n            attribute vec2 aTexturePosition;\n            uniform mat4 uModelMatrix;\n            uniform mat4 uViewMatrix;\n            uniform mat4 uProjectionMatrix;\n            varying lowp vec2 vTexturePosition;\n            void main(void) {\n              gl_Position = uProjectionMatrix * uViewMatrix * uModelMatrix * aVertexPosition;\n              vTexturePosition = aTexturePosition;\n            }\n        "),r=m(e.FRAGMENT_SHADER,"\n            precision highp float;\n            varying highp vec2 vTexturePosition;\n            uniform int isyuv;\n            uniform sampler2D rgbaTexture;\n            uniform sampler2D yTexture;\n            uniform sampler2D uTexture;\n            uniform sampler2D vTexture;\n\n            const mat4 YUV2RGB = mat4( 1.1643828125, 0, 1.59602734375, -.87078515625,\n                                       1.1643828125, -.39176171875, -.81296875, .52959375,\n                                       1.1643828125, 2.017234375, 0, -1.081390625,\n                                       0, 0, 0, 1);\n\n\n            void main(void) {\n\n                if (isyuv>0) {\n\n                    highp float y = texture2D(yTexture,  vTexturePosition).r;\n                    highp float u = texture2D(uTexture,  vTexturePosition).r;\n                    highp float v = texture2D(vTexture,  vTexturePosition).r;\n                    gl_FragColor = vec4(y, u, v, 1) * YUV2RGB;\n\n                } else {\n                    gl_FragColor =  texture2D(rgbaTexture, vTexturePosition);\n                }\n            }\n        "),i=e.createProgram();if(e.attachShader(i,t),e.attachShader(i,r),e.linkProgram(i),!e.getProgramParameter(i,e.LINK_STATUS))return console.log("Unable to initialize the shader program: "+e.getProgramInfoLog(i)),null;return i}();let n={program:r,attribLocations:{vertexPosition:e.getAttribLocation(r,"aVertexPosition"),texturePosition:e.getAttribLocation(r,"aTexturePosition")},uniformLocations:{projectionMatrix:e.getUniformLocation(r,"uProjectionMatrix"),modelMatrix:e.getUniformLocation(r,"uModelMatrix"),viewMatrix:e.getUniformLocation(r,"uViewMatrix"),rgbatexture:e.getUniformLocation(r,"rgbaTexture"),ytexture:e.getUniformLocation(r,"yTexture"),utexture:e.getUniformLocation(r,"uTexture"),vtexture:e.getUniformLocation(r,"vTexture"),isyuv:e.getUniformLocation(r,"isyuv")}},a=function(){const t=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,t);e.bufferData(e.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,-1,-1,1,1,-1,-1,1,-1]),e.STATIC_DRAW);var r=[];r=r.concat([0,1],[1,1],[1,0],[0,0]);const i=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,i),e.bufferData(e.ARRAY_BUFFER,new Float32Array(r),e.STATIC_DRAW);const n=e.createBuffer();e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,n);return e.bufferData(e.ELEMENT_ARRAY_BUFFER,new Uint16Array([0,1,2,0,2,3]),e.STATIC_DRAW),{position:t,texPosition:i,indices:n}}(),u=p(),c=p(),f=p(),h=p();function p(){let t=e.createTexture();return e.bindTexture(e.TEXTURE_2D,t),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),t}function m(t,r){const i=e.createShader(t);return e.shaderSource(i,r),e.compileShader(i),e.getShaderParameter(i,e.COMPILE_STATUS)?i:(console.log("An error occurred compiling the shaders: "+e.getShaderInfoLog(i)),e.deleteShader(i),null)}function _(t,r){e.viewport(0,0,t,r),e.clearColor(0,0,0,0),e.clearDepth(1),e.enable(e.DEPTH_TEST),e.depthFunc(e.LEQUAL),e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT);const u=s();l(u,-1,1,-1,1,.1,100);const p=s();o(p);const m=s();!function(e,t,r,n){var s,a,l,d,u,c,f,h,p,m,_=t[0],y=t[1],g=t[2],v=n[0],b=n[1],w=n[2],E=r[0],S=r[1],U=r[2];Math.abs(_-E)<i&&Math.abs(y-S)<i&&Math.abs(g-U)<i?o(e):(f=_-E,h=y-S,p=g-U,s=b*(p*=m=1/Math.hypot(f,h,p))-w*(h*=m),a=w*(f*=m)-v*p,l=v*h-b*f,(m=Math.hypot(s,a,l))?(s*=m=1/m,a*=m,l*=m):(s=0,a=0,l=0),d=h*l-p*a,u=p*s-f*l,c=f*a-h*s,(m=Math.hypot(d,u,c))?(d*=m=1/m,u*=m,c*=m):(d=0,u=0,c=0),e[0]=s,e[1]=d,e[2]=f,e[3]=0,e[4]=a,e[5]=u,e[6]=h,e[7]=0,e[8]=l,e[9]=c,e[10]=p,e[11]=0,e[12]=-(s*_+a*y+l*g),e[13]=-(d*_+u*y+c*g),e[14]=-(f*_+h*y+p*g),e[15]=1)}(m,d(0,0,0),d(0,0,-1),d(0,1,0));{const t=3,r=e.FLOAT,i=!1,s=0,o=0;e.bindBuffer(e.ARRAY_BUFFER,a.position),e.vertexAttribPointer(n.attribLocations.vertexPosition,t,r,i,s,o),e.enableVertexAttribArray(n.attribLocations.vertexPosition)}{const t=2,r=e.FLOAT,i=!1,s=0,o=0;e.bindBuffer(e.ARRAY_BUFFER,a.texPosition),e.vertexAttribPointer(n.attribLocations.texturePosition,t,r,i,s,o),e.enableVertexAttribArray(n.attribLocations.texturePosition)}e.activeTexture(e.TEXTURE0+3),e.bindTexture(e.TEXTURE_2D,c),e.activeTexture(e.TEXTURE0+4),e.bindTexture(e.TEXTURE_2D,f),e.activeTexture(e.TEXTURE0+5),e.bindTexture(e.TEXTURE_2D,h),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,a.indices),e.useProgram(n.program),e.uniformMatrix4fv(n.uniformLocations.projectionMatrix,!1,u),e.uniformMatrix4fv(n.uniformLocations.modelMatrix,!1,p),e.uniformMatrix4fv(n.uniformLocations.viewMatrix,!1,m),e.uniform1i(n.uniformLocations.rgbatexture,2),e.uniform1i(n.uniformLocations.ytexture,3),e.uniform1i(n.uniformLocations.utexture,4),e.uniform1i(n.uniformLocations.vtexture,5),e.uniform1i(n.uniformLocations.isyuv,1);{const t=6,r=e.UNSIGNED_SHORT,i=0;e.drawElements(e.TRIANGLES,t,r,i)}}return{render:function(t,r,i,n,s){e.activeTexture(e.TEXTURE0),e.bindTexture(e.TEXTURE_2D,c),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t,r,0,e.LUMINANCE,e.UNSIGNED_BYTE,i),e.activeTexture(e.TEXTURE1),e.bindTexture(e.TEXTURE_2D,f),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,r/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,n),e.activeTexture(e.TEXTURE2),e.bindTexture(e.TEXTURE_2D,h),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,r/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,s),_(t,r)},renderYUV:function(t,r,i){let n=i.slice(0,t*r),s=i.slice(t*r,t*r*5/4),o=i.slice(t*r*5/4,t*r*3/2);e.activeTexture(e.TEXTURE0),e.bindTexture(e.TEXTURE_2D,c),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t,r,0,e.LUMINANCE,e.UNSIGNED_BYTE,n),e.activeTexture(e.TEXTURE1),e.bindTexture(e.TEXTURE_2D,f),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,r/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,s),e.activeTexture(e.TEXTURE2),e.bindTexture(e.TEXTURE_2D,h),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,r/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,o),_(t,r)},destroy:function(){e.deleteProgram(n.program),e.deleteBuffer(a.position),e.deleteBuffer(a.texPosition),e.deleteBuffer(a.indices),e.deleteTexture(u),e.deleteTexture(c),e.deleteTexture(f),e.deleteTexture(h),n=null,a=null,u=null,c=null,f=null,h=null}}};const c=1,f=2,h="fetch",p="websocket",m="player",_="playbackTF",y="mp4",g="debug",v="warn",b=36e5,w={playType:m,container:"",videoBuffer:1e3,videoBufferDelay:1e3,networkDelay:1e4,isResize:!0,isFullResize:!1,isFlv:!1,isHls:!1,isFmp4:!1,isFmp4Private:!1,isWebrtc:!1,isWebrtcForZLM:!1,isWebrtcForSRS:!1,isWebrtcForOthers:!0,isNakedFlow:!1,isMpeg4:!1,isAliyunRtc:!1,isTs:!1,debug:!1,debugLevel:v,debugUuid:"",isMulti:!0,multiIndex:-1,hotKey:!1,loadingTimeout:10,heartTimeout:10,timeout:10,pageVisibilityHiddenTimeout:300,loadingTimeoutReplay:!0,heartTimeoutReplay:!0,loadingTimeoutReplayTimes:3,heartTimeoutReplayTimes:3,heartTimeoutReplayUseLastFrameShow:!0,replayUseLastFrameShow:!0,replayShowLoadingIcon:!1,supportDblclickFullscreen:!1,showBandwidth:!1,showPerformance:!1,mseCorrectTimeDuration:20,keepScreenOn:!0,isNotMute:!1,hasAudio:!0,hasVideo:!0,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1,ptz:!1,quality:!1,zoom:!1,close:!1,scale:!1,performance:!1,logSave:!1,aiFace:!1,aiObject:!1,aiOcclusion:!1,fullscreenFn:null,fullscreenExitFn:null,screenshotFn:null,playFn:null,pauseFn:null,recordFn:null,recordStopFn:null},extendOperateBtns:[],contextmenuBtns:[],watermarkConfig:{},controlAutoHide:!1,hasControl:!1,loadingIcon:!0,loadingIconStyle:{},loadingText:"",background:"",backgroundLoadingShow:!0,loadingBackground:"",loadingBackgroundWidth:0,loadingBackgroundHeight:0,decoder:"decoder-pro.js",decoderAudio:"decoder-pro-audio.js",decoderHard:"decoder-pro-hard.js",decoderHardNotWasm:"decoder-pro-hard-not-wasm.js",wasmMp4RecorderDecoder:"easyplayer-pro-mp4-recorder-decoder.js",decoderWASM:"",isDecoderUseCDN:!1,url:"",rotate:0,mirrorRotate:"none",aspectRatio:"default",playbackConfig:{playList:[],fps:"",showControl:!0,controlType:"normal",duration:0,startTime:"",showRateBtn:!1,rateConfig:[],showPrecision:"",showPrecisionBtn:!0,isCacheBeforeDecodeForFpsRender:!1,uiUsePlaybackPause:!1,isPlaybackPauseClearCache:!0,isUseFpsRender:!1,isUseLocalCalculateTime:!1,localOneFrameTimestamp:40,supportWheel:!1,useWCS:!1,useMSE:!1},qualityConfig:[],defaultStreamQuality:"",scaleConfig:["拉伸","缩放","正常"],forceNoOffscreen:!0,hiddenAutoPause:!1,protocol:f,demuxType:"flv",useWasm:!1,useMSE:!1,useWCS:!1,useSIMD:!0,useMThreading:!1,wcsUseVideoRender:!0,wcsUseWebgl2Render:!0,wasmUseVideoRender:!0,mseUseCanvasRender:!1,hlsUseCanvasRender:!1,webrtcUseCanvasRender:!1,useOffscreen:!1,useWebGPU:!1,mseDecodeErrorReplay:!0,wcsDecodeErrorReplay:!0,wasmDecodeErrorReplay:!0,simdDecodeErrorReplay:!0,simdDecodeErrorReplayType:"wasm",autoWasm:!0,decoderErrorAutoWasm:!0,hardDecodingNotSupportAutoWasm:!0,webglAlignmentErrorReplay:!0,webglContextLostErrorReplay:!0,openWebglAlignment:!1,syncAudioAndVideo:!1,syncAudioAndVideoDiff:500,playbackDelayTime:1e3,playbackFps:25,playbackForwardMaxRateDecodeIFrame:4,playbackCurrentTimeMove:!0,useVideoRender:!0,useCanvasRender:!1,networkDelayTimeoutReplay:!1,recordType:y,checkFirstIFrame:!0,nakedFlowFps:25,audioEngine:null,isShowRecordingUI:!0,isShowZoomingUI:!0,useFaceDetector:!1,useObjectDetector:!1,useImageDetector:!1,useOcclusionDetector:!1,ptzClickType:"click",ptzStopEmitDelay:.3,ptzZoomShow:!1,ptzApertureShow:!1,ptzFocusShow:!1,ptzMoreArrowShow:!1,weiXinInAndroidAudioBufferSize:4800,isM7sCrypto:!1,m7sCryptoAudio:!1,isSm4Crypto:!1,isXorCrypto:!1,sm4CryptoKey:"",m7sCryptoKey:"",xorCryptoKey:"",cryptoKey:"",cryptoIV:"",cryptoKeyUrl:"",autoResize:!1,useWebFullScreen:!1,ptsMaxDiff:3600,aiFaceDetectLevel:2,aiFaceDetectWidth:240,aiFaceDetectShowRect:!0,aiFaceDetectInterval:1e3,aiFaceDetectRectConfig:{},aiObjectDetectLevel:2,aiObjectDetectWidth:240,aiObjectDetectShowRect:!0,aiObjectDetectInterval:1e3,aiObjectDetectRectConfig:{},aiOcclusionDetectInterval:1e3,aiImageDetectDrop:!1,aiImageDetectActive:!1,videoRenderSupportScale:!0,mediaSourceTsIsMaxDiffReplay:!0,controlHtml:"",isH265:!1,isWebrtcH265:!1,supportLockScreenPlayAudio:!0,supportHls265:!0,isEmitSEI:!1,pauseAndNextPlayUseLastFrameShow:!1,demuxUseWorker:!1,playFailedAndReplay:!0,showMessageConfig:{webglAlignmentError:"Webgl 渲染失败",webglContextLostError:"webgl 上下文丢失",mediaSourceH265NotSupport:"不支持硬解码H265",mediaSourceFull:"缓冲区已满",mediaSourceAppendBufferError:"初始化解码器失败",mseSourceBufferError:"解码失败",mseAddSourceBufferError:"初始化解码器失败",mediaSourceDecoderConfigurationError:"初始化解码器失败",mediaSourceTsIsMaxDiff:"流异常",mseWidthOrHeightChange:"流异常",mediaSourceAudioG711NotSupport:"硬解码不支持G711a/u音频格式",mediaSourceUseCanvasRenderPlayFailed:"MediaSource解码使用canvas渲染失败",webcodecsH265NotSupport:"不支持硬解码H265",webcodecsUnsupportedConfigurationError:"初始化解码器失败",webcodecsDecodeConfigureError:"初始化解码器失败",webcodecsDecodeError:"解码失败",wcsWidthOrHeightChange:"解码失败",wasmDecodeError:"解码失败",simdDecodeError:"解码失败",wasmWidthOrHeightChange:"流异常",wasmUseVideoRenderError:"video自动渲染失败",videoElementPlayingFailed:"video自动渲染失败",simdH264DecodeVideoWidthIsTooLarge:"不支持该分辨率的视频",networkDelayTimeout:"网络超时重播失败",fetchError:"请求失败",streamEnd:"请求结束",websocketError:"请求失败",webrtcError:"请求失败",hlsError:"请求失败",decoderWorkerInitError:"初始化worker失败",videoElementPlayingFailedForWebrtc:"video自动渲染失败",videoInfoError:"解析视频分辨率失败",webrtcStreamH265:"webrtc不支持H265",delayTimeout:"播放超时重播失败",loadingTimeout:"加载超时重播失败",loadingTimeoutRetryEnd:"加载超时重播失败",delayTimeoutRetryEnd:"播放超时重播失败"},videoElementPlayingFailedReplay:!0,mp4RecordUseWasm:!0,mseAutoCleanupSourceBuffer:!0,mseAutoCleanupMaxBackwardDuration:30,mseAutoCleanupMinBackwardDuration:10,widthOrHeightChangeReplay:!0,simdH264DecodeVideoWidthIsTooLargeReplay:!0,mediaSourceAudioG711NotSupportReplay:!0,mediaSourceAudioInitTimeoutReplay:!0,mediaSourceUseCanvasRenderPlayFailedReplay:!0,mediaSourceUseCanvasRenderPlayFailedReplayType:"video",widthOrHeightChangeReplayDelayTime:0,ghostWatermarkConfig:{on:5,off:5,content:"",fontSize:12,color:"white",opacity:.15,speed:.2},dynamicWatermarkConfig:{content:"",speed:.2,fontSize:12,color:"white",opacity:.15},isDropSameTimestampGop:!1,mseDecodeAudio:!1,nakedFlowH265DemuxUseNew:!0,extendDomConfig:{html:"",showBeforePlay:!1,showAfterLoading:!0},disableContextmenu:!1,websocket1006ErrorReplay:!1,websocket1006ErrorReplayDelayTime:0,mseDecoderUseWorker:!1},E="init",S="initVideo",U="render",x="playAudio",A="initAudio",k="audioCode",T="audioNalu",B="audioAACSequenceHeader",C="videoCode",D="videoCodec",F="videoNalu",P="videoPayload",I="audioPayload",L="workerFetch",R="iframeIntervalTs",M="isDropping",z="playbackStreamVideoFps",N="wasmWidthOrHeightChange",O="simdDecodeError",$="simdH264DecodeVideoWidthIsTooLarge",G="closeEnd",H="tempStream",V="videoSEI",j="flvScriptData",W="aacSequenceHeader",Y="videoSequenceHeader",q="flvBufferData",K="checkFirstIFrame",X=1,Z=2,J=8,Q=9,ee=18,te="init",re="decode",ie="audioDecode",ne="videoDecode",se="close",oe="updateConfig",ae="clearBuffer",le="fetchStream",de="sendWsMessage",ue="streamEnd",ce="streamRate",fe="streamAbps",he="streamVbps",pe="streamDts",me="streamSuccess",_e="streamStats",ye="networkDelayTimeout",ge="websocketOpen",ve={playError:"playIsNotPauseOrUrlIsNull",fetchError:"fetchError",websocketError:"websocketError",webcodecsH265NotSupport:"webcodecsH265NotSupport",webcodecsDecodeError:"webcodecsDecodeError",webcodecsUnsupportedConfigurationError:"webcodecsUnsupportedConfigurationError",webcodecsDecodeConfigureError:"webcodecsDecodeConfigureError",mediaSourceH265NotSupport:"mediaSourceH265NotSupport",mediaSourceAudioG711NotSupport:"mediaSourceAudioG711NotSupport",mediaSourceAudioInitTimeout:"mediaSourceAudioInitTimeout",mediaSourceDecoderConfigurationError:"mediaSourceDecoderConfigurationError",mediaSourceFull:"mseSourceBufferFull",mseSourceBufferError:"mseSourceBufferError",mseAddSourceBufferError:"mseAddSourceBufferError",mediaSourceAppendBufferError:"mediaSourceAppendBufferError",mediaSourceTsIsMaxDiff:"mediaSourceTsIsMaxDiff",mediaSourceUseCanvasRenderPlayFailed:"mediaSourceUseCanvasRenderPlayFailed",mediaSourceBufferedIsZeroError:"mediaSourceBufferedIsZeroError",wasmDecodeError:"wasmDecodeError",wasmUseVideoRenderError:"wasmUseVideoRenderError",hlsError:"hlsError",webrtcError:"webrtcError",webrtcClosed:"webrtcClosed",webrtcIceCandidateError:"webrtcIceCandidateError",webglAlignmentError:"webglAlignmentError",wasmWidthOrHeightChange:"wasmWidthOrHeightChange",mseWidthOrHeightChange:"mseWidthOrHeightChange",wcsWidthOrHeightChange:"wcsWidthOrHeightChange",widthOrHeightChange:"widthOrHeightChange",tallWebsocketClosedByError:"tallWebsocketClosedByError",flvDemuxBufferSizeTooLarge:"flvDemuxBufferSizeTooLarge",wasmDecodeVideoNoResponseError:"wasmDecodeVideoNoResponseError",audioChannelError:"audioChannelError",simdH264DecodeVideoWidthIsTooLarge:"simdH264DecodeVideoWidthIsTooLarge",simdDecodeError:"simdDecodeError",webglContextLostError:"webglContextLostError",videoElementPlayingFailed:"videoElementPlayingFailed",videoElementPlayingFailedForWebrtc:"videoElementPlayingFailedForWebrtc",decoderWorkerInitError:"decoderWorkerInitError",videoInfoError:"videoInfoError",videoCodecIdError:"videoCodecIdError",streamEnd:ue,delayTimeout:"delayTimeout",loadingTimeout:"loadingTimeout",networkDelayTimeout:ye,aliyunRtcError:"aliyunRtcError",...{talkStreamError:"talkStreamError",talkStreamClose:"talkStreamClose"}},be=1,we=7,Ee=12,Se=99,Ue={h264:"H264(AVC)",h265:"H265(HEVC)"},xe={AAC:10,ALAW:7,MULAW:8,MP3:2},Ae={sps:7,pps:8,iFrame:5,kUnspecified:0,kSliceNonIDR:1,kSliceDPA:2,kSliceDPB:3,kSliceDPC:4,kSliceIDR:5,kSliceSEI:6,kSliceSPS:7,kSlicePPS:8,kSliceAUD:9,kEndOfSequence:10,kEndOfStream:11,kFiller:12,kSPSExt:13,kReserved0:14},ke={pFrame:1,iFrame:19,nLp:20,vps:32,sps:33,pps:34,sei:39,prefixSei:39,suffixSei:40},Te="key",Be="delta",Ce={h264:"avc",h265:"hevc"},De="AbortError",Fe={sequenceHeader:0,nalu:1},Pe={keyFrame:1,interFrame:2},Ie=1,Le="idle",Re="buffering",Me="complete",ze=0,Ne=1,Oe=3,$e=16,Ge=9e4,He=45e4;function Ve(e,t){return e(t={exports:{}},t.exports),t.exports}function je({profile:e,sampleRate:t,channel:r}){return new Uint8Array([175,0,e<<3|(14&t)>>1,(1&t)<<7|r<<3])}function We(e){return Ye(e)&&e[1]===Fe.sequenceHeader}function Ye(e){return e[0]>>4===xe.AAC}Ve((function(e){!function(){var t="undefined"!=typeof window&&void 0!==window.document?window.document:{},r=e.exports,i=function(){for(var e,r=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],i=0,n=r.length,s={};i<n;i++)if((e=r[i])&&e[1]in t){for(i=0;i<e.length;i++)s[r[0][i]]=e[i];return s}return!1}(),n={change:i.fullscreenchange,error:i.fullscreenerror},s={request:function(e,r){return new Promise(function(n,s){var o=function(){this.off("change",o),n()}.bind(this);this.on("change",o);var a=(e=e||t.documentElement)[i.requestFullscreen](r);a instanceof Promise&&a.then(o).catch(s)}.bind(this))},exit:function(){return new Promise(function(e,r){if(this.isFullscreen){var n=function(){this.off("change",n),e()}.bind(this);this.on("change",n);var s=t[i.exitFullscreen]();s instanceof Promise&&s.then(n).catch(r)}else e()}.bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,r){var i=n[e];i&&t.addEventListener(i,r,!1)},off:function(e,r){var i=n[e];i&&t.removeEventListener(i,r,!1)},raw:i};i?(Object.defineProperties(s,{isFullscreen:{get:function(){return Boolean(t[i.fullscreenElement])}},element:{enumerable:!0,get:function(){return t[i.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(t[i.fullscreenEnabled])}}}),r?e.exports=s:window.screenfull=s):r?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}}()})).isEnabled;const qe=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],Ke=qe;function Xe(e,t){if("mp4a.40.2"===e){if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}}function Ze(e,t=9e4){return 1024*t/e}function Je(){return(new Date).getTime()}function Qe(){return performance&&"function"==typeof performance.now?performance.now():Date.now()}function et(e){let t=0,r=Qe();return i=>{if(n=i,"[object Number]"!==Object.prototype.toString.call(n))return;var n;t+=i;const s=Qe(),o=s-r;o>=1e3&&(e(t/o*1e3),r=s,t=0)}}function tt(){const e=window.navigator.userAgent;return!e.match(/Chrome/gi)&&!!e.match(/Safari/gi)}function rt(e){return null==e}function it(e){e.close()}function nt(e,t){t&&(e=e.filter((e=>e.type&&e.type===t)));let r=e[0],i=null,n=1;if(e.length>0){let t=e[1];t&&t.ts-r.ts>1e5&&(r=t,n=2)}if(r)for(let s=n;s<e.length;s++){let n=e[s];if(t&&n.type&&n.type!==t&&(n=null),n){if(n.ts-r.ts>=1e3){e[s-1].ts-r.ts<1e3&&(i=s+1)}}}return i}function st(){return function(e){let t="";if("object"==typeof e)try{t=JSON.stringify(e),t=JSON.parse(t)}catch(r){t=e}else t=e;return t}(w)}function ot(e){return e[0]>>4===Pe.keyFrame&&e[1]===Fe.sequenceHeader}function at(e){return!0===e||"true"===e}function lt(e){return!0!==e&&"true"!==e}(()=>{try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(e instanceof WebAssembly.Module)return new WebAssembly.Instance(e)instanceof WebAssembly.Instance}}catch(e){}})();var dt=function(e,t,r,i){return new(r||(r=Promise))((function(n,s){function o(e){try{l(i.next(e))}catch(e){s(e)}}function a(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}l((i=i.apply(e,t||[])).next())}))};const ut=Symbol(32),ct=Symbol(16),ft=Symbol(8);class ht{constructor(e){this.g=e,this.consumed=0,e&&(this.need=e.next().value)}setG(e){this.g=e,this.demand(e.next().value,!0)}consume(){this.buffer&&this.consumed&&(this.buffer.copyWithin(0,this.consumed),this.buffer=this.buffer.subarray(0,this.buffer.length-this.consumed),this.consumed=0)}demand(e,t){return t&&this.consume(),this.need=e,this.flush()}read(e){return dt(this,void 0,void 0,(function*(){return this.lastReadPromise&&(yield this.lastReadPromise),this.lastReadPromise=new Promise(((t,r)=>{var i;this.reject=r,this.resolve=e=>{delete this.lastReadPromise,delete this.resolve,delete this.need,t(e)};this.demand(e,!0)||null===(i=this.pull)||void 0===i||i.call(this,e)}))}))}readU32(){return this.read(ut)}readU16(){return this.read(ct)}readU8(){return this.read(ft)}close(){var e;this.g&&this.g.return(),this.buffer&&this.buffer.subarray(0,0),null===(e=this.reject)||void 0===e||e.call(this,new Error("EOF")),delete this.lastReadPromise}flush(){if(!this.buffer||!this.need)return;let e=null;const t=this.buffer.subarray(this.consumed);let r=0;const i=e=>t.length<(r=e);if("number"==typeof this.need){if(i(this.need))return;e=t.subarray(0,r)}else if(this.need===ut){if(i(4))return;e=t[0]<<24|t[1]<<16|t[2]<<8|t[3]}else if(this.need===ct){if(i(2))return;e=t[0]<<8|t[1]}else if(this.need===ft){if(i(1))return;e=t[0]}else if("buffer"in this.need){if("byteOffset"in this.need){if(i(this.need.byteLength-this.need.byteOffset))return;new Uint8Array(this.need.buffer,this.need.byteOffset).set(t.subarray(0,r)),e=this.need}else if(this.g)return void this.g.throw(new Error("Unsupported type"))}else{if(i(this.need.byteLength))return;new Uint8Array(this.need).set(t.subarray(0,r)),e=this.need}return this.consumed+=r,this.g?this.demand(this.g.next(e).value,!0):this.resolve&&this.resolve(e),e}write(e){if(e instanceof Uint8Array?this.malloc(e.length).set(e):"buffer"in e?this.malloc(e.byteLength).set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength)):this.malloc(e.byteLength).set(new Uint8Array(e)),!this.g&&!this.resolve)return new Promise((e=>this.pull=e));this.flush()}writeU32(e){this.malloc(4).set([e>>24&255,e>>16&255,e>>8&255,255&e]),this.flush()}writeU16(e){this.malloc(2).set([e>>8&255,255&e]),this.flush()}writeU8(e){this.malloc(1)[0]=e,this.flush()}malloc(e){if(this.buffer){const t=this.buffer.length,r=t+e;if(r<=this.buffer.buffer.byteLength-this.buffer.byteOffset)this.buffer=new Uint8Array(this.buffer.buffer,this.buffer.byteOffset,r);else{const e=new Uint8Array(r);e.set(this.buffer),this.buffer=e}return this.buffer.subarray(t,r)}return this.buffer=new Uint8Array(e),this.buffer}}ht.U32=ut,ht.U16=ct,ht.U8=ft;class pt{constructor(e){this.log=(t,...r)=>{if(e._opt.debug&&e._opt.debugLevel==g){const i=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";console.log(`EasyPro${i}[✅✅✅][${t}]`,...r)}},this.warn=(t,...r)=>{if(e._opt.debug&&(e._opt.debugLevel==g||e._opt.debugLevel==v)){const i=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";console.log(`EasyPro${i}[❗❗❗][${t}]`,...r)}},this.error=(t,...r)=>{const i=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";console.error(`EasyPro${i}[❌❌❌][${t}]`,...r)}}}class mt{constructor(e){this._buffer=e,this._buffer_index=0,this._total_bytes=e.byteLength,this._total_bits=8*e.byteLength,this._current_word=0,this._current_word_bits_left=0}destroy(){this._buffer=null}_fillCurrentWord(){let e=this._total_bytes-this._buffer_index;if(e<=0)return void console.error("ExpGolomb: _fillCurrentWord() but no bytes available",this._total_bytes,this._buffer_index);let t=Math.min(4,e),r=new Uint8Array(4);r.set(this._buffer.subarray(this._buffer_index,this._buffer_index+t)),this._current_word=new DataView(r.buffer).getUint32(0,!1),this._buffer_index+=t,this._current_word_bits_left=8*t}readBits(e){if(e>32&&console.error("ExpGolomb: readBits() bits exceeded max 32bits!"),e<=this._current_word_bits_left){let t=this._current_word>>>32-e;return this._current_word<<=e,this._current_word_bits_left-=e,t}let t=this._current_word_bits_left?this._current_word:0;t>>>=32-this._current_word_bits_left;let r=e-this._current_word_bits_left;this._fillCurrentWord();let i=Math.min(r,this._current_word_bits_left),n=this._current_word>>>32-i;return this._current_word<<=i,this._current_word_bits_left-=i,t=t<<i|n,t}readBool(){return 1===this.readBits(1)}readByte(){return this.readBits(8)}_skipLeadingZero(){let e;for(e=0;e<this._current_word_bits_left;e++)if(this._current_word&2147483648>>>e)return this._current_word<<=e,this._current_word_bits_left-=e,e;return this._fillCurrentWord(),e+this._skipLeadingZero()}readUEG(){let e=this._skipLeadingZero();return this.readBits(e+1)-1}readSEG(){let e=this.readUEG();return 1&e?e+1>>>1:-1*(e>>>1)}}class _t{static _ebsp2rbsp(e){let t=e,r=t.byteLength,i=new Uint8Array(r),n=0;for(let e=0;e<r;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(i[n]=t[e],n++);return new Uint8Array(i.buffer,0,n)}static parseSPS(e){let t=_t._ebsp2rbsp(e),r=new mt(t);r.readByte();let i=r.readByte();r.readByte();let n=r.readByte();r.readUEG();let s=_t.getProfileString(i),o=_t.getLevelString(n),a=1,l=420,d=[0,420,422,444],u=8;if((100===i||110===i||122===i||244===i||44===i||83===i||86===i||118===i||128===i||138===i||144===i)&&(a=r.readUEG(),3===a&&r.readBits(1),a<=3&&(l=d[a]),u=r.readUEG()+8,r.readUEG(),r.readBits(1),r.readBool())){let e=3!==a?8:12;for(let t=0;t<e;t++)r.readBool()&&(t<6?_t._skipScalingList(r,16):_t._skipScalingList(r,64))}r.readUEG();let c=r.readUEG();if(0===c)r.readUEG();else if(1===c){r.readBits(1),r.readSEG(),r.readSEG();let e=r.readUEG();for(let t=0;t<e;t++)r.readSEG()}let f=r.readUEG();r.readBits(1);let h=r.readUEG(),p=r.readUEG(),m=r.readBits(1);0===m&&r.readBits(1),r.readBits(1);let _=0,y=0,g=0,v=0;r.readBool()&&(_=r.readUEG(),y=r.readUEG(),g=r.readUEG(),v=r.readUEG());let b=1,w=1,E=0,S=!0,U=0,x=0;if(r.readBool()){if(r.readBool()){let e=r.readByte();e>0&&e<16?(b=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][e-1],w=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][e-1]):255===e&&(b=r.readByte()<<8|r.readByte(),w=r.readByte()<<8|r.readByte())}if(r.readBool()&&r.readBool(),r.readBool()&&(r.readBits(4),r.readBool()&&r.readBits(24)),r.readBool()&&(r.readUEG(),r.readUEG()),r.readBool()){let e=r.readBits(32),t=r.readBits(32);S=r.readBool(),U=t,x=2*e,E=U/x}}let A=1;1===b&&1===w||(A=b/w);let k=0,T=0;if(0===a)k=1,T=2-m;else{k=3===a?1:2,T=(1===a?2:1)*(2-m)}let B=16*(h+1),C=16*(p+1)*(2-m);B-=(_+y)*k,C-=(g+v)*T;let D=Math.ceil(B*A);return r.destroy(),r=null,{profile_string:s,level_string:o,bit_depth:u,ref_frames:f,chroma_format:l,chroma_format_string:_t.getChromaFormatString(l),frame_rate:{fixed:S,fps:E,fps_den:x,fps_num:U},sar_ratio:{width:b,height:w},codec_size:{width:B,height:C},present_size:{width:D,height:C}}}static parseSPS$2(e){let t=e.subarray(1,4),r="avc1.";for(let e=0;e<3;e++){let i=t[e].toString(16);i.length<2&&(i="0"+i),r+=i}let i=_t._ebsp2rbsp(e),n=new mt(i);n.readByte();let s=n.readByte();n.readByte();let o=n.readByte();n.readUEG();let a=_t.getProfileString(s),l=_t.getLevelString(o),d=1,u=420,c=[0,420,422,444],f=8,h=8;if((100===s||110===s||122===s||244===s||44===s||83===s||86===s||118===s||128===s||138===s||144===s)&&(d=n.readUEG(),3===d&&n.readBits(1),d<=3&&(u=c[d]),f=n.readUEG()+8,h=n.readUEG()+8,n.readBits(1),n.readBool())){let e=3!==d?8:12;for(let t=0;t<e;t++)n.readBool()&&(t<6?_t._skipScalingList(n,16):_t._skipScalingList(n,64))}n.readUEG();let p=n.readUEG();if(0===p)n.readUEG();else if(1===p){n.readBits(1),n.readSEG(),n.readSEG();let e=n.readUEG();for(let t=0;t<e;t++)n.readSEG()}let m=n.readUEG();n.readBits(1);let _=n.readUEG(),y=n.readUEG(),g=n.readBits(1);0===g&&n.readBits(1),n.readBits(1);let v=0,b=0,w=0,E=0;n.readBool()&&(v=n.readUEG(),b=n.readUEG(),w=n.readUEG(),E=n.readUEG());let S=1,U=1,x=0,A=!0,k=0,T=0;if(n.readBool()){if(n.readBool()){let e=n.readByte();e>0&&e<16?(S=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][e-1],U=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][e-1]):255===e&&(S=n.readByte()<<8|n.readByte(),U=n.readByte()<<8|n.readByte())}if(n.readBool()&&n.readBool(),n.readBool()&&(n.readBits(4),n.readBool()&&n.readBits(24)),n.readBool()&&(n.readUEG(),n.readUEG()),n.readBool()){let e=n.readBits(32),t=n.readBits(32);A=n.readBool(),k=t,T=2*e,x=k/T}}let B=1;1===S&&1===U||(B=S/U);let C=0,D=0;if(0===d)C=1,D=2-g;else{C=3===d?1:2,D=(1===d?2:1)*(2-g)}let F=16*(_+1),P=16*(y+1)*(2-g);F-=(v+b)*C,P-=(w+E)*D;let I=Math.ceil(F*B);return n.destroy(),n=null,{codec_mimetype:r,profile_idc:s,level_idc:o,profile_string:a,level_string:l,chroma_format_idc:d,bit_depth:f,bit_depth_luma:f,bit_depth_chroma:h,ref_frames:m,chroma_format:u,chroma_format_string:_t.getChromaFormatString(u),frame_rate:{fixed:A,fps:x,fps_den:T,fps_num:k},sar_ratio:{width:S,height:U},codec_size:{width:F,height:P},present_size:{width:I,height:P}}}static _skipScalingList(e,t){let r=8,i=8,n=0;for(let s=0;s<t;s++)0!==i&&(n=e.readSEG(),i=(r+n+256)%256),r=0===i?r:i}static getProfileString(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}}static getLevelString(e){return(e/10).toFixed(1)}static getChromaFormatString(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}}}class yt{constructor(e){this.buffer=e,this.buflen=e.length,this.bufpos=0,this.bufoff=0,this.iserro=!1}read(e){let t=0,r=0;for(;e;){if(e<0||this.bufpos>=this.buflen)return this.iserro=!0,0;this.iserro=!1,r=this.bufoff+e>8?8-this.bufoff:e,t<<=r,t+=this.buffer[this.bufpos]>>8-this.bufoff-r&255>>8-r,this.bufoff+=r,e-=r,8==this.bufoff&&(this.bufpos++,this.bufoff=0)}return t}look(e){let t=this.bufpos,r=this.bufoff,i=this.read(e);return this.bufpos=t,this.bufoff=r,i}read_golomb(){let e;for(e=0;0===this.read(1)&&!this.iserro;e++);return(1<<e)+this.read(e)-1}}function gt(e){const t={},r=new DataView(e.buffer);let i=r.getUint8(0),n=r.getUint8(1);if(r.getUint8(2),r.getUint8(3),1!==i||0===n)return{};const s=1+(3&r.getUint8(4));if(3!==s&&4!==s)return{};let o=31&r.getUint8(5);if(0===o)return{};let a=6;for(let i=0;i<o;i++){let n=r.getUint16(a,!1);if(a+=2,0===n)continue;let s=new Uint8Array(e.buffer,a,n);a+=n;let o=_t.parseSPS(s);if(0!==i)continue;t.sps=s,t.timescale=1e3,t.codecWidth=o.codec_size.width,t.codecHeight=o.codec_size.height,t.presentWidth=o.present_size.width,t.presentHeight=o.present_size.height,t.profile=o.profile_string,t.level=o.level_string,t.bitDepth=o.bit_depth,t.chromaFormat=o.chroma_format,t.sarRatio=o.sar_ratio,t.frameRate=o.frame_rate,!1!==o.frame_rate.fixed&&0!==o.frame_rate.fps_num&&0!==o.frame_rate.fps_den||(t.frameRate={fixed:!0,fps:25,fps_num:25e3,fps_den:1e3});let l=t.frameRate.fps_den,d=t.frameRate.fps_num;t.refSampleDuration=t.timescale*(l/d);let u=s.subarray(1,4),c="avc1.";for(let e=0;e<3;e++){let t=u[e].toString(16);t.length<2&&(t="0"+t),c+=t}t.codec=c}let l=r.getUint8(a);if(0===l)return{};a++;for(let i=0;i<l;i++){let i=r.getUint16(a,!1);if(a+=2,0===i)continue;let n=new Uint8Array(e.buffer,a,i);a+=i,t.pps=n}if(t.videoType="avc",t.sps){const e=t.sps.byteLength,r=new Uint8Array([e>>>24&255,e>>>16&255,e>>>8&255,255&e]),i=new Uint8Array(e+4);i.set(r,0),i.set(t.sps,4),t.sps=i}if(t.pps){const e=t.pps.byteLength,r=new Uint8Array([e>>>24&255,e>>>16&255,e>>>8&255,255&e]),i=new Uint8Array(e+4);i.set(r,0),i.set(t.pps,4),t.pps=i}return t}function vt({sps:e,pps:t}){const r=[23,0,0,0,0,1,66,0,30,255];r[0]=23,r[6]=e[1],r[7]=e[2],r[8]=e[3],r[10]=225,r[11]=e.byteLength>>8&255,r[12]=255&e.byteLength,r.push(...e,1,t.byteLength>>8&255,255&t.byteLength,...t);return new Uint8Array(r)}function bt(e,t){let r=[];r[0]=t?23:39,r[1]=1,r[2]=0,r[3]=0,r[4]=0;const i=new Uint8Array(r.length+e.byteLength);return i.set(r,0),i.set(e,r.length),i}function wt(e){return 31&e[0]}function Et(e){return e===Ae.kSliceSEI}function St(e){return!function(e){return e===Ae.sps||e===Ae.pps}(e)&&!Et(e)}function Ut(e){return e===Ae.iFrame}const xt=e=>{let t=e,r=t.byteLength,i=new Uint8Array(r),n=0;for(let e=0;e<r;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(i[n]=t[e],n++);return new Uint8Array(i.buffer,0,n)},At=e=>{switch(e){case 0:return"4:0:0";case 1:return"4:2:0";case 2:return"4:2:2";case 3:return"4:4:4";default:return"Unknown"}},kt=e=>{let t=xt(e),r=new mt(t);r.readByte(),r.readByte();let i=0,n=0,s=0,o=0;r.readBits(4);let a=r.readBits(3);r.readBool();let l=r.readBits(2),d=r.readBool(),u=r.readBits(5),c=r.readByte(),f=r.readByte(),h=r.readByte(),p=r.readByte(),m=r.readByte(),_=r.readByte(),y=r.readByte(),g=r.readByte(),v=r.readByte(),b=r.readByte(),w=r.readByte(),E=[],S=[];for(let e=0;e<a;e++)E.push(r.readBool()),S.push(r.readBool());if(a>0)for(let e=a;e<8;e++)r.readBits(2);for(let e=0;e<a;e++)E[e]&&(r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte()),E[e]&&r.readByte();r.readUEG();let U=r.readUEG();3==U&&r.readBits(1);let x=r.readUEG(),A=r.readUEG();r.readBool()&&(i+=r.readUEG(),n+=r.readUEG(),s+=r.readUEG(),o+=r.readUEG());let k=r.readUEG(),T=r.readUEG(),B=r.readUEG();for(let e=r.readBool()?0:a;e<=a;e++)r.readUEG(),r.readUEG(),r.readUEG();if(r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readBool()){if(r.readBool())for(let e=0;e<4;e++)for(let t=0;t<(3===e?2:6);t++){if(r.readBool()){let t=Math.min(64,1<<4+(e<<1));e>1&&r.readSEG();for(let e=0;e<t;e++)r.readSEG()}else r.readUEG()}}r.readBool(),r.readBool(),r.readBool()&&(r.readByte(),r.readUEG(),r.readUEG(),r.readBool());let C=r.readUEG(),D=0;for(let e=0;e<C;e++){let t=!1;if(0!==e&&(t=r.readBool()),t){e===C&&r.readUEG(),r.readBool(),r.readUEG();let t=0;for(let e=0;e<=D;e++){let e=r.readBool(),i=!1;e||(i=r.readBool()),(e||i)&&t++}D=t}else{let e=r.readUEG(),t=r.readUEG();D=e+t;for(let t=0;t<e;t++)r.readUEG(),r.readBool();for(let e=0;e<t;e++)r.readUEG(),r.readBool()}}if(r.readBool()){let e=r.readUEG();for(let t=0;t<e;t++){for(let e=0;e<B+4;e++)r.readBits(1);r.readBits(1)}}let F=!1,P=0,I=1,L=1,R=!1,M=1,z=1;if(r.readBool(),r.readBool(),r.readBool()){if(r.readBool()){let e=r.readByte();e>0&&e<16?(I=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][e-1],L=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][e-1]):255===e&&(I=r.readBits(16),L=r.readBits(16))}if(r.readBool()&&r.readBool(),r.readBool()){r.readBits(3),r.readBool(),r.readBool()&&(r.readByte(),r.readByte(),r.readByte())}if(r.readBool()&&(r.readUEG(),r.readUEG()),r.readBool(),r.readBool(),r.readBool(),F=r.readBool(),F&&(i+=r.readUEG(),n+=r.readUEG(),s+=r.readUEG(),o+=r.readUEG()),r.readBool()){if(M=r.readBits(32),z=r.readBits(32),r.readBool()){if(r.readUEG(),r.readBool()){let e=!1,t=!1,i=!1;e=r.readBool(),t=r.readBool(),(e||t)&&(i=r.readBool(),i&&(r.readByte(),r.readBits(5),r.readBool(),r.readBits(5)),r.readBits(4),r.readBits(4),i&&r.readBits(4),r.readBits(5),r.readBits(5),r.readBits(5));for(let n=0;n<=a;n++){let n=r.readBool();R=n;let s=!1,o=1;n||(s=r.readBool());let a=!1;if(s?r.readSEG():a=r.readBool(),a||(cpbcnt=r.readUEG()+1),e)for(let e=0;e<o;e++)r.readUEG(),r.readUEG(),i&&(r.readUEG(),r.readUEG());if(t)for(let e=0;e<o;e++)r.readUEG(),r.readUEG(),i&&(r.readUEG(),r.readUEG())}}}}r.readBool()&&(r.readBool(),r.readBool(),r.readBool(),P=r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG())}r.readBool();let N=`hvc1.${u}.1.L${w}.B0`,O=x,$=A,G=1;return 1!==I&&1!==L&&(G=I/L),r.destroy(),r=null,{codec_mimetype:N,level_string:(H=w,(H/30).toFixed(1)),profile_idc:u,bit_depth:k+8,ref_frames:1,chroma_format:U,chroma_format_string:At(U),general_level_idc:w,general_profile_space:l,general_tier_flag:d,general_profile_idc:u,general_profile_compatibility_flags_1:c,general_profile_compatibility_flags_2:f,general_profile_compatibility_flags_3:h,general_profile_compatibility_flags_4:p,general_constraint_indicator_flags_1:m,general_constraint_indicator_flags_2:_,general_constraint_indicator_flags_3:y,general_constraint_indicator_flags_4:g,general_constraint_indicator_flags_5:v,general_constraint_indicator_flags_6:b,min_spatial_segmentation_idc:P,constant_frame_rate:0,chroma_format_idc:U,bit_depth_luma_minus8:k,bit_depth_chroma_minus8:T,frame_rate:{fixed:R,fps:z/M,fps_den:M,fps_num:z},sar_ratio:{width:I,height:L},codec_size:{width:O,height:$},present_size:{width:O*G,height:$}};var H},Tt=e=>{let t=xt(e),r=new mt(t);return r.readByte(),r.readByte(),r.readBits(4),r.readBits(2),r.readBits(6),{num_temporal_layers:r.readBits(3)+1,temporal_id_nested:r.readBool()}},Bt=e=>{let t=xt(e),r=new mt(t);r.readByte(),r.readByte(),r.readUEG(),r.readUEG(),r.readBool(),r.readBool(),r.readBits(3),r.readBool(),r.readBool(),r.readUEG(),r.readUEG(),r.readSEG(),r.readBool(),r.readBool(),r.readBool()&&r.readUEG(),r.readSEG(),r.readSEG(),r.readBool(),r.readBool(),r.readBool(),r.readBool();let i=r.readBool(),n=r.readBool(),s=1;return n&&i?s=0:n?s=3:i&&(s=2),{parallelismType:s}};function Ct(e,t=0){return(e[t]<<24>>>0)+(e[t+1]<<16)+(e[t+2]<<8)+(e[t+3]||0)}function Dt(e,t=4){if(e.length<4)return;const r=e.length,i=[];let n,s=0;for(;s+t<r;)if(n=Ct(e,s),3===t&&(n>>>=8),s+=t,n){if(s+n>r)break;i.push(e.subarray(s,s+n)),s+=n}return i}function Ft(e){const t=e.byteLength,r=new Uint8Array(4);r[0]=t>>>24&255,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t;const i=new Uint8Array(t+4);return i.set(r,0),i.set(e,4),i}function Pt(e,t){let r=null;return t?e.length>=28&&(r=1+(3&e[26])):e.length>=12&&(r=1+(3&e[9])),r}function It(e,t){let r={},i=e.length,n=[],s=new yt(e);s.read(1),s.read(6),s.read(6),s.read(3);for(let e=2;e<i;e++)e+2<i&&3==s.look(24)?(n.push(s.read(8)),n.push(s.read(8)),e+=2,s.read(8)):n.push(s.read(8));let o=new Uint8Array(n),a=new yt(o);if(r.sps_video_parameter_set_id=a.read(4),r.sps_max_sub_layers_minus1=a.read(3),r.sps_temporal_id_nesting_flag=a.read(1),r.profile_tier_level=function(e,t,r){let i={};i.profile_space=e.read(2),i.tier_flag=e.read(1),i.profile_idc=e.read(5),i.profile_compatibility_flags=e.read(32),i.general_progressive_source_flag=e.read(1),i.general_interlaced_source_flag=e.read(1),i.general_non_packed_constraint_flag=e.read(1),i.general_frame_only_constraint_flag=e.read(1),e.read(32),e.read(12),i.level_idc=e.read(8),i.sub_layer_profile_present_flag=[],i.sub_layer_level_present_flag=[];for(let t=0;t<r;t++)i.sub_layer_profile_present_flag[t]=e.read(1),i.sub_layer_level_present_flag[t]=e.read(1);if(r>0)for(let t=r;t<8;t++)e.read(2);i.sub_layer_profile_space=[],i.sub_layer_tier_flag=[],i.sub_layer_profile_idc=[],i.sub_layer_profile_compatibility_flag=[],i.sub_layer_progressive_source_flag=[],i.sub_layer_interlaced_source_flag=[],i.sub_layer_non_packed_constraint_flag=[],i.sub_layer_frame_only_constraint_flag=[],i.sub_layer_level_idc=[];for(let t=0;t<r;t++)i.sub_layer_profile_present_flag[t]&&(i.sub_layer_profile_space[t]=e.read(2),i.sub_layer_tier_flag[t]=e.read(1),i.sub_layer_profile_idc[t]=e.read(5),i.sub_layer_profile_compatibility_flag[t]=e.read(32),i.sub_layer_progressive_source_flag[t]=e.read(1),i.sub_layer_interlaced_source_flag[t]=e.read(1),i.sub_layer_non_packed_constraint_flag[t]=e.read(1),i.sub_layer_frame_only_constraint_flag[t]=e.read(1),e.read(32),e.read(12)),i.sub_layer_level_present_flag[t]?i.sub_layer_level_idc[t]=e.read(8):i.sub_layer_level_idc[t]=1;return i}(a,0,r.sps_max_sub_layers_minus1),r.sps_seq_parameter_set_id=a.read_golomb(),r.chroma_format_idc=a.read_golomb(),3==r.chroma_format_idc?r.separate_colour_plane_flag=a.read(1):r.separate_colour_plane_flag=0,r.pic_width_in_luma_samples=a.read_golomb(),r.pic_height_in_luma_samples=a.read_golomb(),r.conformance_window_flag=a.read(1),r.conformance_window_flag){let e=1+(r.chroma_format_idc<2),t=1+(r.chroma_format_idc<3);r.conf_win_left_offset=a.read_golomb()*t,r.conf_win_right_offset=a.read_golomb()*t,r.conf_win_top_offset=a.read_golomb()*e,r.conf_win_bottom_offset=a.read_golomb()*e}else r.conf_win_left_offset=0,r.conf_win_right_offset=0,r.conf_win_top_offset=0,r.conf_win_bottom_offset=0;return r}function Lt({vps:e,pps:t,sps:r}){let i={configurationVersion:1};const n=Tt(e),s=kt(r),o=Bt(t);i=Object.assign(i,n,s,o);let a=23+(5+e.byteLength)+(5+r.byteLength)+(5+t.byteLength),l=new Uint8Array(a);l[0]=1,l[1]=(3&i.general_profile_space)<<6|(i.general_tier_flag?1:0)<<5|31&i.general_profile_idc,l[2]=i.general_profile_compatibility_flags_1||0,l[3]=i.general_profile_compatibility_flags_2||0,l[4]=i.general_profile_compatibility_flags_3||0,l[5]=i.general_profile_compatibility_flags_4||0,l[6]=i.general_constraint_indicator_flags_1||0,l[7]=i.general_constraint_indicator_flags_2||0,l[8]=i.general_constraint_indicator_flags_3||0,l[9]=i.general_constraint_indicator_flags_4||0,l[10]=i.general_constraint_indicator_flags_5||0,l[11]=i.general_constraint_indicator_flags_6||0,l[12]=60,l[13]=240|(3840&i.min_spatial_segmentation_idc)>>8,l[14]=255&i.min_spatial_segmentation_idc,l[15]=252|3&i.parallelismType,l[16]=252|3&i.chroma_format_idc,l[17]=248|7&i.bit_depth_luma_minus8,l[18]=248|7&i.bit_depth_chroma_minus8,l[19]=0,l[20]=0,l[21]=(3&i.constant_frame_rate)<<6|(7&i.num_temporal_layers)<<3|(i.temporal_id_nested?1:0)<<2|3,l[22]=3,l[23]=128|ke.vps,l[24]=0,l[25]=1,l[26]=(65280&e.byteLength)>>8,l[27]=255&e.byteLength,l.set(e,28),l[23+(5+e.byteLength)+0]=128|ke.sps,l[23+(5+e.byteLength)+1]=0,l[23+(5+e.byteLength)+2]=1,l[23+(5+e.byteLength)+3]=(65280&r.byteLength)>>8,l[23+(5+e.byteLength)+4]=255&r.byteLength,l.set(r,23+(5+e.byteLength)+5),l[23+(5+e.byteLength+5+r.byteLength)+0]=128|ke.pps,l[23+(5+e.byteLength+5+r.byteLength)+1]=0,l[23+(5+e.byteLength+5+r.byteLength)+2]=1,l[23+(5+e.byteLength+5+r.byteLength)+3]=(65280&t.byteLength)>>8,l[23+(5+e.byteLength+5+r.byteLength)+4]=255&t.byteLength,l.set(t,23+(5+e.byteLength+5+r.byteLength)+5);const d=[28,0,0,0,0],u=new Uint8Array(d.length+l.byteLength);return u.set(d,0),u.set(l,d.length),u}function Rt(e,t){let r=[];r[0]=t?28:44,r[1]=1,r[2]=0,r[3]=0,r[4]=0;const i=new Uint8Array(r.length+e.byteLength);return i.set(r,0),i.set(e,r.length),i}function Mt(e){return(126&e[0])>>1}function zt(e){return!function(e){return e>=32&&e<=40}(e)}function Nt(e){return e>=16&&e<=21}function Ot(e){return parseInt(e)===e}function $t(e){if(!Ot(e.length))return!1;for(var t=0;t<e.length;t++)if(!Ot(e[t])||e[t]<0||e[t]>255)return!1;return!0}function Gt(e,t){if(e.buffer&&"Uint8Array"===e.name)return t&&(e=e.slice?e.slice():Array.prototype.slice.call(e)),e;if(Array.isArray(e)){if(!$t(e))throw new Error("Array contains invalid value: "+e);return new Uint8Array(e)}if(Ot(e.length)&&$t(e))return new Uint8Array(e);throw new Error("unsupported array-like object")}function Ht(e){return new Uint8Array(e)}function Vt(e,t,r,i,n){null==i&&null==n||(e=e.slice?e.slice(i,n):Array.prototype.slice.call(e,i,n)),t.set(e,r)}var jt,Wt={toBytes:function(e){var t=[],r=0;for(e=encodeURI(e);r<e.length;){var i=e.charCodeAt(r++);37===i?(t.push(parseInt(e.substr(r,2),16)),r+=2):t.push(i)}return Gt(t)},fromBytes:function(e){for(var t=[],r=0;r<e.length;){var i=e[r];i<128?(t.push(String.fromCharCode(i)),r++):i>191&&i<224?(t.push(String.fromCharCode((31&i)<<6|63&e[r+1])),r+=2):(t.push(String.fromCharCode((15&i)<<12|(63&e[r+1])<<6|63&e[r+2])),r+=3)}return t.join("")}},Yt=(jt="0123456789abcdef",{toBytes:function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e.substr(r,2),16));return t},fromBytes:function(e){for(var t=[],r=0;r<e.length;r++){var i=e[r];t.push(jt[(240&i)>>4]+jt[15&i])}return t.join("")}}),qt={16:10,24:12,32:14},Kt=[1,2,4,8,16,32,64,128,27,54,108,216,171,77,154,47,94,188,99,198,151,53,106,212,179,125,250,239,197,145],Xt=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],Zt=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],Jt=[3328402341,4168907908,4000806809,4135287693,4294111757,3597364157,3731845041,2445657428,1613770832,33620227,3462883241,1445669757,3892248089,3050821474,1303096294,3967186586,2412431941,528646813,2311702848,4202528135,4026202645,2992200171,2387036105,4226871307,1101901292,3017069671,1604494077,1169141738,597466303,1403299063,3832705686,2613100635,1974974402,3791519004,1033081774,1277568618,1815492186,2118074177,4126668546,2211236943,1748251740,1369810420,3521504564,4193382664,3799085459,2883115123,1647391059,706024767,134480908,2512897874,1176707941,2646852446,806885416,932615841,168101135,798661301,235341577,605164086,461406363,3756188221,3454790438,1311188841,2142417613,3933566367,302582043,495158174,1479289972,874125870,907746093,3698224818,3025820398,1537253627,2756858614,1983593293,3084310113,2108928974,1378429307,3722699582,1580150641,327451799,2790478837,3117535592,0,3253595436,1075847264,3825007647,2041688520,3059440621,3563743934,2378943302,1740553945,1916352843,2487896798,2555137236,2958579944,2244988746,3151024235,3320835882,1336584933,3992714006,2252555205,2588757463,1714631509,293963156,2319795663,3925473552,67240454,4269768577,2689618160,2017213508,631218106,1269344483,2723238387,1571005438,2151694528,93294474,1066570413,563977660,1882732616,4059428100,1673313503,2008463041,2950355573,1109467491,537923632,3858759450,4260623118,3218264685,2177748300,403442708,638784309,3287084079,3193921505,899127202,2286175436,773265209,2479146071,1437050866,4236148354,2050833735,3362022572,3126681063,840505643,3866325909,3227541664,427917720,2655997905,2749160575,1143087718,1412049534,999329963,193497219,2353415882,3354324521,1807268051,672404540,2816401017,3160301282,369822493,2916866934,3688947771,1681011286,1949973070,336202270,2454276571,201721354,1210328172,3093060836,2680341085,3184776046,1135389935,3294782118,965841320,831886756,3554993207,4068047243,3588745010,2345191491,1849112409,3664604599,26054028,2983581028,2622377682,1235855840,3630984372,2891339514,4092916743,3488279077,3395642799,4101667470,1202630377,268961816,1874508501,4034427016,1243948399,1546530418,941366308,1470539505,1941222599,2546386513,3421038627,2715671932,3899946140,1042226977,2521517021,1639824860,227249030,260737669,3765465232,2084453954,1907733956,3429263018,2420656344,100860677,4160157185,470683154,3261161891,1781871967,2924959737,1773779408,394692241,2579611992,974986535,664706745,3655459128,3958962195,731420851,571543859,3530123707,2849626480,126783113,865375399,765172662,1008606754,361203602,3387549984,2278477385,2857719295,1344809080,2782912378,59542671,1503764984,160008576,437062935,1707065306,3622233649,2218934982,3496503480,2185314755,697932208,1512910199,504303377,2075177163,2824099068,1841019862,739644986],Qt=[2781242211,2230877308,2582542199,2381740923,234877682,3184946027,2984144751,1418839493,1348481072,50462977,2848876391,2102799147,434634494,1656084439,3863849899,2599188086,1167051466,2636087938,1082771913,2281340285,368048890,3954334041,3381544775,201060592,3963727277,1739838676,4250903202,3930435503,3206782108,4149453988,2531553906,1536934080,3262494647,484572669,2923271059,1783375398,1517041206,1098792767,49674231,1334037708,1550332980,4098991525,886171109,150598129,2481090929,1940642008,1398944049,1059722517,201851908,1385547719,1699095331,1587397571,674240536,2704774806,252314885,3039795866,151914247,908333586,2602270848,1038082786,651029483,1766729511,3447698098,2682942837,454166793,2652734339,1951935532,775166490,758520603,3000790638,4004797018,4217086112,4137964114,1299594043,1639438038,3464344499,2068982057,1054729187,1901997871,2534638724,4121318227,1757008337,0,750906861,1614815264,535035132,3363418545,3988151131,3201591914,1183697867,3647454910,1265776953,3734260298,3566750796,3903871064,1250283471,1807470800,717615087,3847203498,384695291,3313910595,3617213773,1432761139,2484176261,3481945413,283769337,100925954,2180939647,4037038160,1148730428,3123027871,3813386408,4087501137,4267549603,3229630528,2315620239,2906624658,3156319645,1215313976,82966005,3747855548,3245848246,1974459098,1665278241,807407632,451280895,251524083,1841287890,1283575245,337120268,891687699,801369324,3787349855,2721421207,3431482436,959321879,1469301956,4065699751,2197585534,1199193405,2898814052,3887750493,724703513,2514908019,2696962144,2551808385,3516813135,2141445340,1715741218,2119445034,2872807568,2198571144,3398190662,700968686,3547052216,1009259540,2041044702,3803995742,487983883,1991105499,1004265696,1449407026,1316239930,504629770,3683797321,168560134,1816667172,3837287516,1570751170,1857934291,4014189740,2797888098,2822345105,2754712981,936633572,2347923833,852879335,1133234376,1500395319,3084545389,2348912013,1689376213,3533459022,3762923945,3034082412,4205598294,133428468,634383082,2949277029,2398386810,3913789102,403703816,3580869306,2297460856,1867130149,1918643758,607656988,4049053350,3346248884,1368901318,600565992,2090982877,2632479860,557719327,3717614411,3697393085,2249034635,2232388234,2430627952,1115438654,3295786421,2865522278,3633334344,84280067,33027830,303828494,2747425121,1600795957,4188952407,3496589753,2434238086,1486471617,658119965,3106381470,953803233,334231800,3005978776,857870609,3151128937,1890179545,2298973838,2805175444,3056442267,574365214,2450884487,550103529,1233637070,4289353045,2018519080,2057691103,2399374476,4166623649,2148108681,387583245,3664101311,836232934,3330556482,3100665960,3280093505,2955516313,2002398509,287182607,3413881008,4238890068,3597515707,975967766],er=[1671808611,2089089148,2006576759,2072901243,4061003762,1807603307,1873927791,3310653893,810573872,16974337,1739181671,729634347,4263110654,3613570519,2883997099,1989864566,3393556426,2191335298,3376449993,2106063485,4195741690,1508618841,1204391495,4027317232,2917941677,3563566036,2734514082,2951366063,2629772188,2767672228,1922491506,3227229120,3082974647,4246528509,2477669779,644500518,911895606,1061256767,4144166391,3427763148,878471220,2784252325,3845444069,4043897329,1905517169,3631459288,827548209,356461077,67897348,3344078279,593839651,3277757891,405286936,2527147926,84871685,2595565466,118033927,305538066,2157648768,3795705826,3945188843,661212711,2999812018,1973414517,152769033,2208177539,745822252,439235610,455947803,1857215598,1525593178,2700827552,1391895634,994932283,3596728278,3016654259,695947817,3812548067,795958831,2224493444,1408607827,3513301457,0,3979133421,543178784,4229948412,2982705585,1542305371,1790891114,3410398667,3201918910,961245753,1256100938,1289001036,1491644504,3477767631,3496721360,4012557807,2867154858,4212583931,1137018435,1305975373,861234739,2241073541,1171229253,4178635257,33948674,2139225727,1357946960,1011120188,2679776671,2833468328,1374921297,2751356323,1086357568,2408187279,2460827538,2646352285,944271416,4110742005,3168756668,3066132406,3665145818,560153121,271589392,4279952895,4077846003,3530407890,3444343245,202643468,322250259,3962553324,1608629855,2543990167,1154254916,389623319,3294073796,2817676711,2122513534,1028094525,1689045092,1575467613,422261273,1939203699,1621147744,2174228865,1339137615,3699352540,577127458,712922154,2427141008,2290289544,1187679302,3995715566,3100863416,339486740,3732514782,1591917662,186455563,3681988059,3762019296,844522546,978220090,169743370,1239126601,101321734,611076132,1558493276,3260915650,3547250131,2901361580,1655096418,2443721105,2510565781,3828863972,2039214713,3878868455,3359869896,928607799,1840765549,2374762893,3580146133,1322425422,2850048425,1823791212,1459268694,4094161908,3928346602,1706019429,2056189050,2934523822,135794696,3134549946,2022240376,628050469,779246638,472135708,2800834470,3032970164,3327236038,3894660072,3715932637,1956440180,522272287,1272813131,3185336765,2340818315,2323976074,1888542832,1044544574,3049550261,1722469478,1222152264,50660867,4127324150,236067854,1638122081,895445557,1475980887,3117443513,2257655686,3243809217,489110045,2662934430,3778599393,4162055160,2561878936,288563729,1773916777,3648039385,2391345038,2493985684,2612407707,505560094,2274497927,3911240169,3460925390,1442818645,678973480,3749357023,2358182796,2717407649,2306869641,219617805,3218761151,3862026214,1120306242,1756942440,1103331905,2578459033,762796589,252780047,2966125488,1425844308,3151392187,372911126],tr=[1667474886,2088535288,2004326894,2071694838,4075949567,1802223062,1869591006,3318043793,808472672,16843522,1734846926,724270422,4278065639,3621216949,2880169549,1987484396,3402253711,2189597983,3385409673,2105378810,4210693615,1499065266,1195886990,4042263547,2913856577,3570689971,2728590687,2947541573,2627518243,2762274643,1920112356,3233831835,3082273397,4261223649,2475929149,640051788,909531756,1061110142,4160160501,3435941763,875846760,2779116625,3857003729,4059105529,1903268834,3638064043,825316194,353713962,67374088,3351728789,589522246,3284360861,404236336,2526454071,84217610,2593830191,117901582,303183396,2155911963,3806477791,3958056653,656894286,2998062463,1970642922,151591698,2206440989,741110872,437923380,454765878,1852748508,1515908788,2694904667,1381168804,993742198,3604373943,3014905469,690584402,3823320797,791638366,2223281939,1398011302,3520161977,0,3991743681,538992704,4244381667,2981218425,1532751286,1785380564,3419096717,3200178535,960056178,1246420628,1280103576,1482221744,3486468741,3503319995,4025428677,2863326543,4227536621,1128514950,1296947098,859002214,2240123921,1162203018,4193849577,33687044,2139062782,1347481760,1010582648,2678045221,2829640523,1364325282,2745433693,1077985408,2408548869,2459086143,2644360225,943212656,4126475505,3166494563,3065430391,3671750063,555836226,269496352,4294908645,4092792573,3537006015,3452783745,202118168,320025894,3974901699,1600119230,2543297077,1145359496,387397934,3301201811,2812801621,2122220284,1027426170,1684319432,1566435258,421079858,1936954854,1616945344,2172753945,1330631070,3705438115,572679748,707427924,2425400123,2290647819,1179044492,4008585671,3099120491,336870440,3739122087,1583276732,185277718,3688593069,3772791771,842159716,976899700,168435220,1229577106,101059084,606366792,1549591736,3267517855,3553849021,2897014595,1650632388,2442242105,2509612081,3840161747,2038008818,3890688725,3368567691,926374254,1835907034,2374863873,3587531953,1313788572,2846482505,1819063512,1448540844,4109633523,3941213647,1701162954,2054852340,2930698567,134748176,3132806511,2021165296,623210314,774795868,471606328,2795958615,3031746419,3334885783,3907527627,3722280097,1953799400,522133822,1263263126,3183336545,2341176845,2324333839,1886425312,1044267644,3048588401,1718004428,1212733584,50529542,4143317495,235803164,1633788866,892690282,1465383342,3115962473,2256965911,3250673817,488449850,2661202215,3789633753,4177007595,2560144171,286339874,1768537042,3654906025,2391705863,2492770099,2610673197,505291324,2273808917,3924369609,3469625735,1431699370,673740880,3755965093,2358021891,2711746649,2307489801,218961690,3217021541,3873845719,1111672452,1751693520,1094828930,2576986153,757954394,252645662,2964376443,1414855848,3149649517,370555436],rr=[1374988112,2118214995,437757123,975658646,1001089995,530400753,2902087851,1273168787,540080725,2910219766,2295101073,4110568485,1340463100,3307916247,641025152,3043140495,3736164937,632953703,1172967064,1576976609,3274667266,2169303058,2370213795,1809054150,59727847,361929877,3211623147,2505202138,3569255213,1484005843,1239443753,2395588676,1975683434,4102977912,2572697195,666464733,3202437046,4035489047,3374361702,2110667444,1675577880,3843699074,2538681184,1649639237,2976151520,3144396420,4269907996,4178062228,1883793496,2403728665,2497604743,1383856311,2876494627,1917518562,3810496343,1716890410,3001755655,800440835,2261089178,3543599269,807962610,599762354,33778362,3977675356,2328828971,2809771154,4077384432,1315562145,1708848333,101039829,3509871135,3299278474,875451293,2733856160,92987698,2767645557,193195065,1080094634,1584504582,3178106961,1042385657,2531067453,3711829422,1306967366,2438237621,1908694277,67556463,1615861247,429456164,3602770327,2302690252,1742315127,2968011453,126454664,3877198648,2043211483,2709260871,2084704233,4169408201,0,159417987,841739592,504459436,1817866830,4245618683,260388950,1034867998,908933415,168810852,1750902305,2606453969,607530554,202008497,2472011535,3035535058,463180190,2160117071,1641816226,1517767529,470948374,3801332234,3231722213,1008918595,303765277,235474187,4069246893,766945465,337553864,1475418501,2943682380,4003061179,2743034109,4144047775,1551037884,1147550661,1543208500,2336434550,3408119516,3069049960,3102011747,3610369226,1113818384,328671808,2227573024,2236228733,3535486456,2935566865,3341394285,496906059,3702665459,226906860,2009195472,733156972,2842737049,294930682,1206477858,2835123396,2700099354,1451044056,573804783,2269728455,3644379585,2362090238,2564033334,2801107407,2776292904,3669462566,1068351396,742039012,1350078989,1784663195,1417561698,4136440770,2430122216,775550814,2193862645,2673705150,1775276924,1876241833,3475313331,3366754619,270040487,3902563182,3678124923,3441850377,1851332852,3969562369,2203032232,3868552805,2868897406,566021896,4011190502,3135740889,1248802510,3936291284,699432150,832877231,708780849,3332740144,899835584,1951317047,4236429990,3767586992,866637845,4043610186,1106041591,2144161806,395441711,1984812685,1139781709,3433712980,3835036895,2664543715,1282050075,3240894392,1181045119,2640243204,25965917,4203181171,4211818798,3009879386,2463879762,3910161971,1842759443,2597806476,933301370,1509430414,3943906441,3467192302,3076639029,3776767469,2051518780,2631065433,1441952575,404016761,1942435775,1408749034,1610459739,3745345300,2017778566,3400528769,3110650942,941896748,3265478751,371049330,3168937228,675039627,4279080257,967311729,135050206,3635733660,1683407248,2076935265,3576870512,1215061108,3501741890],ir=[1347548327,1400783205,3273267108,2520393566,3409685355,4045380933,2880240216,2471224067,1428173050,4138563181,2441661558,636813900,4233094615,3620022987,2149987652,2411029155,1239331162,1730525723,2554718734,3781033664,46346101,310463728,2743944855,3328955385,3875770207,2501218972,3955191162,3667219033,768917123,3545789473,692707433,1150208456,1786102409,2029293177,1805211710,3710368113,3065962831,401639597,1724457132,3028143674,409198410,2196052529,1620529459,1164071807,3769721975,2226875310,486441376,2499348523,1483753576,428819965,2274680428,3075636216,598438867,3799141122,1474502543,711349675,129166120,53458370,2592523643,2782082824,4063242375,2988687269,3120694122,1559041666,730517276,2460449204,4042459122,2706270690,3446004468,3573941694,533804130,2328143614,2637442643,2695033685,839224033,1973745387,957055980,2856345839,106852767,1371368976,4181598602,1033297158,2933734917,1179510461,3046200461,91341917,1862534868,4284502037,605657339,2547432937,3431546947,2003294622,3182487618,2282195339,954669403,3682191598,1201765386,3917234703,3388507166,0,2198438022,1211247597,2887651696,1315723890,4227665663,1443857720,507358933,657861945,1678381017,560487590,3516619604,975451694,2970356327,261314535,3535072918,2652609425,1333838021,2724322336,1767536459,370938394,182621114,3854606378,1128014560,487725847,185469197,2918353863,3106780840,3356761769,2237133081,1286567175,3152976349,4255350624,2683765030,3160175349,3309594171,878443390,1988838185,3704300486,1756818940,1673061617,3403100636,272786309,1075025698,545572369,2105887268,4174560061,296679730,1841768865,1260232239,4091327024,3960309330,3497509347,1814803222,2578018489,4195456072,575138148,3299409036,446754879,3629546796,4011996048,3347532110,3252238545,4270639778,915985419,3483825537,681933534,651868046,2755636671,3828103837,223377554,2607439820,1649704518,3270937875,3901806776,1580087799,4118987695,3198115200,2087309459,2842678573,3016697106,1003007129,2802849917,1860738147,2077965243,164439672,4100872472,32283319,2827177882,1709610350,2125135846,136428751,3874428392,3652904859,3460984630,3572145929,3593056380,2939266226,824852259,818324884,3224740454,930369212,2801566410,2967507152,355706840,1257309336,4148292826,243256656,790073846,2373340630,1296297904,1422699085,3756299780,3818836405,457992840,3099667487,2135319889,77422314,1560382517,1945798516,788204353,1521706781,1385356242,870912086,325965383,2358957921,2050466060,2388260884,2313884476,4006521127,901210569,3990953189,1014646705,1503449823,1062597235,2031621326,3212035895,3931371469,1533017514,350174575,2256028891,2177544179,1052338372,741876788,1606591296,1914052035,213705253,2334669897,1107234197,1899603969,3725069491,2631447780,2422494913,1635502980,1893020342,1950903388,1120974935],nr=[2807058932,1699970625,2764249623,1586903591,1808481195,1173430173,1487645946,59984867,4199882800,1844882806,1989249228,1277555970,3623636965,3419915562,1149249077,2744104290,1514790577,459744698,244860394,3235995134,1963115311,4027744588,2544078150,4190530515,1608975247,2627016082,2062270317,1507497298,2200818878,567498868,1764313568,3359936201,2305455554,2037970062,1047239e3,1910319033,1337376481,2904027272,2892417312,984907214,1243112415,830661914,861968209,2135253587,2011214180,2927934315,2686254721,731183368,1750626376,4246310725,1820824798,4172763771,3542330227,48394827,2404901663,2871682645,671593195,3254988725,2073724613,145085239,2280796200,2779915199,1790575107,2187128086,472615631,3029510009,4075877127,3802222185,4107101658,3201631749,1646252340,4270507174,1402811438,1436590835,3778151818,3950355702,3963161475,4020912224,2667994737,273792366,2331590177,104699613,95345982,3175501286,2377486676,1560637892,3564045318,369057872,4213447064,3919042237,1137477952,2658625497,1119727848,2340947849,1530455833,4007360968,172466556,266959938,516552836,0,2256734592,3980931627,1890328081,1917742170,4294704398,945164165,3575528878,958871085,3647212047,2787207260,1423022939,775562294,1739656202,3876557655,2530391278,2443058075,3310321856,547512796,1265195639,437656594,3121275539,719700128,3762502690,387781147,218828297,3350065803,2830708150,2848461854,428169201,122466165,3720081049,1627235199,648017665,4122762354,1002783846,2117360635,695634755,3336358691,4234721005,4049844452,3704280881,2232435299,574624663,287343814,612205898,1039717051,840019705,2708326185,793451934,821288114,1391201670,3822090177,376187827,3113855344,1224348052,1679968233,2361698556,1058709744,752375421,2431590963,1321699145,3519142200,2734591178,188127444,2177869557,3727205754,2384911031,3215212461,2648976442,2450346104,3432737375,1180849278,331544205,3102249176,4150144569,2952102595,2159976285,2474404304,766078933,313773861,2570832044,2108100632,1668212892,3145456443,2013908262,418672217,3070356634,2594734927,1852171925,3867060991,3473416636,3907448597,2614737639,919489135,164948639,2094410160,2997825956,590424639,2486224549,1723872674,3157750862,3399941250,3501252752,3625268135,2555048196,3673637356,1343127501,4130281361,3599595085,2957853679,1297403050,81781910,3051593425,2283490410,532201772,1367295589,3926170974,895287692,1953757831,1093597963,492483431,3528626907,1446242576,1192455638,1636604631,209336225,344873464,1015671571,669961897,3375740769,3857572124,2973530695,3747192018,1933530610,3464042516,935293895,3454686199,2858115069,1863638845,3683022916,4085369519,3292445032,875313188,1080017571,3279033885,621591778,1233856572,2504130317,24197544,3017672716,3835484340,3247465558,2220981195,3060847922,1551124588,1463996600],sr=[4104605777,1097159550,396673818,660510266,2875968315,2638606623,4200115116,3808662347,821712160,1986918061,3430322568,38544885,3856137295,718002117,893681702,1654886325,2975484382,3122358053,3926825029,4274053469,796197571,1290801793,1184342925,3556361835,2405426947,2459735317,1836772287,1381620373,3196267988,1948373848,3764988233,3385345166,3263785589,2390325492,1480485785,3111247143,3780097726,2293045232,548169417,3459953789,3746175075,439452389,1362321559,1400849762,1685577905,1806599355,2174754046,137073913,1214797936,1174215055,3731654548,2079897426,1943217067,1258480242,529487843,1437280870,3945269170,3049390895,3313212038,923313619,679998e3,3215307299,57326082,377642221,3474729866,2041877159,133361907,1776460110,3673476453,96392454,878845905,2801699524,777231668,4082475170,2330014213,4142626212,2213296395,1626319424,1906247262,1846563261,562755902,3708173718,1040559837,3871163981,1418573201,3294430577,114585348,1343618912,2566595609,3186202582,1078185097,3651041127,3896688048,2307622919,425408743,3371096953,2081048481,1108339068,2216610296,0,2156299017,736970802,292596766,1517440620,251657213,2235061775,2933202493,758720310,265905162,1554391400,1532285339,908999204,174567692,1474760595,4002861748,2610011675,3234156416,3693126241,2001430874,303699484,2478443234,2687165888,585122620,454499602,151849742,2345119218,3064510765,514443284,4044981591,1963412655,2581445614,2137062819,19308535,1928707164,1715193156,4219352155,1126790795,600235211,3992742070,3841024952,836553431,1669664834,2535604243,3323011204,1243905413,3141400786,4180808110,698445255,2653899549,2989552604,2253581325,3252932727,3004591147,1891211689,2487810577,3915653703,4237083816,4030667424,2100090966,865136418,1229899655,953270745,3399679628,3557504664,4118925222,2061379749,3079546586,2915017791,983426092,2022837584,1607244650,2118541908,2366882550,3635996816,972512814,3283088770,1568718495,3499326569,3576539503,621982671,2895723464,410887952,2623762152,1002142683,645401037,1494807662,2595684844,1335535747,2507040230,4293295786,3167684641,367585007,3885750714,1865862730,2668221674,2960971305,2763173681,1059270954,2777952454,2724642869,1320957812,2194319100,2429595872,2815956275,77089521,3973773121,3444575871,2448830231,1305906550,4021308739,2857194700,2516901860,3518358430,1787304780,740276417,1699839814,1592394909,2352307457,2272556026,188821243,1729977011,3687994002,274084841,3594982253,3613494426,2701949495,4162096729,322734571,2837966542,1640576439,484830689,1202797690,3537852828,4067639125,349075736,3342319475,4157467219,4255800159,1030690015,1155237496,2951971274,1757691577,607398968,2738905026,499347990,3794078908,1011452712,227885567,2818666809,213114376,3034881240,1455525988,3414450555,850817237,1817998408,3092726480],or=[0,235474187,470948374,303765277,941896748,908933415,607530554,708780849,1883793496,2118214995,1817866830,1649639237,1215061108,1181045119,1417561698,1517767529,3767586992,4003061179,4236429990,4069246893,3635733660,3602770327,3299278474,3400528769,2430122216,2664543715,2362090238,2193862645,2835123396,2801107407,3035535058,3135740889,3678124923,3576870512,3341394285,3374361702,3810496343,3977675356,4279080257,4043610186,2876494627,2776292904,3076639029,3110650942,2472011535,2640243204,2403728665,2169303058,1001089995,899835584,666464733,699432150,59727847,226906860,530400753,294930682,1273168787,1172967064,1475418501,1509430414,1942435775,2110667444,1876241833,1641816226,2910219766,2743034109,2976151520,3211623147,2505202138,2606453969,2302690252,2269728455,3711829422,3543599269,3240894392,3475313331,3843699074,3943906441,4178062228,4144047775,1306967366,1139781709,1374988112,1610459739,1975683434,2076935265,1775276924,1742315127,1034867998,866637845,566021896,800440835,92987698,193195065,429456164,395441711,1984812685,2017778566,1784663195,1683407248,1315562145,1080094634,1383856311,1551037884,101039829,135050206,437757123,337553864,1042385657,807962610,573804783,742039012,2531067453,2564033334,2328828971,2227573024,2935566865,2700099354,3001755655,3168937228,3868552805,3902563182,4203181171,4102977912,3736164937,3501741890,3265478751,3433712980,1106041591,1340463100,1576976609,1408749034,2043211483,2009195472,1708848333,1809054150,832877231,1068351396,766945465,599762354,159417987,126454664,361929877,463180190,2709260871,2943682380,3178106961,3009879386,2572697195,2538681184,2236228733,2336434550,3509871135,3745345300,3441850377,3274667266,3910161971,3877198648,4110568485,4211818798,2597806476,2497604743,2261089178,2295101073,2733856160,2902087851,3202437046,2968011453,3936291284,3835036895,4136440770,4169408201,3535486456,3702665459,3467192302,3231722213,2051518780,1951317047,1716890410,1750902305,1113818384,1282050075,1584504582,1350078989,168810852,67556463,371049330,404016761,841739592,1008918595,775550814,540080725,3969562369,3801332234,4035489047,4269907996,3569255213,3669462566,3366754619,3332740144,2631065433,2463879762,2160117071,2395588676,2767645557,2868897406,3102011747,3069049960,202008497,33778362,270040487,504459436,875451293,975658646,675039627,641025152,2084704233,1917518562,1615861247,1851332852,1147550661,1248802510,1484005843,1451044056,933301370,967311729,733156972,632953703,260388950,25965917,328671808,496906059,1206477858,1239443753,1543208500,1441952575,2144161806,1908694277,1675577880,1842759443,3610369226,3644379585,3408119516,3307916247,4011190502,3776767469,4077384432,4245618683,2809771154,2842737049,3144396420,3043140495,2673705150,2438237621,2203032232,2370213795],ar=[0,185469197,370938394,487725847,741876788,657861945,975451694,824852259,1483753576,1400783205,1315723890,1164071807,1950903388,2135319889,1649704518,1767536459,2967507152,3152976349,2801566410,2918353863,2631447780,2547432937,2328143614,2177544179,3901806776,3818836405,4270639778,4118987695,3299409036,3483825537,3535072918,3652904859,2077965243,1893020342,1841768865,1724457132,1474502543,1559041666,1107234197,1257309336,598438867,681933534,901210569,1052338372,261314535,77422314,428819965,310463728,3409685355,3224740454,3710368113,3593056380,3875770207,3960309330,4045380933,4195456072,2471224067,2554718734,2237133081,2388260884,3212035895,3028143674,2842678573,2724322336,4138563181,4255350624,3769721975,3955191162,3667219033,3516619604,3431546947,3347532110,2933734917,2782082824,3099667487,3016697106,2196052529,2313884476,2499348523,2683765030,1179510461,1296297904,1347548327,1533017514,1786102409,1635502980,2087309459,2003294622,507358933,355706840,136428751,53458370,839224033,957055980,605657339,790073846,2373340630,2256028891,2607439820,2422494913,2706270690,2856345839,3075636216,3160175349,3573941694,3725069491,3273267108,3356761769,4181598602,4063242375,4011996048,3828103837,1033297158,915985419,730517276,545572369,296679730,446754879,129166120,213705253,1709610350,1860738147,1945798516,2029293177,1239331162,1120974935,1606591296,1422699085,4148292826,4233094615,3781033664,3931371469,3682191598,3497509347,3446004468,3328955385,2939266226,2755636671,3106780840,2988687269,2198438022,2282195339,2501218972,2652609425,1201765386,1286567175,1371368976,1521706781,1805211710,1620529459,2105887268,1988838185,533804130,350174575,164439672,46346101,870912086,954669403,636813900,788204353,2358957921,2274680428,2592523643,2441661558,2695033685,2880240216,3065962831,3182487618,3572145929,3756299780,3270937875,3388507166,4174560061,4091327024,4006521127,3854606378,1014646705,930369212,711349675,560487590,272786309,457992840,106852767,223377554,1678381017,1862534868,1914052035,2031621326,1211247597,1128014560,1580087799,1428173050,32283319,182621114,401639597,486441376,768917123,651868046,1003007129,818324884,1503449823,1385356242,1333838021,1150208456,1973745387,2125135846,1673061617,1756818940,2970356327,3120694122,2802849917,2887651696,2637442643,2520393566,2334669897,2149987652,3917234703,3799141122,4284502037,4100872472,3309594171,3460984630,3545789473,3629546796,2050466060,1899603969,1814803222,1730525723,1443857720,1560382517,1075025698,1260232239,575138148,692707433,878443390,1062597235,243256656,91341917,409198410,325965383,3403100636,3252238545,3704300486,3620022987,3874428392,3990953189,4042459122,4227665663,2460449204,2578018489,2226875310,2411029155,3198115200,3046200461,2827177882,2743944855],lr=[0,218828297,437656594,387781147,875313188,958871085,775562294,590424639,1750626376,1699970625,1917742170,2135253587,1551124588,1367295589,1180849278,1265195639,3501252752,3720081049,3399941250,3350065803,3835484340,3919042237,4270507174,4085369519,3102249176,3051593425,2734591178,2952102595,2361698556,2177869557,2530391278,2614737639,3145456443,3060847922,2708326185,2892417312,2404901663,2187128086,2504130317,2555048196,3542330227,3727205754,3375740769,3292445032,3876557655,3926170974,4246310725,4027744588,1808481195,1723872674,1910319033,2094410160,1608975247,1391201670,1173430173,1224348052,59984867,244860394,428169201,344873464,935293895,984907214,766078933,547512796,1844882806,1627235199,2011214180,2062270317,1507497298,1423022939,1137477952,1321699145,95345982,145085239,532201772,313773861,830661914,1015671571,731183368,648017665,3175501286,2957853679,2807058932,2858115069,2305455554,2220981195,2474404304,2658625497,3575528878,3625268135,3473416636,3254988725,3778151818,3963161475,4213447064,4130281361,3599595085,3683022916,3432737375,3247465558,3802222185,4020912224,4172763771,4122762354,3201631749,3017672716,2764249623,2848461854,2331590177,2280796200,2431590963,2648976442,104699613,188127444,472615631,287343814,840019705,1058709744,671593195,621591778,1852171925,1668212892,1953757831,2037970062,1514790577,1463996600,1080017571,1297403050,3673637356,3623636965,3235995134,3454686199,4007360968,3822090177,4107101658,4190530515,2997825956,3215212461,2830708150,2779915199,2256734592,2340947849,2627016082,2443058075,172466556,122466165,273792366,492483431,1047239e3,861968209,612205898,695634755,1646252340,1863638845,2013908262,1963115311,1446242576,1530455833,1277555970,1093597963,1636604631,1820824798,2073724613,1989249228,1436590835,1487645946,1337376481,1119727848,164948639,81781910,331544205,516552836,1039717051,821288114,669961897,719700128,2973530695,3157750862,2871682645,2787207260,2232435299,2283490410,2667994737,2450346104,3647212047,3564045318,3279033885,3464042516,3980931627,3762502690,4150144569,4199882800,3070356634,3121275539,2904027272,2686254721,2200818878,2384911031,2570832044,2486224549,3747192018,3528626907,3310321856,3359936201,3950355702,3867060991,4049844452,4234721005,1739656202,1790575107,2108100632,1890328081,1402811438,1586903591,1233856572,1149249077,266959938,48394827,369057872,418672217,1002783846,919489135,567498868,752375421,209336225,24197544,376187827,459744698,945164165,895287692,574624663,793451934,1679968233,1764313568,2117360635,1933530610,1343127501,1560637892,1243112415,1192455638,3704280881,3519142200,3336358691,3419915562,3907448597,3857572124,4075877127,4294704398,3029510009,3113855344,2927934315,2744104290,2159976285,2377486676,2594734927,2544078150],dr=[0,151849742,303699484,454499602,607398968,758720310,908999204,1059270954,1214797936,1097159550,1517440620,1400849762,1817998408,1699839814,2118541908,2001430874,2429595872,2581445614,2194319100,2345119218,3034881240,3186202582,2801699524,2951971274,3635996816,3518358430,3399679628,3283088770,4237083816,4118925222,4002861748,3885750714,1002142683,850817237,698445255,548169417,529487843,377642221,227885567,77089521,1943217067,2061379749,1640576439,1757691577,1474760595,1592394909,1174215055,1290801793,2875968315,2724642869,3111247143,2960971305,2405426947,2253581325,2638606623,2487810577,3808662347,3926825029,4044981591,4162096729,3342319475,3459953789,3576539503,3693126241,1986918061,2137062819,1685577905,1836772287,1381620373,1532285339,1078185097,1229899655,1040559837,923313619,740276417,621982671,439452389,322734571,137073913,19308535,3871163981,4021308739,4104605777,4255800159,3263785589,3414450555,3499326569,3651041127,2933202493,2815956275,3167684641,3049390895,2330014213,2213296395,2566595609,2448830231,1305906550,1155237496,1607244650,1455525988,1776460110,1626319424,2079897426,1928707164,96392454,213114376,396673818,514443284,562755902,679998e3,865136418,983426092,3708173718,3557504664,3474729866,3323011204,4180808110,4030667424,3945269170,3794078908,2507040230,2623762152,2272556026,2390325492,2975484382,3092726480,2738905026,2857194700,3973773121,3856137295,4274053469,4157467219,3371096953,3252932727,3673476453,3556361835,2763173681,2915017791,3064510765,3215307299,2156299017,2307622919,2459735317,2610011675,2081048481,1963412655,1846563261,1729977011,1480485785,1362321559,1243905413,1126790795,878845905,1030690015,645401037,796197571,274084841,425408743,38544885,188821243,3613494426,3731654548,3313212038,3430322568,4082475170,4200115116,3780097726,3896688048,2668221674,2516901860,2366882550,2216610296,3141400786,2989552604,2837966542,2687165888,1202797690,1320957812,1437280870,1554391400,1669664834,1787304780,1906247262,2022837584,265905162,114585348,499347990,349075736,736970802,585122620,972512814,821712160,2595684844,2478443234,2293045232,2174754046,3196267988,3079546586,2895723464,2777952454,3537852828,3687994002,3234156416,3385345166,4142626212,4293295786,3841024952,3992742070,174567692,57326082,410887952,292596766,777231668,660510266,1011452712,893681702,1108339068,1258480242,1343618912,1494807662,1715193156,1865862730,1948373848,2100090966,2701949495,2818666809,3004591147,3122358053,2235061775,2352307457,2535604243,2653899549,3915653703,3764988233,4219352155,4067639125,3444575871,3294430577,3746175075,3594982253,836553431,953270745,600235211,718002117,367585007,484830689,133361907,251657213,2041877159,1891211689,1806599355,1654886325,1568718495,1418573201,1335535747,1184342925];function ur(e){for(var t=[],r=0;r<e.length;r+=4)t.push(e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3]);return t}var cr=function(e){if(!(this instanceof cr))throw Error("AES must be instanitated with `new`");Object.defineProperty(this,"key",{value:Gt(e,!0)}),this._prepare()};cr.prototype._prepare=function(){var e=qt[this.key.length];if(null==e)throw new Error("invalid key size (must be 16, 24 or 32 bytes)");this._Ke=[],this._Kd=[];for(var t=0;t<=e;t++)this._Ke.push([0,0,0,0]),this._Kd.push([0,0,0,0]);var r,i=4*(e+1),n=this.key.length/4,s=ur(this.key);for(t=0;t<n;t++)r=t>>2,this._Ke[r][t%4]=s[t],this._Kd[e-r][t%4]=s[t];for(var o,a=0,l=n;l<i;){if(o=s[n-1],s[0]^=Xt[o>>16&255]<<24^Xt[o>>8&255]<<16^Xt[255&o]<<8^Xt[o>>24&255]^Kt[a]<<24,a+=1,8!=n)for(t=1;t<n;t++)s[t]^=s[t-1];else{for(t=1;t<n/2;t++)s[t]^=s[t-1];o=s[n/2-1],s[n/2]^=Xt[255&o]^Xt[o>>8&255]<<8^Xt[o>>16&255]<<16^Xt[o>>24&255]<<24;for(t=n/2+1;t<n;t++)s[t]^=s[t-1]}for(t=0;t<n&&l<i;)d=l>>2,u=l%4,this._Ke[d][u]=s[t],this._Kd[e-d][u]=s[t++],l++}for(var d=1;d<e;d++)for(var u=0;u<4;u++)o=this._Kd[d][u],this._Kd[d][u]=or[o>>24&255]^ar[o>>16&255]^lr[o>>8&255]^dr[255&o]},cr.prototype.encrypt=function(e){if(16!=e.length)throw new Error("invalid plaintext size (must be 16 bytes)");for(var t=this._Ke.length-1,r=[0,0,0,0],i=ur(e),n=0;n<4;n++)i[n]^=this._Ke[0][n];for(var s=1;s<t;s++){for(n=0;n<4;n++)r[n]=Jt[i[n]>>24&255]^Qt[i[(n+1)%4]>>16&255]^er[i[(n+2)%4]>>8&255]^tr[255&i[(n+3)%4]]^this._Ke[s][n];i=r.slice()}var o,a=Ht(16);for(n=0;n<4;n++)o=this._Ke[t][n],a[4*n]=255&(Xt[i[n]>>24&255]^o>>24),a[4*n+1]=255&(Xt[i[(n+1)%4]>>16&255]^o>>16),a[4*n+2]=255&(Xt[i[(n+2)%4]>>8&255]^o>>8),a[4*n+3]=255&(Xt[255&i[(n+3)%4]]^o);return a},cr.prototype.decrypt=function(e){if(16!=e.length)throw new Error("invalid ciphertext size (must be 16 bytes)");for(var t=this._Kd.length-1,r=[0,0,0,0],i=ur(e),n=0;n<4;n++)i[n]^=this._Kd[0][n];for(var s=1;s<t;s++){for(n=0;n<4;n++)r[n]=rr[i[n]>>24&255]^ir[i[(n+3)%4]>>16&255]^nr[i[(n+2)%4]>>8&255]^sr[255&i[(n+1)%4]]^this._Kd[s][n];i=r.slice()}var o,a=Ht(16);for(n=0;n<4;n++)o=this._Kd[t][n],a[4*n]=255&(Zt[i[n]>>24&255]^o>>24),a[4*n+1]=255&(Zt[i[(n+3)%4]>>16&255]^o>>16),a[4*n+2]=255&(Zt[i[(n+2)%4]>>8&255]^o>>8),a[4*n+3]=255&(Zt[255&i[(n+1)%4]]^o);return a};var fr=function(e){if(!(this instanceof fr))throw Error("AES must be instanitated with `new`");this.description="Electronic Code Block",this.name="ecb",this._aes=new cr(e)};fr.prototype.encrypt=function(e){if((e=Gt(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=Ht(e.length),r=Ht(16),i=0;i<e.length;i+=16)Vt(e,r,0,i,i+16),Vt(r=this._aes.encrypt(r),t,i);return t},fr.prototype.decrypt=function(e){if((e=Gt(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=Ht(e.length),r=Ht(16),i=0;i<e.length;i+=16)Vt(e,r,0,i,i+16),Vt(r=this._aes.decrypt(r),t,i);return t};var hr=function(e,t){if(!(this instanceof hr))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Block Chaining",this.name="cbc",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=Ht(16);this._lastCipherblock=Gt(t,!0),this._aes=new cr(e)};hr.prototype.encrypt=function(e){if((e=Gt(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=Ht(e.length),r=Ht(16),i=0;i<e.length;i+=16){Vt(e,r,0,i,i+16);for(var n=0;n<16;n++)r[n]^=this._lastCipherblock[n];this._lastCipherblock=this._aes.encrypt(r),Vt(this._lastCipherblock,t,i)}return t},hr.prototype.decrypt=function(e){if((e=Gt(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=Ht(e.length),r=Ht(16),i=0;i<e.length;i+=16){Vt(e,r,0,i,i+16),r=this._aes.decrypt(r);for(var n=0;n<16;n++)t[i+n]=r[n]^this._lastCipherblock[n];Vt(e,this._lastCipherblock,0,i,i+16)}return t};var pr=function(e,t,r){if(!(this instanceof pr))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Feedback",this.name="cfb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 size)")}else t=Ht(16);r||(r=1),this.segmentSize=r,this._shiftRegister=Gt(t,!0),this._aes=new cr(e)};pr.prototype.encrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid plaintext size (must be segmentSize bytes)");for(var t,r=Gt(e,!0),i=0;i<r.length;i+=this.segmentSize){t=this._aes.encrypt(this._shiftRegister);for(var n=0;n<this.segmentSize;n++)r[i+n]^=t[n];Vt(this._shiftRegister,this._shiftRegister,0,this.segmentSize),Vt(r,this._shiftRegister,16-this.segmentSize,i,i+this.segmentSize)}return r},pr.prototype.decrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid ciphertext size (must be segmentSize bytes)");for(var t,r=Gt(e,!0),i=0;i<r.length;i+=this.segmentSize){t=this._aes.encrypt(this._shiftRegister);for(var n=0;n<this.segmentSize;n++)r[i+n]^=t[n];Vt(this._shiftRegister,this._shiftRegister,0,this.segmentSize),Vt(e,this._shiftRegister,16-this.segmentSize,i,i+this.segmentSize)}return r};var mr=function(e,t){if(!(this instanceof mr))throw Error("AES must be instanitated with `new`");if(this.description="Output Feedback",this.name="ofb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=Ht(16);this._lastPrecipher=Gt(t,!0),this._lastPrecipherIndex=16,this._aes=new cr(e)};mr.prototype.encrypt=function(e){for(var t=Gt(e,!0),r=0;r<t.length;r++)16===this._lastPrecipherIndex&&(this._lastPrecipher=this._aes.encrypt(this._lastPrecipher),this._lastPrecipherIndex=0),t[r]^=this._lastPrecipher[this._lastPrecipherIndex++];return t},mr.prototype.decrypt=mr.prototype.encrypt;var _r=function(e){if(!(this instanceof _r))throw Error("Counter must be instanitated with `new`");0===e||e||(e=1),"number"==typeof e?(this._counter=Ht(16),this.setValue(e)):this.setBytes(e)};_r.prototype.setValue=function(e){if("number"!=typeof e||parseInt(e)!=e)throw new Error("invalid counter value (must be an integer)");if(e>Number.MAX_SAFE_INTEGER)throw new Error("integer value out of safe range");for(var t=15;t>=0;--t)this._counter[t]=e%256,e=parseInt(e/256)},_r.prototype.setBytes=function(e){if(16!=(e=Gt(e,!0)).length)throw new Error("invalid counter bytes size (must be 16 bytes)");this._counter=e},_r.prototype.increment=function(){for(var e=15;e>=0;e--){if(255!==this._counter[e]){this._counter[e]++;break}this._counter[e]=0}};var yr=function(e,t){if(!(this instanceof yr))throw Error("AES must be instanitated with `new`");this.description="Counter",this.name="ctr",t instanceof _r||(t=new _r(t)),this._counter=t,this._remainingCounter=null,this._remainingCounterIndex=16,this._aes=new cr(e)};yr.prototype.encrypt=function(e){for(var t=Gt(e,!0),r=0;r<t.length;r++)16===this._remainingCounterIndex&&(this._remainingCounter=this._aes.encrypt(this._counter._counter),this._remainingCounterIndex=0,this._counter.increment()),t[r]^=this._remainingCounter[this._remainingCounterIndex++];return t},yr.prototype.decrypt=yr.prototype.encrypt;const gr={AES:cr,Counter:_r,ModeOfOperation:{ecb:fr,cbc:hr,cfb:pr,ofb:mr,ctr:yr},utils:{hex:Yt,utf8:Wt},padding:{pkcs7:{pad:function(e){var t=16-(e=Gt(e,!0)).length%16,r=Ht(e.length+t);Vt(e,r);for(var i=e.length;i<r.length;i++)r[i]=t;return r},strip:function(e){if((e=Gt(e,!0)).length<16)throw new Error("PKCS#7 invalid length");var t=e[e.length-1];if(t>16)throw new Error("PKCS#7 padding byte out of range");for(var r=e.length-t,i=0;i<t;i++)if(e[r+i]!==t)throw new Error("PKCS#7 invalid padding byte");var n=Ht(r);return Vt(e,n,0,0,r),n}}},_arrayTest:{coerceArray:Gt,createArray:Ht,copyArray:Vt}};var vr=Ve((function(e,t){var r,i,n,s=(r=new Date,i=4,n={setLogLevel:function(e){i=e==this.debug?1:e==this.info?2:e==this.warn?3:(this.error,4)},debug:function(e,t){void 0===console.debug&&(console.debug=console.log),1>=i&&console.debug("["+s.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)},log:function(e,t){this.debug(e.msg)},info:function(e,t){2>=i&&console.info("["+s.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)},warn:function(e,t){3>=i&&console.warn("["+s.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)},error:function(e,t){4>=i&&console.error("["+s.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)}},n);s.getDurationString=function(e,t){var r;function i(e,t){for(var r=(""+e).split(".");r[0].length<t;)r[0]="0"+r[0];return r.join(".")}e<0?(r=!0,e=-e):r=!1;var n=e/(t||1),s=Math.floor(n/3600);n-=3600*s;var o=Math.floor(n/60),a=1e3*(n-=60*o);return a-=1e3*(n=Math.floor(n)),a=Math.floor(a),(r?"-":"")+s+":"+i(o,2)+":"+i(n,2)+"."+i(a,3)},s.printRanges=function(e){var t=e.length;if(t>0){for(var r="",i=0;i<t;i++)i>0&&(r+=","),r+="["+s.getDurationString(e.start(i))+","+s.getDurationString(e.end(i))+"]";return r}return"(empty)"},t.Log=s;var o=function(e){if(!(e instanceof ArrayBuffer))throw"Needs an array buffer";this.buffer=e,this.dataview=new DataView(e),this.position=0};o.prototype.getPosition=function(){return this.position},o.prototype.getEndPosition=function(){return this.buffer.byteLength},o.prototype.getLength=function(){return this.buffer.byteLength},o.prototype.seek=function(e){var t=Math.max(0,Math.min(this.buffer.byteLength,e));return this.position=isNaN(t)||!isFinite(t)?0:t,!0},o.prototype.isEos=function(){return this.getPosition()>=this.getEndPosition()},o.prototype.readAnyInt=function(e,t){var r=0;if(this.position+e<=this.buffer.byteLength){switch(e){case 1:r=t?this.dataview.getInt8(this.position):this.dataview.getUint8(this.position);break;case 2:r=t?this.dataview.getInt16(this.position):this.dataview.getUint16(this.position);break;case 3:if(t)throw"No method for reading signed 24 bits values";r=this.dataview.getUint8(this.position)<<16,r|=this.dataview.getUint8(this.position+1)<<8,r|=this.dataview.getUint8(this.position+2);break;case 4:r=t?this.dataview.getInt32(this.position):this.dataview.getUint32(this.position);break;case 8:if(t)throw"No method for reading signed 64 bits values";r=this.dataview.getUint32(this.position)<<32,r|=this.dataview.getUint32(this.position+4);break;default:throw"readInt method not implemented for size: "+e}return this.position+=e,r}throw"Not enough bytes in buffer"},o.prototype.readUint8=function(){return this.readAnyInt(1,!1)},o.prototype.readUint16=function(){return this.readAnyInt(2,!1)},o.prototype.readUint24=function(){return this.readAnyInt(3,!1)},o.prototype.readUint32=function(){return this.readAnyInt(4,!1)},o.prototype.readUint64=function(){return this.readAnyInt(8,!1)},o.prototype.readString=function(e){if(this.position+e<=this.buffer.byteLength){for(var t="",r=0;r<e;r++)t+=String.fromCharCode(this.readUint8());return t}throw"Not enough bytes in buffer"},o.prototype.readCString=function(){for(var e=[];;){var t=this.readUint8();if(0===t)break;e.push(t)}return String.fromCharCode.apply(null,e)},o.prototype.readInt8=function(){return this.readAnyInt(1,!0)},o.prototype.readInt16=function(){return this.readAnyInt(2,!0)},o.prototype.readInt32=function(){return this.readAnyInt(4,!0)},o.prototype.readInt64=function(){return this.readAnyInt(8,!1)},o.prototype.readUint8Array=function(e){for(var t=new Uint8Array(e),r=0;r<e;r++)t[r]=this.readUint8();return t},o.prototype.readInt16Array=function(e){for(var t=new Int16Array(e),r=0;r<e;r++)t[r]=this.readInt16();return t},o.prototype.readUint16Array=function(e){for(var t=new Int16Array(e),r=0;r<e;r++)t[r]=this.readUint16();return t},o.prototype.readUint32Array=function(e){for(var t=new Uint32Array(e),r=0;r<e;r++)t[r]=this.readUint32();return t},o.prototype.readInt32Array=function(e){for(var t=new Int32Array(e),r=0;r<e;r++)t[r]=this.readInt32();return t},t.MP4BoxStream=o;var a=function(e,t,r){this._byteOffset=t||0,e instanceof ArrayBuffer?this.buffer=e:"object"==typeof e?(this.dataView=e,t&&(this._byteOffset+=t)):this.buffer=new ArrayBuffer(e||0),this.position=0,this.endianness=null==r?a.LITTLE_ENDIAN:r};a.prototype={},a.prototype.getPosition=function(){return this.position},a.prototype._realloc=function(e){if(this._dynamicSize){var t=this._byteOffset+this.position+e,r=this._buffer.byteLength;if(t<=r)t>this._byteLength&&(this._byteLength=t);else{for(r<1&&(r=1);t>r;)r*=2;var i=new ArrayBuffer(r),n=new Uint8Array(this._buffer);new Uint8Array(i,0,n.length).set(n),this.buffer=i,this._byteLength=t}}},a.prototype._trimAlloc=function(){if(this._byteLength!=this._buffer.byteLength){var e=new ArrayBuffer(this._byteLength),t=new Uint8Array(e),r=new Uint8Array(this._buffer,0,t.length);t.set(r),this.buffer=e}},a.BIG_ENDIAN=!1,a.LITTLE_ENDIAN=!0,a.prototype._byteLength=0,Object.defineProperty(a.prototype,"byteLength",{get:function(){return this._byteLength-this._byteOffset}}),Object.defineProperty(a.prototype,"buffer",{get:function(){return this._trimAlloc(),this._buffer},set:function(e){this._buffer=e,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(a.prototype,"byteOffset",{get:function(){return this._byteOffset},set:function(e){this._byteOffset=e,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(a.prototype,"dataView",{get:function(){return this._dataView},set:function(e){this._byteOffset=e.byteOffset,this._buffer=e.buffer,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._byteOffset+e.byteLength}}),a.prototype.seek=function(e){var t=Math.max(0,Math.min(this.byteLength,e));this.position=isNaN(t)||!isFinite(t)?0:t},a.prototype.isEof=function(){return this.position>=this._byteLength},a.prototype.mapUint8Array=function(e){this._realloc(1*e);var t=new Uint8Array(this._buffer,this.byteOffset+this.position,e);return this.position+=1*e,t},a.prototype.readInt32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var r=new Int32Array(e);return a.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),a.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},a.prototype.readInt16Array=function(e,t){e=null==e?this.byteLength-this.position/2:e;var r=new Int16Array(e);return a.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),a.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},a.prototype.readInt8Array=function(e){e=null==e?this.byteLength-this.position:e;var t=new Int8Array(e);return a.memcpy(t.buffer,0,this.buffer,this.byteOffset+this.position,e*t.BYTES_PER_ELEMENT),this.position+=t.byteLength,t},a.prototype.readUint32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var r=new Uint32Array(e);return a.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),a.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},a.prototype.readUint16Array=function(e,t){e=null==e?this.byteLength-this.position/2:e;var r=new Uint16Array(e);return a.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),a.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},a.prototype.readUint8Array=function(e){e=null==e?this.byteLength-this.position:e;var t=new Uint8Array(e);return a.memcpy(t.buffer,0,this.buffer,this.byteOffset+this.position,e*t.BYTES_PER_ELEMENT),this.position+=t.byteLength,t},a.prototype.readFloat64Array=function(e,t){e=null==e?this.byteLength-this.position/8:e;var r=new Float64Array(e);return a.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),a.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},a.prototype.readFloat32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var r=new Float32Array(e);return a.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),a.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},a.prototype.readInt32=function(e){var t=this._dataView.getInt32(this.position,null==e?this.endianness:e);return this.position+=4,t},a.prototype.readInt16=function(e){var t=this._dataView.getInt16(this.position,null==e?this.endianness:e);return this.position+=2,t},a.prototype.readInt8=function(){var e=this._dataView.getInt8(this.position);return this.position+=1,e},a.prototype.readUint32=function(e){var t=this._dataView.getUint32(this.position,null==e?this.endianness:e);return this.position+=4,t},a.prototype.readUint16=function(e){var t=this._dataView.getUint16(this.position,null==e?this.endianness:e);return this.position+=2,t},a.prototype.readUint8=function(){var e=this._dataView.getUint8(this.position);return this.position+=1,e},a.prototype.readFloat32=function(e){var t=this._dataView.getFloat32(this.position,null==e?this.endianness:e);return this.position+=4,t},a.prototype.readFloat64=function(e){var t=this._dataView.getFloat64(this.position,null==e?this.endianness:e);return this.position+=8,t},a.endianness=new Int8Array(new Int16Array([1]).buffer)[0]>0,a.memcpy=function(e,t,r,i,n){var s=new Uint8Array(e,t,n),o=new Uint8Array(r,i,n);s.set(o)},a.arrayToNative=function(e,t){return t==this.endianness?e:this.flipArrayEndianness(e)},a.nativeToEndian=function(e,t){return this.endianness==t?e:this.flipArrayEndianness(e)},a.flipArrayEndianness=function(e){for(var t=new Uint8Array(e.buffer,e.byteOffset,e.byteLength),r=0;r<e.byteLength;r+=e.BYTES_PER_ELEMENT)for(var i=r+e.BYTES_PER_ELEMENT-1,n=r;i>n;i--,n++){var s=t[n];t[n]=t[i],t[i]=s}return e},a.prototype.failurePosition=0,String.fromCharCodeUint8=function(e){for(var t=[],r=0;r<e.length;r++)t[r]=e[r];return String.fromCharCode.apply(null,t)},a.prototype.readString=function(e,t){return null==t||"ASCII"==t?String.fromCharCodeUint8.apply(null,[this.mapUint8Array(null==e?this.byteLength-this.position:e)]):new TextDecoder(t).decode(this.mapUint8Array(e))},a.prototype.readCString=function(e){var t=this.byteLength-this.position,r=new Uint8Array(this._buffer,this._byteOffset+this.position),i=t;null!=e&&(i=Math.min(e,t));for(var n=0;n<i&&0!==r[n];n++);var s=String.fromCharCodeUint8.apply(null,[this.mapUint8Array(n)]);return null!=e?this.position+=i-n:n!=t&&(this.position+=1),s};var l=Math.pow(2,32);a.prototype.readInt64=function(){return this.readInt32()*l+this.readUint32()},a.prototype.readUint64=function(){return this.readUint32()*l+this.readUint32()},a.prototype.readInt64=function(){return this.readUint32()*l+this.readUint32()},a.prototype.readUint24=function(){return(this.readUint8()<<16)+(this.readUint8()<<8)+this.readUint8()},t.DataStream=a,a.prototype.save=function(e){var t=new Blob([this.buffer]);if(!window.URL||!URL.createObjectURL)throw"DataStream.save: Can't create object URL.";var r=window.URL.createObjectURL(t),i=document.createElement("a");document.body.appendChild(i),i.setAttribute("href",r),i.setAttribute("download",e),i.setAttribute("target","_self"),i.click(),window.URL.revokeObjectURL(r)},a.prototype._dynamicSize=!0,Object.defineProperty(a.prototype,"dynamicSize",{get:function(){return this._dynamicSize},set:function(e){e||this._trimAlloc(),this._dynamicSize=e}}),a.prototype.shift=function(e){var t=new ArrayBuffer(this._byteLength-e),r=new Uint8Array(t),i=new Uint8Array(this._buffer,e,r.length);r.set(i),this.buffer=t,this.position-=e},a.prototype.writeInt32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Int32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)a.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt32Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeInt32(e[r],t)},a.prototype.writeInt16Array=function(e,t){if(this._realloc(2*e.length),e instanceof Int16Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)a.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt16Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeInt16(e[r],t)},a.prototype.writeInt8Array=function(e){if(this._realloc(1*e.length),e instanceof Int8Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)a.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt8Array(e.length);else for(var t=0;t<e.length;t++)this.writeInt8(e[t])},a.prototype.writeUint32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Uint32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)a.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint32Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeUint32(e[r],t)},a.prototype.writeUint16Array=function(e,t){if(this._realloc(2*e.length),e instanceof Uint16Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)a.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint16Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeUint16(e[r],t)},a.prototype.writeUint8Array=function(e){if(this._realloc(1*e.length),e instanceof Uint8Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)a.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint8Array(e.length);else for(var t=0;t<e.length;t++)this.writeUint8(e[t])},a.prototype.writeFloat64Array=function(e,t){if(this._realloc(8*e.length),e instanceof Float64Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)a.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapFloat64Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeFloat64(e[r],t)},a.prototype.writeFloat32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Float32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)a.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapFloat32Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeFloat32(e[r],t)},a.prototype.writeInt32=function(e,t){this._realloc(4),this._dataView.setInt32(this.position,e,null==t?this.endianness:t),this.position+=4},a.prototype.writeInt16=function(e,t){this._realloc(2),this._dataView.setInt16(this.position,e,null==t?this.endianness:t),this.position+=2},a.prototype.writeInt8=function(e){this._realloc(1),this._dataView.setInt8(this.position,e),this.position+=1},a.prototype.writeUint32=function(e,t){this._realloc(4),this._dataView.setUint32(this.position,e,null==t?this.endianness:t),this.position+=4},a.prototype.writeUint16=function(e,t){this._realloc(2),this._dataView.setUint16(this.position,e,null==t?this.endianness:t),this.position+=2},a.prototype.writeUint8=function(e){this._realloc(1),this._dataView.setUint8(this.position,e),this.position+=1},a.prototype.writeFloat32=function(e,t){this._realloc(4),this._dataView.setFloat32(this.position,e,null==t?this.endianness:t),this.position+=4},a.prototype.writeFloat64=function(e,t){this._realloc(8),this._dataView.setFloat64(this.position,e,null==t?this.endianness:t),this.position+=8},a.prototype.writeUCS2String=function(e,t,r){null==r&&(r=e.length);for(var i=0;i<e.length&&i<r;i++)this.writeUint16(e.charCodeAt(i),t);for(;i<r;i++)this.writeUint16(0)},a.prototype.writeString=function(e,t,r){var i=0;if(null==t||"ASCII"==t)if(null!=r){var n=Math.min(e.length,r);for(i=0;i<n;i++)this.writeUint8(e.charCodeAt(i));for(;i<r;i++)this.writeUint8(0)}else for(i=0;i<e.length;i++)this.writeUint8(e.charCodeAt(i));else this.writeUint8Array(new TextEncoder(t).encode(e.substring(0,r)))},a.prototype.writeCString=function(e,t){var r=0;if(null!=t){var i=Math.min(e.length,t);for(r=0;r<i;r++)this.writeUint8(e.charCodeAt(r));for(;r<t;r++)this.writeUint8(0)}else{for(r=0;r<e.length;r++)this.writeUint8(e.charCodeAt(r));this.writeUint8(0)}},a.prototype.writeStruct=function(e,t){for(var r=0;r<e.length;r+=2){var i=e[r+1];this.writeType(i,t[e[r]],t)}},a.prototype.writeType=function(e,t,r){var i;if("function"==typeof e)return e(this,t);if("object"==typeof e&&!(e instanceof Array))return e.set(this,t,r);var n=null,s="ASCII",o=this.position;switch("string"==typeof e&&/:/.test(e)&&(i=e.split(":"),e=i[0],n=parseInt(i[1])),"string"==typeof e&&/,/.test(e)&&(i=e.split(","),e=i[0],s=parseInt(i[1])),e){case"uint8":this.writeUint8(t);break;case"int8":this.writeInt8(t);break;case"uint16":this.writeUint16(t,this.endianness);break;case"int16":this.writeInt16(t,this.endianness);break;case"uint32":this.writeUint32(t,this.endianness);break;case"int32":this.writeInt32(t,this.endianness);break;case"float32":this.writeFloat32(t,this.endianness);break;case"float64":this.writeFloat64(t,this.endianness);break;case"uint16be":this.writeUint16(t,a.BIG_ENDIAN);break;case"int16be":this.writeInt16(t,a.BIG_ENDIAN);break;case"uint32be":this.writeUint32(t,a.BIG_ENDIAN);break;case"int32be":this.writeInt32(t,a.BIG_ENDIAN);break;case"float32be":this.writeFloat32(t,a.BIG_ENDIAN);break;case"float64be":this.writeFloat64(t,a.BIG_ENDIAN);break;case"uint16le":this.writeUint16(t,a.LITTLE_ENDIAN);break;case"int16le":this.writeInt16(t,a.LITTLE_ENDIAN);break;case"uint32le":this.writeUint32(t,a.LITTLE_ENDIAN);break;case"int32le":this.writeInt32(t,a.LITTLE_ENDIAN);break;case"float32le":this.writeFloat32(t,a.LITTLE_ENDIAN);break;case"float64le":this.writeFloat64(t,a.LITTLE_ENDIAN);break;case"cstring":this.writeCString(t,n);break;case"string":this.writeString(t,s,n);break;case"u16string":this.writeUCS2String(t,this.endianness,n);break;case"u16stringle":this.writeUCS2String(t,a.LITTLE_ENDIAN,n);break;case"u16stringbe":this.writeUCS2String(t,a.BIG_ENDIAN,n);break;default:if(3==e.length){for(var l=e[1],d=0;d<t.length;d++)this.writeType(l,t[d]);break}this.writeStruct(e,t)}null!=n&&(this.position=o,this._realloc(n),this.position=o+n)},a.prototype.writeUint64=function(e){var t=Math.floor(e/l);this.writeUint32(t),this.writeUint32(4294967295&e)},a.prototype.writeUint24=function(e){this.writeUint8((16711680&e)>>16),this.writeUint8((65280&e)>>8),this.writeUint8(255&e)},a.prototype.adjustUint32=function(e,t){var r=this.position;this.seek(e),this.writeUint32(t),this.seek(r)},a.prototype.mapInt32Array=function(e,t){this._realloc(4*e);var r=new Int32Array(this._buffer,this.byteOffset+this.position,e);return a.arrayToNative(r,null==t?this.endianness:t),this.position+=4*e,r},a.prototype.mapInt16Array=function(e,t){this._realloc(2*e);var r=new Int16Array(this._buffer,this.byteOffset+this.position,e);return a.arrayToNative(r,null==t?this.endianness:t),this.position+=2*e,r},a.prototype.mapInt8Array=function(e){this._realloc(1*e);var t=new Int8Array(this._buffer,this.byteOffset+this.position,e);return this.position+=1*e,t},a.prototype.mapUint32Array=function(e,t){this._realloc(4*e);var r=new Uint32Array(this._buffer,this.byteOffset+this.position,e);return a.arrayToNative(r,null==t?this.endianness:t),this.position+=4*e,r},a.prototype.mapUint16Array=function(e,t){this._realloc(2*e);var r=new Uint16Array(this._buffer,this.byteOffset+this.position,e);return a.arrayToNative(r,null==t?this.endianness:t),this.position+=2*e,r},a.prototype.mapFloat64Array=function(e,t){this._realloc(8*e);var r=new Float64Array(this._buffer,this.byteOffset+this.position,e);return a.arrayToNative(r,null==t?this.endianness:t),this.position+=8*e,r},a.prototype.mapFloat32Array=function(e,t){this._realloc(4*e);var r=new Float32Array(this._buffer,this.byteOffset+this.position,e);return a.arrayToNative(r,null==t?this.endianness:t),this.position+=4*e,r};var d=function(e){this.buffers=[],this.bufferIndex=-1,e&&(this.insertBuffer(e),this.bufferIndex=0)};(d.prototype=new a(new ArrayBuffer,0,a.BIG_ENDIAN)).initialized=function(){var e;return this.bufferIndex>-1||(this.buffers.length>0?0===(e=this.buffers[0]).fileStart?(this.buffer=e,this.bufferIndex=0,s.debug("MultiBufferStream","Stream ready for parsing"),!0):(s.warn("MultiBufferStream","The first buffer should have a fileStart of 0"),this.logBufferLevel(),!1):(s.warn("MultiBufferStream","No buffer to start parsing from"),this.logBufferLevel(),!1))},ArrayBuffer.concat=function(e,t){s.debug("ArrayBuffer","Trying to create a new buffer of size: "+(e.byteLength+t.byteLength));var r=new Uint8Array(e.byteLength+t.byteLength);return r.set(new Uint8Array(e),0),r.set(new Uint8Array(t),e.byteLength),r.buffer},d.prototype.reduceBuffer=function(e,t,r){var i;return(i=new Uint8Array(r)).set(new Uint8Array(e,t,r)),i.buffer.fileStart=e.fileStart+t,i.buffer.usedBytes=0,i.buffer},d.prototype.insertBuffer=function(e){for(var t=!0,r=0;r<this.buffers.length;r++){var i=this.buffers[r];if(e.fileStart<=i.fileStart){if(e.fileStart===i.fileStart){if(e.byteLength>i.byteLength){this.buffers.splice(r,1),r--;continue}s.warn("MultiBufferStream","Buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+") already appended, ignoring")}else e.fileStart+e.byteLength<=i.fileStart||(e=this.reduceBuffer(e,0,i.fileStart-e.fileStart)),s.debug("MultiBufferStream","Appending new buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+")"),this.buffers.splice(r,0,e),0===r&&(this.buffer=e);t=!1;break}if(e.fileStart<i.fileStart+i.byteLength){var n=i.fileStart+i.byteLength-e.fileStart,o=e.byteLength-n;if(!(o>0)){t=!1;break}e=this.reduceBuffer(e,n,o)}}t&&(s.debug("MultiBufferStream","Appending new buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+")"),this.buffers.push(e),0===r&&(this.buffer=e))},d.prototype.logBufferLevel=function(e){var t,r,i,n,o,a=[],l="";for(i=0,n=0,t=0;t<this.buffers.length;t++)r=this.buffers[t],0===t?(o={},a.push(o),o.start=r.fileStart,o.end=r.fileStart+r.byteLength,l+="["+o.start+"-"):o.end===r.fileStart?o.end=r.fileStart+r.byteLength:((o={}).start=r.fileStart,l+=a[a.length-1].end-1+"], ["+o.start+"-",o.end=r.fileStart+r.byteLength,a.push(o)),i+=r.usedBytes,n+=r.byteLength;a.length>0&&(l+=o.end-1+"]");var d=e?s.info:s.debug;0===this.buffers.length?d("MultiBufferStream","No more buffer in memory"):d("MultiBufferStream",this.buffers.length+" stored buffer(s) ("+i+"/"+n+" bytes), continuous ranges: "+l)},d.prototype.cleanBuffers=function(){var e,t;for(e=0;e<this.buffers.length;e++)(t=this.buffers[e]).usedBytes===t.byteLength&&(s.debug("MultiBufferStream","Removing buffer #"+e),this.buffers.splice(e,1),e--)},d.prototype.mergeNextBuffer=function(){var e;if(this.bufferIndex+1<this.buffers.length){if((e=this.buffers[this.bufferIndex+1]).fileStart===this.buffer.fileStart+this.buffer.byteLength){var t=this.buffer.byteLength,r=this.buffer.usedBytes,i=this.buffer.fileStart;return this.buffers[this.bufferIndex]=ArrayBuffer.concat(this.buffer,e),this.buffer=this.buffers[this.bufferIndex],this.buffers.splice(this.bufferIndex+1,1),this.buffer.usedBytes=r,this.buffer.fileStart=i,s.debug("ISOFile","Concatenating buffer for box parsing (length: "+t+"->"+this.buffer.byteLength+")"),!0}return!1}return!1},d.prototype.findPosition=function(e,t,r){var i,n=null,o=-1;for(i=!0===e?0:this.bufferIndex;i<this.buffers.length&&(n=this.buffers[i]).fileStart<=t;)o=i,r&&(n.fileStart+n.byteLength<=t?n.usedBytes=n.byteLength:n.usedBytes=t-n.fileStart,this.logBufferLevel()),i++;return-1!==o&&(n=this.buffers[o]).fileStart+n.byteLength>=t?(s.debug("MultiBufferStream","Found position in existing buffer #"+o),o):-1},d.prototype.findEndContiguousBuf=function(e){var t,r,i,n=void 0!==e?e:this.bufferIndex;if(r=this.buffers[n],this.buffers.length>n+1)for(t=n+1;t<this.buffers.length&&(i=this.buffers[t]).fileStart===r.fileStart+r.byteLength;t++)r=i;return r.fileStart+r.byteLength},d.prototype.getEndFilePositionAfter=function(e){var t=this.findPosition(!0,e,!1);return-1!==t?this.findEndContiguousBuf(t):e},d.prototype.addUsedBytes=function(e){this.buffer.usedBytes+=e,this.logBufferLevel()},d.prototype.setAllUsedBytes=function(){this.buffer.usedBytes=this.buffer.byteLength,this.logBufferLevel()},d.prototype.seek=function(e,t,r){var i;return-1!==(i=this.findPosition(t,e,r))?(this.buffer=this.buffers[i],this.bufferIndex=i,this.position=e-this.buffer.fileStart,s.debug("MultiBufferStream","Repositioning parser at buffer position: "+this.position),!0):(s.debug("MultiBufferStream","Position "+e+" not found in buffered data"),!1)},d.prototype.getPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.position},d.prototype.getLength=function(){return this.byteLength},d.prototype.getEndPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.byteLength},t.MultiBufferStream=d;var u=function(){var e=[];e[3]="ES_Descriptor",e[4]="DecoderConfigDescriptor",e[5]="DecoderSpecificInfo",e[6]="SLConfigDescriptor",this.getDescriptorName=function(t){return e[t]};var t=this,r={};return this.parseOneDescriptor=function(t){var i,n,o,a=0;for(i=t.readUint8(),o=t.readUint8();128&o;)a=(127&o)<<7,o=t.readUint8();return a+=127&o,s.debug("MPEG4DescriptorParser","Found "+(e[i]||"Descriptor "+i)+", size "+a+" at position "+t.getPosition()),(n=e[i]?new r[e[i]](a):new r.Descriptor(a)).parse(t),n},r.Descriptor=function(e,t){this.tag=e,this.size=t,this.descs=[]},r.Descriptor.prototype.parse=function(e){this.data=e.readUint8Array(this.size)},r.Descriptor.prototype.findDescriptor=function(e){for(var t=0;t<this.descs.length;t++)if(this.descs[t].tag==e)return this.descs[t];return null},r.Descriptor.prototype.parseRemainingDescriptors=function(e){for(var r=e.position;e.position<r+this.size;){var i=t.parseOneDescriptor(e);this.descs.push(i)}},r.ES_Descriptor=function(e){r.Descriptor.call(this,3,e)},r.ES_Descriptor.prototype=new r.Descriptor,r.ES_Descriptor.prototype.parse=function(e){if(this.ES_ID=e.readUint16(),this.flags=e.readUint8(),this.size-=3,128&this.flags?(this.dependsOn_ES_ID=e.readUint16(),this.size-=2):this.dependsOn_ES_ID=0,64&this.flags){var t=e.readUint8();this.URL=e.readString(t),this.size-=t+1}else this.URL="";32&this.flags?(this.OCR_ES_ID=e.readUint16(),this.size-=2):this.OCR_ES_ID=0,this.parseRemainingDescriptors(e)},r.ES_Descriptor.prototype.getOTI=function(e){var t=this.findDescriptor(4);return t?t.oti:0},r.ES_Descriptor.prototype.getAudioConfig=function(e){var t=this.findDescriptor(4);if(!t)return null;var r=t.findDescriptor(5);if(r&&r.data){var i=(248&r.data[0])>>3;return 31===i&&r.data.length>=2&&(i=32+((7&r.data[0])<<3)+((224&r.data[1])>>5)),i}return null},r.DecoderConfigDescriptor=function(e){r.Descriptor.call(this,4,e)},r.DecoderConfigDescriptor.prototype=new r.Descriptor,r.DecoderConfigDescriptor.prototype.parse=function(e){this.oti=e.readUint8(),this.streamType=e.readUint8(),this.bufferSize=e.readUint24(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32(),this.size-=13,this.parseRemainingDescriptors(e)},r.DecoderSpecificInfo=function(e){r.Descriptor.call(this,5,e)},r.DecoderSpecificInfo.prototype=new r.Descriptor,r.SLConfigDescriptor=function(e){r.Descriptor.call(this,6,e)},r.SLConfigDescriptor.prototype=new r.Descriptor,this};t.MPEG4DescriptorParser=u;var c={ERR_INVALID_DATA:-1,ERR_NOT_ENOUGH_DATA:0,OK:1,BASIC_BOXES:["mdat","idat","free","skip","meco","strk"],FULL_BOXES:["hmhd","nmhd","iods","xml ","bxml","ipro","mere"],CONTAINER_BOXES:[["moov",["trak","pssh"]],["trak"],["edts"],["mdia"],["minf"],["dinf"],["stbl",["sgpd","sbgp"]],["mvex",["trex"]],["moof",["traf"]],["traf",["trun","sgpd","sbgp"]],["vttc"],["tref"],["iref"],["mfra",["tfra"]],["meco"],["hnti"],["hinf"],["strk"],["strd"],["sinf"],["rinf"],["schi"],["trgr"],["udta",["kind"]],["iprp",["ipma"]],["ipco"]],boxCodes:[],fullBoxCodes:[],containerBoxCodes:[],sampleEntryCodes:{},sampleGroupEntryCodes:[],trackGroupTypes:[],UUIDBoxes:{},UUIDs:[],initialize:function(){c.FullBox.prototype=new c.Box,c.ContainerBox.prototype=new c.Box,c.SampleEntry.prototype=new c.Box,c.TrackGroupTypeBox.prototype=new c.FullBox,c.BASIC_BOXES.forEach((function(e){c.createBoxCtor(e)})),c.FULL_BOXES.forEach((function(e){c.createFullBoxCtor(e)})),c.CONTAINER_BOXES.forEach((function(e){c.createContainerBoxCtor(e[0],null,e[1])}))},Box:function(e,t,r){this.type=e,this.size=t,this.uuid=r},FullBox:function(e,t,r){c.Box.call(this,e,t,r),this.flags=0,this.version=0},ContainerBox:function(e,t,r){c.Box.call(this,e,t,r),this.boxes=[]},SampleEntry:function(e,t,r,i){c.ContainerBox.call(this,e,t),this.hdr_size=r,this.start=i},SampleGroupEntry:function(e){this.grouping_type=e},TrackGroupTypeBox:function(e,t){c.FullBox.call(this,e,t)},createBoxCtor:function(e,t){c.boxCodes.push(e),c[e+"Box"]=function(t){c.Box.call(this,e,t)},c[e+"Box"].prototype=new c.Box,t&&(c[e+"Box"].prototype.parse=t)},createFullBoxCtor:function(e,t){c[e+"Box"]=function(t){c.FullBox.call(this,e,t)},c[e+"Box"].prototype=new c.FullBox,c[e+"Box"].prototype.parse=function(e){this.parseFullHeader(e),t&&t.call(this,e)}},addSubBoxArrays:function(e){if(e){this.subBoxNames=e;for(var t=e.length,r=0;r<t;r++)this[e[r]+"s"]=[]}},createContainerBoxCtor:function(e,t,r){c[e+"Box"]=function(t){c.ContainerBox.call(this,e,t),c.addSubBoxArrays.call(this,r)},c[e+"Box"].prototype=new c.ContainerBox,t&&(c[e+"Box"].prototype.parse=t)},createMediaSampleEntryCtor:function(e,t,r){c.sampleEntryCodes[e]=[],c[e+"SampleEntry"]=function(e,t){c.SampleEntry.call(this,e,t),c.addSubBoxArrays.call(this,r)},c[e+"SampleEntry"].prototype=new c.SampleEntry,t&&(c[e+"SampleEntry"].prototype.parse=t)},createSampleEntryCtor:function(e,t,r,i){c.sampleEntryCodes[e].push(t),c[t+"SampleEntry"]=function(r){c[e+"SampleEntry"].call(this,t,r),c.addSubBoxArrays.call(this,i)},c[t+"SampleEntry"].prototype=new c[e+"SampleEntry"],r&&(c[t+"SampleEntry"].prototype.parse=r)},createEncryptedSampleEntryCtor:function(e,t,r){c.createSampleEntryCtor.call(this,e,t,r,["sinf"])},createSampleGroupCtor:function(e,t){c[e+"SampleGroupEntry"]=function(t){c.SampleGroupEntry.call(this,e,t)},c[e+"SampleGroupEntry"].prototype=new c.SampleGroupEntry,t&&(c[e+"SampleGroupEntry"].prototype.parse=t)},createTrackGroupCtor:function(e,t){c[e+"TrackGroupTypeBox"]=function(t){c.TrackGroupTypeBox.call(this,e,t)},c[e+"TrackGroupTypeBox"].prototype=new c.TrackGroupTypeBox,t&&(c[e+"TrackGroupTypeBox"].prototype.parse=t)},createUUIDBox:function(e,t,r,i){c.UUIDs.push(e),c.UUIDBoxes[e]=function(i){t?c.FullBox.call(this,"uuid",i,e):r?c.ContainerBox.call(this,"uuid",i,e):c.Box.call(this,"uuid",i,e)},c.UUIDBoxes[e].prototype=t?new c.FullBox:r?new c.ContainerBox:new c.Box,i&&(c.UUIDBoxes[e].prototype.parse=t?function(e){this.parseFullHeader(e),i&&i.call(this,e)}:i)}};c.initialize(),c.TKHD_FLAG_ENABLED=1,c.TKHD_FLAG_IN_MOVIE=2,c.TKHD_FLAG_IN_PREVIEW=4,c.TFHD_FLAG_BASE_DATA_OFFSET=1,c.TFHD_FLAG_SAMPLE_DESC=2,c.TFHD_FLAG_SAMPLE_DUR=8,c.TFHD_FLAG_SAMPLE_SIZE=16,c.TFHD_FLAG_SAMPLE_FLAGS=32,c.TFHD_FLAG_DUR_EMPTY=65536,c.TFHD_FLAG_DEFAULT_BASE_IS_MOOF=131072,c.TRUN_FLAGS_DATA_OFFSET=1,c.TRUN_FLAGS_FIRST_FLAG=4,c.TRUN_FLAGS_DURATION=256,c.TRUN_FLAGS_SIZE=512,c.TRUN_FLAGS_FLAGS=1024,c.TRUN_FLAGS_CTS_OFFSET=2048,c.Box.prototype.add=function(e){return this.addBox(new c[e+"Box"])},c.Box.prototype.addBox=function(e){return this.boxes.push(e),this[e.type+"s"]?this[e.type+"s"].push(e):this[e.type]=e,e},c.Box.prototype.set=function(e,t){return this[e]=t,this},c.Box.prototype.addEntry=function(e,t){var r=t||"entries";return this[r]||(this[r]=[]),this[r].push(e),this},t.BoxParser=c,c.parseUUID=function(e){return c.parseHex16(e)},c.parseHex16=function(e){for(var t="",r=0;r<16;r++){var i=e.readUint8().toString(16);t+=1===i.length?"0"+i:i}return t},c.parseOneBox=function(e,t,r){var i,n,o,a=e.getPosition(),l=0;if(e.getEndPosition()-a<8)return s.debug("BoxParser","Not enough data in stream to parse the type and size of the box"),{code:c.ERR_NOT_ENOUGH_DATA};if(r&&r<8)return s.debug("BoxParser","Not enough bytes left in the parent box to parse a new box"),{code:c.ERR_NOT_ENOUGH_DATA};var d=e.readUint32(),u=e.readString(4),f=u;if(s.debug("BoxParser","Found box of type '"+u+"' and size "+d+" at position "+a),l=8,"uuid"==u){if(e.getEndPosition()-e.getPosition()<16||r-l<16)return e.seek(a),s.debug("BoxParser","Not enough bytes left in the parent box to parse a UUID box"),{code:c.ERR_NOT_ENOUGH_DATA};l+=16,f=o=c.parseUUID(e)}if(1==d){if(e.getEndPosition()-e.getPosition()<8||r&&r-l<8)return e.seek(a),s.warn("BoxParser",'Not enough data in stream to parse the extended size of the "'+u+'" box'),{code:c.ERR_NOT_ENOUGH_DATA};d=e.readUint64(),l+=8}else if(0===d)if(r)d=r;else if("mdat"!==u)return s.error("BoxParser","Unlimited box size not supported for type: '"+u+"'"),i=new c.Box(u,d),{code:c.OK,box:i,size:i.size};return 0!==d&&d<l?(s.error("BoxParser","Box of type "+u+" has an invalid size "+d+" (too small to be a box)"),{code:c.ERR_NOT_ENOUGH_DATA,type:u,size:d,hdr_size:l,start:a}):0!==d&&r&&d>r?(s.error("BoxParser","Box of type '"+u+"' has a size "+d+" greater than its container size "+r),{code:c.ERR_NOT_ENOUGH_DATA,type:u,size:d,hdr_size:l,start:a}):0!==d&&a+d>e.getEndPosition()?(e.seek(a),s.info("BoxParser","Not enough data in stream to parse the entire '"+u+"' box"),{code:c.ERR_NOT_ENOUGH_DATA,type:u,size:d,hdr_size:l,start:a}):t?{code:c.OK,type:u,size:d,hdr_size:l,start:a}:(c[u+"Box"]?i=new c[u+"Box"](d):"uuid"!==u?(s.warn("BoxParser","Unknown box type: '"+u+"'"),(i=new c.Box(u,d)).has_unparsed_data=!0):c.UUIDBoxes[o]?i=new c.UUIDBoxes[o](d):(s.warn("BoxParser","Unknown uuid type: '"+o+"'"),(i=new c.Box(u,d)).uuid=o,i.has_unparsed_data=!0),i.hdr_size=l,i.start=a,i.write===c.Box.prototype.write&&"mdat"!==i.type&&(s.info("BoxParser","'"+f+"' box writing not yet implemented, keeping unparsed data in memory for later write"),i.parseDataAndRewind(e)),i.parse(e),(n=e.getPosition()-(i.start+i.size))<0?(s.warn("BoxParser","Parsing of box '"+f+"' did not read the entire indicated box data size (missing "+-n+" bytes), seeking forward"),e.seek(i.start+i.size)):n>0&&(s.error("BoxParser","Parsing of box '"+f+"' read "+n+" more bytes than the indicated box data size, seeking backwards"),0!==i.size&&e.seek(i.start+i.size)),{code:c.OK,box:i,size:i.size})},c.Box.prototype.parse=function(e){"mdat"!=this.type?this.data=e.readUint8Array(this.size-this.hdr_size):0===this.size?e.seek(e.getEndPosition()):e.seek(this.start+this.size)},c.Box.prototype.parseDataAndRewind=function(e){this.data=e.readUint8Array(this.size-this.hdr_size),e.position-=this.size-this.hdr_size},c.FullBox.prototype.parseDataAndRewind=function(e){this.parseFullHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size),this.hdr_size-=4,e.position-=this.size-this.hdr_size},c.FullBox.prototype.parseFullHeader=function(e){this.version=e.readUint8(),this.flags=e.readUint24(),this.hdr_size+=4},c.FullBox.prototype.parse=function(e){this.parseFullHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size)},c.ContainerBox.prototype.parse=function(e){for(var t,r;e.getPosition()<this.start+this.size;){if((t=c.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==c.OK)return;if(r=t.box,this.boxes.push(r),this.subBoxNames&&-1!=this.subBoxNames.indexOf(r.type))this[this.subBoxNames[this.subBoxNames.indexOf(r.type)]+"s"].push(r);else{var i="uuid"!==r.type?r.type:r.uuid;this[i]?s.warn("Box of type "+i+" already stored in field of this type"):this[i]=r}}},c.Box.prototype.parseLanguage=function(e){this.language=e.readUint16();var t=[];t[0]=this.language>>10&31,t[1]=this.language>>5&31,t[2]=31&this.language,this.languageString=String.fromCharCode(t[0]+96,t[1]+96,t[2]+96)},c.SAMPLE_ENTRY_TYPE_VISUAL="Visual",c.SAMPLE_ENTRY_TYPE_AUDIO="Audio",c.SAMPLE_ENTRY_TYPE_HINT="Hint",c.SAMPLE_ENTRY_TYPE_METADATA="Metadata",c.SAMPLE_ENTRY_TYPE_SUBTITLE="Subtitle",c.SAMPLE_ENTRY_TYPE_SYSTEM="System",c.SAMPLE_ENTRY_TYPE_TEXT="Text",c.SampleEntry.prototype.parseHeader=function(e){e.readUint8Array(6),this.data_reference_index=e.readUint16(),this.hdr_size+=8},c.SampleEntry.prototype.parse=function(e){this.parseHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size)},c.SampleEntry.prototype.parseDataAndRewind=function(e){this.parseHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size),this.hdr_size-=8,e.position-=this.size-this.hdr_size},c.SampleEntry.prototype.parseFooter=function(e){c.ContainerBox.prototype.parse.call(this,e)},c.createMediaSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_HINT),c.createMediaSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_METADATA),c.createMediaSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_SUBTITLE),c.createMediaSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_SYSTEM),c.createMediaSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_TEXT),c.createMediaSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,(function(e){var t;this.parseHeader(e),e.readUint16(),e.readUint16(),e.readUint32Array(3),this.width=e.readUint16(),this.height=e.readUint16(),this.horizresolution=e.readUint32(),this.vertresolution=e.readUint32(),e.readUint32(),this.frame_count=e.readUint16(),t=Math.min(31,e.readUint8()),this.compressorname=e.readString(t),t<31&&e.readString(31-t),this.depth=e.readUint16(),e.readUint16(),this.parseFooter(e)})),c.createMediaSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_AUDIO,(function(e){this.parseHeader(e),e.readUint32Array(2),this.channel_count=e.readUint16(),this.samplesize=e.readUint16(),e.readUint16(),e.readUint16(),this.samplerate=e.readUint32()/65536,this.parseFooter(e)})),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"avc1"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"avc2"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"avc3"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"avc4"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"av01"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"hvc1"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"hev1"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"vvc1"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"vvi1"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"vvs1"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"vvcN"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"vp08"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"vp09"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_AUDIO,"mp4a"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_AUDIO,"ac-3"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_AUDIO,"ec-3"),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_AUDIO,"Opus"),c.createEncryptedSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_VISUAL,"encv"),c.createEncryptedSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_AUDIO,"enca"),c.createEncryptedSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_SUBTITLE,"encu"),c.createEncryptedSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_SYSTEM,"encs"),c.createEncryptedSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_TEXT,"enct"),c.createEncryptedSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_METADATA,"encm"),c.createBoxCtor("a1lx",(function(e){var t=16*(1+(1&(1&e.readUint8())));this.layer_size=[];for(var r=0;r<3;r++)this.layer_size[r]=16==t?e.readUint16():e.readUint32()})),c.createBoxCtor("a1op",(function(e){this.op_index=e.readUint8()})),c.createFullBoxCtor("auxC",(function(e){this.aux_type=e.readCString();var t=this.size-this.hdr_size-(this.aux_type.length+1);this.aux_subtype=e.readUint8Array(t)})),c.createBoxCtor("av1C",(function(e){var t=e.readUint8();if(t>>7&!1)s.error("av1C marker problem");else if(this.version=127&t,1===this.version)if(t=e.readUint8(),this.seq_profile=t>>5&7,this.seq_level_idx_0=31&t,t=e.readUint8(),this.seq_tier_0=t>>7&1,this.high_bitdepth=t>>6&1,this.twelve_bit=t>>5&1,this.monochrome=t>>4&1,this.chroma_subsampling_x=t>>3&1,this.chroma_subsampling_y=t>>2&1,this.chroma_sample_position=3&t,t=e.readUint8(),this.reserved_1=t>>5&7,0===this.reserved_1){if(this.initial_presentation_delay_present=t>>4&1,1===this.initial_presentation_delay_present)this.initial_presentation_delay_minus_one=15&t;else if(this.reserved_2=15&t,0!==this.reserved_2)return void s.error("av1C reserved_2 parsing problem");var r=this.size-this.hdr_size-4;this.configOBUs=e.readUint8Array(r)}else s.error("av1C reserved_1 parsing problem");else s.error("av1C version "+this.version+" not supported")})),c.createBoxCtor("avcC",(function(e){var t,r;for(this.configurationVersion=e.readUint8(),this.AVCProfileIndication=e.readUint8(),this.profile_compatibility=e.readUint8(),this.AVCLevelIndication=e.readUint8(),this.lengthSizeMinusOne=3&e.readUint8(),this.nb_SPS_nalus=31&e.readUint8(),r=this.size-this.hdr_size-6,this.SPS=[],t=0;t<this.nb_SPS_nalus;t++)this.SPS[t]={},this.SPS[t].length=e.readUint16(),this.SPS[t].nalu=e.readUint8Array(this.SPS[t].length),r-=2+this.SPS[t].length;for(this.nb_PPS_nalus=e.readUint8(),r--,this.PPS=[],t=0;t<this.nb_PPS_nalus;t++)this.PPS[t]={},this.PPS[t].length=e.readUint16(),this.PPS[t].nalu=e.readUint8Array(this.PPS[t].length),r-=2+this.PPS[t].length;r>0&&(this.ext=e.readUint8Array(r))})),c.createBoxCtor("btrt",(function(e){this.bufferSizeDB=e.readUint32(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32()})),c.createBoxCtor("clap",(function(e){this.cleanApertureWidthN=e.readUint32(),this.cleanApertureWidthD=e.readUint32(),this.cleanApertureHeightN=e.readUint32(),this.cleanApertureHeightD=e.readUint32(),this.horizOffN=e.readUint32(),this.horizOffD=e.readUint32(),this.vertOffN=e.readUint32(),this.vertOffD=e.readUint32()})),c.createBoxCtor("clli",(function(e){this.max_content_light_level=e.readUint16(),this.max_pic_average_light_level=e.readUint16()})),c.createFullBoxCtor("co64",(function(e){var t,r;if(t=e.readUint32(),this.chunk_offsets=[],0===this.version)for(r=0;r<t;r++)this.chunk_offsets.push(e.readUint64())})),c.createFullBoxCtor("CoLL",(function(e){this.maxCLL=e.readUint16(),this.maxFALL=e.readUint16()})),c.createBoxCtor("colr",(function(e){if(this.colour_type=e.readString(4),"nclx"===this.colour_type){this.colour_primaries=e.readUint16(),this.transfer_characteristics=e.readUint16(),this.matrix_coefficients=e.readUint16();var t=e.readUint8();this.full_range_flag=t>>7}else("rICC"===this.colour_type||"prof"===this.colour_type)&&(this.ICC_profile=e.readUint8Array(this.size-4))})),c.createFullBoxCtor("cprt",(function(e){this.parseLanguage(e),this.notice=e.readCString()})),c.createFullBoxCtor("cslg",(function(e){0===this.version&&(this.compositionToDTSShift=e.readInt32(),this.leastDecodeToDisplayDelta=e.readInt32(),this.greatestDecodeToDisplayDelta=e.readInt32(),this.compositionStartTime=e.readInt32(),this.compositionEndTime=e.readInt32())})),c.createFullBoxCtor("ctts",(function(e){var t,r;if(t=e.readUint32(),this.sample_counts=[],this.sample_offsets=[],0===this.version)for(r=0;r<t;r++){this.sample_counts.push(e.readUint32());var i=e.readInt32();i<0&&s.warn("BoxParser","ctts box uses negative values without using version 1"),this.sample_offsets.push(i)}else if(1==this.version)for(r=0;r<t;r++)this.sample_counts.push(e.readUint32()),this.sample_offsets.push(e.readInt32())})),c.createBoxCtor("dac3",(function(e){var t=e.readUint8(),r=e.readUint8(),i=e.readUint8();this.fscod=t>>6,this.bsid=t>>1&31,this.bsmod=(1&t)<<2|r>>6&3,this.acmod=r>>3&7,this.lfeon=r>>2&1,this.bit_rate_code=3&r|i>>5&7})),c.createBoxCtor("dec3",(function(e){var t=e.readUint16();this.data_rate=t>>3,this.num_ind_sub=7&t,this.ind_subs=[];for(var r=0;r<this.num_ind_sub+1;r++){var i={};this.ind_subs.push(i);var n=e.readUint8(),s=e.readUint8(),o=e.readUint8();i.fscod=n>>6,i.bsid=n>>1&31,i.bsmod=(1&n)<<4|s>>4&15,i.acmod=s>>1&7,i.lfeon=1&s,i.num_dep_sub=o>>1&15,i.num_dep_sub>0&&(i.chan_loc=(1&o)<<8|e.readUint8())}})),c.createFullBoxCtor("dfLa",(function(e){var t=[],r=["STREAMINFO","PADDING","APPLICATION","SEEKTABLE","VORBIS_COMMENT","CUESHEET","PICTURE","RESERVED"];for(this.parseFullHeader(e);;){var i=e.readUint8(),n=Math.min(127&i,r.length-1);if(n?e.readUint8Array(e.readUint24()):(e.readUint8Array(13),this.samplerate=e.readUint32()>>12,e.readUint8Array(20)),t.push(r[n]),128&i)break}this.numMetadataBlocks=t.length+" ("+t.join(", ")+")"})),c.createBoxCtor("dimm",(function(e){this.bytessent=e.readUint64()})),c.createBoxCtor("dmax",(function(e){this.time=e.readUint32()})),c.createBoxCtor("dmed",(function(e){this.bytessent=e.readUint64()})),c.createBoxCtor("dOps",(function(e){if(this.Version=e.readUint8(),this.OutputChannelCount=e.readUint8(),this.PreSkip=e.readUint16(),this.InputSampleRate=e.readUint32(),this.OutputGain=e.readInt16(),this.ChannelMappingFamily=e.readUint8(),0!==this.ChannelMappingFamily){this.StreamCount=e.readUint8(),this.CoupledCount=e.readUint8(),this.ChannelMapping=[];for(var t=0;t<this.OutputChannelCount;t++)this.ChannelMapping[t]=e.readUint8()}})),c.createFullBoxCtor("dref",(function(e){var t,r;this.entries=[];for(var i=e.readUint32(),n=0;n<i;n++){if((t=c.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==c.OK)return;r=t.box,this.entries.push(r)}})),c.createBoxCtor("drep",(function(e){this.bytessent=e.readUint64()})),c.createFullBoxCtor("elng",(function(e){this.extended_language=e.readString(this.size-this.hdr_size)})),c.createFullBoxCtor("elst",(function(e){this.entries=[];for(var t=e.readUint32(),r=0;r<t;r++){var i={};this.entries.push(i),1===this.version?(i.segment_duration=e.readUint64(),i.media_time=e.readInt64()):(i.segment_duration=e.readUint32(),i.media_time=e.readInt32()),i.media_rate_integer=e.readInt16(),i.media_rate_fraction=e.readInt16()}})),c.createFullBoxCtor("emsg",(function(e){1==this.version?(this.timescale=e.readUint32(),this.presentation_time=e.readUint64(),this.event_duration=e.readUint32(),this.id=e.readUint32(),this.scheme_id_uri=e.readCString(),this.value=e.readCString()):(this.scheme_id_uri=e.readCString(),this.value=e.readCString(),this.timescale=e.readUint32(),this.presentation_time_delta=e.readUint32(),this.event_duration=e.readUint32(),this.id=e.readUint32());var t=this.size-this.hdr_size-(16+(this.scheme_id_uri.length+1)+(this.value.length+1));1==this.version&&(t-=4),this.message_data=e.readUint8Array(t)})),c.createFullBoxCtor("esds",(function(e){var t=e.readUint8Array(this.size-this.hdr_size);if(void 0!==u){var r=new u;this.esd=r.parseOneDescriptor(new a(t.buffer,0,a.BIG_ENDIAN))}})),c.createBoxCtor("fiel",(function(e){this.fieldCount=e.readUint8(),this.fieldOrdering=e.readUint8()})),c.createBoxCtor("frma",(function(e){this.data_format=e.readString(4)})),c.createBoxCtor("ftyp",(function(e){var t=this.size-this.hdr_size;this.major_brand=e.readString(4),this.minor_version=e.readUint32(),t-=8,this.compatible_brands=[];for(var r=0;t>=4;)this.compatible_brands[r]=e.readString(4),t-=4,r++})),c.createFullBoxCtor("hdlr",(function(e){0===this.version&&(e.readUint32(),this.handler=e.readString(4),e.readUint32Array(3),this.name=e.readString(this.size-this.hdr_size-20),"\0"===this.name[this.name.length-1]&&(this.name=this.name.slice(0,-1)))})),c.createBoxCtor("hvcC",(function(e){var t,r,i,n;this.configurationVersion=e.readUint8(),n=e.readUint8(),this.general_profile_space=n>>6,this.general_tier_flag=(32&n)>>5,this.general_profile_idc=31&n,this.general_profile_compatibility=e.readUint32(),this.general_constraint_indicator=e.readUint8Array(6),this.general_level_idc=e.readUint8(),this.min_spatial_segmentation_idc=4095&e.readUint16(),this.parallelismType=3&e.readUint8(),this.chroma_format_idc=3&e.readUint8(),this.bit_depth_luma_minus8=7&e.readUint8(),this.bit_depth_chroma_minus8=7&e.readUint8(),this.avgFrameRate=e.readUint16(),n=e.readUint8(),this.constantFrameRate=n>>6,this.numTemporalLayers=(13&n)>>3,this.temporalIdNested=(4&n)>>2,this.lengthSizeMinusOne=3&n,this.nalu_arrays=[];var s=e.readUint8();for(t=0;t<s;t++){var o=[];this.nalu_arrays.push(o),n=e.readUint8(),o.completeness=(128&n)>>7,o.nalu_type=63&n;var a=e.readUint16();for(r=0;r<a;r++){var l={};o.push(l),i=e.readUint16(),l.data=e.readUint8Array(i)}}})),c.createFullBoxCtor("iinf",(function(e){var t;0===this.version?this.entry_count=e.readUint16():this.entry_count=e.readUint32(),this.item_infos=[];for(var r=0;r<this.entry_count;r++){if((t=c.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==c.OK)return;"infe"!==t.box.type&&s.error("BoxParser","Expected 'infe' box, got "+t.box.type),this.item_infos[r]=t.box}})),c.createFullBoxCtor("iloc",(function(e){var t;t=e.readUint8(),this.offset_size=t>>4&15,this.length_size=15&t,t=e.readUint8(),this.base_offset_size=t>>4&15,1===this.version||2===this.version?this.index_size=15&t:this.index_size=0,this.items=[];var r=0;if(this.version<2)r=e.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";r=e.readUint32()}for(var i=0;i<r;i++){var n={};if(this.items.push(n),this.version<2)n.item_ID=e.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";n.item_ID=e.readUint16()}switch(1===this.version||2===this.version?n.construction_method=15&e.readUint16():n.construction_method=0,n.data_reference_index=e.readUint16(),this.base_offset_size){case 0:n.base_offset=0;break;case 4:n.base_offset=e.readUint32();break;case 8:n.base_offset=e.readUint64();break;default:throw"Error reading base offset size"}var s=e.readUint16();n.extents=[];for(var o=0;o<s;o++){var a={};if(n.extents.push(a),1===this.version||2===this.version)switch(this.index_size){case 0:a.extent_index=0;break;case 4:a.extent_index=e.readUint32();break;case 8:a.extent_index=e.readUint64();break;default:throw"Error reading extent index"}switch(this.offset_size){case 0:a.extent_offset=0;break;case 4:a.extent_offset=e.readUint32();break;case 8:a.extent_offset=e.readUint64();break;default:throw"Error reading extent index"}switch(this.length_size){case 0:a.extent_length=0;break;case 4:a.extent_length=e.readUint32();break;case 8:a.extent_length=e.readUint64();break;default:throw"Error reading extent index"}}}})),c.createBoxCtor("imir",(function(e){var t=e.readUint8();this.reserved=t>>7,this.axis=1&t})),c.createFullBoxCtor("infe",(function(e){if(0!==this.version&&1!==this.version||(this.item_ID=e.readUint16(),this.item_protection_index=e.readUint16(),this.item_name=e.readCString(),this.content_type=e.readCString(),this.content_encoding=e.readCString()),1===this.version)return this.extension_type=e.readString(4),s.warn("BoxParser","Cannot parse extension type"),void e.seek(this.start+this.size);this.version>=2&&(2===this.version?this.item_ID=e.readUint16():3===this.version&&(this.item_ID=e.readUint32()),this.item_protection_index=e.readUint16(),this.item_type=e.readString(4),this.item_name=e.readCString(),"mime"===this.item_type?(this.content_type=e.readCString(),this.content_encoding=e.readCString()):"uri "===this.item_type&&(this.item_uri_type=e.readCString()))})),c.createFullBoxCtor("ipma",(function(e){var t,r;for(entry_count=e.readUint32(),this.associations=[],t=0;t<entry_count;t++){var i={};this.associations.push(i),this.version<1?i.id=e.readUint16():i.id=e.readUint32();var n=e.readUint8();for(i.props=[],r=0;r<n;r++){var s=e.readUint8(),o={};i.props.push(o),o.essential=(128&s)>>7==1,1&this.flags?o.property_index=(127&s)<<8|e.readUint8():o.property_index=127&s}}})),c.createFullBoxCtor("iref",(function(e){var t,r;for(this.references=[];e.getPosition()<this.start+this.size;){if((t=c.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==c.OK)return;(r=0===this.version?new c.SingleItemTypeReferenceBox(t.type,t.size,t.hdr_size,t.start):new c.SingleItemTypeReferenceBoxLarge(t.type,t.size,t.hdr_size,t.start)).write===c.Box.prototype.write&&"mdat"!==r.type&&(s.warn("BoxParser",r.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),r.parseDataAndRewind(e)),r.parse(e),this.references.push(r)}})),c.createBoxCtor("irot",(function(e){this.angle=3&e.readUint8()})),c.createFullBoxCtor("ispe",(function(e){this.image_width=e.readUint32(),this.image_height=e.readUint32()})),c.createFullBoxCtor("kind",(function(e){this.schemeURI=e.readCString(),this.value=e.readCString()})),c.createFullBoxCtor("leva",(function(e){var t=e.readUint8();this.levels=[];for(var r=0;r<t;r++){var i={};this.levels[r]=i,i.track_ID=e.readUint32();var n=e.readUint8();switch(i.padding_flag=n>>7,i.assignment_type=127&n,i.assignment_type){case 0:i.grouping_type=e.readString(4);break;case 1:i.grouping_type=e.readString(4),i.grouping_type_parameter=e.readUint32();break;case 2:case 3:break;case 4:i.sub_track_id=e.readUint32();break;default:s.warn("BoxParser","Unknown leva assignement type")}}})),c.createBoxCtor("lsel",(function(e){this.layer_id=e.readUint16()})),c.createBoxCtor("maxr",(function(e){this.period=e.readUint32(),this.bytes=e.readUint32()})),c.createBoxCtor("mdcv",(function(e){this.display_primaries=[],this.display_primaries[0]={},this.display_primaries[0].x=e.readUint16(),this.display_primaries[0].y=e.readUint16(),this.display_primaries[1]={},this.display_primaries[1].x=e.readUint16(),this.display_primaries[1].y=e.readUint16(),this.display_primaries[2]={},this.display_primaries[2].x=e.readUint16(),this.display_primaries[2].y=e.readUint16(),this.white_point={},this.white_point.x=e.readUint16(),this.white_point.y=e.readUint16(),this.max_display_mastering_luminance=e.readUint32(),this.min_display_mastering_luminance=e.readUint32()})),c.createFullBoxCtor("mdhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.parseLanguage(e),e.readUint16()})),c.createFullBoxCtor("mehd",(function(e){1&this.flags&&(s.warn("BoxParser","mehd box incorrectly uses flags set to 1, converting version to 1"),this.version=1),1==this.version?this.fragment_duration=e.readUint64():this.fragment_duration=e.readUint32()})),c.createFullBoxCtor("meta",(function(e){this.boxes=[],c.ContainerBox.prototype.parse.call(this,e)})),c.createFullBoxCtor("mfhd",(function(e){this.sequence_number=e.readUint32()})),c.createFullBoxCtor("mfro",(function(e){this._size=e.readUint32()})),c.createFullBoxCtor("mvhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.rate=e.readUint32(),this.volume=e.readUint16()>>8,e.readUint16(),e.readUint32Array(2),this.matrix=e.readUint32Array(9),e.readUint32Array(6),this.next_track_id=e.readUint32()})),c.createBoxCtor("npck",(function(e){this.packetssent=e.readUint32()})),c.createBoxCtor("nump",(function(e){this.packetssent=e.readUint64()})),c.createFullBoxCtor("padb",(function(e){var t=e.readUint32();this.padbits=[];for(var r=0;r<Math.floor((t+1)/2);r++)this.padbits=e.readUint8()})),c.createBoxCtor("pasp",(function(e){this.hSpacing=e.readUint32(),this.vSpacing=e.readUint32()})),c.createBoxCtor("payl",(function(e){this.text=e.readString(this.size-this.hdr_size)})),c.createBoxCtor("payt",(function(e){this.payloadID=e.readUint32();var t=e.readUint8();this.rtpmap_string=e.readString(t)})),c.createFullBoxCtor("pdin",(function(e){var t=(this.size-this.hdr_size)/8;this.rate=[],this.initial_delay=[];for(var r=0;r<t;r++)this.rate[r]=e.readUint32(),this.initial_delay[r]=e.readUint32()})),c.createFullBoxCtor("pitm",(function(e){0===this.version?this.item_id=e.readUint16():this.item_id=e.readUint32()})),c.createFullBoxCtor("pixi",(function(e){var t;for(this.num_channels=e.readUint8(),this.bits_per_channels=[],t=0;t<this.num_channels;t++)this.bits_per_channels[t]=e.readUint8()})),c.createBoxCtor("pmax",(function(e){this.bytes=e.readUint32()})),c.createFullBoxCtor("prft",(function(e){this.ref_track_id=e.readUint32(),this.ntp_timestamp=e.readUint64(),0===this.version?this.media_time=e.readUint32():this.media_time=e.readUint64()})),c.createFullBoxCtor("pssh",(function(e){if(this.system_id=c.parseHex16(e),this.version>0){var t=e.readUint32();this.kid=[];for(var r=0;r<t;r++)this.kid[r]=c.parseHex16(e)}var i=e.readUint32();i>0&&(this.data=e.readUint8Array(i))})),c.createFullBoxCtor("clef",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),c.createFullBoxCtor("enof",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),c.createFullBoxCtor("prof",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),c.createContainerBoxCtor("tapt",null,["clef","prof","enof"]),c.createBoxCtor("rtp ",(function(e){this.descriptionformat=e.readString(4),this.sdptext=e.readString(this.size-this.hdr_size-4)})),c.createFullBoxCtor("saio",(function(e){1&this.flags&&(this.aux_info_type=e.readUint32(),this.aux_info_type_parameter=e.readUint32());var t=e.readUint32();this.offset=[];for(var r=0;r<t;r++)0===this.version?this.offset[r]=e.readUint32():this.offset[r]=e.readUint64()})),c.createFullBoxCtor("saiz",(function(e){1&this.flags&&(this.aux_info_type=e.readUint32(),this.aux_info_type_parameter=e.readUint32()),this.default_sample_info_size=e.readUint8();var t=e.readUint32();if(this.sample_info_size=[],0===this.default_sample_info_size)for(var r=0;r<t;r++)this.sample_info_size[r]=e.readUint8()})),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_METADATA,"mett",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_METADATA,"metx",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.namespace=e.readCString(),this.schema_location=e.readCString(),this.parseFooter(e)})),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_SUBTITLE,"sbtt",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_SUBTITLE,"stpp",(function(e){this.parseHeader(e),this.namespace=e.readCString(),this.schema_location=e.readCString(),this.auxiliary_mime_types=e.readCString(),this.parseFooter(e)})),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_SUBTITLE,"stxt",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_SUBTITLE,"tx3g",(function(e){this.parseHeader(e),this.displayFlags=e.readUint32(),this.horizontal_justification=e.readInt8(),this.vertical_justification=e.readInt8(),this.bg_color_rgba=e.readUint8Array(4),this.box_record=e.readInt16Array(4),this.style_record=e.readUint8Array(12),this.parseFooter(e)})),c.createSampleEntryCtor(c.SAMPLE_ENTRY_TYPE_METADATA,"wvtt",(function(e){this.parseHeader(e),this.parseFooter(e)})),c.createSampleGroupCtor("alst",(function(e){var t,r=e.readUint16();for(this.first_output_sample=e.readUint16(),this.sample_offset=[],t=0;t<r;t++)this.sample_offset[t]=e.readUint32();var i=this.description_length-4-4*r;for(this.num_output_samples=[],this.num_total_samples=[],t=0;t<i/4;t++)this.num_output_samples[t]=e.readUint16(),this.num_total_samples[t]=e.readUint16()})),c.createSampleGroupCtor("avll",(function(e){this.layerNumber=e.readUint8(),this.accurateStatisticsFlag=e.readUint8(),this.avgBitRate=e.readUint16(),this.avgFrameRate=e.readUint16()})),c.createSampleGroupCtor("avss",(function(e){this.subSequenceIdentifier=e.readUint16(),this.layerNumber=e.readUint8();var t=e.readUint8();this.durationFlag=t>>7,this.avgRateFlag=t>>6&1,this.durationFlag&&(this.duration=e.readUint32()),this.avgRateFlag&&(this.accurateStatisticsFlag=e.readUint8(),this.avgBitRate=e.readUint16(),this.avgFrameRate=e.readUint16()),this.dependency=[];for(var r=e.readUint8(),i=0;i<r;i++){var n={};this.dependency.push(n),n.subSeqDirectionFlag=e.readUint8(),n.layerNumber=e.readUint8(),n.subSequenceIdentifier=e.readUint16()}})),c.createSampleGroupCtor("dtrt",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),c.createSampleGroupCtor("mvif",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),c.createSampleGroupCtor("prol",(function(e){this.roll_distance=e.readInt16()})),c.createSampleGroupCtor("rap ",(function(e){var t=e.readUint8();this.num_leading_samples_known=t>>7,this.num_leading_samples=127&t})),c.createSampleGroupCtor("rash",(function(e){if(this.operation_point_count=e.readUint16(),this.description_length!==2+(1===this.operation_point_count?2:6*this.operation_point_count)+9)s.warn("BoxParser","Mismatch in "+this.grouping_type+" sample group length"),this.data=e.readUint8Array(this.description_length-2);else{if(1===this.operation_point_count)this.target_rate_share=e.readUint16();else{this.target_rate_share=[],this.available_bitrate=[];for(var t=0;t<this.operation_point_count;t++)this.available_bitrate[t]=e.readUint32(),this.target_rate_share[t]=e.readUint16()}this.maximum_bitrate=e.readUint32(),this.minimum_bitrate=e.readUint32(),this.discard_priority=e.readUint8()}})),c.createSampleGroupCtor("roll",(function(e){this.roll_distance=e.readInt16()})),c.SampleGroupEntry.prototype.parse=function(e){s.warn("BoxParser","Unknown Sample Group type: "+this.grouping_type),this.data=e.readUint8Array(this.description_length)},c.createSampleGroupCtor("scif",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),c.createSampleGroupCtor("scnm",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),c.createSampleGroupCtor("seig",(function(e){this.reserved=e.readUint8();var t=e.readUint8();this.crypt_byte_block=t>>4,this.skip_byte_block=15&t,this.isProtected=e.readUint8(),this.Per_Sample_IV_Size=e.readUint8(),this.KID=c.parseHex16(e),this.constant_IV_size=0,this.constant_IV=0,1===this.isProtected&&0===this.Per_Sample_IV_Size&&(this.constant_IV_size=e.readUint8(),this.constant_IV=e.readUint8Array(this.constant_IV_size))})),c.createSampleGroupCtor("stsa",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),c.createSampleGroupCtor("sync",(function(e){var t=e.readUint8();this.NAL_unit_type=63&t})),c.createSampleGroupCtor("tele",(function(e){var t=e.readUint8();this.level_independently_decodable=t>>7})),c.createSampleGroupCtor("tsas",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),c.createSampleGroupCtor("tscl",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),c.createSampleGroupCtor("vipr",(function(e){s.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),c.createFullBoxCtor("sbgp",(function(e){this.grouping_type=e.readString(4),1===this.version?this.grouping_type_parameter=e.readUint32():this.grouping_type_parameter=0,this.entries=[];for(var t=e.readUint32(),r=0;r<t;r++){var i={};this.entries.push(i),i.sample_count=e.readInt32(),i.group_description_index=e.readInt32()}})),c.createFullBoxCtor("schm",(function(e){this.scheme_type=e.readString(4),this.scheme_version=e.readUint32(),1&this.flags&&(this.scheme_uri=e.readString(this.size-this.hdr_size-8))})),c.createBoxCtor("sdp ",(function(e){this.sdptext=e.readString(this.size-this.hdr_size)})),c.createFullBoxCtor("sdtp",(function(e){var t,r=this.size-this.hdr_size;this.is_leading=[],this.sample_depends_on=[],this.sample_is_depended_on=[],this.sample_has_redundancy=[];for(var i=0;i<r;i++)t=e.readUint8(),this.is_leading[i]=t>>6,this.sample_depends_on[i]=t>>4&3,this.sample_is_depended_on[i]=t>>2&3,this.sample_has_redundancy[i]=3&t})),c.createFullBoxCtor("senc"),c.createFullBoxCtor("sgpd",(function(e){this.grouping_type=e.readString(4),s.debug("BoxParser","Found Sample Groups of type "+this.grouping_type),1===this.version?this.default_length=e.readUint32():this.default_length=0,this.version>=2&&(this.default_group_description_index=e.readUint32()),this.entries=[];for(var t=e.readUint32(),r=0;r<t;r++){var i;i=c[this.grouping_type+"SampleGroupEntry"]?new c[this.grouping_type+"SampleGroupEntry"](this.grouping_type):new c.SampleGroupEntry(this.grouping_type),this.entries.push(i),1===this.version&&0===this.default_length?i.description_length=e.readUint32():i.description_length=this.default_length,i.write===c.SampleGroupEntry.prototype.write&&(s.info("BoxParser","SampleGroup for type "+this.grouping_type+" writing not yet implemented, keeping unparsed data in memory for later write"),i.data=e.readUint8Array(i.description_length),e.position-=i.description_length),i.parse(e)}})),c.createFullBoxCtor("sidx",(function(e){this.reference_ID=e.readUint32(),this.timescale=e.readUint32(),0===this.version?(this.earliest_presentation_time=e.readUint32(),this.first_offset=e.readUint32()):(this.earliest_presentation_time=e.readUint64(),this.first_offset=e.readUint64()),e.readUint16(),this.references=[];for(var t=e.readUint16(),r=0;r<t;r++){var i={};this.references.push(i);var n=e.readUint32();i.reference_type=n>>31&1,i.referenced_size=2147483647&n,i.subsegment_duration=e.readUint32(),n=e.readUint32(),i.starts_with_SAP=n>>31&1,i.SAP_type=n>>28&7,i.SAP_delta_time=268435455&n}})),c.SingleItemTypeReferenceBox=function(e,t,r,i){c.Box.call(this,e,t),this.hdr_size=r,this.start=i},c.SingleItemTypeReferenceBox.prototype=new c.Box,c.SingleItemTypeReferenceBox.prototype.parse=function(e){this.from_item_ID=e.readUint16();var t=e.readUint16();this.references=[];for(var r=0;r<t;r++)this.references[r]=e.readUint16()},c.SingleItemTypeReferenceBoxLarge=function(e,t,r,i){c.Box.call(this,e,t),this.hdr_size=r,this.start=i},c.SingleItemTypeReferenceBoxLarge.prototype=new c.Box,c.SingleItemTypeReferenceBoxLarge.prototype.parse=function(e){this.from_item_ID=e.readUint32();var t=e.readUint16();this.references=[];for(var r=0;r<t;r++)this.references[r]=e.readUint32()},c.createFullBoxCtor("SmDm",(function(e){this.primaryRChromaticity_x=e.readUint16(),this.primaryRChromaticity_y=e.readUint16(),this.primaryGChromaticity_x=e.readUint16(),this.primaryGChromaticity_y=e.readUint16(),this.primaryBChromaticity_x=e.readUint16(),this.primaryBChromaticity_y=e.readUint16(),this.whitePointChromaticity_x=e.readUint16(),this.whitePointChromaticity_y=e.readUint16(),this.luminanceMax=e.readUint32(),this.luminanceMin=e.readUint32()})),c.createFullBoxCtor("smhd",(function(e){this.balance=e.readUint16(),e.readUint16()})),c.createFullBoxCtor("ssix",(function(e){this.subsegments=[];for(var t=e.readUint32(),r=0;r<t;r++){var i={};this.subsegments.push(i),i.ranges=[];for(var n=e.readUint32(),s=0;s<n;s++){var o={};i.ranges.push(o),o.level=e.readUint8(),o.range_size=e.readUint24()}}})),c.createFullBoxCtor("stco",(function(e){var t;if(t=e.readUint32(),this.chunk_offsets=[],0===this.version)for(var r=0;r<t;r++)this.chunk_offsets.push(e.readUint32())})),c.createFullBoxCtor("stdp",(function(e){var t=(this.size-this.hdr_size)/2;this.priority=[];for(var r=0;r<t;r++)this.priority[r]=e.readUint16()})),c.createFullBoxCtor("sthd"),c.createFullBoxCtor("stri",(function(e){this.switch_group=e.readUint16(),this.alternate_group=e.readUint16(),this.sub_track_id=e.readUint32();var t=(this.size-this.hdr_size-8)/4;this.attribute_list=[];for(var r=0;r<t;r++)this.attribute_list[r]=e.readUint32()})),c.createFullBoxCtor("stsc",(function(e){var t,r;if(t=e.readUint32(),this.first_chunk=[],this.samples_per_chunk=[],this.sample_description_index=[],0===this.version)for(r=0;r<t;r++)this.first_chunk.push(e.readUint32()),this.samples_per_chunk.push(e.readUint32()),this.sample_description_index.push(e.readUint32())})),c.createFullBoxCtor("stsd",(function(e){var t,r,i,n;for(this.entries=[],i=e.readUint32(),t=1;t<=i;t++){if((r=c.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==c.OK)return;c[r.type+"SampleEntry"]?((n=new c[r.type+"SampleEntry"](r.size)).hdr_size=r.hdr_size,n.start=r.start):(s.warn("BoxParser","Unknown sample entry type: "+r.type),n=new c.SampleEntry(r.type,r.size,r.hdr_size,r.start)),n.write===c.SampleEntry.prototype.write&&(s.info("BoxParser","SampleEntry "+n.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),n.parseDataAndRewind(e)),n.parse(e),this.entries.push(n)}})),c.createFullBoxCtor("stsg",(function(e){this.grouping_type=e.readUint32();var t=e.readUint16();this.group_description_index=[];for(var r=0;r<t;r++)this.group_description_index[r]=e.readUint32()})),c.createFullBoxCtor("stsh",(function(e){var t,r;if(t=e.readUint32(),this.shadowed_sample_numbers=[],this.sync_sample_numbers=[],0===this.version)for(r=0;r<t;r++)this.shadowed_sample_numbers.push(e.readUint32()),this.sync_sample_numbers.push(e.readUint32())})),c.createFullBoxCtor("stss",(function(e){var t,r;if(r=e.readUint32(),0===this.version)for(this.sample_numbers=[],t=0;t<r;t++)this.sample_numbers.push(e.readUint32())})),c.createFullBoxCtor("stsz",(function(e){var t;if(this.sample_sizes=[],0===this.version)for(this.sample_size=e.readUint32(),this.sample_count=e.readUint32(),t=0;t<this.sample_count;t++)0===this.sample_size?this.sample_sizes.push(e.readUint32()):this.sample_sizes[t]=this.sample_size})),c.createFullBoxCtor("stts",(function(e){var t,r,i;if(t=e.readUint32(),this.sample_counts=[],this.sample_deltas=[],0===this.version)for(r=0;r<t;r++)this.sample_counts.push(e.readUint32()),(i=e.readInt32())<0&&(s.warn("BoxParser","File uses negative stts sample delta, using value 1 instead, sync may be lost!"),i=1),this.sample_deltas.push(i)})),c.createFullBoxCtor("stvi",(function(e){var t=e.readUint32();this.single_view_allowed=3&t,this.stereo_scheme=e.readUint32();var r,i,n=e.readUint32();for(this.stereo_indication_type=e.readString(n),this.boxes=[];e.getPosition()<this.start+this.size;){if((r=c.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==c.OK)return;i=r.box,this.boxes.push(i),this[i.type]=i}})),c.createBoxCtor("styp",(function(e){c.ftypBox.prototype.parse.call(this,e)})),c.createFullBoxCtor("stz2",(function(e){var t,r;if(this.sample_sizes=[],0===this.version)if(this.reserved=e.readUint24(),this.field_size=e.readUint8(),r=e.readUint32(),4===this.field_size)for(t=0;t<r;t+=2){var i=e.readUint8();this.sample_sizes[t]=i>>4&15,this.sample_sizes[t+1]=15&i}else if(8===this.field_size)for(t=0;t<r;t++)this.sample_sizes[t]=e.readUint8();else if(16===this.field_size)for(t=0;t<r;t++)this.sample_sizes[t]=e.readUint16();else s.error("BoxParser","Error in length field in stz2 box")})),c.createFullBoxCtor("subs",(function(e){var t,r,i,n;for(i=e.readUint32(),this.entries=[],t=0;t<i;t++){var s={};if(this.entries[t]=s,s.sample_delta=e.readUint32(),s.subsamples=[],(n=e.readUint16())>0)for(r=0;r<n;r++){var o={};s.subsamples.push(o),1==this.version?o.size=e.readUint32():o.size=e.readUint16(),o.priority=e.readUint8(),o.discardable=e.readUint8(),o.codec_specific_parameters=e.readUint32()}}})),c.createFullBoxCtor("tenc",(function(e){if(e.readUint8(),0===this.version)e.readUint8();else{var t=e.readUint8();this.default_crypt_byte_block=t>>4&15,this.default_skip_byte_block=15&t}this.default_isProtected=e.readUint8(),this.default_Per_Sample_IV_Size=e.readUint8(),this.default_KID=c.parseHex16(e),1===this.default_isProtected&&0===this.default_Per_Sample_IV_Size&&(this.default_constant_IV_size=e.readUint8(),this.default_constant_IV=e.readUint8Array(this.default_constant_IV_size))})),c.createFullBoxCtor("tfdt",(function(e){1==this.version?this.baseMediaDecodeTime=e.readUint64():this.baseMediaDecodeTime=e.readUint32()})),c.createFullBoxCtor("tfhd",(function(e){var t=0;this.track_id=e.readUint32(),this.size-this.hdr_size>t&&this.flags&c.TFHD_FLAG_BASE_DATA_OFFSET?(this.base_data_offset=e.readUint64(),t+=8):this.base_data_offset=0,this.size-this.hdr_size>t&&this.flags&c.TFHD_FLAG_SAMPLE_DESC?(this.default_sample_description_index=e.readUint32(),t+=4):this.default_sample_description_index=0,this.size-this.hdr_size>t&&this.flags&c.TFHD_FLAG_SAMPLE_DUR?(this.default_sample_duration=e.readUint32(),t+=4):this.default_sample_duration=0,this.size-this.hdr_size>t&&this.flags&c.TFHD_FLAG_SAMPLE_SIZE?(this.default_sample_size=e.readUint32(),t+=4):this.default_sample_size=0,this.size-this.hdr_size>t&&this.flags&c.TFHD_FLAG_SAMPLE_FLAGS?(this.default_sample_flags=e.readUint32(),t+=4):this.default_sample_flags=0})),c.createFullBoxCtor("tfra",(function(e){this.track_ID=e.readUint32(),e.readUint24();var t=e.readUint8();this.length_size_of_traf_num=t>>4&3,this.length_size_of_trun_num=t>>2&3,this.length_size_of_sample_num=3&t,this.entries=[];for(var r=e.readUint32(),i=0;i<r;i++)1===this.version?(this.time=e.readUint64(),this.moof_offset=e.readUint64()):(this.time=e.readUint32(),this.moof_offset=e.readUint32()),this.traf_number=e["readUint"+8*(this.length_size_of_traf_num+1)](),this.trun_number=e["readUint"+8*(this.length_size_of_trun_num+1)](),this.sample_number=e["readUint"+8*(this.length_size_of_sample_num+1)]()})),c.createFullBoxCtor("tkhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.track_id=e.readUint32(),e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.track_id=e.readUint32(),e.readUint32(),this.duration=e.readUint32()),e.readUint32Array(2),this.layer=e.readInt16(),this.alternate_group=e.readInt16(),this.volume=e.readInt16()>>8,e.readUint16(),this.matrix=e.readInt32Array(9),this.width=e.readUint32(),this.height=e.readUint32()})),c.createBoxCtor("tmax",(function(e){this.time=e.readUint32()})),c.createBoxCtor("tmin",(function(e){this.time=e.readUint32()})),c.createBoxCtor("totl",(function(e){this.bytessent=e.readUint32()})),c.createBoxCtor("tpay",(function(e){this.bytessent=e.readUint32()})),c.createBoxCtor("tpyl",(function(e){this.bytessent=e.readUint64()})),c.TrackGroupTypeBox.prototype.parse=function(e){this.parseFullHeader(e),this.track_group_id=e.readUint32()},c.createTrackGroupCtor("msrc"),c.TrackReferenceTypeBox=function(e,t,r,i){c.Box.call(this,e,t),this.hdr_size=r,this.start=i},c.TrackReferenceTypeBox.prototype=new c.Box,c.TrackReferenceTypeBox.prototype.parse=function(e){this.track_ids=e.readUint32Array((this.size-this.hdr_size)/4)},c.trefBox.prototype.parse=function(e){for(var t,r;e.getPosition()<this.start+this.size;){if((t=c.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==c.OK)return;(r=new c.TrackReferenceTypeBox(t.type,t.size,t.hdr_size,t.start)).write===c.Box.prototype.write&&"mdat"!==r.type&&(s.info("BoxParser","TrackReference "+r.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),r.parseDataAndRewind(e)),r.parse(e),this.boxes.push(r)}},c.createFullBoxCtor("trep",(function(e){for(this.track_ID=e.readUint32(),this.boxes=[];e.getPosition()<this.start+this.size;){if(ret=c.parseOneBox(e,!1,this.size-(e.getPosition()-this.start)),ret.code!==c.OK)return;box=ret.box,this.boxes.push(box)}})),c.createFullBoxCtor("trex",(function(e){this.track_id=e.readUint32(),this.default_sample_description_index=e.readUint32(),this.default_sample_duration=e.readUint32(),this.default_sample_size=e.readUint32(),this.default_sample_flags=e.readUint32()})),c.createBoxCtor("trpy",(function(e){this.bytessent=e.readUint64()})),c.createFullBoxCtor("trun",(function(e){var t=0;if(this.sample_count=e.readUint32(),t+=4,this.size-this.hdr_size>t&&this.flags&c.TRUN_FLAGS_DATA_OFFSET?(this.data_offset=e.readInt32(),t+=4):this.data_offset=0,this.size-this.hdr_size>t&&this.flags&c.TRUN_FLAGS_FIRST_FLAG?(this.first_sample_flags=e.readUint32(),t+=4):this.first_sample_flags=0,this.sample_duration=[],this.sample_size=[],this.sample_flags=[],this.sample_composition_time_offset=[],this.size-this.hdr_size>t)for(var r=0;r<this.sample_count;r++)this.flags&c.TRUN_FLAGS_DURATION&&(this.sample_duration[r]=e.readUint32()),this.flags&c.TRUN_FLAGS_SIZE&&(this.sample_size[r]=e.readUint32()),this.flags&c.TRUN_FLAGS_FLAGS&&(this.sample_flags[r]=e.readUint32()),this.flags&c.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?this.sample_composition_time_offset[r]=e.readUint32():this.sample_composition_time_offset[r]=e.readInt32())})),c.createFullBoxCtor("tsel",(function(e){this.switch_group=e.readUint32();var t=(this.size-this.hdr_size-4)/4;this.attribute_list=[];for(var r=0;r<t;r++)this.attribute_list[r]=e.readUint32()})),c.createFullBoxCtor("txtC",(function(e){this.config=e.readCString()})),c.createFullBoxCtor("url ",(function(e){1!==this.flags&&(this.location=e.readCString())})),c.createFullBoxCtor("urn ",(function(e){this.name=e.readCString(),this.size-this.hdr_size-this.name.length-1>0&&(this.location=e.readCString())})),c.createUUIDBox("********************************",!0,!1,(function(e){this.LiveServerManifest=e.readString(this.size-this.hdr_size).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})),c.createUUIDBox("********************************",!0,!1,(function(e){this.system_id=c.parseHex16(e);var t=e.readUint32();t>0&&(this.data=e.readUint8Array(t))})),c.createUUIDBox("********************************",!0,!1),c.createUUIDBox("********************************",!0,!1,(function(e){this.default_AlgorithmID=e.readUint24(),this.default_IV_size=e.readUint8(),this.default_KID=c.parseHex16(e)})),c.createUUIDBox("********************************",!0,!1,(function(e){this.fragment_count=e.readUint8(),this.entries=[];for(var t=0;t<this.fragment_count;t++){var r={},i=0,n=0;1===this.version?(i=e.readUint64(),n=e.readUint64()):(i=e.readUint32(),n=e.readUint32()),r.absolute_time=i,r.absolute_duration=n,this.entries.push(r)}})),c.createUUIDBox("********************************",!0,!1,(function(e){1===this.version?(this.absolute_time=e.readUint64(),this.duration=e.readUint64()):(this.absolute_time=e.readUint32(),this.duration=e.readUint32())})),c.createFullBoxCtor("vmhd",(function(e){this.graphicsmode=e.readUint16(),this.opcolor=e.readUint16Array(3)})),c.createFullBoxCtor("vpcC",(function(e){var t;1===this.version?(this.profile=e.readUint8(),this.level=e.readUint8(),t=e.readUint8(),this.bitDepth=t>>4,this.chromaSubsampling=t>>1&7,this.videoFullRangeFlag=1&t,this.colourPrimaries=e.readUint8(),this.transferCharacteristics=e.readUint8(),this.matrixCoefficients=e.readUint8(),this.codecIntializationDataSize=e.readUint16(),this.codecIntializationData=e.readUint8Array(this.codecIntializationDataSize)):(this.profile=e.readUint8(),this.level=e.readUint8(),t=e.readUint8(),this.bitDepth=t>>4&15,this.colorSpace=15&t,t=e.readUint8(),this.chromaSubsampling=t>>4&15,this.transferFunction=t>>1&7,this.videoFullRangeFlag=1&t,this.codecIntializationDataSize=e.readUint16(),this.codecIntializationData=e.readUint8Array(this.codecIntializationDataSize))})),c.createBoxCtor("vttC",(function(e){this.text=e.readString(this.size-this.hdr_size)})),c.createFullBoxCtor("vvcC",(function(e){var t,r,i={held_bits:void 0,num_held_bits:0,stream_read_1_bytes:function(e){this.held_bits=e.readUint8(),this.num_held_bits=8},stream_read_2_bytes:function(e){this.held_bits=e.readUint16(),this.num_held_bits=16},extract_bits:function(e){var t=this.held_bits>>this.num_held_bits-e&(1<<e)-1;return this.num_held_bits-=e,t}};if(i.stream_read_1_bytes(e),i.extract_bits(5),this.lengthSizeMinusOne=i.extract_bits(2),this.ptl_present_flag=i.extract_bits(1),this.ptl_present_flag){if(i.stream_read_2_bytes(e),this.ols_idx=i.extract_bits(9),this.num_sublayers=i.extract_bits(3),this.constant_frame_rate=i.extract_bits(2),this.chroma_format_idc=i.extract_bits(2),i.stream_read_1_bytes(e),this.bit_depth_minus8=i.extract_bits(3),i.extract_bits(5),i.stream_read_2_bytes(e),i.extract_bits(2),this.num_bytes_constraint_info=i.extract_bits(6),this.general_profile_idc=i.extract_bits(7),this.general_tier_flag=i.extract_bits(1),this.general_level_idc=e.readUint8(),i.stream_read_1_bytes(e),this.ptl_frame_only_constraint_flag=i.extract_bits(1),this.ptl_multilayer_enabled_flag=i.extract_bits(1),this.general_constraint_info=new Uint8Array(this.num_bytes_constraint_info),this.num_bytes_constraint_info){for(t=0;t<this.num_bytes_constraint_info-1;t++){var n=i.extract_bits(6);i.stream_read_1_bytes(e);var s=i.extract_bits(2);this.general_constraint_info[t]=n<<2|s}this.general_constraint_info[this.num_bytes_constraint_info-1]=i.extract_bits(6)}else i.extract_bits(6);for(i.stream_read_1_bytes(e),this.ptl_sublayer_present_mask=0,r=this.num_sublayers-2;r>=0;--r){var o=i.extract_bits(1);this.ptl_sublayer_present_mask|=o<<r}for(r=this.num_sublayers;r<=8&&this.num_sublayers>1;++r)i.extract_bits(1);for(r=this.num_sublayers-2;r>=0;--r)this.ptl_sublayer_present_mask&1<<r&&(this.sublayer_level_idc[r]=e.readUint8());if(this.ptl_num_sub_profiles=e.readUint8(),this.general_sub_profile_idc=[],this.ptl_num_sub_profiles)for(t=0;t<this.ptl_num_sub_profiles;t++)this.general_sub_profile_idc.push(e.readUint32());this.max_picture_width=e.readUint16(),this.max_picture_height=e.readUint16(),this.avg_frame_rate=e.readUint16()}this.nalu_arrays=[];var a=e.readUint8();for(t=0;t<a;t++){var l=[];this.nalu_arrays.push(l),i.stream_read_1_bytes(e),l.completeness=i.extract_bits(1),i.extract_bits(2),l.nalu_type=i.extract_bits(5);var d=1;for(13!=l.nalu_type&&12!=l.nalu_type&&(d=e.readUint16()),r=0;r<d;r++){var u=e.readUint16();l.push({data:e.readUint8Array(u),length:u})}}})),c.createFullBoxCtor("vvnC",(function(e){var t=strm.readUint8();this.lengthSizeMinusOne=3&t})),c.SampleEntry.prototype.isVideo=function(){return!1},c.SampleEntry.prototype.isAudio=function(){return!1},c.SampleEntry.prototype.isSubtitle=function(){return!1},c.SampleEntry.prototype.isMetadata=function(){return!1},c.SampleEntry.prototype.isHint=function(){return!1},c.SampleEntry.prototype.getCodec=function(){return this.type.replace(".","")},c.SampleEntry.prototype.getWidth=function(){return""},c.SampleEntry.prototype.getHeight=function(){return""},c.SampleEntry.prototype.getChannelCount=function(){return""},c.SampleEntry.prototype.getSampleRate=function(){return""},c.SampleEntry.prototype.getSampleSize=function(){return""},c.VisualSampleEntry.prototype.isVideo=function(){return!0},c.VisualSampleEntry.prototype.getWidth=function(){return this.width},c.VisualSampleEntry.prototype.getHeight=function(){return this.height},c.AudioSampleEntry.prototype.isAudio=function(){return!0},c.AudioSampleEntry.prototype.getChannelCount=function(){return this.channel_count},c.AudioSampleEntry.prototype.getSampleRate=function(){return this.samplerate},c.AudioSampleEntry.prototype.getSampleSize=function(){return this.samplesize},c.SubtitleSampleEntry.prototype.isSubtitle=function(){return!0},c.MetadataSampleEntry.prototype.isMetadata=function(){return!0},c.decimalToHex=function(e,t){var r=Number(e).toString(16);for(t=null==t?t=2:t;r.length<t;)r="0"+r;return r},c.avc1SampleEntry.prototype.getCodec=c.avc2SampleEntry.prototype.getCodec=c.avc3SampleEntry.prototype.getCodec=c.avc4SampleEntry.prototype.getCodec=function(){var e=c.SampleEntry.prototype.getCodec.call(this);return this.avcC?e+"."+c.decimalToHex(this.avcC.AVCProfileIndication)+c.decimalToHex(this.avcC.profile_compatibility)+c.decimalToHex(this.avcC.AVCLevelIndication):e},c.hev1SampleEntry.prototype.getCodec=c.hvc1SampleEntry.prototype.getCodec=function(){var e,t=c.SampleEntry.prototype.getCodec.call(this);if(this.hvcC){switch(t+=".",this.hvcC.general_profile_space){case 0:t+="";break;case 1:t+="A";break;case 2:t+="B";break;case 3:t+="C"}t+=this.hvcC.general_profile_idc,t+=".";var r=this.hvcC.general_profile_compatibility,i=0;for(e=0;e<32&&(i|=1&r,31!=e);e++)i<<=1,r>>=1;t+=c.decimalToHex(i,0),t+=".",0===this.hvcC.general_tier_flag?t+="L":t+="H",t+=this.hvcC.general_level_idc;var n=!1,s="";for(e=5;e>=0;e--)(this.hvcC.general_constraint_indicator[e]||n)&&(s="."+c.decimalToHex(this.hvcC.general_constraint_indicator[e],0)+s,n=!0);t+=s}return t},c.vvc1SampleEntry.prototype.getCodec=c.vvi1SampleEntry.prototype.getCodec=function(){var e,t=c.SampleEntry.prototype.getCodec.call(this);if(this.vvcC){t+="."+this.vvcC.general_profile_idc,this.vvcC.general_tier_flag?t+=".H":t+=".L",t+=this.vvcC.general_level_idc;var r="";if(this.vvcC.general_constraint_info){var i,n=[],s=0;for(s|=this.vvcC.ptl_frame_only_constraint<<7,s|=this.vvcC.ptl_multilayer_enabled<<6,e=0;e<this.vvcC.general_constraint_info.length;++e)s|=this.vvcC.general_constraint_info[e]>>2&63,n.push(s),s&&(i=e),s=this.vvcC.general_constraint_info[e]>>2&3;if(void 0===i)r=".CA";else{r=".C";var o="ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",a=0,l=0;for(e=0;e<=i;++e)for(a=a<<8|n[e],l+=8;l>=5;){r+=o[a>>l-5&31],a&=(1<<(l-=5))-1}l&&(r+=o[31&(a<<=5-l)])}}t+=r}return t},c.mp4aSampleEntry.prototype.getCodec=function(){var e=c.SampleEntry.prototype.getCodec.call(this);if(this.esds&&this.esds.esd){var t=this.esds.esd.getOTI(),r=this.esds.esd.getAudioConfig();return e+"."+c.decimalToHex(t)+(r?"."+r:"")}return e},c.stxtSampleEntry.prototype.getCodec=function(){var e=c.SampleEntry.prototype.getCodec.call(this);return this.mime_format?e+"."+this.mime_format:e},c.vp08SampleEntry.prototype.getCodec=c.vp09SampleEntry.prototype.getCodec=function(){var e=c.SampleEntry.prototype.getCodec.call(this),t=this.vpcC.level;0==t&&(t="00");var r=this.vpcC.bitDepth;return 8==r&&(r="08"),e+".0"+this.vpcC.profile+"."+t+"."+r},c.av01SampleEntry.prototype.getCodec=function(){var e,t=c.SampleEntry.prototype.getCodec.call(this),r=this.av1C.seq_level_idx_0;return r<10&&(r="0"+r),2===this.av1C.seq_profile&&1===this.av1C.high_bitdepth?e=1===this.av1C.twelve_bit?"12":"10":this.av1C.seq_profile<=2&&(e=1===this.av1C.high_bitdepth?"10":"08"),t+"."+this.av1C.seq_profile+"."+r+(this.av1C.seq_tier_0?"H":"M")+"."+e},c.Box.prototype.writeHeader=function(e,t){this.size+=8,this.size>l&&(this.size+=8),"uuid"===this.type&&(this.size+=16),s.debug("BoxWriter","Writing box "+this.type+" of size: "+this.size+" at position "+e.getPosition()+(t||"")),this.size>l?e.writeUint32(1):(this.sizePosition=e.getPosition(),e.writeUint32(this.size)),e.writeString(this.type,null,4),"uuid"===this.type&&e.writeUint8Array(this.uuid),this.size>l&&e.writeUint64(this.size)},c.FullBox.prototype.writeHeader=function(e){this.size+=4,c.Box.prototype.writeHeader.call(this,e," v="+this.version+" f="+this.flags),e.writeUint8(this.version),e.writeUint24(this.flags)},c.Box.prototype.write=function(e){"mdat"===this.type?this.data&&(this.size=this.data.length,this.writeHeader(e),e.writeUint8Array(this.data)):(this.size=this.data?this.data.length:0,this.writeHeader(e),this.data&&e.writeUint8Array(this.data))},c.ContainerBox.prototype.write=function(e){this.size=0,this.writeHeader(e);for(var t=0;t<this.boxes.length;t++)this.boxes[t]&&(this.boxes[t].write(e),this.size+=this.boxes[t].size);s.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},c.TrackReferenceTypeBox.prototype.write=function(e){this.size=4*this.track_ids.length,this.writeHeader(e),e.writeUint32Array(this.track_ids)},c.avcCBox.prototype.write=function(e){var t;for(this.size=7,t=0;t<this.SPS.length;t++)this.size+=2+this.SPS[t].length;for(t=0;t<this.PPS.length;t++)this.size+=2+this.PPS[t].length;for(this.ext&&(this.size+=this.ext.length),this.writeHeader(e),e.writeUint8(this.configurationVersion),e.writeUint8(this.AVCProfileIndication),e.writeUint8(this.profile_compatibility),e.writeUint8(this.AVCLevelIndication),e.writeUint8(this.lengthSizeMinusOne+252),e.writeUint8(this.SPS.length+224),t=0;t<this.SPS.length;t++)e.writeUint16(this.SPS[t].length),e.writeUint8Array(this.SPS[t].nalu);for(e.writeUint8(this.PPS.length),t=0;t<this.PPS.length;t++)e.writeUint16(this.PPS[t].length),e.writeUint8Array(this.PPS[t].nalu);this.ext&&e.writeUint8Array(this.ext)},c.co64Box.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.chunk_offsets.length,this.writeHeader(e),e.writeUint32(this.chunk_offsets.length),t=0;t<this.chunk_offsets.length;t++)e.writeUint64(this.chunk_offsets[t])},c.cslgBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20,this.writeHeader(e),e.writeInt32(this.compositionToDTSShift),e.writeInt32(this.leastDecodeToDisplayDelta),e.writeInt32(this.greatestDecodeToDisplayDelta),e.writeInt32(this.compositionStartTime),e.writeInt32(this.compositionEndTime)},c.cttsBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(e),e.writeUint32(this.sample_counts.length),t=0;t<this.sample_counts.length;t++)e.writeUint32(this.sample_counts[t]),1===this.version?e.writeInt32(this.sample_offsets[t]):e.writeUint32(this.sample_offsets[t])},c.drefBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++)this.entries[t].write(e),this.size+=this.entries[t].size;s.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},c.elngBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=this.extended_language.length,this.writeHeader(e),e.writeString(this.extended_language)},c.elstBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+12*this.entries.length,this.writeHeader(e),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++){var r=this.entries[t];e.writeUint32(r.segment_duration),e.writeInt32(r.media_time),e.writeInt16(r.media_rate_integer),e.writeInt16(r.media_rate_fraction)}},c.emsgBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=16+this.message_data.length+(this.scheme_id_uri.length+1)+(this.value.length+1),this.writeHeader(e),e.writeCString(this.scheme_id_uri),e.writeCString(this.value),e.writeUint32(this.timescale),e.writeUint32(this.presentation_time_delta),e.writeUint32(this.event_duration),e.writeUint32(this.id),e.writeUint8Array(this.message_data)},c.ftypBox.prototype.write=function(e){this.size=8+4*this.compatible_brands.length,this.writeHeader(e),e.writeString(this.major_brand,null,4),e.writeUint32(this.minor_version);for(var t=0;t<this.compatible_brands.length;t++)e.writeString(this.compatible_brands[t],null,4)},c.hdlrBox.prototype.write=function(e){this.size=20+this.name.length+1,this.version=0,this.flags=0,this.writeHeader(e),e.writeUint32(0),e.writeString(this.handler,null,4),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeCString(this.name)},c.kindBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=this.schemeURI.length+1+(this.value.length+1),this.writeHeader(e),e.writeCString(this.schemeURI),e.writeCString(this.value)},c.mdhdBox.prototype.write=function(e){this.size=20,this.flags=0,this.version=0,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.timescale),e.writeUint32(this.duration),e.writeUint16(this.language),e.writeUint16(0)},c.mehdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.fragment_duration)},c.mfhdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.sequence_number)},c.mvhdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=96,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.timescale),e.writeUint32(this.duration),e.writeUint32(this.rate),e.writeUint16(this.volume<<8),e.writeUint16(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32Array(this.matrix),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(this.next_track_id)},c.SampleEntry.prototype.writeHeader=function(e){this.size=8,c.Box.prototype.writeHeader.call(this,e),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint16(this.data_reference_index)},c.SampleEntry.prototype.writeFooter=function(e){for(var t=0;t<this.boxes.length;t++)this.boxes[t].write(e),this.size+=this.boxes[t].size;s.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},c.SampleEntry.prototype.write=function(e){this.writeHeader(e),e.writeUint8Array(this.data),this.size+=this.data.length,s.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},c.VisualSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=70,e.writeUint16(0),e.writeUint16(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint16(this.width),e.writeUint16(this.height),e.writeUint32(this.horizresolution),e.writeUint32(this.vertresolution),e.writeUint32(0),e.writeUint16(this.frame_count),e.writeUint8(Math.min(31,this.compressorname.length)),e.writeString(this.compressorname,null,31),e.writeUint16(this.depth),e.writeInt16(-1),this.writeFooter(e)},c.AudioSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=20,e.writeUint32(0),e.writeUint32(0),e.writeUint16(this.channel_count),e.writeUint16(this.samplesize),e.writeUint16(0),e.writeUint16(0),e.writeUint32(this.samplerate<<16),this.writeFooter(e)},c.stppSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=this.namespace.length+1+this.schema_location.length+1+this.auxiliary_mime_types.length+1,e.writeCString(this.namespace),e.writeCString(this.schema_location),e.writeCString(this.auxiliary_mime_types),this.writeFooter(e)},c.SampleGroupEntry.prototype.write=function(e){e.writeUint8Array(this.data)},c.sbgpBox.prototype.write=function(e){this.version=1,this.flags=0,this.size=12+8*this.entries.length,this.writeHeader(e),e.writeString(this.grouping_type,null,4),e.writeUint32(this.grouping_type_parameter),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++){var r=this.entries[t];e.writeInt32(r.sample_count),e.writeInt32(r.group_description_index)}},c.sgpdBox.prototype.write=function(e){var t,r;for(this.flags=0,this.size=12,t=0;t<this.entries.length;t++)r=this.entries[t],1===this.version&&(0===this.default_length&&(this.size+=4),this.size+=r.data.length);for(this.writeHeader(e),e.writeString(this.grouping_type,null,4),1===this.version&&e.writeUint32(this.default_length),this.version>=2&&e.writeUint32(this.default_sample_description_index),e.writeUint32(this.entries.length),t=0;t<this.entries.length;t++)r=this.entries[t],1===this.version&&0===this.default_length&&e.writeUint32(r.description_length),r.write(e)},c.sidxBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20+12*this.references.length,this.writeHeader(e),e.writeUint32(this.reference_ID),e.writeUint32(this.timescale),e.writeUint32(this.earliest_presentation_time),e.writeUint32(this.first_offset),e.writeUint16(0),e.writeUint16(this.references.length);for(var t=0;t<this.references.length;t++){var r=this.references[t];e.writeUint32(r.reference_type<<31|r.referenced_size),e.writeUint32(r.subsegment_duration),e.writeUint32(r.starts_with_SAP<<31|r.SAP_type<<28|r.SAP_delta_time)}},c.smhdBox.prototype.write=function(e){this.version=0,this.flags=1,this.size=4,this.writeHeader(e),e.writeUint16(this.balance),e.writeUint16(0)},c.stcoBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+4*this.chunk_offsets.length,this.writeHeader(e),e.writeUint32(this.chunk_offsets.length),e.writeUint32Array(this.chunk_offsets)},c.stscBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+12*this.first_chunk.length,this.writeHeader(e),e.writeUint32(this.first_chunk.length),t=0;t<this.first_chunk.length;t++)e.writeUint32(this.first_chunk[t]),e.writeUint32(this.samples_per_chunk[t]),e.writeUint32(this.sample_description_index[t])},c.stsdBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=0,this.writeHeader(e),e.writeUint32(this.entries.length),this.size+=4,t=0;t<this.entries.length;t++)this.entries[t].write(e),this.size+=this.entries[t].size;s.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},c.stshBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.shadowed_sample_numbers.length,this.writeHeader(e),e.writeUint32(this.shadowed_sample_numbers.length),t=0;t<this.shadowed_sample_numbers.length;t++)e.writeUint32(this.shadowed_sample_numbers[t]),e.writeUint32(this.sync_sample_numbers[t])},c.stssBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+4*this.sample_numbers.length,this.writeHeader(e),e.writeUint32(this.sample_numbers.length),e.writeUint32Array(this.sample_numbers)},c.stszBox.prototype.write=function(e){var t,r=!0;if(this.version=0,this.flags=0,this.sample_sizes.length>0)for(t=0;t+1<this.sample_sizes.length;){if(this.sample_sizes[t+1]!==this.sample_sizes[0]){r=!1;break}t++}else r=!1;this.size=8,r||(this.size+=4*this.sample_sizes.length),this.writeHeader(e),r?e.writeUint32(this.sample_sizes[0]):e.writeUint32(0),e.writeUint32(this.sample_sizes.length),r||e.writeUint32Array(this.sample_sizes)},c.sttsBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(e),e.writeUint32(this.sample_counts.length),t=0;t<this.sample_counts.length;t++)e.writeUint32(this.sample_counts[t]),e.writeUint32(this.sample_deltas[t])},c.tfdtBox.prototype.write=function(e){var t=Math.pow(2,32)-1;this.version=this.baseMediaDecodeTime>t?1:0,this.flags=0,this.size=4,1===this.version&&(this.size+=4),this.writeHeader(e),1===this.version?e.writeUint64(this.baseMediaDecodeTime):e.writeUint32(this.baseMediaDecodeTime)},c.tfhdBox.prototype.write=function(e){this.version=0,this.size=4,this.flags&c.TFHD_FLAG_BASE_DATA_OFFSET&&(this.size+=8),this.flags&c.TFHD_FLAG_SAMPLE_DESC&&(this.size+=4),this.flags&c.TFHD_FLAG_SAMPLE_DUR&&(this.size+=4),this.flags&c.TFHD_FLAG_SAMPLE_SIZE&&(this.size+=4),this.flags&c.TFHD_FLAG_SAMPLE_FLAGS&&(this.size+=4),this.writeHeader(e),e.writeUint32(this.track_id),this.flags&c.TFHD_FLAG_BASE_DATA_OFFSET&&e.writeUint64(this.base_data_offset),this.flags&c.TFHD_FLAG_SAMPLE_DESC&&e.writeUint32(this.default_sample_description_index),this.flags&c.TFHD_FLAG_SAMPLE_DUR&&e.writeUint32(this.default_sample_duration),this.flags&c.TFHD_FLAG_SAMPLE_SIZE&&e.writeUint32(this.default_sample_size),this.flags&c.TFHD_FLAG_SAMPLE_FLAGS&&e.writeUint32(this.default_sample_flags)},c.tkhdBox.prototype.write=function(e){this.version=0,this.size=80,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.track_id),e.writeUint32(0),e.writeUint32(this.duration),e.writeUint32(0),e.writeUint32(0),e.writeInt16(this.layer),e.writeInt16(this.alternate_group),e.writeInt16(this.volume<<8),e.writeUint16(0),e.writeInt32Array(this.matrix),e.writeUint32(this.width),e.writeUint32(this.height)},c.trexBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20,this.writeHeader(e),e.writeUint32(this.track_id),e.writeUint32(this.default_sample_description_index),e.writeUint32(this.default_sample_duration),e.writeUint32(this.default_sample_size),e.writeUint32(this.default_sample_flags)},c.trunBox.prototype.write=function(e){this.version=0,this.size=4,this.flags&c.TRUN_FLAGS_DATA_OFFSET&&(this.size+=4),this.flags&c.TRUN_FLAGS_FIRST_FLAG&&(this.size+=4),this.flags&c.TRUN_FLAGS_DURATION&&(this.size+=4*this.sample_duration.length),this.flags&c.TRUN_FLAGS_SIZE&&(this.size+=4*this.sample_size.length),this.flags&c.TRUN_FLAGS_FLAGS&&(this.size+=4*this.sample_flags.length),this.flags&c.TRUN_FLAGS_CTS_OFFSET&&(this.size+=4*this.sample_composition_time_offset.length),this.writeHeader(e),e.writeUint32(this.sample_count),this.flags&c.TRUN_FLAGS_DATA_OFFSET&&(this.data_offset_position=e.getPosition(),e.writeInt32(this.data_offset)),this.flags&c.TRUN_FLAGS_FIRST_FLAG&&e.writeUint32(this.first_sample_flags);for(var t=0;t<this.sample_count;t++)this.flags&c.TRUN_FLAGS_DURATION&&e.writeUint32(this.sample_duration[t]),this.flags&c.TRUN_FLAGS_SIZE&&e.writeUint32(this.sample_size[t]),this.flags&c.TRUN_FLAGS_FLAGS&&e.writeUint32(this.sample_flags[t]),this.flags&c.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?e.writeUint32(this.sample_composition_time_offset[t]):e.writeInt32(this.sample_composition_time_offset[t]))},c["url Box"].prototype.write=function(e){this.version=0,this.location?(this.flags=0,this.size=this.location.length+1):(this.flags=1,this.size=0),this.writeHeader(e),this.location&&e.writeCString(this.location)},c["urn Box"].prototype.write=function(e){this.version=0,this.flags=0,this.size=this.name.length+1+(this.location?this.location.length+1:0),this.writeHeader(e),e.writeCString(this.name),this.location&&e.writeCString(this.location)},c.vmhdBox.prototype.write=function(e){this.version=0,this.flags=1,this.size=8,this.writeHeader(e),e.writeUint16(this.graphicsmode),e.writeUint16Array(this.opcolor)},c.cttsBox.prototype.unpack=function(e){var t,r,i;for(i=0,t=0;t<this.sample_counts.length;t++)for(r=0;r<this.sample_counts[t];r++)e[i].pts=e[i].dts+this.sample_offsets[t],i++},c.sttsBox.prototype.unpack=function(e){var t,r,i;for(i=0,t=0;t<this.sample_counts.length;t++)for(r=0;r<this.sample_counts[t];r++)e[i].dts=0===i?0:e[i-1].dts+this.sample_deltas[t],i++},c.stcoBox.prototype.unpack=function(e){var t;for(t=0;t<this.chunk_offsets.length;t++)e[t].offset=this.chunk_offsets[t]},c.stscBox.prototype.unpack=function(e){var t,r,i,n,s;for(n=0,s=0,t=0;t<this.first_chunk.length;t++)for(r=0;r<(t+1<this.first_chunk.length?this.first_chunk[t+1]:1/0);r++)for(s++,i=0;i<this.samples_per_chunk[t];i++){if(!e[n])return;e[n].description_index=this.sample_description_index[t],e[n].chunk_index=s,n++}},c.stszBox.prototype.unpack=function(e){var t;for(t=0;t<this.sample_sizes.length;t++)e[t].size=this.sample_sizes[t]},c.DIFF_BOXES_PROP_NAMES=["boxes","entries","references","subsamples","items","item_infos","extents","associations","subsegments","ranges","seekLists","seekPoints","esd","levels"],c.DIFF_PRIMITIVE_ARRAY_PROP_NAMES=["compatible_brands","matrix","opcolor","sample_counts","sample_counts","sample_deltas","first_chunk","samples_per_chunk","sample_sizes","chunk_offsets","sample_offsets","sample_description_index","sample_duration"],c.boxEqualFields=function(e,t){if(e&&!t)return!1;var r;for(r in e)if(!(c.DIFF_BOXES_PROP_NAMES.indexOf(r)>-1||e[r]instanceof c.Box||t[r]instanceof c.Box||void 0===e[r]||void 0===t[r]||"function"==typeof e[r]||"function"==typeof t[r]||e.subBoxNames&&e.subBoxNames.indexOf(r.slice(0,4))>-1||t.subBoxNames&&t.subBoxNames.indexOf(r.slice(0,4))>-1||"data"===r||"start"===r||"size"===r||"creation_time"===r||"modification_time"===r||c.DIFF_PRIMITIVE_ARRAY_PROP_NAMES.indexOf(r)>-1||e[r]===t[r]))return!1;return!0},c.boxEqual=function(e,t){if(!c.boxEqualFields(e,t))return!1;for(var r=0;r<c.DIFF_BOXES_PROP_NAMES.length;r++){var i=c.DIFF_BOXES_PROP_NAMES[r];if(e[i]&&t[i]&&!c.boxEqual(e[i],t[i]))return!1}return!0};var f=function(){};f.prototype.parseSample=function(e){var t,r={};r.resources=[];var i=new o(e.data.buffer);if(e.subsamples&&0!==e.subsamples.length){if(r.documentString=i.readString(e.subsamples[0].size),e.subsamples.length>1)for(t=1;t<e.subsamples.length;t++)r.resources[t]=i.readUint8Array(e.subsamples[t].size)}else r.documentString=i.readString(e.data.length);return"undefined"!=typeof DOMParser&&(r.document=(new DOMParser).parseFromString(r.documentString,"application/xml")),r};var h=function(){};h.prototype.parseSample=function(e){return new o(e.data.buffer).readString(e.data.length)},h.prototype.parseConfig=function(e){var t=new o(e.buffer);return t.readUint32(),t.readCString()},t.XMLSubtitlein4Parser=f,t.Textin4Parser=h;var p=function(e){this.stream=e||new d,this.boxes=[],this.mdats=[],this.moofs=[],this.isProgressive=!1,this.moovStartFound=!1,this.onMoovStart=null,this.moovStartSent=!1,this.onReady=null,this.readySent=!1,this.onSegment=null,this.onSamples=null,this.onError=null,this.sampleListBuilt=!1,this.fragmentedTracks=[],this.extractedTracks=[],this.isFragmentationInitialized=!1,this.sampleProcessingStarted=!1,this.nextMoofNumber=0,this.itemListBuilt=!1,this.onSidx=null,this.sidxSent=!1};p.prototype.setSegmentOptions=function(e,t,r){var i=this.getTrackById(e);if(i){var n={};this.fragmentedTracks.push(n),n.id=e,n.user=t,n.trak=i,i.nextSample=0,n.segmentStream=null,n.nb_samples=1e3,n.rapAlignement=!0,r&&(r.nbSamples&&(n.nb_samples=r.nbSamples),r.rapAlignement&&(n.rapAlignement=r.rapAlignement))}},p.prototype.unsetSegmentOptions=function(e){for(var t=-1,r=0;r<this.fragmentedTracks.length;r++){this.fragmentedTracks[r].id==e&&(t=r)}t>-1&&this.fragmentedTracks.splice(t,1)},p.prototype.setExtractionOptions=function(e,t,r){var i=this.getTrackById(e);if(i){var n={};this.extractedTracks.push(n),n.id=e,n.user=t,n.trak=i,i.nextSample=0,n.nb_samples=1e3,n.samples=[],r&&r.nbSamples&&(n.nb_samples=r.nbSamples)}},p.prototype.unsetExtractionOptions=function(e){for(var t=-1,r=0;r<this.extractedTracks.length;r++){this.extractedTracks[r].id==e&&(t=r)}t>-1&&this.extractedTracks.splice(t,1)},p.prototype.parse=function(){var e,t;if(!this.restoreParsePosition||this.restoreParsePosition())for(;;){if(this.hasIncompleteMdat&&this.hasIncompleteMdat()){if(this.processIncompleteMdat())continue;return}if(this.saveParsePosition&&this.saveParsePosition(),(e=c.parseOneBox(this.stream,false)).code===c.ERR_NOT_ENOUGH_DATA){if(this.processIncompleteBox){if(this.processIncompleteBox(e))continue;return}return}var r;switch(r="uuid"!==(t=e.box).type?t.type:t.uuid,this.boxes.push(t),r){case"mdat":this.mdats.push(t);break;case"moof":this.moofs.push(t);break;case"moov":this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0);default:void 0!==this[r]&&s.warn("ISOFile","Duplicate Box of type: "+r+", overriding previous occurrence"),this[r]=t}this.updateUsedBytes&&this.updateUsedBytes(t,e)}},p.prototype.checkBuffer=function(e){if(null==e)throw"Buffer must be defined and non empty";if(void 0===e.fileStart)throw"Buffer must have a fileStart property";return 0===e.byteLength?(s.warn("ISOFile","Ignoring empty buffer (fileStart: "+e.fileStart+")"),this.stream.logBufferLevel(),!1):(s.info("ISOFile","Processing buffer (fileStart: "+e.fileStart+")"),e.usedBytes=0,this.stream.insertBuffer(e),this.stream.logBufferLevel(),!!this.stream.initialized()||(s.warn("ISOFile","Not ready to start parsing"),!1))},p.prototype.appendBuffer=function(e,t){var r;if(this.checkBuffer(e))return this.parse(),this.moovStartFound&&!this.moovStartSent&&(this.moovStartSent=!0,this.onMoovStart&&this.onMoovStart()),this.moov?(this.sampleListBuilt||(this.buildSampleLists(),this.sampleListBuilt=!0),this.updateSampleLists(),this.onReady&&!this.readySent&&(this.readySent=!0,this.onReady(this.getInfo())),this.processSamples(t),this.nextSeekPosition?(r=this.nextSeekPosition,this.nextSeekPosition=void 0):r=this.nextParsePosition,this.stream.getEndFilePositionAfter&&(r=this.stream.getEndFilePositionAfter(r))):r=this.nextParsePosition?this.nextParsePosition:0,this.sidx&&this.onSidx&&!this.sidxSent&&(this.onSidx(this.sidx),this.sidxSent=!0),this.meta&&(this.flattenItemInfo&&!this.itemListBuilt&&(this.flattenItemInfo(),this.itemListBuilt=!0),this.processItems&&this.processItems(this.onItem)),this.stream.cleanBuffers&&(s.info("ISOFile","Done processing buffer (fileStart: "+e.fileStart+") - next buffer to fetch should have a fileStart position of "+r),this.stream.logBufferLevel(),this.stream.cleanBuffers(),this.stream.logBufferLevel(!0),s.info("ISOFile","Sample data size in memory: "+this.getAllocatedSampleDataSize())),r},p.prototype.getInfo=function(){var e,t,r,i,n,s,o={},a=new Date("1904-01-01T00:00:00Z").getTime();if(this.moov)for(o.hasMoov=!0,o.duration=this.moov.mvhd.duration,o.timescale=this.moov.mvhd.timescale,o.isFragmented=null!=this.moov.mvex,o.isFragmented&&this.moov.mvex.mehd&&(o.fragment_duration=this.moov.mvex.mehd.fragment_duration),o.isProgressive=this.isProgressive,o.hasIOD=null!=this.moov.iods,o.brands=[],o.brands.push(this.ftyp.major_brand),o.brands=o.brands.concat(this.ftyp.compatible_brands),o.created=new Date(a+1e3*this.moov.mvhd.creation_time),o.modified=new Date(a+1e3*this.moov.mvhd.modification_time),o.tracks=[],o.audioTracks=[],o.videoTracks=[],o.subtitleTracks=[],o.metadataTracks=[],o.hintTracks=[],o.otherTracks=[],e=0;e<this.moov.traks.length;e++){if(s=(r=this.moov.traks[e]).mdia.minf.stbl.stsd.entries[0],i={},o.tracks.push(i),i.id=r.tkhd.track_id,i.name=r.mdia.hdlr.name,i.references=[],r.tref)for(t=0;t<r.tref.boxes.length;t++)n={},i.references.push(n),n.type=r.tref.boxes[t].type,n.track_ids=r.tref.boxes[t].track_ids;r.edts&&(i.edits=r.edts.elst.entries),i.created=new Date(a+1e3*r.tkhd.creation_time),i.modified=new Date(a+1e3*r.tkhd.modification_time),i.movie_duration=r.tkhd.duration,i.movie_timescale=o.timescale,i.layer=r.tkhd.layer,i.alternate_group=r.tkhd.alternate_group,i.volume=r.tkhd.volume,i.matrix=r.tkhd.matrix,i.track_width=r.tkhd.width/65536,i.track_height=r.tkhd.height/65536,i.timescale=r.mdia.mdhd.timescale,i.cts_shift=r.mdia.minf.stbl.cslg,i.duration=r.mdia.mdhd.duration,i.samples_duration=r.samples_duration,i.codec=s.getCodec(),i.kind=r.udta&&r.udta.kinds.length?r.udta.kinds[0]:{schemeURI:"",value:""},i.language=r.mdia.elng?r.mdia.elng.extended_language:r.mdia.mdhd.languageString,i.nb_samples=r.samples.length,i.size=r.samples_size,i.bitrate=8*i.size*i.timescale/i.samples_duration,s.isAudio()?(i.type="audio",o.audioTracks.push(i),i.audio={},i.audio.sample_rate=s.getSampleRate(),i.audio.channel_count=s.getChannelCount(),i.audio.sample_size=s.getSampleSize()):s.isVideo()?(i.type="video",o.videoTracks.push(i),i.video={},i.video.width=s.getWidth(),i.video.height=s.getHeight()):s.isSubtitle()?(i.type="subtitles",o.subtitleTracks.push(i)):s.isHint()?(i.type="metadata",o.hintTracks.push(i)):s.isMetadata()?(i.type="metadata",o.metadataTracks.push(i)):(i.type="metadata",o.otherTracks.push(i))}else o.hasMoov=!1;if(o.mime="",o.hasMoov&&o.tracks){for(o.videoTracks&&o.videoTracks.length>0?o.mime+='video/mp4; codecs="':o.audioTracks&&o.audioTracks.length>0?o.mime+='audio/mp4; codecs="':o.mime+='application/mp4; codecs="',e=0;e<o.tracks.length;e++)0!==e&&(o.mime+=","),o.mime+=o.tracks[e].codec;o.mime+='"; profiles="',o.mime+=this.ftyp.compatible_brands.join(),o.mime+='"'}return o},p.prototype.processSamples=function(e){var t,r;if(this.sampleProcessingStarted){if(this.isFragmentationInitialized&&null!==this.onSegment)for(t=0;t<this.fragmentedTracks.length;t++){var i=this.fragmentedTracks[t];for(r=i.trak;r.nextSample<r.samples.length&&this.sampleProcessingStarted;){s.debug("ISOFile","Creating media fragment on track #"+i.id+" for sample "+r.nextSample);var n=this.createFragment(i.id,r.nextSample,i.segmentStream);if(!n)break;if(i.segmentStream=n,r.nextSample++,(r.nextSample%i.nb_samples==0||e||r.nextSample>=r.samples.length)&&(s.info("ISOFile","Sending fragmented data on track #"+i.id+" for samples ["+Math.max(0,r.nextSample-i.nb_samples)+","+(r.nextSample-1)+"]"),s.info("ISOFile","Sample data size in memory: "+this.getAllocatedSampleDataSize()),this.onSegment&&this.onSegment(i.id,i.user,i.segmentStream.buffer,r.nextSample,e||r.nextSample>=r.samples.length),i.segmentStream=null,i!==this.fragmentedTracks[t]))break}}if(null!==this.onSamples)for(t=0;t<this.extractedTracks.length;t++){var o=this.extractedTracks[t];for(r=o.trak;r.nextSample<r.samples.length&&this.sampleProcessingStarted;){s.debug("ISOFile","Exporting on track #"+o.id+" sample #"+r.nextSample);var a=this.getSample(r,r.nextSample);if(!a)break;if(r.nextSample++,o.samples.push(a),(r.nextSample%o.nb_samples==0||r.nextSample>=r.samples.length)&&(s.debug("ISOFile","Sending samples on track #"+o.id+" for sample "+r.nextSample),this.onSamples&&this.onSamples(o.id,o.user,o.samples),o.samples=[],o!==this.extractedTracks[t]))break}}}},p.prototype.getBox=function(e){var t=this.getBoxes(e,!0);return t.length?t[0]:null},p.prototype.getBoxes=function(e,t){var r=[];return p._sweep.call(this,e,r,t),r},p._sweep=function(e,t,r){for(var i in this.type&&this.type==e&&t.push(this),this.boxes){if(t.length&&r)return;p._sweep.call(this.boxes[i],e,t,r)}},p.prototype.getTrackSamplesInfo=function(e){var t=this.getTrackById(e);return t?t.samples:void 0},p.prototype.getTrackSample=function(e,t){var r=this.getTrackById(e);return this.getSample(r,t)},p.prototype.releaseUsedSamples=function(e,t){var r=0,i=this.getTrackById(e);i.lastValidSample||(i.lastValidSample=0);for(var n=i.lastValidSample;n<t;n++)r+=this.releaseSample(i,n);s.info("ISOFile","Track #"+e+" released samples up to "+t+" (released size: "+r+", remaining: "+this.samplesDataSize+")"),i.lastValidSample=t},p.prototype.start=function(){this.sampleProcessingStarted=!0,this.processSamples(!1)},p.prototype.stop=function(){this.sampleProcessingStarted=!1},p.prototype.flush=function(){s.info("ISOFile","Flushing remaining samples"),this.updateSampleLists(),this.processSamples(!0),this.stream.cleanBuffers(),this.stream.logBufferLevel(!0)},p.prototype.seekTrack=function(e,t,r){var i,n,o,a,l=0,d=0;if(0===r.samples.length)return s.info("ISOFile","No sample in track, cannot seek! Using time "+s.getDurationString(0,1)+" and offset: 0"),{offset:0,time:0};for(i=0;i<r.samples.length;i++){if(n=r.samples[i],0===i)d=0,a=n.timescale;else if(n.cts>e*n.timescale){d=i-1;break}t&&n.is_sync&&(l=i)}for(t&&(d=l),e=r.samples[d].cts,r.nextSample=d;r.samples[d].alreadyRead===r.samples[d].size&&r.samples[d+1];)d++;return o=r.samples[d].offset+r.samples[d].alreadyRead,s.info("ISOFile","Seeking to "+(t?"RAP":"")+" sample #"+r.nextSample+" on track "+r.tkhd.track_id+", time "+s.getDurationString(e,a)+" and offset: "+o),{offset:o,time:e/a}},p.prototype.seek=function(e,t){var r,i,n,o=this.moov,a={offset:1/0,time:1/0};if(this.moov){for(n=0;n<o.traks.length;n++)r=o.traks[n],(i=this.seekTrack(e,t,r)).offset<a.offset&&(a.offset=i.offset),i.time<a.time&&(a.time=i.time);return s.info("ISOFile","Seeking at time "+s.getDurationString(a.time,1)+" needs a buffer with a fileStart position of "+a.offset),a.offset===1/0?a={offset:this.nextParsePosition,time:0}:a.offset=this.stream.getEndFilePositionAfter(a.offset),s.info("ISOFile","Adjusted seek position (after checking data already in buffer): "+a.offset),a}throw"Cannot seek: moov not received!"},p.prototype.equal=function(e){for(var t=0;t<this.boxes.length&&t<e.boxes.length;){var r=this.boxes[t],i=e.boxes[t];if(!c.boxEqual(r,i))return!1;t++}return!0},t.ISOFile=p,p.prototype.lastBoxStartPosition=0,p.prototype.parsingMdat=null,p.prototype.nextParsePosition=0,p.prototype.discardMdatData=!1,p.prototype.processIncompleteBox=function(e){var t;return"mdat"===e.type?(t=new c[e.type+"Box"](e.size),this.parsingMdat=t,this.boxes.push(t),this.mdats.push(t),t.start=e.start,t.hdr_size=e.hdr_size,this.stream.addUsedBytes(t.hdr_size),this.lastBoxStartPosition=t.start+t.size,this.stream.seek(t.start+t.size,!1,this.discardMdatData)?(this.parsingMdat=null,!0):(this.moovStartFound?this.nextParsePosition=this.stream.findEndContiguousBuf():this.nextParsePosition=t.start+t.size,!1)):("moov"===e.type&&(this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0)),!!this.stream.mergeNextBuffer&&this.stream.mergeNextBuffer()?(this.nextParsePosition=this.stream.getEndPosition(),!0):(e.type?this.moovStartFound?this.nextParsePosition=this.stream.getEndPosition():this.nextParsePosition=this.stream.getPosition()+e.size:this.nextParsePosition=this.stream.getEndPosition(),!1))},p.prototype.hasIncompleteMdat=function(){return null!==this.parsingMdat},p.prototype.processIncompleteMdat=function(){var e;return e=this.parsingMdat,this.stream.seek(e.start+e.size,!1,this.discardMdatData)?(s.debug("ISOFile","Found 'mdat' end in buffered data"),this.parsingMdat=null,!0):(this.nextParsePosition=this.stream.findEndContiguousBuf(),!1)},p.prototype.restoreParsePosition=function(){return this.stream.seek(this.lastBoxStartPosition,!0,this.discardMdatData)},p.prototype.saveParsePosition=function(){this.lastBoxStartPosition=this.stream.getPosition()},p.prototype.updateUsedBytes=function(e,t){this.stream.addUsedBytes&&("mdat"===e.type?(this.stream.addUsedBytes(e.hdr_size),this.discardMdatData&&this.stream.addUsedBytes(e.size-e.hdr_size)):this.stream.addUsedBytes(e.size))},p.prototype.add=c.Box.prototype.add,p.prototype.addBox=c.Box.prototype.addBox,p.prototype.init=function(e){var t=e||{};this.add("ftyp").set("major_brand",t.brands&&t.brands[0]||"iso4").set("minor_version",0).set("compatible_brands",t.brands||["iso4"]);var r=this.add("moov");return r.add("mvhd").set("timescale",t.timescale||600).set("rate",t.rate||65536).set("creation_time",0).set("modification_time",0).set("duration",t.duration||0).set("volume",t.width?0:256).set("matrix",[65536,0,0,0,65536,0,0,0,1073741824]).set("next_track_id",1),r.add("mvex"),this},p.prototype.addTrack=function(e){this.moov||this.init(e);var t=e||{};t.width=t.width||320,t.height=t.height||320,t.id=t.id||this.moov.mvhd.next_track_id,t.type=t.type||"avc1";var r=this.moov.add("trak");this.moov.mvhd.next_track_id=t.id+1,r.add("tkhd").set("flags",c.TKHD_FLAG_ENABLED|c.TKHD_FLAG_IN_MOVIE|c.TKHD_FLAG_IN_PREVIEW).set("creation_time",0).set("modification_time",0).set("track_id",t.id).set("duration",t.duration||0).set("layer",t.layer||0).set("alternate_group",0).set("volume",1).set("matrix",[0,0,0,0,0,0,0,0,0]).set("width",t.width<<16).set("height",t.height<<16);var i=r.add("mdia");i.add("mdhd").set("creation_time",0).set("modification_time",0).set("timescale",t.timescale||1).set("duration",t.media_duration||0).set("language",t.language||"und"),i.add("hdlr").set("handler",t.hdlr||"vide").set("name",t.name||"Track created with MP4Box.js"),i.add("elng").set("extended_language",t.language||"fr-FR");var n=i.add("minf");if(void 0!==c[t.type+"SampleEntry"]){var s=new c[t.type+"SampleEntry"];s.data_reference_index=1;var a="";for(var l in c.sampleEntryCodes)for(var d=c.sampleEntryCodes[l],u=0;u<d.length;u++)if(d.indexOf(t.type)>-1){a=l;break}switch(a){case"Visual":if(n.add("vmhd").set("graphicsmode",0).set("opcolor",[0,0,0]),s.set("width",t.width).set("height",t.height).set("horizresolution",72<<16).set("vertresolution",72<<16).set("frame_count",1).set("compressorname",t.type+" Compressor").set("depth",24),t.avcDecoderConfigRecord){var f=new c.avcCBox,h=new o(t.avcDecoderConfigRecord);f.parse(h),s.addBox(f)}break;case"Audio":n.add("smhd").set("balance",t.balance||0),s.set("channel_count",t.channel_count||2).set("samplesize",t.samplesize||16).set("samplerate",t.samplerate||65536);break;case"Hint":n.add("hmhd");break;case"Subtitle":if(n.add("sthd"),"stpp"===t.type)s.set("namespace",t.namespace||"nonamespace").set("schema_location",t.schema_location||"").set("auxiliary_mime_types",t.auxiliary_mime_types||"");break;default:n.add("nmhd")}t.description&&s.addBox(t.description),t.description_boxes&&t.description_boxes.forEach((function(e){s.addBox(e)})),n.add("dinf").add("dref").addEntry((new c["url Box"]).set("flags",1));var p=n.add("stbl");return p.add("stsd").addEntry(s),p.add("stts").set("sample_counts",[]).set("sample_deltas",[]),p.add("stsc").set("first_chunk",[]).set("samples_per_chunk",[]).set("sample_description_index",[]),p.add("stco").set("chunk_offsets",[]),p.add("stsz").set("sample_sizes",[]),this.moov.mvex.add("trex").set("track_id",t.id).set("default_sample_description_index",t.default_sample_description_index||1).set("default_sample_duration",t.default_sample_duration||0).set("default_sample_size",t.default_sample_size||0).set("default_sample_flags",t.default_sample_flags||0),this.buildTrakSampleLists(r),t.id}},c.Box.prototype.computeSize=function(e){var t=e||new a;t.endianness=a.BIG_ENDIAN,this.write(t)},p.prototype.addSample=function(e,t,r){var i=r||{},n={},s=this.getTrackById(e);if(null!==s){n.number=s.samples.length,n.track_id=s.tkhd.track_id,n.timescale=s.mdia.mdhd.timescale,n.description_index=i.sample_description_index?i.sample_description_index-1:0,n.description=s.mdia.minf.stbl.stsd.entries[n.description_index],n.data=t,n.size=t.byteLength,n.alreadyRead=n.size,n.duration=i.duration||1,n.cts=i.cts||0,n.dts=i.dts||0,n.is_sync=i.is_sync||!1,n.is_leading=i.is_leading||0,n.depends_on=i.depends_on||0,n.is_depended_on=i.is_depended_on||0,n.has_redundancy=i.has_redundancy||0,n.degradation_priority=i.degradation_priority||0,n.offset=0,n.subsamples=i.subsamples,s.samples.push(n),s.samples_size+=n.size,s.samples_duration+=n.duration,s.first_dts||(s.first_dts=i.dts),this.processSamples();var o=this.createSingleSampleMoof(n);return this.addBox(o),o.computeSize(),o.trafs[0].truns[0].data_offset=o.size+8,this.add("mdat").data=new Uint8Array(t),n}},p.prototype.createSingleSampleMoof=function(e){var t=0;t=e.is_sync?1<<25:65536;var r=new c.moofBox;r.add("mfhd").set("sequence_number",this.nextMoofNumber),this.nextMoofNumber++;var i=r.add("traf"),n=this.getTrackById(e.track_id);return i.add("tfhd").set("track_id",e.track_id).set("flags",c.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),i.add("tfdt").set("baseMediaDecodeTime",e.dts-(n.first_dts||0)),i.add("trun").set("flags",c.TRUN_FLAGS_DATA_OFFSET|c.TRUN_FLAGS_DURATION|c.TRUN_FLAGS_SIZE|c.TRUN_FLAGS_FLAGS|c.TRUN_FLAGS_CTS_OFFSET).set("data_offset",0).set("first_sample_flags",0).set("sample_count",1).set("sample_duration",[e.duration]).set("sample_size",[e.size]).set("sample_flags",[t]).set("sample_composition_time_offset",[e.cts-e.dts]),r},p.prototype.lastMoofIndex=0,p.prototype.samplesDataSize=0,p.prototype.resetTables=function(){var e,t,r,i,n,s;for(this.initial_duration=this.moov.mvhd.duration,this.moov.mvhd.duration=0,e=0;e<this.moov.traks.length;e++){(t=this.moov.traks[e]).tkhd.duration=0,t.mdia.mdhd.duration=0,(t.mdia.minf.stbl.stco||t.mdia.minf.stbl.co64).chunk_offsets=[],(r=t.mdia.minf.stbl.stsc).first_chunk=[],r.samples_per_chunk=[],r.sample_description_index=[],(t.mdia.minf.stbl.stsz||t.mdia.minf.stbl.stz2).sample_sizes=[],(i=t.mdia.minf.stbl.stts).sample_counts=[],i.sample_deltas=[],(n=t.mdia.minf.stbl.ctts)&&(n.sample_counts=[],n.sample_offsets=[]),s=t.mdia.minf.stbl.stss;var o=t.mdia.minf.stbl.boxes.indexOf(s);-1!=o&&(t.mdia.minf.stbl.boxes[o]=null)}},p.initSampleGroups=function(e,t,r,i,n){var s,o,a,l;function d(e,t,r){this.grouping_type=e,this.grouping_type_parameter=t,this.sbgp=r,this.last_sample_in_run=-1,this.entry_index=-1}for(t&&(t.sample_groups_info=[]),e.sample_groups_info||(e.sample_groups_info=[]),o=0;o<r.length;o++){for(l=r[o].grouping_type+"/"+r[o].grouping_type_parameter,a=new d(r[o].grouping_type,r[o].grouping_type_parameter,r[o]),t&&(t.sample_groups_info[l]=a),e.sample_groups_info[l]||(e.sample_groups_info[l]=a),s=0;s<i.length;s++)i[s].grouping_type===r[o].grouping_type&&(a.description=i[s],a.description.used=!0);if(n)for(s=0;s<n.length;s++)n[s].grouping_type===r[o].grouping_type&&(a.fragment_description=n[s],a.fragment_description.used=!0,a.is_fragment=!0)}if(t){if(n)for(o=0;o<n.length;o++)!n[o].used&&n[o].version>=2&&(l=n[o].grouping_type+"/0",(a=new d(n[o].grouping_type,0)).is_fragment=!0,t.sample_groups_info[l]||(t.sample_groups_info[l]=a))}else for(o=0;o<i.length;o++)!i[o].used&&i[o].version>=2&&(l=i[o].grouping_type+"/0",a=new d(i[o].grouping_type,0),e.sample_groups_info[l]||(e.sample_groups_info[l]=a))},p.setSampleGroupProperties=function(e,t,r,i){var n,s;for(n in t.sample_groups=[],i){var o;if(t.sample_groups[n]={},t.sample_groups[n].grouping_type=i[n].grouping_type,t.sample_groups[n].grouping_type_parameter=i[n].grouping_type_parameter,r>=i[n].last_sample_in_run&&(i[n].last_sample_in_run<0&&(i[n].last_sample_in_run=0),i[n].entry_index++,i[n].entry_index<=i[n].sbgp.entries.length-1&&(i[n].last_sample_in_run+=i[n].sbgp.entries[i[n].entry_index].sample_count)),i[n].entry_index<=i[n].sbgp.entries.length-1?t.sample_groups[n].group_description_index=i[n].sbgp.entries[i[n].entry_index].group_description_index:t.sample_groups[n].group_description_index=-1,0!==t.sample_groups[n].group_description_index)o=i[n].fragment_description?i[n].fragment_description:i[n].description,t.sample_groups[n].group_description_index>0?(s=t.sample_groups[n].group_description_index>65535?(t.sample_groups[n].group_description_index>>16)-1:t.sample_groups[n].group_description_index-1,o&&s>=0&&(t.sample_groups[n].description=o.entries[s])):o&&o.version>=2&&o.default_group_description_index>0&&(t.sample_groups[n].description=o.entries[o.default_group_description_index-1])}},p.process_sdtp=function(e,t,r){t&&(e?(t.is_leading=e.is_leading[r],t.depends_on=e.sample_depends_on[r],t.is_depended_on=e.sample_is_depended_on[r],t.has_redundancy=e.sample_has_redundancy[r]):(t.is_leading=0,t.depends_on=0,t.is_depended_on=0,t.has_redundancy=0))},p.prototype.buildSampleLists=function(){var e,t;for(e=0;e<this.moov.traks.length;e++)t=this.moov.traks[e],this.buildTrakSampleLists(t)},p.prototype.buildTrakSampleLists=function(e){var t,r,i,n,s,o,a,l,d,u,c,f,h,m,_,y,g,v,b,w,E,S,U,x;if(e.samples=[],e.samples_duration=0,e.samples_size=0,r=e.mdia.minf.stbl.stco||e.mdia.minf.stbl.co64,i=e.mdia.minf.stbl.stsc,n=e.mdia.minf.stbl.stsz||e.mdia.minf.stbl.stz2,s=e.mdia.minf.stbl.stts,o=e.mdia.minf.stbl.ctts,a=e.mdia.minf.stbl.stss,l=e.mdia.minf.stbl.stsd,d=e.mdia.minf.stbl.subs,f=e.mdia.minf.stbl.stdp,u=e.mdia.minf.stbl.sbgps,c=e.mdia.minf.stbl.sgpds,v=-1,b=-1,w=-1,E=-1,S=0,U=0,x=0,p.initSampleGroups(e,null,u,c),void 0!==n){for(t=0;t<n.sample_sizes.length;t++){var A={};A.number=t,A.track_id=e.tkhd.track_id,A.timescale=e.mdia.mdhd.timescale,A.alreadyRead=0,e.samples[t]=A,A.size=n.sample_sizes[t],e.samples_size+=A.size,0===t?(m=1,h=0,A.chunk_index=m,A.chunk_run_index=h,g=i.samples_per_chunk[h],y=0,_=h+1<i.first_chunk.length?i.first_chunk[h+1]-1:1/0):t<g?(A.chunk_index=m,A.chunk_run_index=h):(m++,A.chunk_index=m,y=0,m<=_||(_=++h+1<i.first_chunk.length?i.first_chunk[h+1]-1:1/0),A.chunk_run_index=h,g+=i.samples_per_chunk[h]),A.description_index=i.sample_description_index[A.chunk_run_index]-1,A.description=l.entries[A.description_index],A.offset=r.chunk_offsets[A.chunk_index-1]+y,y+=A.size,t>v&&(b++,v<0&&(v=0),v+=s.sample_counts[b]),t>0?(e.samples[t-1].duration=s.sample_deltas[b],e.samples_duration+=e.samples[t-1].duration,A.dts=e.samples[t-1].dts+e.samples[t-1].duration):A.dts=0,o?(t>=w&&(E++,w<0&&(w=0),w+=o.sample_counts[E]),A.cts=e.samples[t].dts+o.sample_offsets[E]):A.cts=A.dts,a?(t==a.sample_numbers[S]-1?(A.is_sync=!0,S++):(A.is_sync=!1,A.degradation_priority=0),d&&d.entries[U].sample_delta+x==t+1&&(A.subsamples=d.entries[U].subsamples,x+=d.entries[U].sample_delta,U++)):A.is_sync=!0,p.process_sdtp(e.mdia.minf.stbl.sdtp,A,A.number),A.degradation_priority=f?f.priority[t]:0,d&&d.entries[U].sample_delta+x==t&&(A.subsamples=d.entries[U].subsamples,x+=d.entries[U].sample_delta),(u.length>0||c.length>0)&&p.setSampleGroupProperties(e,A,t,e.sample_groups_info)}t>0&&(e.samples[t-1].duration=Math.max(e.mdia.mdhd.duration-e.samples[t-1].dts,0),e.samples_duration+=e.samples[t-1].duration)}},p.prototype.updateSampleLists=function(){var e,t,r,i,n,s,o,a,l,d,u,f,h,m,_;if(void 0!==this.moov)for(;this.lastMoofIndex<this.moofs.length;)if(l=this.moofs[this.lastMoofIndex],this.lastMoofIndex++,"moof"==l.type)for(d=l,e=0;e<d.trafs.length;e++){for(u=d.trafs[e],f=this.getTrackById(u.tfhd.track_id),h=this.getTrexById(u.tfhd.track_id),i=u.tfhd.flags&c.TFHD_FLAG_SAMPLE_DESC?u.tfhd.default_sample_description_index:h?h.default_sample_description_index:1,n=u.tfhd.flags&c.TFHD_FLAG_SAMPLE_DUR?u.tfhd.default_sample_duration:h?h.default_sample_duration:0,s=u.tfhd.flags&c.TFHD_FLAG_SAMPLE_SIZE?u.tfhd.default_sample_size:h?h.default_sample_size:0,o=u.tfhd.flags&c.TFHD_FLAG_SAMPLE_FLAGS?u.tfhd.default_sample_flags:h?h.default_sample_flags:0,u.sample_number=0,u.sbgps.length>0&&p.initSampleGroups(f,u,u.sbgps,f.mdia.minf.stbl.sgpds,u.sgpds),t=0;t<u.truns.length;t++){var y=u.truns[t];for(r=0;r<y.sample_count;r++){(m={}).moof_number=this.lastMoofIndex,m.number_in_traf=u.sample_number,u.sample_number++,m.number=f.samples.length,u.first_sample_index=f.samples.length,f.samples.push(m),m.track_id=f.tkhd.track_id,m.timescale=f.mdia.mdhd.timescale,m.description_index=i-1,m.description=f.mdia.minf.stbl.stsd.entries[m.description_index],m.size=s,y.flags&c.TRUN_FLAGS_SIZE&&(m.size=y.sample_size[r]),f.samples_size+=m.size,m.duration=n,y.flags&c.TRUN_FLAGS_DURATION&&(m.duration=y.sample_duration[r]),f.samples_duration+=m.duration,f.first_traf_merged||r>0?m.dts=f.samples[f.samples.length-2].dts+f.samples[f.samples.length-2].duration:(u.tfdt?m.dts=u.tfdt.baseMediaDecodeTime:m.dts=0,f.first_traf_merged=!0),m.cts=m.dts,y.flags&c.TRUN_FLAGS_CTS_OFFSET&&(m.cts=m.dts+y.sample_composition_time_offset[r]),_=o,y.flags&c.TRUN_FLAGS_FLAGS?_=y.sample_flags[r]:0===r&&y.flags&c.TRUN_FLAGS_FIRST_FLAG&&(_=y.first_sample_flags),m.is_sync=!(_>>16&1),m.is_leading=_>>26&3,m.depends_on=_>>24&3,m.is_depended_on=_>>22&3,m.has_redundancy=_>>20&3,m.degradation_priority=65535&_;var g=!!(u.tfhd.flags&c.TFHD_FLAG_BASE_DATA_OFFSET),v=!!(u.tfhd.flags&c.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),b=!!(y.flags&c.TRUN_FLAGS_DATA_OFFSET),w=0;w=g?u.tfhd.base_data_offset:v||0===t?d.start:a,m.offset=0===t&&0===r?b?w+y.data_offset:w:a,a=m.offset+m.size,(u.sbgps.length>0||u.sgpds.length>0||f.mdia.minf.stbl.sbgps.length>0||f.mdia.minf.stbl.sgpds.length>0)&&p.setSampleGroupProperties(f,m,m.number_in_traf,u.sample_groups_info)}}if(u.subs){f.has_fragment_subsamples=!0;var E=u.first_sample_index;for(t=0;t<u.subs.entries.length;t++)E+=u.subs.entries[t].sample_delta,(m=f.samples[E-1]).subsamples=u.subs.entries[t].subsamples}}},p.prototype.getSample=function(e,t){var r,i=e.samples[t];if(!this.moov)return null;if(i.data){if(i.alreadyRead==i.size)return i}else i.data=new Uint8Array(i.size),i.alreadyRead=0,this.samplesDataSize+=i.size,s.debug("ISOFile","Allocating sample #"+t+" on track #"+e.tkhd.track_id+" of size "+i.size+" (total: "+this.samplesDataSize+")");for(;;){var n=this.stream.findPosition(!0,i.offset+i.alreadyRead,!1);if(!(n>-1))return null;var o=(r=this.stream.buffers[n]).byteLength-(i.offset+i.alreadyRead-r.fileStart);if(i.size-i.alreadyRead<=o)return s.debug("ISOFile","Getting sample #"+t+" data (alreadyRead: "+i.alreadyRead+" offset: "+(i.offset+i.alreadyRead-r.fileStart)+" read size: "+(i.size-i.alreadyRead)+" full size: "+i.size+")"),a.memcpy(i.data.buffer,i.alreadyRead,r,i.offset+i.alreadyRead-r.fileStart,i.size-i.alreadyRead),r.usedBytes+=i.size-i.alreadyRead,this.stream.logBufferLevel(),i.alreadyRead=i.size,i;if(0===o)return null;s.debug("ISOFile","Getting sample #"+t+" partial data (alreadyRead: "+i.alreadyRead+" offset: "+(i.offset+i.alreadyRead-r.fileStart)+" read size: "+o+" full size: "+i.size+")"),a.memcpy(i.data.buffer,i.alreadyRead,r,i.offset+i.alreadyRead-r.fileStart,o),i.alreadyRead+=o,r.usedBytes+=o,this.stream.logBufferLevel()}},p.prototype.releaseSample=function(e,t){var r=e.samples[t];return r.data?(this.samplesDataSize-=r.size,r.data=null,r.alreadyRead=0,r.size):0},p.prototype.getAllocatedSampleDataSize=function(){return this.samplesDataSize},p.prototype.getCodecs=function(){var e,t="";for(e=0;e<this.moov.traks.length;e++){e>0&&(t+=","),t+=this.moov.traks[e].mdia.minf.stbl.stsd.entries[0].getCodec()}return t},p.prototype.getTrexById=function(e){var t;if(!this.moov||!this.moov.mvex)return null;for(t=0;t<this.moov.mvex.trexs.length;t++){var r=this.moov.mvex.trexs[t];if(r.track_id==e)return r}return null},p.prototype.getTrackById=function(e){if(void 0===this.moov)return null;for(var t=0;t<this.moov.traks.length;t++){var r=this.moov.traks[t];if(r.tkhd.track_id==e)return r}return null},p.prototype.items=[],p.prototype.itemsDataSize=0,p.prototype.flattenItemInfo=function(){var e,t,r,i=this.items,n=this.meta;if(null!=n&&void 0!==n.hdlr&&void 0!==n.iinf){for(e=0;e<n.iinf.item_infos.length;e++)(r={}).id=n.iinf.item_infos[e].item_ID,i[r.id]=r,r.ref_to=[],r.name=n.iinf.item_infos[e].item_name,n.iinf.item_infos[e].protection_index>0&&(r.protection=n.ipro.protections[n.iinf.item_infos[e].protection_index-1]),n.iinf.item_infos[e].item_type?r.type=n.iinf.item_infos[e].item_type:r.type="mime",r.content_type=n.iinf.item_infos[e].content_type,r.content_encoding=n.iinf.item_infos[e].content_encoding;if(n.iloc)for(e=0;e<n.iloc.items.length;e++){var o=n.iloc.items[e];switch(r=i[o.item_ID],0!==o.data_reference_index&&(s.warn("Item storage with reference to other files: not supported"),r.source=n.dinf.boxes[o.data_reference_index-1]),o.construction_method){case 0:break;case 1:case 2:s.warn("Item storage with construction_method : not supported")}for(r.extents=[],r.size=0,t=0;t<o.extents.length;t++)r.extents[t]={},r.extents[t].offset=o.extents[t].extent_offset+o.base_offset,r.extents[t].length=o.extents[t].extent_length,r.extents[t].alreadyRead=0,r.size+=r.extents[t].length}if(n.pitm&&(i[n.pitm.item_id].primary=!0),n.iref)for(e=0;e<n.iref.references.length;e++){var a=n.iref.references[e];for(t=0;t<a.references.length;t++)i[a.from_item_ID].ref_to.push({type:a.type,id:a.references[t]})}if(n.iprp)for(var l=0;l<n.iprp.ipmas.length;l++){var d=n.iprp.ipmas[l];for(e=0;e<d.associations.length;e++){var u=d.associations[e];for(void 0===(r=i[u.id]).properties&&(r.properties={},r.properties.boxes=[]),t=0;t<u.props.length;t++){var c=u.props[t];if(c.property_index>0&&c.property_index-1<n.iprp.ipco.boxes.length){var f=n.iprp.ipco.boxes[c.property_index-1];r.properties[f.type]=f,r.properties.boxes.push(f)}}}}}},p.prototype.getItem=function(e){var t,r;if(!this.meta)return null;if(!(r=this.items[e]).data&&r.size)r.data=new Uint8Array(r.size),r.alreadyRead=0,this.itemsDataSize+=r.size,s.debug("ISOFile","Allocating item #"+e+" of size "+r.size+" (total: "+this.itemsDataSize+")");else if(r.alreadyRead===r.size)return r;for(var i=0;i<r.extents.length;i++){var n=r.extents[i];if(n.alreadyRead!==n.length){var o=this.stream.findPosition(!0,n.offset+n.alreadyRead,!1);if(!(o>-1))return null;var l=(t=this.stream.buffers[o]).byteLength-(n.offset+n.alreadyRead-t.fileStart);if(!(n.length-n.alreadyRead<=l))return s.debug("ISOFile","Getting item #"+e+" extent #"+i+" partial data (alreadyRead: "+n.alreadyRead+" offset: "+(n.offset+n.alreadyRead-t.fileStart)+" read size: "+l+" full extent size: "+n.length+" full item size: "+r.size+")"),a.memcpy(r.data.buffer,r.alreadyRead,t,n.offset+n.alreadyRead-t.fileStart,l),n.alreadyRead+=l,r.alreadyRead+=l,t.usedBytes+=l,this.stream.logBufferLevel(),null;s.debug("ISOFile","Getting item #"+e+" extent #"+i+" data (alreadyRead: "+n.alreadyRead+" offset: "+(n.offset+n.alreadyRead-t.fileStart)+" read size: "+(n.length-n.alreadyRead)+" full extent size: "+n.length+" full item size: "+r.size+")"),a.memcpy(r.data.buffer,r.alreadyRead,t,n.offset+n.alreadyRead-t.fileStart,n.length-n.alreadyRead),t.usedBytes+=n.length-n.alreadyRead,this.stream.logBufferLevel(),r.alreadyRead+=n.length-n.alreadyRead,n.alreadyRead=n.length}}return r.alreadyRead===r.size?r:null},p.prototype.releaseItem=function(e){var t=this.items[e];if(t.data){this.itemsDataSize-=t.size,t.data=null,t.alreadyRead=0;for(var r=0;r<t.extents.length;r++){t.extents[r].alreadyRead=0}return t.size}return 0},p.prototype.processItems=function(e){for(var t in this.items){var r=this.items[t];this.getItem(r.id),e&&!r.sent&&(e(r),r.sent=!0,r.data=null)}},p.prototype.hasItem=function(e){for(var t in this.items){var r=this.items[t];if(r.name===e)return r.id}return-1},p.prototype.getMetaHandler=function(){return this.meta?this.meta.hdlr.handler:null},p.prototype.getPrimaryItem=function(){return this.meta&&this.meta.pitm?this.getItem(this.meta.pitm.item_id):null},p.prototype.itemToFragmentedTrackFile=function(e){var t=e||{},r=null;if(null==(r=t.itemId?this.getItem(t.itemId):this.getPrimaryItem()))return null;var i=new p;i.discardMdatData=!1;var n={type:r.type,description_boxes:r.properties.boxes};r.properties.ispe&&(n.width=r.properties.ispe.image_width,n.height=r.properties.ispe.image_height);var s=i.addTrack(n);return s?(i.addSample(s,r.data),i):null},p.prototype.write=function(e){for(var t=0;t<this.boxes.length;t++)this.boxes[t].write(e)},p.prototype.createFragment=function(e,t,r){var i=this.getTrackById(e),n=this.getSample(i,t);if(null==n)return n=i.samples[t],this.nextSeekPosition?this.nextSeekPosition=Math.min(n.offset+n.alreadyRead,this.nextSeekPosition):this.nextSeekPosition=i.samples[t].offset+n.alreadyRead,null;var o=r||new a;o.endianness=a.BIG_ENDIAN;var l=this.createSingleSampleMoof(n);l.write(o),l.trafs[0].truns[0].data_offset=l.size+8,s.debug("MP4Box","Adjusting data_offset with new value "+l.trafs[0].truns[0].data_offset),o.adjustUint32(l.trafs[0].truns[0].data_offset_position,l.trafs[0].truns[0].data_offset);var d=new c.mdatBox;return d.data=n.data,d.write(o),o},p.writeInitializationSegment=function(e,t,r,i){var n;s.debug("ISOFile","Generating initialization segment");var o=new a;o.endianness=a.BIG_ENDIAN,e.write(o);var l=t.add("mvex");for(r&&l.add("mehd").set("fragment_duration",r),n=0;n<t.traks.length;n++)l.add("trex").set("track_id",t.traks[n].tkhd.track_id).set("default_sample_description_index",1).set("default_sample_duration",i).set("default_sample_size",0).set("default_sample_flags",65536);return t.write(o),o.buffer},p.prototype.save=function(e){var t=new a;t.endianness=a.BIG_ENDIAN,this.write(t),t.save(e)},p.prototype.getBuffer=function(){var e=new a;return e.endianness=a.BIG_ENDIAN,this.write(e),e.buffer},p.prototype.initializeSegmentation=function(){var e,t,r,i;for(null===this.onSegment&&s.warn("MP4Box","No segmentation callback set!"),this.isFragmentationInitialized||(this.isFragmentationInitialized=!0,this.nextMoofNumber=0,this.resetTables()),t=[],e=0;e<this.fragmentedTracks.length;e++){var n=new c.moovBox;n.mvhd=this.moov.mvhd,n.boxes.push(n.mvhd),r=this.getTrackById(this.fragmentedTracks[e].id),n.boxes.push(r),n.traks.push(r),(i={}).id=r.tkhd.track_id,i.user=this.fragmentedTracks[e].user,i.buffer=p.writeInitializationSegment(this.ftyp,n,this.moov.mvex&&this.moov.mvex.mehd?this.moov.mvex.mehd.fragment_duration:void 0,this.moov.traks[e].samples.length>0?this.moov.traks[e].samples[0].duration:0),t.push(i)}return t},c.Box.prototype.printHeader=function(e){this.size+=8,this.size>l&&(this.size+=8),"uuid"===this.type&&(this.size+=16),e.log(e.indent+"size:"+this.size),e.log(e.indent+"type:"+this.type)},c.FullBox.prototype.printHeader=function(e){this.size+=4,c.Box.prototype.printHeader.call(this,e),e.log(e.indent+"version:"+this.version),e.log(e.indent+"flags:"+this.flags)},c.Box.prototype.print=function(e){this.printHeader(e)},c.ContainerBox.prototype.print=function(e){this.printHeader(e);for(var t=0;t<this.boxes.length;t++)if(this.boxes[t]){var r=e.indent;e.indent+=" ",this.boxes[t].print(e),e.indent=r}},p.prototype.print=function(e){e.indent="";for(var t=0;t<this.boxes.length;t++)this.boxes[t]&&this.boxes[t].print(e)},c.mvhdBox.prototype.print=function(e){c.FullBox.prototype.printHeader.call(this,e),e.log(e.indent+"creation_time: "+this.creation_time),e.log(e.indent+"modification_time: "+this.modification_time),e.log(e.indent+"timescale: "+this.timescale),e.log(e.indent+"duration: "+this.duration),e.log(e.indent+"rate: "+this.rate),e.log(e.indent+"volume: "+(this.volume>>8)),e.log(e.indent+"matrix: "+this.matrix.join(", ")),e.log(e.indent+"next_track_id: "+this.next_track_id)},c.tkhdBox.prototype.print=function(e){c.FullBox.prototype.printHeader.call(this,e),e.log(e.indent+"creation_time: "+this.creation_time),e.log(e.indent+"modification_time: "+this.modification_time),e.log(e.indent+"track_id: "+this.track_id),e.log(e.indent+"duration: "+this.duration),e.log(e.indent+"volume: "+(this.volume>>8)),e.log(e.indent+"matrix: "+this.matrix.join(", ")),e.log(e.indent+"layer: "+this.layer),e.log(e.indent+"alternate_group: "+this.alternate_group),e.log(e.indent+"width: "+this.width),e.log(e.indent+"height: "+this.height)};var m={createFile:function(e,t){var r=void 0===e||e,i=new p(t);return i.discardMdatData=!r,i}};t.createFile=m.createFile}));function br(e){return e.reduce(((e,t)=>256*e+t))}function wr(e){const t=[101,103,119,99],r=e.length-28,i=e.slice(r,r+t.length);return t.every(((e,t)=>e===i[t]))}vr.Log,vr.MP4BoxStream,vr.DataStream,vr.MultiBufferStream,vr.MPEG4DescriptorParser,vr.BoxParser,vr.XMLSubtitlein4Parser,vr.Textin4Parser,vr.ISOFile,vr.createFile;class Er{constructor(){this.s=null,this.a=null,this.l=0,this.c=0,this.u=1/0,this.A=!1,this.d=!1,this.r=4194304,this.n=new Uint8Array([30,158,90,33,244,57,83,165,2,70,35,87,215,231,226,108]),this.t=this.n.slice().reverse()}destroy(){this.s=null,this.a=null,this.l=0,this.c=0,this.u=1/0,this.A=!1,this.d=!1,this.r=4194304,this.n=null,this.t=null}transport(e){if(!this.s&&this.l>50)return e;if(this.l++,this.d)return e;const t=new Uint8Array(e);if(this.A){if(!(this.c<this.u))return this.a&&this.s?(this.a.set(t,this.r),this.s.parse(null,this.r,t.byteLength),this.a.slice(this.r,this.r+t.byteLength)):(console.error("video_error_2"),this.d=!0,e);wr(t)&&this.c++}else{const r=function(e,t){const r=function(e,t){for(let r=0;r<e.byteLength-t.length;r++)for(let i=0;i<t.length&&e[r+i]===t[i];i++)if(i===t.length-1)return r;return null}(e,t);if(r){const t=br(e.slice(r+16,r+16+8));return[t,br(e.slice(r+24,r+24+8)),function(e){return e.map((e=>~e))}(e.slice(r+32,r+32+t))]}return null}(t,this.t);if(!r)return e;const i=function(e){try{if("object"!=typeof WebAssembly||"function"!=typeof WebAssembly.instantiate)throw null;{const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(!(e instanceof WebAssembly.Module&&new WebAssembly.Instance(e)instanceof WebAssembly.Instance))throw null}}catch(e){return new Error("video_error_4")}let t;try{t={env:{__handle_stack_overflow:()=>e(new Error("video_error_1")),memory:new WebAssembly.Memory({initial:256,maximum:256})}}}catch(e){return new Error("video_error_5")}return t}(e);if(i instanceof Error)return console.error(i.message),this.d=!0,e;this.A=!0,this.u=r[1],wr(t)&&this.c++,WebAssembly.instantiate(r[2],i).then((e=>{if("function"!=typeof(t=e.instance.exports).parse||"object"!=typeof t.memory)return this.d=!0,void console.error("video_error_3");var t;this.s=e.instance.exports,this.a=new Uint8Array(this.s.memory.buffer)})).catch((e=>{this.d=!0,console.error("video_error_6")}))}return e}}const Sr=0,Ur=32,xr=16,Ar=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72],kr=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257];function Tr(e){const t=[];for(let r=0,i=e.length;r<i;r+=2)t.push(parseInt(e.substr(r,2),16));return t}function Br(e,t){const r=31&t;return e<<r|e>>>32-r}function Cr(e){return(255&Ar[e>>>24&255])<<24|(255&Ar[e>>>16&255])<<16|(255&Ar[e>>>8&255])<<8|255&Ar[255&e]}function Dr(e){return e^Br(e,2)^Br(e,10)^Br(e,18)^Br(e,24)}function Fr(e){return e^Br(e,13)^Br(e,23)}function Pr(e,t,r){const i=new Array(4),n=new Array(4);for(let t=0;t<4;t++)n[0]=255&e[4*t],n[1]=255&e[4*t+1],n[2]=255&e[4*t+2],n[3]=255&e[4*t+3],i[t]=n[0]<<24|n[1]<<16|n[2]<<8|n[3];for(let e,t=0;t<32;t+=4)e=i[1]^i[2]^i[3]^r[t+0],i[0]^=Dr(Cr(e)),e=i[2]^i[3]^i[0]^r[t+1],i[1]^=Dr(Cr(e)),e=i[3]^i[0]^i[1]^r[t+2],i[2]^=Dr(Cr(e)),e=i[0]^i[1]^i[2]^r[t+3],i[3]^=Dr(Cr(e));for(let e=0;e<16;e+=4)t[e]=i[3-e/4]>>>24&255,t[e+1]=i[3-e/4]>>>16&255,t[e+2]=i[3-e/4]>>>8&255,t[e+3]=255&i[3-e/4]}function Ir(e,t,r,{padding:i="pkcs#7",mode:n,iv:s=[],output:o="string"}={}){if("cbc"===n&&("string"==typeof s&&(s=Tr(s)),16!==s.length))throw new Error("iv is invalid");if("string"==typeof t&&(t=Tr(t)),16!==t.length)throw new Error("key is invalid");if(e="string"==typeof e?r!==Sr?function(e){const t=[];for(let r=0,i=e.length;r<i;r++){const i=e.codePointAt(r);if(i<=127)t.push(i);else if(i<=2047)t.push(192|i>>>6),t.push(128|63&i);else if(i<=55295||i>=57344&&i<=65535)t.push(224|i>>>12),t.push(128|i>>>6&63),t.push(128|63&i);else{if(!(i>=65536&&i<=1114111))throw t.push(i),new Error("input is not supported");r++,t.push(240|i>>>18&28),t.push(128|i>>>12&63),t.push(128|i>>>6&63),t.push(128|63&i)}}return t}(e):Tr(e):[...e],("pkcs#5"===i||"pkcs#7"===i)&&r!==Sr){const t=xr-e.length%xr;for(let r=0;r<t;r++)e.push(t)}const a=new Array(Ur);!function(e,t,r){const i=new Array(4),n=new Array(4);for(let t=0;t<4;t++)n[0]=255&e[0+4*t],n[1]=255&e[1+4*t],n[2]=255&e[2+4*t],n[3]=255&e[3+4*t],i[t]=n[0]<<24|n[1]<<16|n[2]<<8|n[3];i[0]^=2746333894,i[1]^=1453994832,i[2]^=1736282519,i[3]^=2993693404;for(let e,r=0;r<32;r+=4)e=i[1]^i[2]^i[3]^kr[r+0],t[r+0]=i[0]^=Fr(Cr(e)),e=i[2]^i[3]^i[0]^kr[r+1],t[r+1]=i[1]^=Fr(Cr(e)),e=i[3]^i[0]^i[1]^kr[r+2],t[r+2]=i[2]^=Fr(Cr(e)),e=i[0]^i[1]^i[2]^kr[r+3],t[r+3]=i[3]^=Fr(Cr(e));if(r===Sr)for(let e,r=0;r<16;r++)e=t[r],t[r]=t[31-r],t[31-r]=e}(t,a,r);const l=[];let d=s,u=e.length,c=0;for(;u>=xr;){const t=e.slice(c,c+16),i=new Array(16);if("cbc"===n)for(let e=0;e<xr;e++)r!==Sr&&(t[e]^=d[e]);Pr(t,i,a);for(let e=0;e<xr;e++)"cbc"===n&&r===Sr&&(i[e]^=d[e]),l[c+e]=i[e];"cbc"===n&&(d=r!==Sr?i:t),u-=xr,c+=xr}if(("pkcs#5"===i||"pkcs#7"===i)&&r===Sr){const e=l.length,t=l[e-1];for(let r=1;r<=t;r++)if(l[e-r]!==t)throw new Error("padding is invalid");l.splice(e-t,t)}return"array"!==o?r!==Sr?l.map((e=>1===(e=e.toString(16)).length?"0"+e:e)).join(""):function(e){const t=[];for(let r=0,i=e.length;r<i;r++)e[r]>=240&&e[r]<=247?(t.push(String.fromCodePoint(((7&e[r])<<18)+((63&e[r+1])<<12)+((63&e[r+2])<<6)+(63&e[r+3]))),r+=3):e[r]>=224&&e[r]<=239?(t.push(String.fromCodePoint(((15&e[r])<<12)+((63&e[r+1])<<6)+(63&e[r+2]))),r+=2):e[r]>=192&&e[r]<=223?(t.push(String.fromCodePoint(((31&e[r])<<6)+(63&e[r+1]))),r++):t.push(String.fromCodePoint(e[r]));return t.join("")}(l):l}class Lr{on(e,t,r){const i=this.e||(this.e={});return(i[e]||(i[e]=[])).push({fn:t,ctx:r}),this}once(e,t,r){const i=this;function n(...s){i.off(e,n),t.apply(r,s)}return n._=t,this.on(e,n,r)}emit(e,...t){const r=((this.e||(this.e={}))[e]||[]).slice();for(let e=0;e<r.length;e+=1)r[e].fn.apply(r[e].ctx,t);return this}off(e,t){const r=this.e||(this.e={});if(!e)return Object.keys(r).forEach((e=>{delete r[e]})),void delete this.e;const i=r[e],n=[];if(i&&t)for(let e=0,r=i.length;e<r;e+=1)i[e].fn!==t&&i[e].fn._!==t&&n.push(i[e]);return n.length?r[e]=n:delete r[e],this}}const Rr={init:0,findFirstStartCode:1,findSecondStartCode:2};class Mr extends Lr{constructor(e){super(),this.player=e,this.isDestroyed=!1,this.reset()}destroy(){this.isDestroyed=!1,this.off(),this.reset()}reset(){this.stats=Rr.init,this.tempBuffer=new Uint8Array(0),this.parsedOffset=0,this.versionLayer=0}dispatch(e,t){let r=new Uint8Array(this.tempBuffer.length+e.length);for(r.set(this.tempBuffer,0),r.set(e,this.tempBuffer.length),this.tempBuffer=r;!this.isDestroyed;){if(this.state==Rr.Init){let e=!1;for(;this.tempBuffer.length-this.parsedOffset>=2&&!this.isDestroyed;)if(255==this.tempBuffer[this.parsedOffset]){if(!(!1&this.tempBuffer[this.parsedOffset+1])){this.versionLayer=this.tempBuffer[this.parsedOffset+1],this.state=Rr.findFirstStartCode,this.fisrtStartCodeOffset=this.parsedOffset,this.parsedOffset+=2,e=!0;break}this.parsedOffset++}else this.parsedOffset++;if(e)continue;break}if(this.state==Rr.findFirstStartCode){let e=!1;for(;this.tempBuffer.length-this.parsedOffset>=2&&!this.isDestroyed;)if(255==this.tempBuffer[this.parsedOffset]){if(this.tempBuffer[this.parsedOffset+1]==this.versionLayer){this.state=Rr.findSecondStartCode,this.secondStartCodeOffset=this.parsedOffset,this.parsedOffset+=2,e=!0;break}this.parsedOffset++}else this.parsedOffset++;if(e)continue;break}if(this.state==Rr.findSecondStartCode){let e=this.tempBuffer.slice(this.fisrtStartCodeOffset,this.secondStartCodeOffset);this.emit("data",e,t),this.tempBuffer=this.tempBuffer.slice(this.secondStartCodeOffset),this.fisrtStartCodeOffset=0,this.parsedOffset=2,this.state=Rr.findFirstStartCode}}}}function zr(e,t,r){for(let i=2;i<e.length;++i){const n=i-2,s=t[n%t.length],o=r[n%r.length];e[i]=e[i]^s^o}return e}function Nr(...e){if((e=e.filter(Boolean)).length<2)return e[0];const t=new Uint8Array(e.reduce(((e,t)=>e+t.byteLength),0));let r=0;return e.forEach((e=>{t.set(e,r),r+=e.byteLength})),t}class Or{static init(){Or.types={avc1:[],avcC:[],hvc1:[],hvcC:[],av01:[],av1C:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[],".mp3":[],Opus:[],dOps:[],"ac-3":[],dac3:[],"ec-3":[],dec3:[]};for(let e in Or.types)Or.types.hasOwnProperty(e)&&(Or.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);let e=Or.constants={};e.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]),e.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),e.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),e.STSC=e.STCO=e.STTS,e.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),e.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),e.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),e.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),e.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),e.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}static box(e){let t=8,r=null,i=Array.prototype.slice.call(arguments,1),n=i.length;for(let e=0;e<n;e++)t+=i[e].byteLength;r=new Uint8Array(t),r[0]=t>>>24&255,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t,r.set(e,4);let s=8;for(let e=0;e<n;e++)r.set(i[e],s),s+=i[e].byteLength;return r}static generateInitSegment(e){let t=Or.box(Or.types.ftyp,Or.constants.FTYP),r=Or.moov(e),i=new Uint8Array(t.byteLength+r.byteLength);return i.set(t,0),i.set(r,t.byteLength),i}static moov(e){let t=Or.mvhd(e.timescale,e.duration),r=Or.trak(e),i=Or.mvex(e);return Or.box(Or.types.moov,t,r,i)}static mvhd(e,t){return Or.box(Or.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))}static trak(e){return Or.box(Or.types.trak,Or.tkhd(e),Or.mdia(e))}static tkhd(e){let t=e.id,r=e.duration,i=e.presentWidth,n=e.presentHeight;return Or.box(Or.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,i>>>8&255,255&i,0,0,n>>>8&255,255&n,0,0]))}static mdia(e){return Or.box(Or.types.mdia,Or.mdhd(e),Or.hdlr(e),Or.minf(e))}static mdhd(e){let t=e.timescale,r=e.duration;return Or.box(Or.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,r>>>24&255,r>>>16&255,r>>>8&255,255&r,85,196,0,0]))}static hdlr(e){let t=null;return t="audio"===e.type?Or.constants.HDLR_AUDIO:Or.constants.HDLR_VIDEO,Or.box(Or.types.hdlr,t)}static minf(e){let t=null;return t="audio"===e.type?Or.box(Or.types.smhd,Or.constants.SMHD):Or.box(Or.types.vmhd,Or.constants.VMHD),Or.box(Or.types.minf,t,Or.dinf(),Or.stbl(e))}static dinf(){return Or.box(Or.types.dinf,Or.box(Or.types.dref,Or.constants.DREF))}static stbl(e){return Or.box(Or.types.stbl,Or.stsd(e),Or.box(Or.types.stts,Or.constants.STTS),Or.box(Or.types.stsc,Or.constants.STSC),Or.box(Or.types.stsz,Or.constants.STSZ),Or.box(Or.types.stco,Or.constants.STCO))}static stsd(e){return"audio"===e.type?"mp3"===e.audioType?Or.box(Or.types.stsd,Or.constants.STSD_PREFIX,Or.mp3(e)):Or.box(Or.types.stsd,Or.constants.STSD_PREFIX,Or.mp4a(e)):"avc"===e.videoType?Or.box(Or.types.stsd,Or.constants.STSD_PREFIX,Or.avc1(e)):Or.box(Or.types.stsd,Or.constants.STSD_PREFIX,Or.hvc1(e))}static mp3(e){let t=e.channelCount,r=e.audioSampleRate,i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t,0,16,0,0,0,0,r>>>8&255,255&r,0,0]);return Or.box(Or.types[".mp3"],i)}static mp4a(e){let t=e.channelCount,r=e.audioSampleRate,i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t,0,16,0,0,0,0,r>>>8&255,255&r,0,0]);return Or.box(Or.types.mp4a,i,Or.esds(e))}static esds(e){let t=e.config||[],r=t.length,i=new Uint8Array([0,0,0,0,3,23+r,0,1,0,4,15+r,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([r]).concat(t).concat([6,1,2]));return Or.box(Or.types.esds,i)}static avc1(e){let t=e.avcc;const r=e.codecWidth,i=e.codecHeight;let n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,r>>>8&255,255&r,i>>>8&255,255&i,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return Or.box(Or.types.avc1,n,Or.box(Or.types.avcC,t))}static hvc1(e){let t=e.avcc;const r=e.codecWidth,i=e.codecHeight;let n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,r>>>8&255,255&r,i>>>8&255,255&i,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return Or.box(Or.types.hvc1,n,Or.box(Or.types.hvcC,t))}static mvex(e){return Or.box(Or.types.mvex,Or.trex(e))}static trex(e){let t=e.id,r=new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return Or.box(Or.types.trex,r)}static moof(e,t){return Or.box(Or.types.moof,Or.mfhd(e.sequenceNumber),Or.traf(e,t))}static mfhd(e){let t=new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e]);return Or.box(Or.types.mfhd,t)}static traf(e,t){let r=e.id,i=Or.box(Or.types.tfhd,new Uint8Array([0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r])),n=Or.box(Or.types.tfdt,new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t])),s=Or.sdtp(e),o=Or.trun(e,s.byteLength+16+16+8+16+8+8);return Or.box(Or.types.traf,i,n,o,s)}static sdtp(e){let t=new Uint8Array(5),r=e.flags;return t[4]=r.isLeading<<6|r.dependsOn<<4|r.isDependedOn<<2|r.hasRedundancy,Or.box(Or.types.sdtp,t)}static trun(e,t){let r=new Uint8Array(28);t+=36,r.set([0,0,15,1,0,0,0,1,t>>>24&255,t>>>16&255,t>>>8&255,255&t],0);let i=e.duration,n=e.size,s=e.flags,o=e.cts;return r.set([i>>>24&255,i>>>16&255,i>>>8&255,255&i,n>>>24&255,n>>>16&255,n>>>8&255,255&n,s.isLeading<<2|s.dependsOn,s.isDependedOn<<6|s.hasRedundancy<<4|s.isNonSync,0,0,o>>>24&255,o>>>16&255,o>>>8&255,255&o],12),Or.box(Or.types.trun,r)}static mdat(e){return Or.box(Or.types.mdat,e)}}function $r(e={},t={},r=!1){let i=[],n=[],s={},o=new AbortController,a=null,l=null,d=null,g=null,v=null,w=null,Fe=!1,Pe=!1,Ve=!!at(r),Qe=!1,dt=null,ut=null,ct=null,ft=[],mt=null,yt=null,xt=0,At=0,kt=null,Tt=null,Bt=0,Ct=0,Ot=!1,$t=!1,Gt=!1,Ht=null,Vt=null,jt=null,Wt=!1,Yt=()=>{const e=st();return{debug:e.debug,debugLevel:e.debugLevel,debugUuid:e.debugUuid,useOffscreen:e.useOffscreen,useWCS:e.useWCS,useMSE:e.useMSE,videoBuffer:e.videoBuffer,videoBufferDelay:e.videoBufferDelay,openWebglAlignment:e.openWebglAlignment,playType:e.playType,hasAudio:e.hasAudio,hasVideo:e.hasVideo,playbackRate:1,playbackForwardMaxRateDecodeIFrame:e.playbackForwardMaxRateDecodeIFrame,playbackIsCacheBeforeDecodeForFpsRender:e.playbackConfig.isCacheBeforeDecodeForFpsRender,sampleRate:0,networkDelay:e.networkDelay,visibility:!0,useSIMD:e.useSIMD,isRecording:!1,recordType:e.recordType,isNakedFlow:e.isNakedFlow,checkFirstIFrame:e.checkFirstIFrame,audioBufferSize:1024,isM7sCrypto:e.isM7sCrypto,m7sCryptoAudio:e.m7sCryptoAudio,cryptoKey:e.cryptoKey,cryptoIV:e.cryptoIV,isSm4Crypto:e.isSm4Crypto,sm4CryptoKey:e.sm4CryptoKey,isXorCrypto:e.isXorCrypto,isHls265:!1,isFlv:e.isFlv,isFmp4:e.isFmp4,isMpeg4:e.isMpeg4,isTs:e.isTs,isFmp4Private:e.isFmp4Private,isEmitSEI:e.isEmitSEI,isRecordTypeFlv:!1,isWasmMp4:!1,isChrome:!1,isDropSameTimestampGop:e.isDropSameTimestampGop,mseDecodeAudio:e.mseDecodeAudio,nakedFlowH265DemuxUseNew:e.nakedFlowH265DemuxUseNew,mseDecoderUseWorker:e.mseDecoderUseWorker,mseAutoCleanupSourceBuffer:e.mseAutoCleanupSourceBuffer,mseAutoCleanupMaxBackwardDuration:e.mseAutoCleanupMaxBackwardDuration,mseAutoCleanupMinBackwardDuration:e.mseAutoCleanupMinBackwardDuration,mseCorrectTimeDuration:e.mseCorrectTimeDuration}};"VideoEncoder"in self&&(s={hasInit:!1,isEmitInfo:!1,offscreenCanvas:null,offscreenCanvasCtx:null,decoder:new VideoDecoder({output:function(e){if(s.isEmitInfo||(Qt.debug.log("worker","Webcodecs Video Decoder initSize"),postMessage({cmd:S,w:e.codedWidth,h:e.codedHeight}),s.isEmitInfo=!0,s.offscreenCanvas=new OffscreenCanvas(e.codedWidth,e.codedHeight),s.offscreenCanvasCtx=s.offscreenCanvas.getContext("2d")),"function"==typeof e.createImageBitmap)e.createImageBitmap().then((t=>{s.offscreenCanvasCtx.drawImage(t,0,0,e.codedWidth,e.codedHeight);let r=s.offscreenCanvas.transferToImageBitmap();postMessage({cmd:U,buffer:r,delay:Qt.delay,ts:0},[r]),it(e)}));else{s.offscreenCanvasCtx.drawImage(e,0,0,e.codedWidth,e.codedHeight);let t=s.offscreenCanvas.transferToImageBitmap();postMessage({cmd:U,buffer:t,delay:Qt.delay,ts:0},[t]),it(e)}},error:function(e){Qt.debug.error("worker","VideoDecoder error",e)}}),decode:function(e,t,r){const i=e[0]>>4==1;if(s.hasInit){const r=new EncodedVideoChunk({data:e.slice(5),timestamp:t,type:i?Te:Be});s.decoder.decode(r)}else if(i&&0===e[1]){const t=15&e[0];postMessage({cmd:C,code:t});const r=new Uint8Array(e);postMessage({cmd:D,buffer:r,codecId:t},[r.buffer]);const i=function(e){let t=e.subarray(1,4),r="avc1.";for(let e=0;e<3;e++){let i=t[e].toString(16);i.length<2&&(i="0"+i),r+=i}return{codec:r,description:e}}(e.slice(5));s.decoder.configure(i),s.hasInit=!0}},reset(){s.hasInit=!1,s.isEmitInfo=!1,s.offscreenCanvas=null,s.offscreenCanvasCtx=null}});let qt=function(){if(Wt=!0,Qt.fetchStatus!==Re||lt(Qt._opt.isChrome)){if(o)try{o.abort(),o=null}catch(e){Qt.debug.log("worker","abort catch",e)}}else o=null,Qt.debug.log("worker",`abort() and not abortController.abort() _status is ${Qt.fetchStatus} and _isChrome is ${Qt._opt.isChrome}`)},Kt={init(){Kt.lastBuf=null,Kt.vps=null,Kt.sps=null,Kt.pps=null,Kt.streamType=null,Kt.localDts=0,Kt.isSendSeqHeader=!1},destroy(){Kt.lastBuf=null,Kt.vps=null,Kt.sps=null,Kt.pps=null,Kt.streamType=null,Kt.localDts=0,Kt.isSendSeqHeader=!1},dispatch(e){const t=new Uint8Array(e);Kt.extractNALu$2(t)},getNaluDts(){let e=Kt.localDts;return Kt.localDts=Kt.localDts+40,e},getNaluAudioDts(){const e=Qt._opt.sampleRate,t=Qt._opt.audioBufferSize;return Kt.localDts+parseInt(t/e*1e3)},extractNALu(e){let t,r,i=0,n=e.byteLength,s=0,o=[];for(;i<n;)switch(t=e[i++],s){case 0:0===t&&(s=1);break;case 1:s=0===t?2:0;break;case 2:case 3:0===t?s=3:1===t&&i<n?(r&&o.push(e.subarray(r,i-s-1)),r=i,s=0):s=0}return r&&o.push(e.subarray(r,n)),o},extractNALu$2(e){let t=null;if(!e||e.byteLength<1)return;Kt.lastBuf?(t=new Uint8Array(e.byteLength+Kt.lastBuf.length),t.set(Kt.lastBuf),t.set(new Uint8Array(e),Kt.lastBuf.length)):t=new Uint8Array(e);let r=0,i=-1,n=-2;const s=new Array;for(let e=0;e<t.length;e+=2){const r=t[e],o=t[e+1];0==i&&0==r&&0==o?s.push(e-1):1==o&&0==r&&0==i&&0==n&&s.push(e-2),n=r,i=o}if(s.length>1)for(let e=0;e<s.length-1;++e){const i=t.subarray(s[e],s[e+1]+1);Kt.handleNALu(i),r=s[e+1]}else r=s[0];if(0!=r&&r<t.length)Kt.lastBuf=t.subarray(r);else{Kt.lastBuf||(Kt.lastBuf=t);const r=new Uint8Array(Kt.lastBuf.length+e.byteLength);r.set(Kt.lastBuf),r.set(new Uint8Array(e),Kt.lastBuf.length),Kt.lastBuf=r}},handleNALu(e){e.byteLength<=4?Qt.debug.warn("worker",`handleNALu nalu byteLength is ${e.byteLength} <= 4`):(e=e.slice(4),Kt.handleVideoNalu(e))},handleVideoNalu(e){if(Kt.streamType||(Kt.streamType=function(e){let t=null,r=31&e[0];return r!==Ae.sps&&r!==Ae.pps||(t=Ue.h264),t||(r=(126&e[0])>>1,r!==ke.vps&&r!==ke.sps&&r!==ke.pps||(t=Ue.h265)),t}(e),Ht=Kt.streamType===Ue.h265),Kt.streamType===Ue.h264){const t=Kt.handleAddNaluStartCode(e),r=Kt.extractNALu(t);if(0===r.length)return void Qt.debug.warn("worker","handleVideoNalu","h264 naluList.length === 0");const i=[];if(r.forEach((e=>{const t=wt(e);t===Ae.pps||t===Ae.sps?Kt.handleVideoH264Nalu(e):St(t)&&i.push(e)})),1===i.length)Kt.handleVideoH264Nalu(i[0]);else{const e=function(e){if(0===e.length)return!1;const t=wt(e[0]);for(let r=1;r<e.length;r++)if(t!==wt(e[r]))return!1;return!0}(i);if(e){const e=wt(i[0]),t=Ut(e);Kt.handleVideoH264NaluList(i,t,e)}else i.forEach((e=>{Kt.handleVideoH264Nalu(e)}))}}else if(Kt.streamType===Ue.h265)if(Qt._opt.nakedFlowH265DemuxUseNew){const t=Kt.handleAddNaluStartCode(e),r=Kt.extractNALu(t);if(0===r.length)return void Qt.debug.warn("worker","handleVideoNalu","h265 naluList.length === 0");const i=[];if(r.forEach((e=>{const t=Mt(e);t===ke.pps||t===ke.sps||t===ke.vps?Kt.handleVideoH265Nalu(e):zt(t)&&i.push(e)})),1===i.length)Kt.handleVideoH265Nalu(i[0]);else{const e=function(e){if(0===e.length)return!1;const t=Mt(e[0]);for(let r=1;r<e.length;r++)if(t!==Mt(e[r]))return!1;return!0}(i);if(e){const e=Mt(i[0]),t=Nt(e);Kt.handleVideoH265NaluList(i,t,e)}else i.forEach((e=>{Kt.handleVideoH265Nalu(e)}))}}else{Mt(e)===ke.pps?Kt.extractH265PPS(e):Kt.handleVideoH265Nalu(e)}},extractH264PPS(e){const t=Kt.handleAddNaluStartCode(e);Kt.extractNALu(t).forEach((e=>{Et(wt(e))?Kt.extractH264SEI(e):Kt.handleVideoH264Nalu(e)}))},extractH265PPS(e){const t=Kt.handleAddNaluStartCode(e);Kt.extractNALu(t).forEach((e=>{const t=Mt(e);t===ke.sei?Kt.extractH265SEI(e):Kt.handleVideoH265Nalu(e)}))},extractH264SEI(e){const t=Kt.handleAddNaluStartCode(e);Kt.extractNALu(t).forEach((e=>{Kt.handleVideoH264Nalu(e)}))},extractH265SEI(e){const t=Kt.handleAddNaluStartCode(e);Kt.extractNALu(t).forEach((e=>{Kt.handleVideoH265Nalu(e)}))},handleAddNaluStartCode(e){const t=[0,0,0,1],r=new Uint8Array(e.length+t.length);return r.set(t),r.set(e,t.length),r},handleVideoH264Nalu(e){const t=wt(e);switch(t){case Ae.sps:Kt.sps=e;break;case Ae.pps:Kt.pps=e}if(Kt.isSendSeqHeader){if(Kt.sps&&Kt.pps){const e=vt({sps:Kt.sps,pps:Kt.pps}),t=Kt.getNaluDts();Qt.decode(e,{type:Z,ts:t,isIFrame:!0,cts:0}),Kt.sps=null,Kt.pps=null}if(St(t)){const r=Ut(t),i=Kt.getNaluDts(),n=function(e,t){let r=[];r[0]=t?23:39,r[1]=1,r[2]=0,r[3]=0,r[4]=0,r[5]=e.byteLength>>24&255,r[6]=e.byteLength>>16&255,r[7]=e.byteLength>>8&255,r[8]=255&e.byteLength;const i=new Uint8Array(r.length+e.byteLength);return i.set(r,0),i.set(e,r.length),i}(e,r);Kt.doDecode(n,{type:Z,ts:i,isIFrame:r,cts:0})}else Qt.debug.warn("work",`handleVideoH264Nalu Avc Seq Head is ${t}`)}else if(Kt.sps&&Kt.pps){Kt.isSendSeqHeader=!0;const e=vt({sps:Kt.sps,pps:Kt.pps});Qt.decode(e,{type:Z,ts:0,isIFrame:!0,cts:0}),Kt.sps=null,Kt.pps=null}},handleVideoH264NaluList(e,t,r){if(Kt.isSendSeqHeader){const i=Kt.getNaluDts(),n=bt(e.reduce(((e,t)=>{const r=Ft(e),i=Ft(t),n=new Uint8Array(r.byteLength+i.byteLength);return n.set(r,0),n.set(i,r.byteLength),n})),t);Kt.doDecode(n,{type:Z,ts:i,isIFrame:t,cts:0}),Qt.debug.log("worker",`handleVideoH264NaluList list size is ${e.length} package length is ${n.byteLength} isIFrame is ${t},nalu type is ${r}, dts is ${i}`)}else Qt.debug.warn("worker","handleVideoH264NaluList isSendSeqHeader is false")},handleVideoH265Nalu(e){const t=Mt(e);switch(t){case ke.vps:Kt.vps=e;break;case ke.sps:Kt.sps=e;break;case ke.pps:Kt.pps=e}if(Kt.isSendSeqHeader){if(Kt.vps&&Kt.sps&&Kt.pps){const e=Lt({vps:Kt.vps,sps:Kt.sps,pps:Kt.pps}),t=Kt.getNaluDts();Qt.decode(e,{type:Z,ts:t,isIFrame:!0,cts:0}),Kt.vps=null,Kt.sps=null,Kt.pps=null}if(zt(t)){const r=Nt(t),i=Kt.getNaluDts(),n=function(e,t){let r=[];r[0]=t?28:44,r[1]=1,r[2]=0,r[3]=0,r[4]=0,r[5]=e.byteLength>>24&255,r[6]=e.byteLength>>16&255,r[7]=e.byteLength>>8&255,r[8]=255&e.byteLength;const i=new Uint8Array(r.length+e.byteLength);return i.set(r,0),i.set(e,r.length),i}(e,r);Kt.doDecode(n,{type:Z,ts:i,isIFrame:r,cts:0})}else Qt.debug.warn("work",`handleVideoH265Nalu HevcSeqHead is ${t}`)}else if(Kt.vps&&Kt.sps&&Kt.pps){Kt.isSendSeqHeader=!0;const e=Lt({vps:Kt.vps,sps:Kt.sps,pps:Kt.pps});Qt.decode(e,{type:Z,ts:0,isIFrame:!0,cts:0}),Kt.vps=null,Kt.sps=null,Kt.pps=null}},handleVideoH265NaluList(e,t,r){if(Kt.isSendSeqHeader){const i=Kt.getNaluDts(),n=Rt(e.reduce(((e,t)=>{const r=Ft(e),i=Ft(t),n=new Uint8Array(r.byteLength+i.byteLength);return n.set(r,0),n.set(i,r.byteLength),n})),t);Kt.doDecode(n,{type:Z,ts:i,isIFrame:t,cts:0}),Qt.debug.log("worker",`handleVideoH265NaluList list size is ${e.length} package length is ${n.byteLength} isIFrame is ${t},nalu type is ${r}, dts is ${i}`)}else Qt.debug.warn("worker","handleVideoH265NaluList isSendSeqHeader is false")},doDecode(e,t){Qt.calcNetworkDelay(t.ts),t.isIFrame&&Qt.calcIframeIntervalTimestamp(t.ts),postMessage({cmd:L,type:he,value:e.byteLength}),postMessage({cmd:L,type:pe,value:t.ts}),Qt.decode(e,t)}},Xt={LOG_NAME:"worker fmp4Demuxer",mp4Box:vr.createFile(),offset:0,videoTrackId:null,audioTrackId:null,isHevc:!1,listenMp4Box(){Xt.mp4Box.onReady=Xt.onReady,Xt.mp4Box.onError=Xt.onError,Xt.mp4Box.onSamples=Xt.onSamples},initTransportDescarmber(){Xt.transportDescarmber=new Er},_getSeqHeader(e){const t=Xt.mp4Box.getTrackById(e.id);for(const e of t.mdia.minf.stbl.stsd.entries)if(e.avcC||e.hvcC){const t=new vr.DataStream(void 0,0,vr.DataStream.BIG_ENDIAN);let r=[];e.avcC?(e.avcC.write(t),r=[23,0,0,0,0]):(Xt.isHevc=!0,Ht=!0,e.hvcC.write(t),r=[28,0,0,0,0]);const i=new Uint8Array(t.buffer,8),n=new Uint8Array(r.length+i.length);return n.set(r,0),n.set(i,r.length),n}return null},onReady(e){Qt.debug.log(Xt.LOG_NAME,"onReady()",e);const t=e.videoTracks[0],r=e.audioTracks[0];if(t){Xt.videoTrackId=t.id;const e=Xt._getSeqHeader(t);e&&(Qt.debug.log(Xt.LOG_NAME,"seqHeader"),Qt.decodeVideo(e,0,!0,0)),Xt.mp4Box.setExtractionOptions(t.id)}if(r&&Qt._opt.hasAudio){Xt.audioTrackId=r.id;const e=r.audio||{},t=Ke.indexOf(e.sample_rate),i=r.codec.replace("mp4a.40.","");Xt.mp4Box.setExtractionOptions(r.id);const n=je({profile:parseInt(i,10),sampleRate:t,channel:e.channel_count});Qt.debug.log(Xt.LOG_NAME,"aacADTSHeader"),Qt.decodeAudio(n,0)}Xt.mp4Box.start()},onError(e){Qt.debug.error(Xt.LOG_NAME,"mp4Box onError",e)},onSamples(e,t,r){if(e===Xt.videoTrackId)for(const e of r){const t=e.data,r=e.is_sync,i=1e3*e.cts/e.timescale;e.duration,e.timescale,r&&Qt.calcIframeIntervalTimestamp(i);let n=null;n=Xt.isHevc?Rt(t,r):bt(t,r),postMessage({cmd:L,type:he,value:n.byteLength}),postMessage({cmd:L,type:pe,value:i}),Qt.decode(n,{type:Z,ts:i,isIFrame:r,cts:0})}else if(e===Xt.audioTrackId){if(Qt._opt.hasAudio)for(const e of r){const t=e.data,r=1e3*e.cts/e.timescale;e.duration,e.timescale;const i=new Uint8Array(t.byteLength+2);i.set([175,1],0),i.set(t,2),postMessage({cmd:L,type:fe,value:i.byteLength}),Qt.decode(i,{type:X,ts:r,isIFrame:!1,cts:0})}}else Qt.debug.warn(Xt.LOG_NAME,"onSamples() trackId error",e)},dispatch(e){let t=e;"string"!=typeof e?"object"==typeof e?(Xt.transportDescarmber&&(t=Xt.transportDescarmber.transport(t)),t.buffer.fileStart=Xt.offset,Xt.offset+=t.byteLength,Xt.mp4Box.appendBuffer(t.buffer)):Qt.debug.warn(Xt.LOG_NAME,"dispatch()","data is not object",e):Qt.debug.warn(Xt.LOG_NAME,"dispatch()","data is string",e)},destroy(){Xt.mp4Box&&(Xt.mp4Box.flush(),Xt.mp4Box=null),Xt.transportDescarmber&&(Xt.transportDescarmber.destroy(),Xt.transportDescarmber=null),Xt.offset=0,Xt.videoTrackId=null,Xt.audioTrackId=null,Xt.isHevc=!1}},Zt={LOG_NAME:"worker mpeg4Demuxer",lastBuffer:new Uint8Array(0),parsedOffset:0,firstStartCodeOffset:0,secondStartCodeOffset:0,state:"init",hasInitVideoCodec:!1,localDts:0,dispatch(e){const t=new Uint8Array(e);Zt.extractNALu(t)},destroy(){Zt.lastBuffer=new Uint8Array(0),Zt.parsedOffset=0,Zt.firstStartCodeOffset=0,Zt.secondStartCodeOffset=0,Zt.state="init",Zt.hasInitVideoCodec=!1,Zt.localDts=0},extractNALu(e){if(!e||e.byteLength<1)return void Qt.debug.warn(Zt.LOG_NAME,"extractNALu() buffer error",e);const t=new Uint8Array(Zt.lastBuffer.length+e.length);for(t.set(Zt.lastBuffer,0),t.set(new Uint8Array(e),Zt.lastBuffer.length),Zt.lastBuffer=t;;){if("init"===Zt.state){let e=!1;for(;Zt.lastBuffer.length-Zt.parsedOffset>=4;)if(0===Zt.lastBuffer[Zt.parsedOffset])if(0===Zt.lastBuffer[Zt.parsedOffset+1])if(1===Zt.lastBuffer[Zt.parsedOffset+2]){if(182===Zt.lastBuffer[Zt.parsedOffset+3]){Zt.state="findFirstStartCode",Zt.firstStartCodeOffset=Zt.parsedOffset,Zt.parsedOffset+=4,e=!0;break}Zt.parsedOffset++}else Zt.parsedOffset++;else Zt.parsedOffset++;else Zt.parsedOffset++;if(e)continue;break}if("findFirstStartCode"===Zt.state){let e=!1;for(;Zt.lastBuffer.length-Zt.parsedOffset>=4;)if(0===Zt.lastBuffer[Zt.parsedOffset])if(0===Zt.lastBuffer[Zt.parsedOffset+1])if(1===Zt.lastBuffer[Zt.parsedOffset+2]){if(182===Zt.lastBuffer[Zt.parsedOffset+3]){Zt.state="findSecondStartCode",Zt.secondStartCodeOffset=Zt.parsedOffset,Zt.parsedOffset+=4,e=!0;break}Zt.parsedOffset++}else Zt.parsedOffset++;else Zt.parsedOffset++;else Zt.parsedOffset++;if(e)continue;break}if("findSecondStartCode"===Zt.state){if(!(Zt.lastBuffer.length-Zt.parsedOffset>0))break;{let e,t,r=192&Zt.lastBuffer[Zt.parsedOffset];e=0==r?Zt.secondStartCodeOffset-14:Zt.secondStartCodeOffset;let i=0==(192&Zt.lastBuffer[Zt.firstStartCodeOffset+4]);if(i){if(Zt.firstStartCodeOffset-14<0)return void Qt.debug.warn(Zt.LOG_NAME,"firstStartCodeOffset -14 is",Zt.firstStartCodeOffset-14);Zt.hasInitVideoCodec||(Zt.hasInitVideoCodec=!0,Qt.debug.log(Zt.LOG_NAME,"setCodec"),tr.setCodec(Se,"")),t=Zt.lastBuffer.subarray(Zt.firstStartCodeOffset-14,e)}else t=Zt.lastBuffer.subarray(Zt.firstStartCodeOffset,e);let n=Zt.getNaluDts();Zt.hasInitVideoCodec?(postMessage({cmd:L,type:he,value:t.byteLength}),postMessage({cmd:L,type:pe,value:n}),tr.decode(t,i?1:0,n)):Qt.debug.warn(Zt.LOG_NAME,"has not init video codec"),Zt.lastBuffer=Zt.lastBuffer.subarray(e),Zt.firstStartCodeOffset=0==r?14:0,Zt.parsedOffset=Zt.firstStartCodeOffset+4,Zt.state="findFirstStartCode"}}}},getNaluDts(){let e=Zt.localDts;return Zt.localDts=Zt.localDts+40,e}},Jt={isFirstDispatch:!0,_pmtId:-1,_remainingPacketData:null,_videoPesData:[],_audioPesData:[],_gopId:0,_videoPid:-1,_audioPid:-1,_codecType:we,_audioCodecType:xe.AAC,_vps:null,_sps:null,_pps:null,TAG_NAME:"worker TsDemuxer",videoTrack:{samples:[]},audioTrack:{samples:[]},_baseDts:-1,_audioNextPts:void 0,_videoNextDts:void 0,_audioTimestampBreak:!1,_videoTimestampBreak:!1,_lastAudioExceptionGapDot:0,_lastAudioExceptionOverlapDot:0,_lastAudioExceptionLargeGapDot:0,_isSendAACSeqHeader:!1,init(){},dispatch(e){const t=new Uint8Array(e);Jt.demuxAndFix(t,Jt.isFirstDispatch,!0,0),Jt.isFirstDispatch&&(Jt.isFirstDispatch=!1)},_probe:e=>!!e.length&&(71===e[0]&&71===e[188]&&71===e[376]),_parsePES(e){const t=e[8];if(null==t||e.length<t+9)return;if(1!==(e[0]<<16|e[1]<<8|e[2]))return;const r=(e[4]<<8)+e[5];if(r&&r>e.length-6)return;let i,n;const s=e[7];return 192&s&&(i=536870912*(14&e[9])+4194304*(255&e[10])+16384*(254&e[11])+128*(255&e[12])+(254&e[13])/2,64&s?(n=536870912*(14&e[14])+4194304*(255&e[15])+16384*(254&e[16])+128*(255&e[17])+(254&e[18])/2,i-n>54e5&&(i=n)):n=i),{data:e.subarray(9+t),pts:i,dts:n,originalPts:i,originalDts:n}},demuxAndFix(e,t,r,i){Jt._demux(e,t,r),Jt._fix(i,t,r)},_initVideoTrack:()=>({samples:[]}),_initAudioTrack:()=>({samples:[]}),_demux(e,t=!1,r=!0){t&&(Jt._pmtId=-1,Jt.videoTrack=Jt._initVideoTrack(),Jt.audioTrack=Jt._initAudioTrack()),!r||t?(Jt._remainingPacketData=null,Jt._videoPesData=[],Jt._audioPesData=[]):(Jt.videoTrack.samples=[],Jt.audioTrack.samples=[],Jt._remainingPacketData&&(e=Nr(Jt._remainingPacketData,e),Jt._remainingPacketData=null));let i=e.length;const n=i%188;n&&(Jt._remainingPacketData=e.subarray(i-n),i-=n);for(let t=0;t<i;t+=188){if(71!==e[t])throw new Error("TS packet did not start with 0x47");const r=!!(64&e[t+1]),i=((31&e[t+1])<<8)+e[t+2];let n;if((48&e[t+3])>>4>1){if(n=t+5+e[t+4],n===t+188)continue}else n=t+4;switch(i){case 0:r&&(n+=e[n]+1),Jt._pmtId=(31&e[n+10])<<8|e[n+11];break;case Jt._pmtId:{r&&(n+=e[n]+1);const t=n+3+((15&e[n+1])<<8|e[n+2])-4;for(n+=12+((15&e[n+10])<<8|e[n+11]);n<t;){const t=(31&e[n+1])<<8|e[n+2];switch(e[n]){case 15:Jt._audioPid=t,Jt._audioCodecType=xe.AAC;break;case 27:Jt._videoPid=t,Jt._codecType=we;break;case 36:Jt._videoPid=t,Jt._codecType=Ee;break;default:Qt.debug.warn(Jt.TAG_NAME,`Unsupported stream. type: ${e[n]}, pid: ${t}`)}n+=5+((15&e[n+3])<<8|e[n+4])}}break;case Jt._videoPid:r&&Jt._videoPesData.length&&Jt._parseVideoData(),Jt._videoPesData.push(e.subarray(n,t+188));break;case Jt._audioPid:r&&Jt._audioPesData.length&&Jt._parseAudioData(),Jt._audioPesData.push(e.subarray(n,t+188));break;case 17:case 8191:break;default:Qt.debug.warn(Jt.TAG_NAME,`Unknown pid: ${i}`)}}Jt._parseVideoData(),Jt._parseAudioData(),Jt.audioTrack.formatTimescale=Jt.videoTrack.formatTimescale=Jt.videoTrack.timescale=9e4,Jt.audioTrack.timescale=Jt.audioTrack.sampleRate||0},_parseVideoData(){if(!Jt._videoPesData.length)return void Qt.debug.log(Jt.TAG_NAME,"_parseVideoData","no video pes data");const e=Jt._parsePES(Nr(...Jt._videoPesData));if(!e)return void Qt.debug.warn(Jt.TAG_NAME,"Cannot parse video pes data length",Jt._videoPesData.length);const t=function(e){const t=e.length;let r=2,i=0;for(;null!==e[r]&&void 0!==e[r]&&1!==e[r];)r++;if(r++,i=r+2,i>=t)return[];const n=[];for(;i<t;)switch(e[i]){case 0:if(0!==e[i-1]){i+=2;break}if(0!==e[i-2]){i++;break}r!==i-2&&n.push(e.subarray(r,i-2));do{i++}while(1!==e[i]&&i<t);r=i+1,i=r+2;break;case 1:if(0!==e[i-1]||0!==e[i-2]){i+=3;break}r!==i-2&&n.push(e.subarray(r,i-2)),r=i+1,i=r+2;break;default:i+=3}return r<t&&n.push(e.subarray(r)),n}(e.data);t?Jt._createVideoSample(t,e.pts,e.dts):Qt.debug.warn(Jt.TAG_NAME,"Cannot parse avc units",e),Jt._videoPesData=[]},_parseAudioData(){if(!Jt._audioPesData.length)return;const e=Jt._parsePES(Nr(...Jt._audioPesData));if(e){if(Qt._opt.hasAudio){if(Jt._audioCodecType===xe.AAC){const t=function(e,t){const r=e.length;let i=0;for(;i+2<r&&(255!==e[i]||240!=(246&e[i+1]));)i++;if(i>=r)return;const n=i,s=[],o=(60&e[i+2])>>>2,a=qe[o];if(!a)throw new Error(`Invalid sampling index: ${o}`);const l=1+((192&e[i+2])>>>6),d=(1&e[i+2])<<2|(192&e[i+3])>>>6;let u,c,f=0;const h=Ze(a);for(;i+7<r;)if(255===e[i]&&240==(246&e[i+1])){if(c=(3&e[i+3])<<11|e[i+4]<<3|(224&e[i+5])>>5,r-i<c)break;u=2*(1&~e[i+1]),s.push({pts:t+f*h,data:e.subarray(i+7+u,i+c)}),f++,i+=c}else i++;return{skip:n,remaining:i>=r?void 0:e.subarray(i),frames:s,samplingFrequencyIndex:o,sampleRate:a,objectType:l,channelCount:d,originCodec:`mp4a.40.${l}`}}(e.data,e.originalPts);if(t){if(Jt.audioTrack.codec=t.codec,Jt.audioTrack.sampleRate=t.sampleRate,Jt.audioTrack.channelCount=t.channelCount,!Jt._isSendAACSeqHeader){const e=je({profile:t.objectType,sampleRate:t.samplingFrequencyIndex,channel:t.channelCount});Jt._isSendAACSeqHeader=!0,Qt.debug.log(Jt.TAG_NAME,"aac seq header",`profile: ${t.objectType}, sampleRate:${t.sampleRate},sampleRateIndex: ${t.samplingFrequencyIndex}, channel: ${t.channelCount}`),Qt.decodeAudio(e,0)}if(Jt._isSendAACSeqHeader){const e=[];t.frames.forEach((t=>{const r=t.pts,i=new Uint8Array(t.data.length+2);i.set([175,1],0),i.set(t.data,2);const n={type:X,pts:r,dts:r,payload:i};e.push(n)})),Jt.audioTrack.samples=Jt.audioTrack.samples.concat(e)}else Qt.debug.warn(Jt.TAG_NAME,"aac seq header not send")}else Qt.debug.warn(Jt.TAG_NAME,"aac parseADTS error")}Jt._audioPesData=[]}}else Qt.debug.warn(Jt.TAG_NAME,"Cannot parse audio pes",Jt._audioPesData)},_fix(e=0,t=!1,r=!0){e=Math.round(9e4*e);const i=Jt.videoTrack,n=Jt.audioTrack,s=i.samples,o=n.samples;if(!s.length&&!o.length)return;const a=s[0],l=o[0];let d=0;if(s.length&&o.length&&(d=a.dts-l.pts),Jt._baseDtsInited||Jt._calculateBaseDts(),t&&(Jt._calculateBaseDts(),Jt._baseDts-=e),!r){Jt._videoNextDts=d>0?e+d:e,Jt._audioNextPts=d>0?e:e-d;const t=a?a.dts-Jt._baseDts-Jt._videoNextDts:0,r=l?l.pts-Jt._baseDts-Jt._audioNextPts:0;Math.abs(t||r)>MAX_VIDEO_FRAME_DURATION&&(Jt._calculateBaseDts(Jt.audioTrack,Jt.videoTrack),Jt._baseDts-=e)}Jt._resetBaseDtsWhenStreamBreaked(),Jt._fixAudio(n),Jt._fixVideo(i);let u=i.samples.concat(n.samples);u=u.map((e=>(e.dts=Math.round(e.dts/90),e.pts=Math.round(e.pts/90),e.cts=e.pts-e.dts,e))).sort(((e,t)=>e.dts-t.dts)),u.forEach((e=>{const t=new Uint8Array(e.payload);delete e.payload,e.type===Z?Jt._doDecodeVideo({...e,payload:t}):e.type===X&&Jt._doDecodeAudio({...e,payload:t})}))},_calculateBaseDts(){const e=Jt.audioTrack,t=Jt.videoTrack,r=e.samples,i=t.samples;if(!r.length&&!i.length)return!1;let n=1/0,s=1/0;r.length&&(e.baseDts=n=r[0].pts),i.length&&(t.baseDts=s=i[0].dts),Jt._baseDts=Math.min(n,s);const o=s-n;return Number.isFinite(o)&&Math.abs(o)>45e3&&Qt.debug.warn(Jt.TAG_NAME,`large av first frame gap,\n                video pts: ${s},\n                audio pts: ${n},\n                base dts: ${Jt._baseDts},\n                detect is: ${o}`),Jt._baseDtsInited=!0,!0},_resetBaseDtsWhenStreamBreaked(){if(Jt._baseDtsInited&&Jt._videoTimestampBreak&&Jt._audioTimestampBreak){if(!Jt._calculateBaseDts(Jt.audioTrack,Jt.videoTrack))return;Jt._baseDts-=Math.min(Jt._audioNextPts,Jt._videoNextDts),Jt._audioLastSample=null,Jt._videoLastSample=null,Jt._videoTimestampBreak=!1,Jt._audioTimestampBreak=!1}},_createVideoSample(e,t,r){if(!e.length)return;const i=Jt._codecType===Ee,n={isIFrame:!1,type:Z,isHevc:i,vps:null,sps:null,pps:null,pts:t,dts:r,payload:null};e.forEach((e=>{const t=i?e[0]>>>1&63:31&e[0];switch(t){case 5:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:if(!i&&5!==t||i&&5===t)break;n.isIFrame=!0,Jt._gopId++;break;case 6:case 39:case 40:if(!i&&6!==t||i&&6===t)break;return void function(e,t){const r=e.length;let i=t?2:1,n=0,s=0,o="";for(;255===e[i];)n+=255,i++;for(n+=e[i++];255===e[i];)s+=255,i++;if(s+=e[i++],5===n&&r>i+16)for(let t=0;t<16;t++)o+=e[i].toString(16),i++;e.subarray(i)}(function(e){const t=e.byteLength,r=[];let i=1;for(;i<t-2;)0===e[i]&&0===e[i+1]&&3===e[i+2]?(r.push(i+2),i+=2):i++;if(!r.length)return e;const n=t-r.length,s=new Uint8Array(n);let o=0;for(i=0;i<n;o++,i++)o===r[0]&&(o++,r.shift()),s[i]=e[o];return s}(e),i);case 32:if(!i)break;n.vps||(n.vps=e);break;case 7:case 33:if(!i&&7!==t||i&&7===t)break;n.sps||(n.sps=e);break;case 8:case 34:if(!i&&8!==t||i&&8===t)break;n.pps||(n.pps=e)}if(i&&zt(t)||!i&&St(t)){const t=Ft(e);if(n.payload){const e=new Uint8Array(n.payload.byteLength+t.byteLength);e.set(n.payload,0),e.set(t,n.payload.byteLength),n.payload=e}else n.payload=t}}));let s=null;i?n.sps&&n.vps&&n.pps&&(s=Lt({vps:n.vps,sps:n.sps,pps:n.pps})):n.sps&&n.pps&&(s=function({sps:e,pps:t}){let r=8+e.byteLength+1+2+t.byteLength,i=!1;const n=_t.parseSPS$2(e);66!==e[3]&&77!==e[3]&&88!==e[3]&&(i=!0,r+=4);let s=new Uint8Array(r);s[0]=1,s[1]=e[1],s[2]=e[2],s[3]=e[3],s[4]=255,s[5]=225;let o=e.byteLength;s[6]=o>>>8,s[7]=255&o;let a=8;s.set(e,8),a+=o,s[a]=1;let l=t.byteLength;s[a+1]=l>>>8,s[a+2]=255&l,s.set(t,a+3),a+=3+l,i&&(s[a]=252|n.chroma_format_idc,s[a+1]=248|n.bit_depth_luma-8,s[a+2]=248|n.bit_depth_chroma-8,s[a+3]=0,a+=4);const d=[23,0,0,0,0],u=new Uint8Array(d.length+s.byteLength);return u.set(d,0),u.set(s,d.length),u}({sps:n.sps,pps:n.pps})),s&&(Qt.debug.log(Jt.TAG_NAME,"_createVideoSample","seqHeader"),Qt.decodeVideo(s,Z,Math.round(n.pts/90),!0,0)),n.isIFrame&&Qt.calcIframeIntervalTimestamp(Math.round(n.dts/90)),Jt.videoTrack.samples=Jt.videoTrack.samples.concat(n)},_fixAudio(e){const t=e.samples;t.length&&(t.forEach((e=>{e.pts-=Jt._baseDts,e.dts=e.pts})),Jt._doFixAudioInternal(e,t,9e4))},_fixVideo(e){const t=e.samples;if(!t.length)return;if(t.forEach((e=>{e.dts-=Jt._baseDts,e.pts-=Jt._baseDts})),void 0===Jt._videoNextDts){const e=t[0];Jt._videoNextDts=e.dts}const r=t.length;let i=0;const n=t[0],s=t[1],o=Jt._videoNextDts-n.dts;let a;Math.abs(o)>45e3&&(n.dts+=o,n.pts+=o,Qt.debug.warn(Jt.TAG_NAME,`large video gap between chunk,\n             next dts is ${Jt._videoNextDts},\n             first dts is ${n.dts},\n             next dts is ${s.dts},\n             duration is ${o}`),s&&Math.abs(s.dts-n.dts)>9e4&&(Jt._videoTimestampBreak=!0,t.forEach(((e,t)=>{0!==t&&(e.dts+=o,e.pts+=o)}))));const l=e.samples[0],d=e.samples[r-1];a=1===r?9e3:Math.floor((d.dts-l.dts)/(r-1));for(let n=0;n<r;n++){const s=t[n].dts,o=t[n+1];if(i=n<r-1?o.dts-s:t[n-1]?Math.min(s-t[n-1].dts,a):a,i>9e4||i<0){Jt._videoTimestampBreak=!0,i=Jt._audioTimestampBreak?a:Math.max(i,2700);const r=Jt._audioNextPts||0;o&&o.dts>r&&(i=a),Qt.debug.warn(Jt.TAG_NAME,`large video gap between frames,\n                time is ${s/e.timescale},\n                dts is ${s},\n                origin dts is ${t[n].originalDts},\n                next dts is ${Jt._videoNextDts},\n                sample Duration is ${i} ,\n                ref Sample DurationInt is ${a}`)}t[n].duration=i,Jt._videoNextDts+=i}},_doFixAudioInternal(e,t,r){e.sampleDuration||(e.sampleDuration=Ze(e.timescale,r));const i=e.sampleDuration;if(void 0===Jt._audioNextPts){const e=t[0];Jt._audioNextPts=e.pts}for(let r=0;r<t.length;r++){const n=Jt._audioNextPts,s=t[r],o=s.pts-n;if(!Jt._audioTimestampBreak&&o>=3*i&&o<=Ge&&!tt()){Xe(e.codec,e.channelCount)||t[0].data.subarray();const a=Math.floor(o/i);Math.abs(s.pts-Jt._lastAudioExceptionGapDot)>AUDIO_EXCETION_LOG_EMIT_DURATION&&(Jt._lastAudioExceptionGapDot=s.pts),Qt.debug.warn(Jt.TAG_NAME,`audio gap detected,\n                pts is ${t.pts},\n                originPts is ${t.originalPts},\n                count is ${a},\n                nextPts is ${n},\n                ref sample duration is ${i}`);for(let e=0;e<a;e++)Jt._audioNextPts+=i,r++;r--}else o<=-3*i&&o>=-9e4?(Math.abs(s.pts-Jt._lastAudioExceptionOverlapDot)>He&&(Jt._lastAudioExceptionOverlapDot=s.pts,Qt.debug.warn(Jt.TAG_NAME,`audio overlap detected,\n                    pts is ${s.pts},\n                    originPts is ${s.originalPts},\n                    nextPts is ${n},\n                    ref sample duration is ${i}`)),t.splice(r,1),r--):(Math.abs(o)>=Ge&&(Jt._audioTimestampBreak=!0,Math.abs(s.pts-Jt._lastAudioExceptionLargeGapDot)>He&&(Jt._lastAudioExceptionLargeGapDot=s.pts,Qt.debug.warn(Jt.TAG_NAME,`large audio gap detected,\n                        time is ${s.pts/1e3}\n                        pts is ${s.pts},\n                        originPts is ${s.originalPts},\n                        nextPts is ${n},\n                        sample duration is ${o}\n                        ref sample duration is ${i}`))),s.dts=s.pts=n,Jt._audioNextPts+=i)}},_doDecodeVideo(e){const t=new Uint8Array(e.payload);let r=null;r=e.isHevc?Rt(t,e.isIFrame):bt(t,e.isIFrame),postMessage({cmd:L,type:he,value:r.byteLength}),postMessage({cmd:L,type:pe,value:e.dts});const i=e.pts-e.dts;let n=Qt.cryptoPayload(r,e.isIFrame);Qt.decode(n,{type:Z,ts:e.dts,isIFrame:e.isIFrame,cts:i})},_doDecodeAudio(){const e=new Uint8Array(sample.payload);postMessage({cmd:L,type:fe,value:e.byteLength});let t=e;at(Qt._opt.m7sCryptoAudio)&&(t=Qt.cryptoPayloadAudio(e)),Qt.decode(t,{type:X,ts:sample.dts,isIFrame:!1,cts:0})},destroy(){Jt.videoTrack=null,Jt.audioTrack=null,Jt.tempSampleListInfo={},Jt._baseDts=-1,Jt._baseDtsInited=!1,Jt._basefps=50,Jt._hasCalcFps=!1,Jt._audioNextPts=void 0,Jt._videoNextDts=void 0,Jt._audioTimestampBreak=!1,Jt._videoTimestampBreak=!1,Jt._lastAudioExceptionGapDot=0,Jt._lastAudioExceptionOverlapDot=0,Jt._lastAudioExceptionLargeGapDot=0,Jt._isForHls=!0,Jt._isSendAACSeqHeader=!1,Qt.debug.log(Jt.TAG_NAME,"destroy")}},Qt={isPlayer:!0,isPlayback:!1,dropping:!1,isPushDropping:!1,isWorkerFetch:!1,isDestroyed:!1,fetchStatus:Le,_opt:Yt(),mp3Demuxer:null,delay:-1,pushLatestDelay:-1,firstTimestamp:null,startTimestamp:null,preDelayTimestamp:null,stopId:null,streamFps:null,streamAudioFps:null,streamVideoFps:null,writableStream:null,networkDelay:0,webglObj:null,startStreamRateAndStatsInterval:function(){Qt.stopStreamRateAndStatsInterval(),d=setInterval((()=>{l&&l(0);const e=JSON.stringify({demuxBufferDelay:Qt.getVideoBufferLength(),audioDemuxBufferDelay:Qt.getAudioBufferLength(),flvBufferByteLength:Qt.getFlvBufferLength(),netBuf:Qt.networkDelay||0,pushLatestDelay:Qt.pushLatestDelay||0,latestDelay:Qt.delay,isStreamTsMoreThanLocal:Qe});postMessage({cmd:L,type:_e,value:e})}),1e3)},stopStreamRateAndStatsInterval:function(){d&&(clearInterval(d),d=null)},useOffscreen:function(){return Qt._opt.useOffscreen&&"undefined"!=typeof OffscreenCanvas},getDelay:function(e,t){if(!e||Qt._opt.hasVideo&&!Ve)return-1;if(t===X)return Qt.delay;if(Qt.preDelayTimestamp&&Qt.preDelayTimestamp>e)return Qt.preDelayTimestamp-e>1e3&&Qt.debug.warn("worker",`getDelay() and preDelayTimestamp is ${Qt.preDelayTimestamp} > timestamp is ${e} more than ${Qt.preDelayTimestamp-e}ms and return ${Qt.delay}`),Qt.preDelayTimestamp=e,Qt.delay;if(Qt.firstTimestamp){if(e){const t=Date.now()-Qt.startTimestamp,r=e-Qt.firstTimestamp;t>=r?(Qe=!1,Qt.delay=t-r):(Qe=!0,Qt.delay=r-t)}}else Qt.firstTimestamp=e,Qt.startTimestamp=Date.now(),Qt.delay=-1;return Qt.preDelayTimestamp=e,Qt.delay},getDelayNotUpdateDelay:function(e,t){if(!e||Qt._opt.hasVideo&&!Ve)return-1;if(t===X)return Qt.pushLatestDelay;if(Qt.preDelayTimestamp&&Qt.preDelayTimestamp-e>1e3)return Qt.debug.warn("worker",`getDelayNotUpdateDelay() and preDelayTimestamp is ${Qt.preDelayTimestamp} > timestamp is ${e} more than ${Qt.preDelayTimestamp-e}ms and return -1`),-1;if(Qt.firstTimestamp){let t=-1;if(e){const r=Date.now()-Qt.startTimestamp,i=e-Qt.firstTimestamp;r>=i?(Qe=!1,t=r-i):(Qe=!0,t=i-r)}return t}return-1},resetDelay:function(){Qt.firstTimestamp=null,Qt.startTimestamp=null,Qt.delay=-1,Qt.dropping=!1},resetAllDelay:function(){Qt.resetDelay(),Qt.preDelayTimestamp=null},doDecode:function(e){Qt._opt.isEmitSEI&&e.type===Z&&Qt.isWorkerFetch&&Qt.findSei(e.payload,e.ts),Qt._opt.useWCS&&Qt.useOffscreen()&&e.type===Z&&s.decode?s.decode(e.payload,e.ts,e.cts):e.decoder.decode(e.payload,e.ts,e.isIFrame,e.cts)},decodeNext(e){if(0===i.length)return;const t=e.ts,n=i[0],s=e.type===Z&&ot(e.payload);if(lt(r))s&&(Qt.debug.log("worker",`decode data type is ${e.type} and\n                ts is ${t} next data type is ${n.type} ts is ${n.ts}\n                isVideoSqeHeader is ${s}`),i.shift(),Qt.doDecode(n));else{const r=n.ts-t,o=n.type===X&&e.type===Z;(r<=20||o||s)&&(Qt.debug.log("worker",`decode data type is ${e.type} and\n                ts is ${t} next data type is ${n.type} ts is ${n.ts}\n                diff is ${r} and isVideoAndNextAudio is ${o} and isVideoSqeHeader is ${s}`),i.shift(),Qt.doDecode(n))}},init:function(){Qt.debug.log("worker","init and opt is",JSON.stringify(Qt._opt));const e=Qt._opt.playType===m,t=Qt._opt.playType===_;if(Kt.init(),Qt.isPlayer=e,Qt.isPlayback=t,Qt.isPlaybackCacheBeforeDecodeForFpsRender())Qt.debug.log("worker","playback and playbackIsCacheBeforeDecodeForFpsRender is true");else{Qt.debug.log("worker","setInterval()");const t=Qt._opt.videoBuffer+Qt._opt.videoBufferDelay,r=()=>{let r=null;if(i.length){if(Qt.isPushDropping)return void Qt.debug.warn("worker",`loop() isPushDropping is true and bufferList length is ${i.length}`);if(Qt.dropping){for(r=i.shift(),Qt.debug.warn("worker",`loop() dropBuffer is dropping and isIFrame ${r.isIFrame} and delay is ${Qt.delay} and bufferlist is ${i.length}`);!r.isIFrame&&i.length;)r=i.shift();const e=Qt.getDelayNotUpdateDelay(r.ts,r.type);r.isIFrame&&e<=Qt.getNotDroppingDelayTs()&&(Qt.debug.log("worker","loop() is dropping = false, is iFrame"),Qt.dropping=!1,Qt.doDecode(r),Qt.decodeNext(r))}else if(Qt.isPlayback||Qt.isPlayUseMSE()||0===Qt._opt.videoBuffer)for(;i.length;)r=i.shift(),Qt.doDecode(r);else if(r=i[0],-1===Qt.getDelay(r.ts,r.type))Qt.debug.log("worker","loop() common dumex delay is -1 ,data.ts is",r.ts),i.shift(),Qt.doDecode(r),Qt.decodeNext(r);else if(Qt.delay>t&&e)Qt.hasIframeInBufferList()?(Qt.debug.log("worker",`delay is ${Qt.delay} > maxDelay ${t}, set dropping is true`),Qt.resetAllDelay(),Qt.dropping=!0,postMessage({cmd:M})):(i.shift(),Qt.doDecode(r),Qt.decodeNext(r));else for(;i.length;){if(r=i[0],!(Qt.getDelay(r.ts,r.type)>Qt._opt.videoBuffer)){Qt.delay<0&&Qt.debug.warn("worker",`loop() do not decode and delay is ${Qt.delay}, bufferList is ${i.length}`);break}i.shift(),Qt.doDecode(r)}}else-1!==Qt.delay&&Qt.debug.log("worker","loop() bufferList is empty and reset delay"),Qt.resetAllDelay()};Qt.stopId=setInterval((()=>{let e=(new Date).getTime();dt||(dt=e);const t=e-dt;t>100&&Qt.debug.warn("worker",`loop demux diff time is ${t}`),r(),dt=(new Date).getTime()}),20)}lt(Qt._opt.checkFirstIFrame)&&(Ve=!0)},playbackCacheLoop:function(){Qt.stopId&&(clearInterval(Qt.stopId),Qt.stopId=null);const e=()=>{let e=null;i.length&&(e=i.shift(),Qt.doDecode(e))};e();const t=Math.ceil(1e3/(Qt.streamFps*Qt._opt.playbackRate));Qt.debug.log("worker",`playbackCacheLoop fragDuration is ${t}, streamFps is ${Qt.streamFps}, streamAudioFps is ${Qt.streamAudioFps} ,streamVideoFps is ${Qt.streamVideoFps} playbackRate is ${Qt._opt.playbackRate}`),Qt.stopId=setInterval(e,t)},close:function(){if(Qt.debug.log("worker","close"),Qt.isDestroyed=!0,qt(),!a||1!==a.readyState&&2!==a.readyState?a&&Qt.debug.log("worker",`close() and socket.readyState is ${a.readyState}`):(Wt=!0,a.close(1e3,"Client disconnecting")),a=null,Qt.stopStreamRateAndStatsInterval(),Qt.stopId&&(clearInterval(Qt.stopId),Qt.stopId=null),Qt.mp3Demuxer&&(Qt.mp3Demuxer.destroy(),Qt.mp3Demuxer=null),Qt.writableStream&&lt(Qt.writableStream.locked)&&Qt.writableStream.close().catch((e=>{Qt.debug.log("worker","close() and writableStream.close() error",e)})),Qt.writableStream=null,er)try{er.clear&&er.clear(),er=null}catch(e){Qt.debug.warn("worker","close() and audioDecoder.clear error",e)}if(tr)try{tr.clear&&tr.clear(),tr=null}catch(e){Qt.debug.warn("worker","close() and videoDecoder.clear error",e)}l=null,dt=null,Qe=!1,s&&(s.reset&&s.reset(),s=null),Qt.firstTimestamp=null,Qt.startTimestamp=null,Qt.networkDelay=0,Qt.streamFps=null,Qt.streamAudioFps=null,Qt.streamVideoFps=null,Qt.delay=-1,Qt.pushLatestDelay=-1,Qt.preDelayTimestamp=null,Qt.dropping=!1,Qt.isPushDropping=!1,Qt.isPlayer=!0,Qt.isPlayback=!1,Qt.isWorkerFetch=!1,Qt._opt=Yt(),Qt.webglObj&&(Qt.webglObj.destroy(),Qt.offscreenCanvas.removeEventListener("webglcontextlost",Qt.onOffscreenCanvasWebglContextLost),Qt.offscreenCanvas.removeEventListener("webglcontextrestored",Qt.onOffscreenCanvasWebglContextRestored),Qt.offscreenCanvas=null,Qt.offscreenCanvasGL=null,Qt.offscreenCanvasCtx=null),i=[],n=[],g=null,v=null,w=null,Fe=!1,Pe=!1,Ve=!1,Ot=!1,$t=!1,Gt=!1,Ht=null,Vt=null,ft=[],xt=0,At=0,ut=null,ct=null,kt=null,Tt=null,jt=null,Bt=0,Ct=0,mt=null,yt=null,Qt.fetchStatus=Le,Kt.destroy(),Xt.destroy(),Zt.destroy(),Jt.destroy(),postMessage({cmd:G})},pushBuffer:function(e,t){if(t.type===X&&We(e)){if(Qt.debug.log("worker",`pushBuffer audio ts is ${t.ts}, isAacCodecPacket is true`),Qt._opt.isRecordTypeFlv){const t=new Uint8Array(e);postMessage({cmd:W,buffer:t},[t.buffer])}Qt.decodeAudio(e,t.ts)}else if(t.type===Z&&t.isIFrame&&ot(e)){if(Qt.debug.log("worker",`pushBuffer video ts is ${t.ts}, isVideoSequenceHeader is true`),Qt._opt.isRecordTypeFlv){const t=new Uint8Array(e);postMessage({cmd:Y,buffer:t},[t.buffer])}Qt.decodeVideo(e,t.ts,t.isIFrame,t.cts)}else{if(Qt._opt.isRecording)if(Qt._opt.isRecordTypeFlv){const r=new Uint8Array(e);postMessage({cmd:q,type:t.type,buffer:r,ts:t.ts},[r.buffer])}else if(Qt._opt.recordType===y)if(t.type===Z){const r=new Uint8Array(e).slice(5);postMessage({cmd:F,buffer:r,isIFrame:t.isIFrame,ts:t.ts,cts:t.cts},[r.buffer])}else if(t.type===X&&Qt._opt.isWasmMp4){const r=new Uint8Array(e),i=Ye(r)?r.slice(2):r.slice(1);postMessage({cmd:T,buffer:i,ts:t.ts},[i.buffer])}if(Qt.isPlayer&&Bt>0&&Tt>0&&t.type===Z){const e=t.ts-Tt,r=Bt+Bt/2;e>r&&Qt.debug.log("worker",`pushBuffer video\n                    ts is ${t.ts}, preTimestamp is ${Tt},\n                    diff is ${e} and preTimestampDuration is ${Bt} and maxDiff is ${r}\n                    maybe trigger black screen or flower screen\n                    `)}if(Qt.isPlayer&&Tt>0&&t.type===Z&&t.ts<Tt&&Tt-t.ts>b&&(Qt.debug.warn("worker",`pushBuffer,\n                preTimestamp is ${Tt}, options.ts is ${t.ts},\n                diff is ${Tt-t.ts} more than 3600000,\n                and resetAllDelay`),Qt.resetAllDelay(),Tt=null,Bt=0),Qt.isPlayer&&Tt>0&&t.ts<=Tt&&t.type===Z&&(Qt.debug.warn("worker",`pushBuffer,\n                options.ts is ${t.ts} less than (or equal) preTimestamp is ${Tt} and\n                payloadBufferSize is ${e.byteLength} and prevPayloadBufferSize is ${Ct}`),Qt._opt.isDropSameTimestampGop&&Ve)){const e=Qt.hasIframeInBufferList(),t=lt(Qt.isPushDropping);return Qt.debug.log("worker",`pushBuffer, isDropSameTimestampGop is true and\n                    hasIframe is ${e} and isNotPushDropping is ${t} and next dropBuffer`),void(e&&t?Qt.dropBuffer$2():(Qt.clearBuffer(!0),at(Qt._opt.checkFirstIFrame)&&at(r)&&postMessage({cmd:K})))}if(Qt.isPlayer&&Ve){const e=Qt._opt.videoBuffer+Qt._opt.videoBufferDelay,r=Qt.getDelayNotUpdateDelay(t.ts,t.type);Qt.pushLatestDelay=r,r>e&&Qt.delay<e&&Qt.delay>0&&Qt.hasIframeInBufferList()&&!1===Qt.isPushDropping&&(Qt.debug.log("worker",`pushBuffer(), pushLatestDelay is ${r} more than ${e} and decoder.delay is ${Qt.delay} and has iIframe and next decoder.dropBuffer$2()`),Qt.dropBuffer$2())}if(Qt.isPlayer&&t.type===Z&&(Tt>0&&(Bt=t.ts-Tt),Ct=e.byteLength,Tt=t.ts),t.type===X?i.push({ts:t.ts,payload:e,decoder:{decode:Qt.decodeAudio},type:X,isIFrame:!1}):t.type===Z&&i.push({ts:t.ts,cts:t.cts,payload:e,decoder:{decode:Qt.decodeVideo},type:Z,isIFrame:t.isIFrame}),Qt.isPlaybackCacheBeforeDecodeForFpsRender()&&(rt(Qt.streamVideoFps)||rt(Qt.streamAudioFps))){let e=Qt.streamVideoFps,t=Qt.streamAudioFps;if(rt(Qt.streamVideoFps)&&(e=nt(i,Z),e&&(Qt.streamVideoFps=e,postMessage({cmd:z,value:Qt.streamVideoFps}),Qt.streamFps=t?e+t:e,lt(Qt._opt.hasAudio)&&(Qt.debug.log("worker","playbackCacheBeforeDecodeForFpsRender, _opt.hasAudio is false and set streamAudioFps is 0"),Qt.streamAudioFps=0),Qt.playbackCacheLoop())),rt(Qt.streamAudioFps)&&(t=nt(i,X),t&&(Qt.streamAudioFps=t,Qt.streamFps=e?e+t:t,Qt.playbackCacheLoop())),rt(Qt.streamVideoFps)&&rt(Qt.streamAudioFps)){const r=i.map((e=>({type:e.type,ts:e.ts})));Qt.debug.log("worker",`playbackCacheBeforeDecodeForFpsRender, calc streamAudioFps is ${t}, streamVideoFps is ${e}, bufferListLength  is ${i.length}, and ts list is ${JSON.stringify(r)}`)}const r=Qt.getAudioBufferLength()>0,n=r?60:40;i.length>=n&&(Qt.debug.warn("worker",`playbackCacheBeforeDecodeForFpsRender, bufferListLength  is ${i.length} more than ${n}, and hasAudio is ${r} an set streamFps is 25`),Qt.streamVideoFps=25,postMessage({cmd:z,value:Qt.streamVideoFps}),r?(Qt.streamAudioFps=25,Qt.streamFps=Qt.streamVideoFps+Qt.streamAudioFps):Qt.streamFps=Qt.streamVideoFps,Qt.playbackCacheLoop())}}},getVideoBufferLength(){let e=0;return i.forEach((t=>{t.type===Z&&(e+=1)})),e},hasIframeInBufferList:()=>i.some((e=>e.type===Z&&e.isIFrame)),isAllIframeInBufferList(){const e=Qt.getVideoBufferLength();let t=0;return i.forEach((e=>{e.type===Z&&e.isIFrame&&(t+=1)})),e===t},getNotDroppingDelayTs:()=>Qt._opt.videoBuffer+Qt._opt.videoBufferDelay/2,getAudioBufferLength(){let e=0;return i.forEach((t=>{t.type===X&&(e+=1)})),e},getFlvBufferLength(){let e=0;return g&&g.buffer&&(e=g.buffer.byteLength),Qt._opt.isNakedFlow&&Kt.lastBuf&&(e=Kt.lastBuf.byteLength),e},fetchStream:function(e,t){Qt.debug.log("worker","fetchStream, url is "+e,"options:",JSON.stringify(t)),Qt.isWorkerFetch=!0,t.isFlv?Qt._opt.isFlv=!0:t.isFmp4?Qt._opt.isFmp4=!0:t.isMpeg4?Qt._opt.isMpeg4=!0:t.isNakedFlow?Qt._opt.isNakedFlow=!0:t.isTs&&(Qt._opt.isTs=!0),l=et((e=>{postMessage({cmd:L,type:ce,value:e})})),Qt.startStreamRateAndStatsInterval(),t.isFmp4&&(Xt.listenMp4Box(),Qt._opt.isFmp4Private&&Xt.initTransportDescarmber()),t.protocol===f?(g=new ht(Qt.demuxFlv()),fetch(e,{signal:o.signal}).then((e=>{if(at(Wt))return Qt.debug.log("worker","request abort and run res.body.cancel()"),Qt.fetchStatus=Le,void e.body.cancel();if(!function(e){return e.ok&&e.status>=200&&e.status<=299}(e))return Qt.debug.warn("worker",`fetch response status is ${e.status} and ok is ${e.ok} and emit error and next abort()`),qt(),void postMessage({cmd:L,type:ve.fetchError,value:`fetch response status is ${e.status} and ok is ${e.ok}`});if(postMessage({cmd:L,type:me}),"undefined"!=typeof WritableStream)Qt.writableStream=new WritableStream({write:e=>o&&o.signal&&o.signal.aborted?(Qt.debug.log("worker","writableStream write() and abortController.signal.aborted is true so return"),void(Qt.fetchStatus=Me)):at(Wt)?(Qt.debug.log("worker","writableStream write() and requestAbort is true so return"),void(Qt.fetchStatus=Me)):(Qt.fetchStatus=Re,l(e.byteLength),void(t.isFlv?g.write(e):t.isFmp4?Qt.demuxFmp4(e):t.isMpeg4?Qt.demuxMpeg4(e):t.isTs&&Qt.demuxTs(e))),close:()=>{Qt.fetchStatus=Me,g=null,qt(),postMessage({cmd:L,type:ue,value:h,msg:"fetch done"})},abort:e=>{if(o&&o.signal&&o.signal.aborted)return Qt.debug.log("worker","writableStream abort() and abortController.signal.aborted is true so return"),void(Qt.fetchStatus=Me);g=null,e.name!==De&&(qt(),postMessage({cmd:L,type:ve.fetchError,value:e.toString()}))}}),e.body.pipeTo(Qt.writableStream);else{const r=e.body.getReader(),i=()=>{r.read().then((({done:e,value:r})=>e?(Qt.fetchStatus=Me,g=null,qt(),void postMessage({cmd:L,type:ue,value:h,msg:"fetch done"})):o&&o.signal&&o.signal.aborted?(Qt.debug.log("worker","fetchNext().then() and abortController.signal.aborted is true so return"),void(Qt.fetchStatus=Me)):at(Wt)?(Qt.debug.log("worker","fetchNext().then() and requestAbort is true so return"),void(Qt.fetchStatus=Me)):(Qt.fetchStatus=Re,l(r.byteLength),t.isFlv?g.write(r):t.isFmp4?Qt.demuxFmp4(r):t.isMpeg4&&Qt.demuxMpeg4(r),void i()))).catch((e=>{if(o&&o.signal&&o.signal.aborted)return Qt.debug.log("worker","fetchNext().catch() and abortController.signal.aborted is true so return"),void(Qt.fetchStatus=Me);g=null,e.name!==De&&(qt(),postMessage({cmd:L,type:ve.fetchError,value:e.toString()}))}))};i()}})).catch((e=>{o&&o.signal&&o.signal.aborted?Qt.debug.log("worker","fetch().catch() and abortController.signal.aborted is true so return"):e.name!==De&&(qt(),postMessage({cmd:L,type:ve.fetchError,value:e.toString()}),g=null)}))):t.protocol===c&&(t.isFlv&&(g=new ht(Qt.demuxFlv())),a=new WebSocket(e),a.binaryType="arraybuffer",a.onopen=()=>{Qt.debug.log("worker","fetchStream, WebsocketStream  socket open"),postMessage({cmd:L,type:me}),postMessage({cmd:L,type:ge})},a.onclose=e=>{Qt.debug.log("worker",`fetchStream, WebsocketStream socket close and code is ${e.code}`),1006===e.code&&Qt.debug.error("worker",`fetchStream, WebsocketStream socket close abnormally and code is ${e.code}`),at(Wt)?Qt.debug.log("worker","fetchStream, WebsocketStream socket close and requestAbort is true so return"):(g=null,postMessage({cmd:L,type:ue,value:p,msg:e.code}))},a.onerror=e=>{Qt.debug.error("worker","fetchStream, WebsocketStream socket error",e),g=null,postMessage({cmd:L,type:ve.websocketError,value:e.isTrusted?"websocket user aborted":"websocket error"})},a.onmessage=e=>{l(e.data.byteLength),t.isFlv?g.write(e.data):t.isFmp4?Qt.demuxFmp4(e.data):t.isMpeg4?Qt.demuxMpeg4(e.data):Qt._opt.isNakedFlow?Qt.demuxNakedFlow(e.data):Qt.demuxM7s(e.data)})},demuxFlv:function*(){yield 9;const e=new ArrayBuffer(4),t=new Uint8Array(e),r=new Uint32Array(e);for(;;){t[3]=0;const e=yield 15,i=e[4];t[0]=e[7],t[1]=e[6],t[2]=e[5];const n=r[0];t[0]=e[10],t[1]=e[9],t[2]=e[8],t[3]=e[11];let s=r[0];const o=(yield n).slice();switch(i){case J:if(o.byteLength>0){let e=o;at(Qt._opt.m7sCryptoAudio)&&(e=Qt.cryptoPayloadAudio(o)),Qt.decode(e,{type:X,ts:s})}else Qt.debug.warn("worker",`demuxFlv() type is audio and payload.byteLength is ${o.byteLength} and return`);break;case Q:if(o.byteLength>=6){const e=o[0];if(Qt._isEnhancedH265Header(e))Qt._decodeEnhancedH265Video(o,s);else{o[0];const e=o[0]>>4===Ie;if(e&&ot(o)&&null===Ht){const e=15&o[0];Ht=e===Ee,Vt=Pt(o,Ht),Qt.debug.log("worker",`demuxFlv() isVideoSequenceHeader is true and isHevc is ${Ht} and nalUnitSize is ${Vt}`)}e&&Qt.calcIframeIntervalTimestamp(s),Qt.isPlayer&&Qt.calcNetworkDelay(s),r[0]=o[4],r[1]=o[3],r[2]=o[2],r[3]=0;let t=r[0],i=Qt.cryptoPayload(o,e);Qt.decode(i,{type:Z,ts:s,isIFrame:e,cts:t})}}else Qt.debug.warn("worker",`demuxFlv() type is video and payload.byteLength is ${o.byteLength} and return`);break;case ee:postMessage({cmd:j,buffer:o},[o.buffer]);break;default:Qt.debug.log("worker",`demuxFlv() type is ${i}`)}}},decode:function(e,t){t.type===X?Qt._opt.hasAudio&&(postMessage({cmd:L,type:fe,value:e.byteLength}),Qt.isPlayer?Qt.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts}):Qt.isPlayback&&(Qt.isPlaybackOnlyDecodeIFrame()||(Qt.isPlaybackCacheBeforeDecodeForFpsRender(),Qt.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts})))):t.type===Z&&Qt._opt.hasVideo&&(postMessage({cmd:L,type:he,value:e.byteLength}),postMessage({cmd:L,type:pe,value:t.ts}),Qt.isPlayer?Qt.pushBuffer(e,{type:t.type,ts:t.ts,isIFrame:t.isIFrame,cts:t.cts}):Qt.isPlayback&&(Qt.isPlaybackOnlyDecodeIFrame()?t.isIFrame&&Qt.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts,isIFrame:t.isIFrame}):(Qt.isPlaybackCacheBeforeDecodeForFpsRender(),Qt.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts,isIFrame:t.isIFrame}))))},cryptoPayload:function(e,t){let r=e;return Qt._opt.isM7sCrypto?Qt._opt.cryptoIV&&Qt._opt.cryptoIV.byteLength>0&&Qt._opt.cryptoKey&&Qt._opt.cryptoKey.byteLength>0?r=function(e,t,r,i=!1){t=new Uint8Array(t),r=new Uint8Array(r);const n=e.byteLength;let s=5;for(;s<n;){let a=(o=e.slice(s,s+4))[3]|o[2]<<8|o[1]<<16|o[0]<<24;if(a>n)break;let l=e[s+4],d=!1;if(i?(l=l>>>1&63,d=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(l)):(l&=31,d=1===l||5===l),d){const i=e.slice(s+4+2,s+4+a);let n=new gr.ModeOfOperation.ctr(t,new gr.Counter(r));const o=n.decrypt(i);n=null,e.set(o,s+4+2)}s=s+4+a}var o;return e}(e,Qt._opt.cryptoKey,Qt._opt.cryptoIV,Ht):Qt.debug.error("worker",`isM7sCrypto cryptoKey.length is ${Qt._opt.cryptoKey&&Qt._opt.cryptoKey.byteLength} or cryptoIV.length is ${Qt._opt.cryptoIV&&Qt._opt.cryptoIV.byteLength} null`):Qt._opt.isSm4Crypto?Qt._opt.sm4CryptoKey&&t?r=function(e,t,r=!1){const i=e.byteLength;let n=5;for(;n<i;){let o=(s=e.slice(n,n+4))[3]|s[2]<<8|s[1]<<16|s[0]<<24;if(o>i)break;let a=e[n+4],l=!1;if(r?(a=a>>>1&63,l=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(a)):(a&=31,l=1===a||5===a),l){const r=Ir(e.slice(n+4+2,n+4+o),t,0,{padding:"none",output:"array"});e.set(r,n+4+2)}n=n+4+o}var s;return e}(e,Qt._opt.sm4CryptoKey):Qt._opt.sm4CryptoKey||Qt.debug.error("worker","isSm4Crypto opt.sm4CryptoKey is null"):Qt._opt.isXorCrypto&&(Qt._opt.cryptoIV&&Qt._opt.cryptoIV.byteLength>0&&Qt._opt.cryptoKey&&Qt._opt.cryptoKey.byteLength>0?r=function(e,t,r,i=!1){const n=e.byteLength;let s=5;for(;s<n;){let a=(o=e.slice(s,s+4))[3]|o[2]<<8|o[1]<<16|o[0]<<24;if(a>n)break;let l=e[s+4],d=!1;if(i?(l=l>>>1&63,d=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(l)):(l&=31,d=1===l||5===l),d){const i=zr(e.slice(s+4,s+4+a),t,r);e.set(i,s+4)}s=s+4+a}var o;return e}(e,Qt._opt.cryptoKey,Qt._opt.cryptoIV,Ht):Qt.debug.error("worker",`isXorCrypto cryptoKey.length is ${Qt._opt.cryptoKey&&Qt._opt.cryptoKey.byteLength} or cryptoIV.length is ${Qt._opt.cryptoIV&&Qt._opt.cryptoIV.byteLength} null`)),r},cryptoPayloadAudio:function(e){let t=e;if(Qt._opt.isM7sCrypto)if(Qt._opt.cryptoIV&&Qt._opt.cryptoIV.byteLength>0&&Qt._opt.cryptoKey&&Qt._opt.cryptoKey.byteLength>0){e[0]>>4===xe.AAC&&(t=function(e,t,r){if(e.byteLength<=30)return e;const i=e.slice(32);let n=new gr.ModeOfOperation.ctr(t,new gr.Counter(r));const s=n.decrypt(i);return n=null,e.set(s,32),e}(e,Qt._opt.cryptoKey,Qt._opt.cryptoIV))}else Qt.debug.error("worker",`isM7sCrypto cryptoKey.length is ${Qt._opt.cryptoKey&&Qt._opt.cryptoKey.byteLength} or cryptoIV.length is ${Qt._opt.cryptoIV&&Qt._opt.cryptoIV.byteLength} null`);return t},setCodecAudio:function(e,t){const r=e[0]>>4,i=e[0]>>1&1;if(jt=r===xe.AAC?i?16:8:0===i?8:16,er&&er.setCodec)if(We(e)||r===xe.ALAW||r===xe.MULAW||r===xe.MP3){Qt.debug.log("worker",`setCodecAudio: init audio codec, codeId is ${r}`);const i=r===xe.AAC?e.slice(2):new Uint8Array(0);er.setCodec(r,Qt._opt.sampleRate,i),r===xe.AAC&&postMessage({cmd:B,buffer:i},[i.buffer]),Pe=!0,r!==xe.AAC&&(r===xe.MP3?(Qt.mp3Demuxer||(Qt.mp3Demuxer=new Mr(Qt),Qt.mp3Demuxer.on("data",((e,t)=>{er.decode(e,t)}))),Qt.mp3Demuxer.dispatch(e.slice(1),t)):er.decode(e.slice(1),t))}else Qt.debug.warn("worker","setCodecAudio: hasInitAudioCodec is false, codecId is ",r);else Qt.debug.error("worker","setCodecAudio: audioDecoder or audioDecoder.setCodec is null")},decodeAudio:function(e,t){if(Qt.isDestroyed)Qt.debug.log("worker","decodeAudio, decoder is destroyed and return");else if(at(r)&&at(Qt._opt.mseDecodeAudio))postMessage({cmd:I,payload:e,ts:t,cts:t},[e.buffer]);else{const r=e[0]>>4;Pe?r===xe.MP3?Qt.mp3Demuxer.dispatch(e.slice(1),t):er.decode(r===xe.AAC?e.slice(2):e.slice(1),t):Qt.setCodecAudio(e,t)}},setCodecVideo:function(e){const t=15&e[0];if(tr&&tr.setCodec)if(ot(e))if(t===we||t===Ee){Qt.debug.log("worker",`setCodecVideo: init video codec , codecId is ${t}`);const r=e.slice(5);if(t===we&&Qt._opt.useSIMD){const e=gt(r);if(e.codecWidth>4080||e.codecHeight>4080)return postMessage({cmd:$}),void Qt.debug.warn("worker",`setCodecVideo: SIMD H264 decode video width is too large, width is ${e.codecWidth}, height is ${e.codecHeight}`)}const i=new Uint8Array(e);Fe=!0,tr.setCodec(t,r),postMessage({cmd:C,code:t}),postMessage({cmd:D,buffer:i,codecId:t},[i.buffer])}else Qt.debug.warn("worker",`setCodecVideo: hasInitVideoCodec is false, codecId is ${t} is not H264 or H265`);else Qt.debug.warn("worker",`decodeVideo: hasInitVideoCodec is false, codecId is ${t} and frameType is ${e[0]>>4} and packetType is ${e[1]}`);else Qt.debug.error("worker","setCodecVideo: videoDecoder or videoDecoder.setCodec is null")},decodeVideo:function(e,t,i,n=0){if(Qt.isDestroyed)Qt.debug.log("worker","decodeVideo, decoder is destroyed and return");else if(at(r))postMessage({cmd:P,payload:e,isIFrame:i,ts:t,cts:n,delay:Qt.delay},[e.buffer]);else if(Fe)if(!Ve&&i&&(Ve=!0),Ve){if(i&&ot(e)){const t=15&e[0];let r={};if(t===we){r=gt(e.slice(5))}else t===Ee&&(r=function(e){let t={codecWidth:0,codecHeight:0,videoType:Ce.h265,width:0,height:0,profile:0,level:0};e=e.slice(5);do{let r={};if(e.length<23){console.warn("parseHEVCDecoderConfigurationRecord$2",`arrayBuffer.length ${e.length} < 23`);break}if(r.configurationVersion=e[0],1!=r.configurationVersion)break;r.general_profile_space=e[1]>>6&3,r.general_tier_flag=e[1]>>5&1,r.general_profile_idc=31&e[1],r.general_profile_compatibility_flags=e[2]<<24|e[3]<<16|e[4]<<8|e[5],r.general_constraint_indicator_flags=e[6]<<24|e[7]<<16|e[8]<<8|e[9],r.general_constraint_indicator_flags=r.general_constraint_indicator_flags<<16|e[10]<<8|e[11],r.general_level_idc=e[12],r.min_spatial_segmentation_idc=(15&e[13])<<8|e[14],r.parallelismType=3&e[15],r.chromaFormat=3&e[16],r.bitDepthLumaMinus8=7&e[17],r.bitDepthChromaMinus8=7&e[18],r.avgFrameRate=e[19]<<8|e[20],r.constantFrameRate=e[21]>>6&3,r.numTemporalLayers=e[21]>>3&7,r.temporalIdNested=e[21]>>2&1,r.lengthSizeMinusOne=3&e[21];let i=e[22],n=e.slice(23);for(let e=0;e<i&&!(n.length<3);e++){let e=63&n[0],i=n[1]<<8|n[2];n=n.slice(3);for(let s=0;s<i&&!(n.length<2);s++){let i=n[0]<<8|n[1];if(n.length<2+i)break;if(n=n.slice(2),33==e){let e=new Uint8Array(i);e.set(n.slice(0,i),0),r.psps=It(e),t.profile=r.general_profile_idc,t.level=r.general_level_idc/30,t.width=r.psps.pic_width_in_luma_samples-(r.psps.conf_win_left_offset+r.psps.conf_win_right_offset),t.height=r.psps.pic_height_in_luma_samples-(r.psps.conf_win_top_offset+r.psps.conf_win_bottom_offset)}n=n.slice(i)}}}while(0);return t.codecWidth=t.width||1920,t.codecHeight=t.height||1080,t.presentHeight=t.codecHeight,t.presentWidth=t.codecWidth,t.timescale=1e3,t.refSampleDuration=1e3/23976*1e3,t}(e));r.codecWidth&&r.codecHeight&&v&&w&&(r.codecWidth!==v||r.codecHeight!==w)&&(Qt.debug.warn("worker",`\n                            decodeVideo: video width or height is changed,\n                            old width is ${v}, old height is ${w},\n                            new width is ${r.codecWidth}, new height is ${r.codecHeight},\n                            and emit change event`),$t=!0,postMessage({cmd:N}))}if($t)return void Qt.debug.warn("worker","decodeVideo: video width or height is changed, and return");if(Gt)return void Qt.debug.warn("worker","decodeVideo: simd decode error, and return");if(ot(e))return void Qt.debug.warn("worker","decodeVideo and payload is video sequence header so drop this frame");if(e.byteLength<12)return void Qt.debug.warn("worker",`decodeVideo and payload is too small , payload length is ${e.byteLength}`);const r=e.slice(5);tr.decode(r,i?1:0,t)}else Qt.debug.log("worker","decodeVideo first frame is not iFrame");else Qt.setCodecVideo(e)},clearBuffer:function(e=!1){Qt.debug.log("worker",`clearBuffer,bufferList length is ${i.length}, need clear is ${e}`),e&&(i=[]),Qt.isPlayer&&(Qt.resetAllDelay(),at(Qt._opt.checkFirstIFrame)&&(Qt.dropping=!0,postMessage({cmd:M}))),at(Qt._opt.checkFirstIFrame)&&lt(r)&&(Ve=!1)},dropBuffer$2:function(){if(i.length>0){let e=i.findIndex((e=>at(e.isIFrame)&&e.type===Z));if(Qt.isAllIframeInBufferList())for(let t=0;t<i.length;t++){const r=i[t],n=Qt.getDelayNotUpdateDelay(r.ts,r.type);if(n>=Qt.getNotDroppingDelayTs()){Qt.debug.log("worker",`dropBuffer$2() isAllIframeInBufferList() is true, and index is ${t} and tempDelay is ${n} and notDroppingDelayTs is ${Qt.getNotDroppingDelayTs()}`),e=t;break}}if(e>=0){Qt.isPushDropping=!0,postMessage({cmd:M});const t=i.length;i=i.slice(e);const r=i.shift();Qt.resetAllDelay(),Qt.getDelay(r.ts,r.type),Qt.doDecode(r),Qt.isPushDropping=!1,Qt.debug.log("worker",`dropBuffer$2() iFrameIndex is ${e},and old bufferList length is ${t} ,new bufferList is ${i.length} and new delay is ${Qt.delay} `)}else Qt.isPushDropping=!1}0===i.length&&(Qt.isPushDropping=!1)},demuxM7s:function(e){const t=new DataView(e),r=t.getUint32(1,!1),i=t.getUint8(0),n=new ArrayBuffer(4),s=new Uint32Array(n);switch(i){case X:Qt.decode(new Uint8Array(e,5),{type:X,ts:r});break;case Z:if(t.byteLength>=11){const i=new Uint8Array(e,5),n=i[0];if(Qt._isEnhancedH265Header(n))Qt._decodeEnhancedH265Video(i,r);else{const e=t.getUint8(5)>>4==1;if(e&&(Qt.calcIframeIntervalTimestamp(r),ot(i)&&null===Ht)){const e=15&i[0];Ht=e===Ee}Qt.isPlayer&&Qt.calcNetworkDelay(r),s[0]=i[4],s[1]=i[3],s[2]=i[2],s[3]=0;let n=s[0],o=Qt.cryptoPayload(i,e);Qt.decode(o,{type:Z,ts:r,isIFrame:e,cts:n})}}else Qt.debug.warn("worker",`demuxM7s() type is video and arrayBuffer length is ${e.byteLength} and return`)}},demuxNakedFlow:function(e){Kt.dispatch(e)},demuxFmp4:function(e){const t=new Uint8Array(e);Xt.dispatch(t)},demuxMpeg4:function(e){Zt.dispatch(e)},demuxTs:function(e){Jt.dispatch(e)},_decodeEnhancedH265Video:function(e,t){const r=e[0],i=48&r,n=15&r,s=e.slice(1,5),o=new ArrayBuffer(4),a=new Uint32Array(o),l="a"==String.fromCharCode(s[0]);if(Ht=lt(l),n===ze){if(i===$e){const r=e.slice(5);if(l);else{const i=new Uint8Array(5+r.length);i.set([28,0,0,0,0],0),i.set(r,5),Vt=Pt(e,Ht),Qt.debug.log("worker",`demuxFlv() isVideoSequenceHeader(enhancedH265) is true and isHevc is ${Ht} and nalUnitSize is ${Vt}`),Qt.decode(i,{type:Z,ts:t,isIFrame:!0,cts:0})}}}else if(n===Ne){let r=e,n=0;const s=i===$e;if(s&&Qt.calcIframeIntervalTimestamp(t),l);else{a[0]=e[4],a[1]=e[3],a[2]=e[2],a[3]=0,n=a[0];r=Rt(e.slice(8),s),r=Qt.cryptoPayload(r,s),Qt.decode(r,{type:Z,ts:t,isIFrame:s,cts:n})}}else if(n===Oe){const r=i===$e;r&&Qt.calcIframeIntervalTimestamp(t);let n=Rt(e.slice(5),r);n=Qt.cryptoPayload(n,r),Qt.decode(n,{type:Z,ts:t,isIFrame:r,cts:0})}},_isEnhancedH265Header:function(e){return!(128&~e)},findSei:function(e,t){let r=4;rt(Vt)||(r=Vt);Dt(e.slice(5),r).forEach((e=>{const r=Ht?e[0]>>>1&63:31&e[0];(Ht&&(r===ke.suffixSei||r===ke.prefixSei)||lt(Ht)&&r===Ae.kSliceSEI)&&postMessage({cmd:V,buffer:e,ts:t},[e.buffer])}))},flvHasUnitTypeIDR(e,t){const r=Dt(e.slice(5));let i=!1;return r.forEach((e=>{const r=t?e[0]>>>1&63:31&e[0];(t&&(r===ke.iFrame||r===ke.nLp)||lt(t)&&r===Ae.iFrame)&&(i=!0)})),i},calcNetworkDelay:function(e){if(!(Ve&&e>0))return;null===ut?(ut=e,ct=Je()):e<ut&&(Qt.debug.warn("worker",`calcNetworkDelay, dts is ${e} less than bufferStartDts is ${ut}`),ut=e,ct=Je());const t=e-ut,r=Je()-ct,i=r>t?r-t:0;Qt.networkDelay=i,i>Qt._opt.networkDelay&&Qt._opt.playType===m&&(Qt.debug.warn("worker",`calcNetworkDelay now dts:${e}, start dts is ${ut} vs start is ${t},local diff is ${r} ,delay is ${i}`),postMessage({cmd:L,type:ye,value:i}))},calcIframeIntervalTimestamp:function(e){null===kt?kt=e:kt<e&&(yt=e-kt,postMessage({cmd:R,value:yt}),kt=e)},canVisibilityDecodeNotDrop:function(){return Qt._opt.visibility&&v*w<=2073600},isPlaybackCacheBeforeDecodeForFpsRender:function(){return Qt.isPlayback&&Qt._opt.playbackIsCacheBeforeDecodeForFpsRender},isPlaybackOnlyDecodeIFrame:function(){return Qt._opt.playbackRate>=Qt._opt.playbackForwardMaxRateDecodeIFrame},isPlayUseMSE:function(){return Qt.isPlayer&&Qt._opt.useMSE&&at(r)},isPlayUseMSEAndDecoderInWorker:function(){return Qt.isPlayUseMSE()&&Qt._opt.mseDecoderUseWorker},playbackUpdatePlaybackRate:function(){Qt.clearBuffer(!0)},onOffscreenCanvasWebglContextLost:function(e){Qt.debug.error("worker","handleOffscreenCanvasWebglContextLost and next try to create webgl"),e.preventDefault(),Ot=!0,Qt.webglObj.destroy(),Qt.webglObj=null,Qt.offscreenCanvasGL=null,setTimeout((()=>{Qt.offscreenCanvasGL=Qt.offscreenCanvas.getContext("webgl"),Qt.offscreenCanvasGL&&Qt.offscreenCanvasGL.getContextAttributes().stencil?(Qt.webglObj=u(Qt.offscreenCanvasGL,Qt._opt.openWebglAlignment),Ot=!1):Qt.debug.error("worker","handleOffscreenCanvasWebglContextLost, stencil is false")}),500)},onOffscreenCanvasWebglContextRestored:function(e){Qt.debug.log("worker","handleOffscreenCanvasWebglContextRestored"),e.preventDefault()},videoInfo:function(e,t,r){postMessage({cmd:C,code:e}),postMessage({cmd:S,w:t,h:r}),v=t,w=r,Qt.useOffscreen()&&(Qt.offscreenCanvas=new OffscreenCanvas(t,r),Qt.offscreenCanvasGL=Qt.offscreenCanvas.getContext("webgl"),Qt.webglObj=u(Qt.offscreenCanvasGL,Qt._opt.openWebglAlignment),Qt.offscreenCanvas.addEventListener("webglcontextlost",Qt.onOffscreenCanvasWebglContextLost,!1),Qt.offscreenCanvas.addEventListener("webglcontextrestored",Qt.onOffscreenCanvasWebglContextRestored,!1))},audioInfo:function(e,t,r){postMessage({cmd:k,code:e}),postMessage({cmd:A,sampleRate:t,channels:r,depth:jt}),At=r},yuvData:function(t,r){if(Qt.isDestroyed)return void Qt.debug.log("worker","yuvData, decoder is destroyed and return");const i=v*w*3/2;let n=e.HEAPU8.subarray(t,t+i),s=new Uint8Array(n);if(mt=null,Qt.useOffscreen())try{if(Ot)return;Qt.webglObj.renderYUV(v,w,s);let e=Qt.offscreenCanvas.transferToImageBitmap();postMessage({cmd:U,buffer:e,delay:Qt.delay,ts:r},[e])}catch(e){Qt.debug.error("worker","yuvData, transferToImageBitmap error is",e)}else postMessage({cmd:U,output:s,delay:Qt.delay,ts:r},[s.buffer])},pcmData:function(e,r,i){if(Qt.isDestroyed)return void Qt.debug.log("worker","pcmData, decoder is destroyed and return");let s=r,o=[],a=0,l=Qt._opt.audioBufferSize;for(let r=0;r<2;r++){let i=t.HEAPU32[(e>>2)+r]>>2;o[r]=t.HEAPF32.subarray(i,i+s)}if(xt){if(!(s>=(r=l-xt)))return xt+=s,n[0]=Float32Array.of(...n[0],...o[0]),void(2==At&&(n[1]=Float32Array.of(...n[1],...o[1])));ft[0]=Float32Array.of(...n[0],...o[0].subarray(0,r)),2==At&&(ft[1]=Float32Array.of(...n[1],...o[1].subarray(0,r))),postMessage({cmd:x,buffer:ft,ts:i},ft.map((e=>e.buffer))),a=r,s-=r}for(xt=s;xt>=l;xt-=l)ft[0]=o[0].slice(a,a+=l),2==At&&(ft[1]=o[1].slice(a-l,a)),postMessage({cmd:x,buffer:ft,ts:i},ft.map((e=>e.buffer)));xt&&(n[0]=o[0].slice(a),2==At&&(n[1]=o[1].slice(a)))},errorInfo:function(e){null===mt&&(mt=Je());const t=Je(),r=(i=yt>0?2*yt:5e3,n=1e3,s=5e3,Math.max(Math.min(i,Math.max(n,s)),Math.min(n,s)));var i,n,s;const o=t-mt;o>r&&(Qt.debug.warn("worker",`errorInfo() emit simdDecodeError and\n                iframeIntervalTimestamp is ${yt} and diff is ${o} and maxDiff is ${r}\n                and replay`),Gt=!0,postMessage({cmd:O}))},sendWebsocketMessage:function(e){a?a.readyState===be?a.send(e):Qt.debug.error("worker","socket is not open"):Qt.debug.error("worker","socket is null")},timeEnd:function(){},postStreamToMain(e,t){postMessage({cmd:H,type:t,buffer:e},[e.buffer])}};Qt.debug=new pt(Qt);let er=null;t.AudioDecoder&&(er=new t.AudioDecoder(Qt));let tr=null;e.VideoDecoder&&(tr=new e.VideoDecoder(Qt)),postMessage({cmd:E}),self.onmessage=function(e){let t=e.data;switch(t.cmd){case te:try{Qt._opt=Object.assign(Qt._opt,JSON.parse(t.opt))}catch(e){}Qt.init();break;case re:Qt.pushBuffer(t.buffer,t.options);break;case ie:Qt.decodeAudio(t.buffer,t.ts);break;case ne:Qt.decodeVideo(t.buffer,t.ts,t.isIFrame);break;case ae:Qt.clearBuffer(t.needClear);break;case le:Qt.fetchStream(t.url,JSON.parse(t.opt));break;case se:Qt.close();break;case oe:Qt.debug.log("worker","updateConfig",t.key,t.value),Qt._opt[t.key]=t.value,"playbackRate"===t.key&&(Qt.playbackUpdatePlaybackRate(),Qt.isPlaybackCacheBeforeDecodeForFpsRender()&&Qt.playbackCacheLoop());break;case de:Qt.sendWebsocketMessage(t.message)}}}Or.init(),Date.now||(Date.now=function(){return(new Date).getTime()});const Gr=[];Gr.push(r({printErr:function(e){console.warn("EasyPro[❌❌❌][worker]:",e)}}),t({printErr:function(e){console.warn("EasyPro[❌❌❌][worker]",e)}})),Promise.all(Gr).then((e=>{const t=e[0];$r(e[1],t)}))}));
