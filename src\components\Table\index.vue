<template>
  <div class="table-wrap">
    <div class="table-content">
      <el-table
        :data="data"
        border
        :show-summary="showSummary"
        :row-key="rowKey"
        ref="table"
        :highlight-current-row="highlightRow"
        :reserve-selection="reserveSelection"
        :row-class-name="rowClass"
        style="width: 100%; height: 100%"
        v-bind="height > 0 ? { height: '60vh', 'max-height': height } : {}"
        :header-cell-style="tableHeaderColor"
        @selection-change="onSelectChange"
        @cell-click="cellClick"
        @row-click="rowClick"
        @select="handleSelect"
      >
        <el-table-column
          v-for="column in filteredColumns"
          :key="column"
          :label="(column as any).title"
          :align="(column as any).align"
          :formatter="(column as any).formatter"
          :selectable="(column as any).selectable"
          :fixed="(column as any).fixed"
          :type="(column as any).type"
          :prop="(column as any).key"
          :width="(column as any).width"
          :props="(column as any).props"
          :show-overflow-tooltip="true"
          :min-width="(column as any).type === 'operations' ? 300 : 50"
        >
          <template v-if="(column as any).slot" #default="scope">
            <slot
              :name="(column as any).slot"
              :row="scope.row"
              :index="scope.$index"
            ></slot>
          </template>

          <template
            v-else-if="(column as any).type === 'input'"
            #default="scope"
          >
            <el-input
              v-model="scope.row[(column as any).key]"
              placeholder="请输入"
            />
          </template>
          <template
            v-else-if="(column as any).type === 'select'"
            #default="scope"
          >
            <el-select
              v-model="scope.row[(column as any).key]"
              placeholder="请选择"
            >
              <template v-for="option in (column as any).options" :key="option">
                <el-option :label="option.label" :value="option.value" />
              </template>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

let sortable: any = null;
export default defineComponent({
  props: {
    data: {
      type: Array,
      default: null,
    },
    highlightRow: {
      type: Boolean,
      default: false,
    },
    rowKey: {
      type: String,
      default: "id",
    },
    reserveSelection: {
      type: Boolean,
      default: false,
    },
    rowClass: {
      type: Function,
    },
    columns: {
      type: Array,
      default: null,
    },
    isDrop: {
      type: Boolean,
      default: false,
    },
    isDropDisabled: {
      type: Boolean,
      default: true,
    },
    showSummary: {
      type: Boolean,
      default: false,
    },
    height: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      levelList: ["一级", "二级", "三级", "四级"],
      filteredColumns: [],
    };
  },
  watch: {
    isDropDisabled: {
      handler(newVal) {
        if (sortable) {
          sortable.options.disabled = newVal;
        }
      },
      immediate: true,
    },

    columns: {
      handler(newVal) {
        this.filteredColumns = this.columns.filter((item: any) => !item.hidden);
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    onSelectChange(evt: any) {
      this.$emit("listSelectChange", evt);
    },
    handleSelect(selection: any, row: any) {
      this.$emit("listSelect", selection, row);
    },
    getRowClass(row: any) {
      this.$emit("rowClass", row);
    },
    rowClick(e: any) {
      this.$emit("rowClick", e);
    },
    cellClick(e: any) {
      this.$emit("listClick", e);
    },
    // 设置表头的颜色
    tableHeaderColor(options: any) {
      const { rowIndex } = options;
      if (rowIndex === 0) {
        return {
          backgroundColor: "#F5F7FA",
        };
      }
    },
  },
});
</script>

<style lang="scss" scoped>
.table-wrap {
  flex: 1;
  position: relative;
  height: 100%;

  .table-content {
    position: absolute;
    width: 100%;
    height: 100%;

    .el-table {
      background-color: transparent;
      border: 0;
    }
  }
}
</style>
