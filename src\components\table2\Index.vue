<template>
  <div class="table-wrap">
    <div class="table-content">
      <el-table :data="data" style="width: 100%" :header-cell-style="tableHeaderColor" :cell-style="{ textAlign: 'center' }"
        @selection-change="onSelectChange">
        <el-table-column v-for="column in columns" :key="column" :show-overflow-tooltip="showOverflowToolTip"
          :label="(column as any).title" :formatter="(column as any).formatter" :fixed="(column as any).fixed"
          :type="(column as any).type" :prop="(column as any).key" :width="(column as any).width"
          :min-width="(column as any).type === 'operations' ? 280 : 500">
          <template v-if="(column as any).type === 'operations'" #default="scope">
            <template v-for="button in (column as any).buttons">
              <el-button v-if="button.visible ? button.visible(scope.row) : true" :key="button" link type="primary"
                size="small" @click="(column as any).onClick(button, scope.row)" class="font-14">
                {{ button.label }}
              </el-button>
            </template>
          </template>
          <template v-else-if="(column as any).type === 'switch'" #default="scope">
            <el-switch v-model="scope.row.status" @change="column?.switchChange(scope.row)" />
          </template>
          <template v-else-if="(column as any).type === 'icon-label'" #default="scope">
            <img :src="column.getIcon(scope.row)" style="margin-right:3px;">
            <label :style="{color: scope.row.fontColor,fontSize: scope.row.fontSize}">{{ scope.row.statusLabel
              }}</label>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  props: {
    data: {
      type: Array,
      default: null
    },
    columns: {
      type: Array,
      default: null
    },
    showOverflowToolTip: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    onSelectChange(evt: any) {
      this.$emit('listSelectChange', evt)
    },
    // 设置表头的颜色
    tableHeaderColor(options: any) {
      const { row, column, rowIndex, columnIndex } = options
      if (rowIndex === 0) {
        return {
          backgroundColor: '#F5F7FA',
          textAlign: "center" // 添加文本居中样式
        }
      }
    }
  }
})

</script>

<style lang="scss">
.table-wrap {
  flex: 1;
  position: relative;
  height: 100%;

  .table-content {
    position: absolute;
    width: 100%;
    height: 100%;

    .el-table__header {
      tr {
        background-color: #ecf5ff;
      }
    }
  }
}
</style>
