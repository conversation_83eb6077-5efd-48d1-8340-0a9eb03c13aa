<template>
  <div class="total-box">
    <div class="total-item">
      <div class="total-label">今日累计人数</div>
      <div class="total-content">
        <div class="total-nums">
          <nums :value="props.datas.todayNumberOfPeople"></nums>
        </div>
        <div class="total-unit">人</div>
      </div>
    </div>
    <div class="total-item">
      <div class="total-label">当前实时人数</div>
      <div class="total-content">
        <div class="total-nums">
          <nums :value="props.datas.currentNumberOfPeople"></nums>
        </div>
        <div class="total-unit">人</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import nums from "../count-up.vue";
const props = defineProps({
  datas: {
    type: Object,
    default: () => {
      return{
        todayNumberOfPeople: 0,
        currentNumberOfPeople: 0,
      }
    },
    required: true,
  },
});
</script>
<style lang="scss" scoped>
.total-box {
  width: 424px;
  height: 80px;
  overflow: hidden;
  padding: 8px 12px 8px 80px;
  background: url(@/assets/data-screen/module-4-bg.png) no-repeat;
  background-size: 100% 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  margin-bottom: 16px;
  grid-gap: 8px;
  .total-item {
    width: 162px;
    height: 64px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .total-label {
      line-height: 23px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
    }
    .total-content {
      display: flex;
      align-items: baseline;
      justify-content: center;
      .total-unit {
        margin-left: 2px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: rgba(199, 214, 255, 0.64);
      }
      .total-nums {
        line-height: 32px;
        font-family: D-DIN-PRO, D-DIN-PRO;
        font-weight: bold;
        font-size: 32px;
        color: transparent;
      }
    }
    &:nth-child(1) {
      .total-nums {
        background: linear-gradient(180deg, #ffffff 0%, #40f9ff 100%);
        -webkit-background-clip: text;
      }
    }
    &:nth-child(2) {
      .total-nums {
       background: linear-gradient(180deg, #FFFFFF 0%, #FFB972 100%);
        -webkit-background-clip: text;
      }
    }
  }
}
</style>
