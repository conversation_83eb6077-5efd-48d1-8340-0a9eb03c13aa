<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>附件</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main over-auto-y" style="height: 40vh">
      <el-image
        class="marg-r-10 marg-t-10"
        v-for="(img, index) in fileList"
        :key="index"
        :src="img"
        :preview-src-list="fileList"
        :initial-index="index"
        show-progress
        :preview-teleported="true"
        style="width: 100px; height: 100px"
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from "vue";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array,
    default: () => {
      [];
    },
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);
const fileList = ref([]);
const returnBtn = () => {
  emits("onChangeVisible");
};
watch(
  () => props.data,
  (newVal) => {
    if (newVal.length > 0) {
      let imgList = [];
      for (let i = 0; i < newVal.length; i++) {
        imgList.push(newVal[i].fileInfo.path);
      }
      fileList.value = imgList;
    } else {
      fileList.value = [];
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
