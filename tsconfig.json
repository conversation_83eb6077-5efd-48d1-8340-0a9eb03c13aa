{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "baseUrl": ".",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "lib": ["es6", "ESNext", "dom"],
    "types": ["vite/client"],
    "paths": {
      "@/*": ["src/*"],
      "/#/*": ["types/*"]
    },
    "noImplicitAny": false, //不允许使用any
    // "strictNullChecks": true, //不允许使用null
    "noImplicitThis": true, //不允许往this上面挂属性,
    "allowJs": true
  },
  "include": [
    "./src/**/*.js",
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ],
  "exclude": ["node_modules", "dist", "**/*.js"]
}
