<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form label-width="112" :inline="true" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <el-form-item label="场景名称:" prop="cjmc">
            <el-input v-model="formData.cjmc" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="室外温度:" required>
            <div class="range-wrap">
              <el-form-item class="range-item" prop="minswwd">
                <el-input-number style="width: 100px;" v-model="formData.minswwd" :controls="false">
                  <template #suffix>
                    <span>℃</span>
                  </template>
                </el-input-number>
              </el-form-item>
              <span>至</span>
              <el-form-item class="range-item" prop="maxswwd">
                <el-input-number style="width: 100px;" v-model="formData.maxswwd" :controls="false">
                  <template #suffix>
                    <span>℃</span>
                  </template>
                </el-input-number>
              </el-form-item>
            </div>
          </el-form-item>
          <el-form-item label="最密人数:" prop="zmrs">
            <el-input-number v-model="formData.zmrs" :controls="false" step-strictly>
              <template #suffix>
                <span>人</span>
              </template>
            </el-input-number>
          </el-form-item>
          <el-form-item label="风速:" required>
            <div class="range-wrap">
              <el-form-item class="range-item" prop="minfs">
                <el-input-number style="width: 100px;" v-model="formData.minfs" :controls="false">
                  <template #suffix>
                    <span>m/s</span>
                  </template>
                </el-input-number>
              </el-form-item>
              <span>至</span>
              <el-form-item class="range-item" prop="maxfs">
                <el-input-number style="width: 100px;" v-model="formData.maxfs" :controls="false">
                  <template #suffix>
                    <span>m/s</span>
                  </template>
                </el-input-number>
              </el-form-item>
            </div>
          </el-form-item>

          <el-form-item label="时间设定:" prop="timeRange">
            <el-date-picker
              style="width: 100%;"
              v-model="formData.timeRange"
              type="datetimerange"
              value-format="YYYY-MM-DD HH:mm:ss"
              range-separator="至"
              :disabled="isLook"
              @change="fun_timeChange"
            />
          </el-form-item>
          <el-form-item label="人流范围:" required>
            <div class="range-wrap">
              <el-form-item class="range-item" prop="minrlfw">
                <el-input-number style="width: 100px;" v-model="formData.minrlfw" :controls="false" step-strictly>
                  <template #suffix>
                    <span>人</span>
                  </template>
                </el-input-number>
              </el-form-item>
              <span>至</span>
              <el-form-item class="range-item" prop="maxrlfw">
                <el-input-number style="width: 100px;" v-model="formData.maxrlfw" :controls="false" step-strictly>
                  <template #suffix>
                    <span>人</span>
                  </template>
                </el-input-number>
              </el-form-item>
            </div>
          </el-form-item>
          <el-form-item label=" ">
            <div style="height: 32px;"></div>
          </el-form-item>
          <el-form-item label="月份:" required>
            <div class="range-wrap">
              <el-form-item class="range-item" prop="startyf">
                <el-input-number style="width: 100px;" v-model="formData.startyf" :controls="false" step-strictly :min="1" :max="12">
                  <template #suffix>
                    <span>月</span>
                  </template>
                </el-input-number>
              </el-form-item>
              <span>至</span>
              <el-form-item class="range-item" prop="endyf">
                <el-input-number style="width: 100px;" v-model="formData.endyf" :controls="false" step-strictly :min="1" :max="12">
                  <template #suffix>
                    <span>月</span>
                  </template>
                </el-input-number>
              </el-form-item>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

import {
  addSceneInfo,
  updateSceneInfo,
} from "@/api/energy-manage/index";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage, ElMessageBox } from "element-plus";
import _ from "lodash";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
}

// 表单数据-回显
const formData = computed(() => {
  console.log('props.data:',props.data);
  //设置初始默认状态
  if(props.data.status===undefined)props.data.status = 1;//启用状态
  //时间
  if(!props.data.startsjsd || !props.data.endsjsd){
    props.data.timeRange = [];
  }else{
    props.data.timeRange = [props.data.startsjsd, props.data.endsjsd]
  }

  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});


onMounted(async () => {
  // fun_getDicts();//获取字典
});

// 获取字典
// const fun_getDicts = async () => {
//   const {data:cgqlxOptionData} = await getDict('cgqLX');//获取传感器类型
//   cgqlxOption.value = cgqlxOptionData.list;
//   console.log('传感器类型cgqlxOption:',cgqlxOption.value);
// }

//时间
const fun_timeChange = (val) => {
  console.log('时间选择:',val);
  if(val){
    formData.value.startsjsd = val[0]
    formData.value.endsjsd = val[1]
  }else{
    formData.value.startsjsd = ''
    formData.value.endsjsd = ''
  }
}

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  cjmc: [
    { required: true, message: "请输入", trigger: "blur" },
  ],
  minswwd: [
    { required: true, message: "请输入", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value > formData.value.maxswwd) {
          callback(new Error("最小值不能大于最大值"));
        } else {
          callback();
        }
      },
    }
  ],
  maxswwd: [
    { required: true, message: "请输入", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value < formData.value.minswwd) {
          callback(new Error("最大值不能小于最小值"));
        } else {
          callback();
        }
      },
    }
  ],
  zmrs: [
    { required: true, message: "请输入", trigger: "blur" }
  ],
  minfs: [
    { required: true, message: "请输入", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value > formData.value.maxfs) {
          callback(new Error("最小值不能大于最大值"));
        } else {
          callback();
        }
      },
    }
  ],
  maxfs: [
    { required: true, message: "请输入", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value < formData.value.minfs) {
          callback(new Error("最大值不能小于最小值"));
        } else {
          callback();
        }
      },
    }
  ],
  timeRange: [
    { required: true, message: "请选择", trigger: ["blur","change"] }
  ],
  minrlfw: [
    { required: true, message: "请输入", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value > formData.value.maxrlfw) {
          callback(new Error("最小值不能大于最大值"));
        } else {
          callback();
        }
      },
    }
  ],
  maxrlfw: [
    { required: true, message: "请输入", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value < formData.value.minrlfw) {
          callback(new Error("最大值不能小于最小值"));
        } else {
          callback();
        }
      },
    }
  ],
  startyf: [
    { required: true, message: "请输入", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value > formData.value.endyf) {
          callback(new Error("最小值不能大于最大值"));
        } else {
          callback();
        }
      },
    }
  ],
  endyf: [
    { required: true, message: "请输入", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value < formData.value.startyf) {
          callback(new Error("最大值不能小于最小值"));
        } else {
          callback();
        }
      },
    }
  ],
  monthMin: [
    { required: true, message: "请输入", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value > formData.value.monthMax) {
          callback(new Error("最大值不能小于最小值"));
        } else {
          callback();
        }
      },
    }
  ],
  monthMax: [
    { required: true, message: "请输入", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value < formData.value.monthMin) {
          callback(new Error("最大值不能小于最小值"));
        } else {
          callback();
        }
      },
    }
  ]
});


// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增") {
        const res = await addSceneInfo(formData.value);
        console.log('新增:',res);
        ElMessage({
          message: res.message,
          type: "success",
        });
      } else {
        const res = await updateSceneInfo(formData.value);
        console.log('修改:',res);
        ElMessage({
          message: res.message,
          type: "success",
        });
      }
      emits("refreshList");
      emits("onChangeVisible");
    }
  });
};

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
      .range-wrap {
        display: flex;
        gap: 10px;
        .range-item {
          margin-right: 0;
          ::v-deep(.el-form-item__content) {
            width: unset;
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>
