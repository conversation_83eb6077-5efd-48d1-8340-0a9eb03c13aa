<template>
  <div class="gird-box">
    <div class="gird-item">
      <div class="gird-content">
        <div class="gird-nums">{{ props.datas?.coolerNums }}</div>
        <div class="gird-total">/{{ props.datas?.coolerTotal }}</div>
        <div class="gird-unit">个</div>
      </div>
      <div class="gird-label">冷机</div>
    </div>
    <div class="gird-item">
      <div class="gird-content">
        <div class="gird-nums">{{ props.datas?.coolingTowerNums }}</div>
        <div class="gird-total">/{{ props.datas?.coolingTowerTotal }}</div>
        <div class="gird-unit">个</div>
      </div>
      <div class="gird-label">冷却塔</div>
    </div>
    <div class="gird-item">
      <div class="gird-content">
        <div class="gird-nums">{{ props.datas?.chilledWaterPumpNums }}</div>
        <div class="gird-total">/{{ props.datas?.chilledWaterPumTotal }}</div>
        <div class="gird-unit">个</div>
      </div>
      <div class="gird-label">冷冻水泵</div>
    </div>
  </div>
</template>
<script setup>
import nums from "../count-up.vue";
const props = defineProps({
  datas: Object,
});
</script>
<style lang="scss" scoped>
.gird-box {
  display: grid;
  width: 100%;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 32px;
  margin-bottom: 16px;
  overflow: hidden;
  .gird-item {
    width: 115px;
    position: relative;
    .gird-content {
      display: flex;
      align-items: baseline;
      justify-content: center;
      height: 28px;
      padding-bottom: 4px;
      margin-bottom: 4px;
      .gird-nums {
        font-family: D-DIN-PRO, D-DIN-PRO;
        font-weight: bold;
        font-size: 24px;
        line-height: 24px;
      }
      .gird-total,
      .gird-unit {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: rgba(199, 214, 255, 0.64);
      }
      .gird-unit{
        margin-left: 4px;
      }
    }
    .gird-label {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
    }
    &:nth-child(1) {
      .gird-content {
        background: url(@/assets/data-screen/module-1-gird-bg-3.png) no-repeat;
        background-size: 100% 100%;
        .gird-nums {
          color: #b8b9ff;
        }
      }
    }
     &:nth-child(2) {
      .gird-content {
        background: url(@/assets/data-screen/module-1-gird-bg-4.png) no-repeat;
        background-size: 100% 100%;
        .gird-nums {
          color: #AEE1FF;
        }
      }
    }
     &:nth-child(3) {
      .gird-content {
        background: url(@/assets/data-screen/module-1-gird-bg-5.png) no-repeat;
        background-size: 100% 100%;
        .gird-nums {
          color: #FFDDAF;
        }
      }
    }
  }
}
</style>
