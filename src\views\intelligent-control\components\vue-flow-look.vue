<template>
  <div class="pointManage font-16">
    <div class="height-all bg-color-fff border-radius-5 padd-20">
      <div class="posit-relat width-all height-all over-auto">
        <div class="width-all height-all over-auto" style="background-color: #07162C;">
          <div v-if="route.name=='Hot-source'">
            <img class="width-all" src="/static/images/Hot-source.jpg" alt="">
          </div>
          <VueFlowEditor v-else :data="flowData" :panelList="flowPanelList" :config="{zoom: 1.5}" :editable="flowIsEdit" @nodeClick="fun_handleClick" @nodeMouseEnter="fun_handleMouseEnter" @nodeMouseLeave="fun_handleMouseLeave" />
        </div>

        <!-- 详情弹窗 -->
        <div class="info-dialog flex flex-col filter-bg-blur font-14" v-show="dataInfoVisibility">
          <div class="flex flex-both">
            <div class="flex flex-middle">
              <img class="marg-0_5" style="width:18px;" src="/static/images/dialog-arrow.png" alt="">
              <span class="font-14 font-bold over-hide-1">{{ dataInfo.name }}</span>
            </div>
            <el-icon class="flex-item-shrink-0 hover font-24 marg-l-5" @click="dataInfoVisibility=false"><Close /></el-icon>
          </div>
          <ul class="flex flex-both flex-wrap max-height-all over-auto-y">
            <li class="flex flex-right flex-middle padd-5_0" style="width:48%;" v-for="(item,key) of dataInfo.list" :key="key">
              <span class="color-ddd padd-r-5 text-right">{{ item.name }}:</span>
              <!-- 启停B -->
              <span class="flex-item-shrink-0 font-bold" v-if="item.type.substring(0,1)=='B'&&item.isAutoHandle!='2'">{{ item.value==1?'已启动':'已停止' }}</span>
              <!-- 手自动启停切换标识属性 -->
              <span class="flex-item-shrink-0 font-bold" v-else-if="item.type.substring(0,1)=='B'&&item.isAutoHandle=='2'">{{ item.value=='0'?'自动':'手动' }}</span>
              <!-- 数值F -->
              <span class="flex-item-shrink-0 font-bold" v-else-if="item.type.substring(0,1)=='F'&&!item.dictName">{{ item.value }}</span>
              <!-- 数值F是字典 -->
              <span class="flex-item-shrink-0 font-bold" v-else-if="item.type.substring(0,1)=='F'&&item.dictName">{{ dataInfoDictNameOption[item.dictName]?.find(v => v.businessValue==item.value)?.businessLabel }}</span>
            </li>
          </ul>
        </div>
        <!-- 启停控制弹窗 -->
        <div class="control-dialog filter-bg-blur flex flex-col" v-show="dataControlVisibility">
          <div class="flex flex-middle flex-both padd-5_10 border-b-ddd">
            <div class="flex flex-middle">
              <el-icon class="font-18 marg-t-2 marg-r-10"><HelpFilled /></el-icon>
              <span class="font-16 font-bold">{{ formData.name }}</span>
            </div>
            <el-icon class="hover font-24" @click="dataControlVisibility=false"><Close /></el-icon>
          </div>
          <div class="padd-20 height-all over-auto-y">
            <div class="height-all over-auto-y">
              <el-form label-width="140" :inline="false" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
                <!-- <el-form-item label="出水温度设定:" prop="cswdsd" v-if="dataInfo.type=='2'">
                  <div class="width-all flex">
                    <el-input v-model.number="formData.cswdsd" type="text" placeholder="请输入" class="input-bg-green" />
                    <span class="flex-item-shrink-0 padd-l-5">℃</span>
                  </div>
                </el-form-item> -->

                <el-form-item label="启停方式:" prop="isAutoHandle" class="marg-b-20" v-if="formData.isAutoHandle!=null">
                  <el-radio-group v-model="formData.isAutoHandle">
                    <el-radio label="0">自动</el-radio>
                    <el-radio label="1">手动</el-radio>
                  </el-radio-group>
                </el-form-item>

                <!-- 非能源策略管理页面引用时,远程启停单独直接启停 -->
                <div v-if="!props.isFrom_energysavingStrategyManage&&formData.sdqd!=null&&formData.isAutoHandle=='1'">
                  <div class="font-24 marg-b-10">远程启停</div>
                  <div class="flex flex-around marg-b-20">
                    <el-button size="large" :color="formData.sdqd=='1'?'#DDF3FF':'#00FF94'" :dark="true" plain style="padding: 25px 40px;" :style="{backgroundColor: formData.sdqd=='1'?'#DDF3FF33':'#00FF9433'}" @click="handleState('start')">{{formData.sdqd=='1'?'已启动':'启动'}}</el-button>
                    <el-button size="large" :color="formData.sdqd=='1'?'#00FF94':'#DDF3FF'" :dark="true" plain style="padding: 25px 40px;" :style="{backgroundColor: formData.sdqd=='1'?'#00FF9433':'#DDF3FF33'}" @click="handleState('stop')">{{formData.sdqd=='1'?'停止':'已停止'}}</el-button>
                  </div>
                </div>
                <!-- 从能源策略管理页面引用时,远程启停不单独直接启停 -->
                <el-form-item label="远程启停:" prop="sdqd" v-if="props.isFrom_energysavingStrategyManage&&formData.sdqd!=null&&formData.isAutoHandle=='1'">
                  <el-select v-model="formData.sdqd" placeholder="请选择">
                    <el-option v-for="(status, index2) in triggerStatusOption" :key="index2" :label="status.label" :value="status.value" />
                  </el-select>
                </el-form-item>

                <el-form-item :label="item.attributeDesc" v-for="(item,key) of dataInfoAutoHandleFormData" :key="key" style="margin-bottom: 5px;">
                  <!-- 启停B -->
                  <div class="width-all flex" v-if="item.attributeMark.substring(0,1)=='B'">
                    <el-select v-model="formData[item.gidMark]" placeholder="请选择">
                      <el-option v-for="(status, index2) in triggerStatusOption" :key="index2" :label="status.label" :value="status.value" />
                    </el-select>
                  </div>
                  <!-- 数值F -->
                  <div class="width-all flex" v-else-if="item.attributeMark.substring(0,1)=='F'">
                    <el-input v-model.number="formData[item.gidMark]" type="text" placeholder="请输入" class="input-bg-green" />
                    <span class="flex-item-shrink-0 padd-l-5" v-if="item.unit">{{ item.unit }}</span>
                  </div>
                </el-form-item>
              </el-form>
  
              <div class="flex flex-center marg-b-20 padd-t-20">
                <el-button type="primary" @click="submitBtn(ruleFormRef)">确定</el-button>
                <el-button type="default" @click="dataControlVisibility=false">取消</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();


import { getDict,getBusinessDict } from "@/api/common";
import {
  getFlowChart,
  getDeviceAttributeList,
  getDeviceAttributeAutoHandleList,
  updateDeviceAttribute,
} from "@/api/intelligent-control/index";

/* VueFlow功能 */
import VueFlowEditor from '@/components/VueFlow/VueFlowEditor.vue';

const props = defineProps({
  //是否从能源策略页面引用
  isFrom_energysavingStrategyManage: {
    type: Boolean,
    default: false,
  },
  // 从能源策略管理页面引用时,需要回显的属性值
  from_energysavingStrategyManageData: {
    type: Array,
    default: () => [],
  },
});

const from_energysavingStrategyManageDataFormData = computed(() => {
  return props.from_energysavingStrategyManageData||[]
})

// 获取字典
const fun_getDicts = async () => {
  // const {data:cycleTypeOptionData} = await getDict('cycle_type');//获取巡检周期
  // cycleTypeOption.value = cycleTypeOptionData.list;
  // console.log('巡检周期cycleTypeOption:',cycleTypeOption.value);
}


// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  console.log('当前路由:',route.name);
  fun_getList(route.name);
});

//启停状态
const triggerStatusOption = ref([
  { label: "启动", value: '1' },
  { label: "关闭", value: '0' },
]);


const dataList = ref([
  // {
  //   name:'新风机组',type:'1',
  //   left:100,top:100,width:100,height:100,
  //   list:[
  //     {name:'冷机运行状态',value:'停止'},
  //     {name:'压缩机状态',value:'停止'},
  //     {name:'故障报警',value:'停止'},
  //     {name:'运行模式',value:'准备启动'},
  //     {name:'电流百分比',value:'0%'},
  //   ]
  // },
  // {
  //   name:'组合式空调机组',type:'2',
  //   left:250,top:150,width:100,height:100,
  //   list:[
  //     {name:'冷机运行状态',value:'停止'},
  //     {name:'压缩机状态',value:'停止'},
  //     {name:'故障报警',value:'停止2'},
  //     {name:'运行模式',value:'准备启动'},
  //     {name:'电流百分比',value:'0%'},
  //   ]
  // },
  // {
  //   name:'送风机',type:'3',
  //   left:300,top:280,width:100,height:100,
  //   list:[
  //     {name:'冷机运行状态',value:'停止'},
  //     {name:'压缩机状态',value:'停止'},
  //     {name:'故障报警',value:'停止3'},
  //     {name:'运行模式',value:'准备启动'},
  //     {name:'电流百分比',value:'0%'},
  //   ]
  // },
  // {
  //   name:'排风机',type:'4',
  //   left:200,top:400,width:100,height:100,
  //   list:[
  //     {name:'冷机运行状态',value:'停止'},
  //     {name:'压缩机状态',value:'停止'},
  //     {name:'故障报警',value:'停止4'},
  //     {name:'运行模式',value:'准备启动'},
  //     {name:'电流百分比',value:'0%'},
  //   ]
  // },
  // {
  //   name:'热风幕',type:'5',
  //   left:400,top:400,width:100,height:100,
  //   list:[
  //     {name:'冷机运行状态',value:'停止'},
  //     {name:'压缩机状态',value:'停止'},
  //     {name:'故障报警',value:'停止5'},
  //     {name:'运行模式',value:'准备启动'},
  //     {name:'电流百分比',value:'0%'},
  //   ]
  // },
]);
const flowData = ref();
const flowIsEdit = ref(false);//false:查看模式,true:编辑模式
// const flowPanelList = ref([]);
const fun_getList = async (routeName) => {
  if(routeName=='Cold-source'||props.isFrom_energysavingStrategyManage){
    const { data } = await getFlowChart('1937014595413544961');//1937014595413544961(冷源)
    console.log('工艺图数据:',data);
    flowData.value = data.procddata?JSON.parse(data.procddata):{};
  }else if(routeName=='Hot-source'){
    // const { data } = await getFlowChart('-1');
    // console.log('工艺图数据:',data);
    // flowData.value = data.procddata?JSON.parse(data.procddata):{};
  }

  // flowData.value = {
  //   "nodes":[
  //     {"id":"999","type":"custom","initialized":false,"position":{"x":291.9375,"y":63.71875},"data":{"id":"999","label":"节点名称999","imgUrl":"/static/images/emergency.png","style":{"width":"74px"},"description":"这是一个自定义节点","type":"custom"}},
  //     {"id":"2","type":"custom","initialized":false,"position":{"x":187.4375,"y":242.21875},"data":{"id":"2","label":"节点名称2","imgUrl":"/static/images/dialog-arrow.png","style":{"width":"18px"},"description":"这是一个自定义节点","type":"custom"}},
  //     {"id":"3","type":"custom","initialized":false,"position":{"x":452.9375,"y":376.71875},"data":{"id":"3","label":"节点名称3","imgUrl":"/static/images/dialog-bg1.png","style":{"width":"120px"},"description":"这是一个自定义节点","type":"custom"}}
  //   ],
  //   "edges":[
  //     {"id":"vueflow__edge-999source-0-2source-5","type":"custom-edge","source":"999","target":"2","sourceHandle":"source-0","targetHandle":"source-5","data":{},"label":"","sourceX":286.9375,"sourceY":84.109375,"targetX":241.296875,"targetY":265.53125},
  //     {"id":"vueflow__edge-999source-11-3source-0","type":"custom-edge","source":"999","target":"3","sourceHandle":"source-11","targetHandle":"source-0","data":{"customColor":"#f00"},"label":"","sourceX":350.34375,"sourceY":170.71875,"targetX":447.9375,"targetY":395.515625},
  //     {"id":"vueflow__edge-2source-10-3source-9","type":"custom-edge","source":"2","target":"3","sourceHandle":"source-10","targetHandle":"source-9","data":{"customColor":"#0f0"},"label":"","sourceX":211.859375,"sourceY":284.21875,"targetX":477.328125,"targetY":475.734375},
  //     {"id":"vueflow__edge-2source-11-3source-7","type":"custom-edge","source":"2","target":"3","sourceHandle":"source-11","targetHandle":"source-7","data":{},"label":"","sourceX":221.421875,"sourceY":284.21875,"targetX":513.9375,"targetY":371.71875}
  //   ],
  //   "position":[0,0],
  //   "zoom":1,
  //   "viewport":{"x":0,"y":0,"zoom":1}
  // }
};

//鼠标单击和双击事件判断
// const clickCount = ref(0)
// const clickTimer = ref(null)
// const handleClickEvent = (node) => {
//   clickCount.value++
//   if (clickCount.value === 1) {
//     clickTimer.value = setTimeout(() => {
//       fun_handleClick(node)//单击事件
//       clickCount.value = 0
//     }, 250) // 250ms内没有第二次点击视为单击
//   } else if (clickCount.value === 2) {
//     clearTimeout(clickTimer.value)
//     fun_handleDblClick(node)//双击事件
//     clickCount.value = 0
//   }
// }

// 详情弹窗
const dataInfo = ref({list:[]})
const dataInfoVisibility = ref(false)
// 鼠标单击-显示详情弹窗
// const fun_handleClick = async (node) => {
//   console.log('鼠标单击:',node);
//   const { data } = await getDeviceAttributeList({equipmentId:node.id,pageSize: 100});
//   console.log('设备属性详情:',data);
//   dataInfo.value.name=node.label;
//   // dataInfo.value.id=node.id;
//   dataInfo.value.list=data?.list.map(item => {return{name:item.attributeDesc,value:item.attributeValue,unit:item.attributeUnit}})||[];
//   dataInfoVisibility.value=true;
// }

// 鼠标移入-显示详情弹窗
const mouseEnterTimer = ref(null)
const fun_handleMouseEnter = async (node) => {
  if(mouseEnterTimer.value){
    clearTimeout(mouseEnterTimer.value);
  }
  mouseEnterTimer.value = setTimeout(async () => {
    console.log('鼠标移入:',node);
    const { data } = await getDeviceAttributeList({equipmentId:node.id,pageSize: 100,status:props.isFrom_energysavingStrategyManage?1:null});
    console.log('移入设备属性详情:',data);
    dataInfo.value.name=node.label;
    // dataInfo.value.id=node.id;
    dataInfo.value.list=data?.list.map(item => {return{type:item.attributeMark,name:item.attributeDesc,value:item.attributeValue,unit:item.attributeUnit,isAutoHandle:item.isAutoHandle,dictName:item.dictName}})||[];
    // console.log('dataInfo.value.list:',dataInfo.value.list);
    dataInfoVisibility.value=true;
    fun_getDictNameOption(dataInfo.value.list);
  }, 250) // 250ms内没有移动视为移入
}
// 鼠标移出-隐藏详情弹窗
const fun_handleMouseLeave = (node) => {
  console.log('鼠标移出:',node);
  if(mouseEnterTimer.value){
    clearTimeout(mouseEnterTimer.value);
  }
  // dataInfoVisibility.value=false;
}

// 获取详情弹窗中的字典名称选项
const dataInfoDictNameOption = ref({});
function fun_getDictNameOption(v_list){
  const dictNameList = v_list.map(item => item.dictName)||[];
  console.log('dictNameList:',dictNameList);
  dictNameList.forEach(async name => {
    if(name){
      if(!dataInfoDictNameOption.value[name]){
        const {data} = await getBusinessDict(name);
        dataInfoDictNameOption.value[name] = data||[];
      }
    }
  })
  console.log('dataInfoDictNameOption:',dataInfoDictNameOption.value);
}

// 启停控制弹窗
const dataControlVisibility = ref(false)
const dataInfoFormData = ref([])
const dataInfoSubmitData = ref([])
// 鼠标单击-显示启停控制弹窗
const fun_handleClick = async (node) => {
  console.log('鼠标单击:',node);
  const { data } = await getDeviceAttributeAutoHandleList({equipmentId:node.id,status:1,pageSize: 100});
  console.log('单击设备属性详情:',data);
  formData.value.name=node.label;
  // formData.value.equipmentId=node.id;
  dataInfoSubmitData.value=data?.list||[];
  // 过滤掉attributeType:sdqd(远程启停(手动启停))->attributeValue:0已停止,1已启动;isAutoHandle:2(手动启停和自动启停切换标识属性)->attributeValue:0自动,1手动;
  dataInfoFormData.value=data?.list.filter(item => item.attributeType!='sdqd'&&item.isAutoHandle!='2')||[];//.map(item => {return{attributeDesc:item.attributeDesc,attributeType:item.attributeType,attributeValue:item.attributeValue,unit:item.attributeUnit,gid:item.gid}})

  // 手动启停控制按钮值和手自动启停切换按钮值
  if(!props.isFrom_energysavingStrategyManage){ //非能源策略管理页面引用时,根据当前状态设置按钮值
    formData.value.isAutoHandle = data?.list.find(item => item.isAutoHandle=='2')?.attributeValue||null;
    formData.value.sdqd = data?.list.find(item => item.attributeType=='sdqd')?.attributeValue||null;
  }else{ //能源策略管理页面引用时,根据当前状态设置按钮值
    if(dataInfoSubmitData.value.length>0){
      // 手自动启停切换标识属性
      const thisData_isAutoHandle = from_energysavingStrategyManageDataFormData.value.find(v => v.isAutoHandle=='2'&&v.equipmentId==dataInfoSubmitData.value[0].equipmentId);
      if(thisData_isAutoHandle){
        formData.value.isAutoHandle = thisData_isAutoHandle.attributeValue;
      }else{
        const thisInfoData_isAutoHandle = dataInfoSubmitData.value.find(v => v.isAutoHandle=='2');
        if(thisInfoData_isAutoHandle){ //有isAutoHandle字段
          formData.value.isAutoHandle = '';
        }else{ //没有isAutoHandle字段
          formData.value.isAutoHandle = null;
        }
      }
      // 远程启停(手动启停)属性
      const thisData_sdqd = from_energysavingStrategyManageDataFormData.value.find(v => v.attributeType=='sdqd'&&v.equipmentId==dataInfoSubmitData.value[0].equipmentId);
      console.log('thisData_sdqd:',thisData_sdqd);
      if(thisData_sdqd){
        formData.value.sdqd = thisData_sdqd.attributeValue;
      }else{
        const thisInfoData_sdqd = dataInfoSubmitData.value.find(v => v.attributeType=='sdqd');
        if(thisInfoData_sdqd){ //有sdqd字段
          formData.value.sdqd = '';
        }else{ //没有sdqd字段
          formData.value.sdqd = null;
        }
      }
    }else{
      formData.value.sdqd = null;
      formData.value.isAutoHandle = null;
    }
  }
  // 控制属性值回显
  dataInfoFormData.value.forEach(item => {
    if(!props.isFrom_energysavingStrategyManage){ //非能源策略管理页面引用时,回显接口数据
      formData.value[item.gidMark]=item.attributeValue;
    }else{ //能源策略管理页面引用时,回显传入的数据或空
      const thisData = from_energysavingStrategyManageDataFormData.value.find(v => v.gid==item.gid);
      console.log('thisData:',thisData);
      if(thisData){
        formData.value[item.gidMark]=thisData.attributeValue;
      }else{
        formData.value[item.gidMark]='';
      }
    }
  })

  if(dataInfoFormData.value.length>0){
    // 如果启停控制属性数组不为空,则显示启停控制弹窗
    dataControlVisibility.value=true;
  }else{
    // 如果启停控制属性数组为空,则隐藏启停控制弹窗
    dataControlVisibility.value=false;
  }
}
//form表单切换属性数组
const dataInfoAutoHandleFormData = computed(() => {
  return dataInfoFormData.value.filter(item => item.isAutoHandle==formData.value.isAutoHandle||item.isAutoHandle=='4')||[];
})
// 启停方式切换
// const fun_isAutoHandleChange = (val) => {
//   console.log('启停方式切换:',val);
// }


// 启用停用
const handleState = (type) => {
  ElMessageBox.confirm(`确定${type=='start'?'启动':'停止'}吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    // 远程启停(手动启停)属性
    formData.value.sdqd=type=='start'?'1':'0';
    for(let item of dataInfoSubmitData.value){
      if(item.attributeType=='sdqd'){
        item.attributeValue=formData.value.sdqd;
        break;
      }
    }
    // 手自动启停切换标识属性
    for(let item of dataInfoSubmitData.value){
      if(item.isAutoHandle=='2'){
        item.attributeValue=formData.value.isAutoHandle;
        break;
      }
    }

    const res = await updateDeviceAttribute({list:dataInfoSubmitData.value});
    ElMessage({
      type: "success",
      message: `${type=='start'?'启动':'停止'}成功`,
    });
    // fun_getList();
  })
};

// 表单提交数据
const formData = ref({})

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  // plsd: [
  //   { required: true, message: "请输入", trigger: ["blur","change"] },
  //   { type: 'number', message: '请输入数字类型' },
  // ],
  // cswdsd: [
  //   { required: true, message: "请输入", trigger: ["blur","change"] },
  //   { type: 'number', message: '请输入数字类型' },
  // ],
});

// 提交
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      ElMessageBox.confirm("确定提交吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        dataInfoSubmitData.value.forEach(item => {
          if(item.attributeType=='sdqd'){
            item.attributeValue=formData.value.sdqd;
          }else if(item.isAutoHandle=='2'){
            item.attributeValue=formData.value.isAutoHandle;
          }else{
            item.attributeValue=formData.value[item.gidMark];
          }
        })
        if(!props.isFrom_energysavingStrategyManage){
          // 非能源策略管理页面引用时,提交数据
          const res = await updateDeviceAttribute({list:dataInfoSubmitData.value});
          ElMessage({
            message: res.message,
            type: "success",
          });
          dataControlVisibility.value=false;
          formData.value={};
          // fun_getList();
        }else{
          // 从能源策略管理页面引用时,返回数据
          dataInfoSubmitData.value.forEach(item => {
            const thisIndex = from_energysavingStrategyManageDataFormData.value.findIndex(v => v.gid==item.gid);
            if(thisIndex>-1){
              from_energysavingStrategyManageDataFormData.value[thisIndex].attributeValue=item.attributeValue;
            }else{
              from_energysavingStrategyManageDataFormData.value.push(item)
            }
          })
          // console.log('from_energysavingStrategyManageDataFormData.value:',from_energysavingStrategyManageDataFormData.value);
          ElMessage({
            message: '暂存成功',
            type: "success",
          });
          dataControlVisibility.value=false;
          formData.value={};
        }
      })
    }
    else{
      console.log('表单验证失败:',fields);
    }
  })
};

</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
//详情弹窗
.info-dialog{
  position: absolute;
  right: 0;
  top: 0;
  width: 350px;
  height: 600px;
  background: url('/static/images/dialog-bg1.png') no-repeat;
  background-size: 100% 100%;
  padding: 72px 22px 54px;
  color: #fff;
}
//启停弹窗
.control-dialog{
  position: absolute;
  left: 50%;
  margin-left: -180px;
  top: 50%;
  margin-top: -205px;
  width: 360px;
  height: 410px;
  background-color: rgba(91, 115, 255, 0.4);
  color: #fff;
  .input-bg-green,
  .input-bg-green :deep(.el-input__wrapper){
      background-color: rgba(0, 255, 255, 0.1);
  }
  :deep(.el-form-item__label){
    color: #fff;
    font-size: 14px;
    line-height: 1;
    align-items: center;
  }
  :deep(.el-form-item){
    max-width: calc(100% - 0.167rem);
  }
  :deep(.el-radio){
    color: #fff;
  }
  :deep(.el-input__inner){
    color: #fff;
  }
  :deep(.el-input__inner::placeholder){
    color: #fff;
  }
  :deep(.el-form-item__label:before){
    display: none;
  }
}
</style>
