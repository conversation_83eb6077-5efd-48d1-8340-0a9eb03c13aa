<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form label-width="112" :inline="true" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <el-form-item label="小组名称:" prop="inspectionTeamName">
            <el-input v-model="formData.inspectionTeamName" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="部门:" prop="ssbmbm">
            <el-cascader class="width-all" ref="deptTreeRef" v-model="formData.inspectionUnit" :options="deptTree" :props="deptTreeProps" :collapse-tags-tooltip="true" clearable @change="fun_deptChange" placeholder="请选择" />
            <!-- <el-select v-model="formData.ssbmbm" placeholder="请选择" @change="fun_ssbmChange">
              <el-option v-for="(item, index) in ssbmOption" :key="index" :label="item.label" :value="item.value" />
            </el-select> -->
          </el-form-item>
          <el-form-item label="描述:" prop="teamDesc" style="display: flex;">
            <el-input type="textarea" v-model="formData.teamDesc" placeholder="请输入" :rows="3" maxlength="500" resize="none" :show-word-limit="true" />
          </el-form-item>
          <div>
            <el-form-item label="启用状态:" prop="triggerStatus">
              <el-switch width="50" v-model="formData.triggerStatus" :active-value="1" :inactive-value="0" active-text="开启" inactive-text="关闭" inline-prompt />
            </el-form-item>
          </div>

          <el-form-item label="选择人员:" prop="memberIds" style="display: flex;">
            <el-transfer v-model="formData.memberIds" :data="membersTransfer" filterable :props="{label:'label',key:'id'}" :titles="['人员列表', '已选人员']" @change="fun_membersChange">
              <template #right-empty>
                <span>未选择</span>
              </template>
            </el-transfer>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

import {
  getDeptTree,
  getPersonnelTree,
  add,
  update,
} from "@/api/comprehensive-config/inspect-team-config/index";

import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";
import { handleTree } from "@/utils/ruoyi";

import { ElMessage, ElMessageBox } from "element-plus";
import _ from "lodash";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
}

// 表单数据-回显
const formData = computed(() => {
  console.log('props.data:',props.data);
  //设置初始默认状态
  if(props.data.triggerStatus==='' || props.data.triggerStatus === null || props.data.triggerStatus===undefined)props.data.triggerStatus = 1;//启用状态
  //人员
  if(props.data.userId === '' || props.data.userId === null || props.data.userId===undefined){
    props.data.memberIds = [];
  }else{
    props.data.memberIds = props.data.userId.split(',');
  }

  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});


onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getDeptTree();//获取部门
  fun_getMembersTransfer();//获取人员
});

// 获取字典
// const fun_getDicts = async () => {
//   const {data:cgqlxOptionData} = await getDict('cgqLX');//获取传感器类型
//   cgqlxOption.value = cgqlxOptionData.list;
//   console.log('传感器类型cgqlxOption:',cgqlxOption.value);
// }

// 获取部门
const deptTreeRef = ref();
const deptTreeProps = {
  label: 'deptName',
  value: 'deptId',
  children: 'children',
  multiple: false,//是否多选
  emitPath: false,//是否返回路径
}
const deptTree = ref([
  // { label: '部门1', value: '1' },
  // { label: '部门2', value: '2' },
  // { label: '部门3', value: '3' },
  // { label: '部门4', value: '4' },
  // { label: '部门5', value: '5' },
]);
const fun_getDeptTree = async () => {
  const {data} = await getDeptTree()
  console.log('部门列表data:',data);
  // console.log('部门列表data:',handleTree(data,'deptId','parentId'));
  deptTree.value = handleTree(data,'deptId','parentId');
  console.log('部门列表deptTree:',deptTree.value);
}
// 部门选择
const fun_deptChange = (val) => {
  console.log('部门选择:',val);
  //部门中文字段
  formData.value.ownerSystem = deptTreeRef.value.getCheckedNodes(true)[0].label;
  // console.log('部门中文字段:',formData.value.ownerSystem);
}

// 获取人员
const membersTransfer = ref([
  // { label: '成员1', id: '1'},
  // { label: '成员2', id: '2'},
  // { label: '成员3', id: '3'},
  // { label: '成员4', id: '4'},
  // { label: '成员5', id: '5'},
])
const fun_getMembersTransfer = async () => {
  const {data} = await getPersonnelTree();
  const childrens = fun_getTreeChildrens(data);//提出树所有子集
  membersTransfer.value = childrens;
  // console.log('人员列表membersTransfer:',membersTransfer.value);
}
//提出树所有子集
function fun_getTreeChildrens(data){
  // 检查输入是否为数组，若不是则返回空数组
  if (!Array.isArray(data)) {
    return [];
  }
  let childrens = [];
  data.forEach(item => {
    if (item.childrenList && item.childrenList.length > 0) {
      // 若当前节点有子节点，递归调用函数处理子节点
      childrens = childrens.concat(fun_getTreeChildrens(item.childrenList));
    }else{
      // 若当前节点没有子节点，则将当前节点添加到结果数组
      childrens = childrens.concat(item);
    }
  });
  return childrens;
}
// 人员选择
const fun_membersChange = (val) => {
  console.log('人员选择:',val);
  formData.value.userId = val.join(',');
  formData.value.inspector = val.map(item => membersTransfer.value.find(v => v.id === item).label).join(',');
  // console.log('人员选择name:',formData.value.inspector);
}

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  inspectionTeamName: [
    { required: true, message: "请输入", trigger: "blur" },
  ],
  // ssbmbm: [
  //   { required: true, message: "请选择", trigger: "blur" },
  // ],
  memberIds: [
    { required: true, message: "请选择", trigger: "blur" },
  ]
});


// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增") {
        const res = await add(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      } else {
        const res = await update(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      }
    }
  });
};

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
