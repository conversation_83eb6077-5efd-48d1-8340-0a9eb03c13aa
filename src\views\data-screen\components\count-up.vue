<template>
  <count-up :end-val="numbers" :duration="2"></count-up>
</template>
<script setup>
import CountUp from "vue-countup-v3";
import { ref, toRefs, watch } from "vue";
const props = defineProps({
  value: Number,
  duration: {
    type: Number,
    default: 0,
  },
});
const numbers = ref(0);
watch(
  () => props.value,
  (val) => {
    if (val) {
      numbers.value = Number(val);
    } else {
      numbers.value = 0;
    }
  },
  {
    immediate: true,
  }
);
</script>
