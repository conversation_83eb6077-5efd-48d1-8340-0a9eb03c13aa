import { ElMessage, ElMessageBox } from "element-plus";
import { ref } from "vue";

export function useDeleteData(){
  const deleleIds = ref([]);

  const deleteData = (api:any,ids:any,callback:any) => {
    deleleIds.value = ids;
    // 删除数据
    if (!deleleIds.value.length) {
      ElMessage.warning('请先选择需要删除的数据')
      return
    }
    ElMessageBox.confirm('确认删除吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      const res = await api({
        ids: deleleIds.value.toString()
      })
      ElMessage({
        message: res.message,
        type: res.success ? 'success' : 'error'
      })
      res.success && callback();
    })
  }

  return{
    deleteData
  }

}