<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">班次名称:</el-col>
        <el-col :span="8">{{ data.classesName }}</el-col>
        <el-col :span="4" class="text-right">上班时间:</el-col>
        <el-col :span="8">{{ data.workStartTime }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">下班时间:</el-col>
        <el-col :span="8">{{ data.workEndTime }}</el-col>
        <el-col :span="4" class="text-right">启用状态:</el-col>
        <el-col :span="8">{{ triggerStatusOption.find(item => item.value === data.triggerStatus)?.label }}</el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";
import { getDict } from "@/api/common";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      {
      }
    },
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};

// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
})

// 获取字典
const fun_getDicts = async () => {
  // const {data:taskTypeOptionData} = await getDict('task_type');//获取巡检类型
  // taskTypeOption.value = taskTypeOptionData.list;
  // console.log('巡检类型taskTypeOption:',taskTypeOption.value);
}

//启用状态
const triggerStatusOption = ref([
  { label: "启用", value: 1, type: "success" },
  { label: "停用", value: 0, type: "danger" },
]);

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
