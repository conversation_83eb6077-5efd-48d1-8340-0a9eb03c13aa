<template>
  <el-dialog
    v-model="isVisible"
    width="860"
    :show-close="false"
    :close-on-click-modal="false"
    class="dialog"
    @open="fun_open"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main over-auto-y" style="max-height: 60vh">
      <div class="left">
        <el-form
          label-width="112"
          :inline="true"
          :model="formData"
          ref="ruleFormRef"
          :rules="rules"
          status-icon
        >
          <el-form-item label="设备:" prop="xjsbbm">
            <el-select
              v-model="formData.xjsbbm"
              value-key="gid"
              :collapse-tags="true"
              collapse-tags-tooltip
              placeholder="请选择"
              clearable
              @change="fun_xjsbChange"
              :disabled="formData.inspectTaskDialogType"
            >
              <el-option
                v-for="(item, index) in xjsbOption"
                :key="index"
                :label="item.sbmc"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="隐患名称:" prop="yhmc">
            <el-input v-model="formData.yhmc" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="隐患类型:" prop="xjlxbm">
            <el-select
              v-model="formData.xjlxbm"
              placeholder="请选择"
              clearable
              @change="fun_xjlxChange"
            >
              <el-option
                v-for="(item, index) in xjlxOption"
                :key="index"
                :label="item.businessLabel"
                :value="item.businessValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="隐患等级:" prop="xjxbm">
            <el-select
              v-model="formData.xjxbm"
              placeholder="请选择"
              clearable
              @change="fun_xjxChange"
            >
              <el-option
                v-for="(item, index) in xjxOption"
                :key="index"
                :label="item.businessLabel"
                :value="item.businessValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="隐患描述:" prop="remark" style="display: flex">
            <el-input
              type="textarea"
              v-model="formData.remark"
              placeholder="请输入"
              :rows="3"
              maxlength="500"
              resize="none"
              :show-word-limit="true"
            />
          </el-form-item>
          <el-form-item
            label="维修前:"
            prop="fileListEdit"
            style="display: flex"
          >
            <el-upload
              v-model:file-list="formData.fileListEdit"
              :action="action"
              :headers="headers"
              list-type="picture-card"
              :limit="9"
              :on-success="handlePictureCardSuccess"
              :before-upload="handlePictureCardBeforeUpload"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>
          </el-form-item>
          <el-form-item
            label="维修后:"
            prop="fileListEdit2"
            style="display: flex"
            v-if="formData.inspectTaskDialogType != 'warning'"
          >
            <el-upload
              v-model:file-list="formData.fileListEdit2"
              :action="action"
              :headers="headers"
              list-type="picture-card"
              :limit="9"
              :before-upload="handlePictureCardBeforeUpload2"
              :on-success="handlePictureCardSuccess2"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>
          </el-form-item>

          <!-- <el-form-item label="上传附件:" prop="fileAllList">
            <el-upload
              v-model:file-list="formData.fileAllList"
              class="upload-demo"
              :action="action"
              :headers="headers"
              :limit="1"
              :before-upload="beforeAvatarUpload"
              :on-success="handleAvatarSuccess"
            >
              <el-button type="primary" :disabled="formData.fileAllList?.length >= 1">点击上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  文件大小不超过5MB,支持扩展名:doc,docx,pdf
                </div>
              </template>
            </el-upload>
          </el-form-item> -->
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

import {
  getDevice,
  add,
  addTMaintenance
} from "@/api/comprehensive-operation/device-maint-conserve/maint-manage";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage } from "element-plus";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log("打开fun_open:", props.data);
  // 回显附件
  if (props.data.fileList) {
    props.data.fileListEdit = props.data.fileList.map((e) => {
      return {
        name: e.fileName,
        url: import.meta.env.VITE_APP_IMG_URL + e.filePath,
      };
    });
  }
  // 回显附件2
  if (props.data.fileList2) {
    props.data.fileListEdit2 = props.data.fileList2.map((e) => {
      return {
        name: e.fileName,
        url: import.meta.env.VITE_APP_IMG_URL + e.filePath,
      };
    });
  }
  nextTick(() => {
    ruleFormRef.value && ruleFormRef.value.clearValidate();
  });
};

// 表单数据-回显
const formData = computed(() => {
  console.log("props.data:", props.data);
  //设置初始默认状态
  // if(props.data.xjryType===undefined)props.data.xjryType = 1;
  //巡检详情跳转过来的
  if (props.data.inspectTaskDialogType) {
    props.data.xjsbbm = xjsbOption.value.find(
      (item) => item.gid == props.data.inspect_sbbms
    );
    console.log(222, "走这里");
    // fun_xjsbChange(props.data.xjsbbm)
  }

  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});

onMounted(async () => {
  fun_getDicts(); //获取字典
  fun_getxjsbOptions(); //获取设备
});

const xjlxOption = ref([]); //隐患类型
const xjxOption = ref([]); //隐患等级
// 获取字典
const fun_getDicts = async () => {
  const { data: xjlxOptionData } = await getDict("YHLX"); //获取隐患类型
  xjlxOption.value = xjlxOptionData.list;
  const { data: xjxOptionData } = await getDict("YHDJ"); //获取隐患等级
  xjxOption.value = xjxOptionData.list;
};

// 获取设备
const xjsbOption = ref([
  { label: "设备1", value: "sb1" },
  { label: "设备2", value: "sb2" },
  { label: "设备3", value: "sb3" },
]);
const fun_getxjsbOptions = async () => {
  let res = await getDevice();
  if (res.code === 0) {
    xjsbOption.value = res.data.list;
  }
};
// 设备选择
const fun_xjsbChange = (val) => {
  formData.value.bglxmc = val?.sbmc;
};

// 隐患类型
// const xjlxOption = ref([]);
// 隐患类型选择
const fun_xjlxChange = (val) => {
  console.log("隐患类型选择:", val);
};

// // 隐患等级
// const xjxOption = ref([
//   { label: "一级", value: "1" },
//   { label: "二级", value: "2" },
//   { label: "三级", value: "3" },
// ]);
// 隐患等级选择
const fun_xjxChange = (val) => {
  formData.value.bglxmc = xjxOption.value.find(
    (item) => item.businessValue == val
  ).businessLabel;
};

const action =
  import.meta.env.VITE_APP_IMG_URL + "/api/platform/system/fileInfo/uploadFile";
const headers = { Authorization: "Bearer " + getToken() };
// 上传图片前
const handlePictureCardBeforeUpload = (file) => {
  console.log("上传图片前:", file);
  const fileExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
  if (formData.value.fileListEdit) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传jpg,jpeg,png,gif,bmp,webp格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("图片大小不能超过5MB");
    return false;
  }
};
// 上传图片成功
const handlePictureCardSuccess = (file) => {
  console.log("上传图片成功:", file);
  formData.value.fileList = formData.value.fileListEdit.map((e) => ({
    gid: e.gid,
    maintenanceid: formData.value.gid,
    fileGid: e.response.data.gid ?? e.gid,
    // originalName: e.response.data.originalName ?? e.originalName,
  }));
};
// 上传图片前2
const handlePictureCardBeforeUpload2 = (file) => {
  console.log("上传图片前:", file);
  const fileExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
  if (formData.value.fileListEdit2) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传jpg,jpeg,png,gif,bmp,webp格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("图片大小不能超过5MB");
    return false;
  }
};
// 上传图片成功2
const handlePictureCardSuccess2 = (file) => {
  console.log("上传图片成功:", file);
  formData.value.fileList2 = formData.value.fileListEdit2.map((e) => ({
    gid: e.gid,
    maintenanceid: formData.value.gid,
    fileGid: e.response.data.gid ?? e.gid,
    // originalName: e.response.data.originalName ?? e.originalName,
  }));
};

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  xjsbbm: [{ required: true, message: "请选择", trigger: "blur" }],
  yhmc: [{ required: true, message: "请输入", trigger: "blur" }],
  xjlxbm: [{ required: true, message: "请选择", trigger: "blur" }],
  xjxbm: [{ required: true, message: "请选择", trigger: "blur" }],
  // remark: [
  //   { required: true, message: "请输入", trigger: "blur" },
  // ],
  //附件没有上传对接接口，暂时注释掉附件验证规则
  // fileListEdit: [
  //   { required: true, message: "请上传维修前图片", trigger: "change" },
  //   {
  //     validator: (rule, value, callback) => {
  //       if (value.length > 0) {
  //         callback();
  //       } else {
  //         callback(new Error("请上传维修前图片"));
  //       }
  //     },
  //     trigger: "change",
  //   },
  // ],
  // fileListEdit2: [
  //   { required: true, message: "请上传维修后图片", trigger: "change" },
  //   {
  //     validator: (rule, value, callback) => {
  //       if (value.length > 0) {
  //         callback();
  //       } else {
  //         callback(new Error("请上传维修后图片"));
  //       }
  //     },
  //     trigger: "change",
  //   },
  // ],

  // fileAllList: [
  //   { required: true, message: "请上传附件", trigger: "blur" },
  // ],
});

// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (
        props.title == "新增" ||
        props.title == "隐患上报" ||
        props.title == "维修上报"
      ) {
        let res = {};
        if (!formData.value.inspectTaskDialogType) {
          // 新增
          console.log("xing zeng", formData.value);
          let params = {
            deviceName: formData.value?.xjsbbm?.sbmc,
            deviceId: formData.value?.xjsbbm?.gid,
            maintenanceName: formData.value?.yhmc,
            maintenanceType: formData.value?.xjlxbm,
            level: formData.value?.xjxbm,
            descMsg: formData.value?.remark,
            fileBeforeList: formData.value?.fileList,
            fileEndList: formData.value?.fileList2,
          };
          res = await add(params);
          console.log("res", res);
        } else if (formData.value.inspectTaskDialogType == "warning") {
          // 隐患上报
           let params = {
            deviceName: formData.value?.xjsbbm?.sbmc,
            deviceId: formData.value?.xjsbbm?.gid,
            maintenanceName: formData.value?.yhmc,
            maintenanceType: formData.value?.xjlxbm,
            level: formData.value?.xjxbm,
            descMsg: formData.value?.remark,
            fileBeforeList: formData.value?.fileList,
            fileEndList: formData.value?.fileList2,
            planId: props?.data?.planId,
            remark:"隐患"
          };
          res = await addTMaintenance(params);
        } else if (formData.value.inspectTaskDialogType == "maint") {
          // 维修上报
          let params = {
            deviceName: formData.value?.xjsbbm?.sbmc,
            deviceId: formData.value?.xjsbbm?.gid,
            maintenanceName: formData.value?.yhmc,
            maintenanceType: formData.value?.xjlxbm,
            level: formData.value?.xjxbm,
            descMsg: formData.value?.remark,
            fileBeforeList: formData.value?.fileList,
            fileEndList: formData.value?.fileList2,
            planId: props?.data?.planId,
            remark:"维修"
          };
          res = await add(params);
        }
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      } else {
        const res = await updateReport(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      }
      formData.value.fileListEdit = [];
      formData.value.fileListEdit2 = [];
    }
  });
};

// 上传附件
/* const action = import.meta.env.VITE_APP_IMG_URL + "/api/platform/system/fileInfo/uploadFile";
const headers = { Authorization: "Bearer " + getToken() };
const beforeAvatarUpload = (file) => {
  const fileExtensions = ["doc", "docx", "pdf"];
  if (formData.value.fileAllList) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传doc,docx,pdf格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("文件大小不能超过5MB");
    return false;
  }
}; */
/**文件上传成功回调 */
// const handleAvatarSuccess = (response, file) => {
//   formData.value.fileList = formData.value.fileAllList.map((e) => {return {fileGid:e.response.data.gid ?? e.gid}});
//   // formData.value.fj = formData.value.fileAllList.map((e) => e.response.data.originalName ?? e.originalName).toString();
// };
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
