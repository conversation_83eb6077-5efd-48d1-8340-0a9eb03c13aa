import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';


/* 启停时间配置页面 */

// 启停时间配置-新增
export function addStartendTimeInfo(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tTimeModel/addTTimeModel',
    method: 'post',
    data: data
  })
}

// 启停时间配置-修改
export function updateStartendTimeInfo(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tTimeModel/editTTimeModel',
    method: 'post',
    data: data
  })
}

// 启停时间配置-删除
export function delStartendTimeInfos(delIds: any) {
  return request({
    url: baseRouter + apiPrefix + '/tTimeModel/deleteByIds',
    method: 'delete',
    params: { ids: delIds },
  })
}

// 启停时间配置-列表
export function getStartendTimeList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tTimeModel/byPage',
    method: 'post',
    data: {...query}
  })
}

// 启停时间配置-详情
export function getStartendTimeInfo(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tTimeModel/getInfo',
    method: 'get',
    params: {...query}
  })
}

// 启停时间配置-启用停用
export function editStartendTimeStatus(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tTimeModel/editTTimeModelStatus',
    method: 'get',
    params: {...query}
  })
}








