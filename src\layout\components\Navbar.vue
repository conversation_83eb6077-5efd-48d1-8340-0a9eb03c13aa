<template>
  <div class="navbar">
    <!-- <hamburger
      id="hamburger-container"
      :is-active="appStore.sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    /> -->
    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!settingsStore.topNav" />
    <div class="top-logo-container">
      <div class="top-logoDiv">
        <div class="logo"></div>
        <span class="sys-name">智慧园区可视化总控平台</span>
        <!-- <div class="line"></div> -->
      </div>
    </div>
    <div class="width-all flex flex-both flex-middle border-b-ddd">
      <div class="flex flex-both flex-middle">
        <el-icon class="font-20 marg-0_20 marg-t-5"><Expand /></el-icon>
        <top-nav id="topmenu-container" class="topmenu-container" v-if="settingsStore.topNav" />
      </div>
      <div class="flex flex-both flex-middle">
        <img class="skip" src="@/assets/images/skip-blue.png" alt="" @click="skipFn" />
        <div class="right-menu">
          <template v-if="appStore.device !== 'mobile'">
            <!-- <header-search id="header-search" class="right-menu-item" /> -->
            <screenfull id="screenfull" class="right-menu-item hover-effect" />
    
            <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
              <size-select id="size-select" class="right-menu-item hover-effect" />
            </el-tooltip> -->
          </template>
          <div class="avatar-container">
            <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click">
              <div class="avatar-wrapper">
                <!-- <img :src="userStore.avatar" class="user-avatar" /> -->
                <el-icon size="26" class="user-avatar">
                  <Avatar />
                </el-icon>
                <div class="user-name">
                  {{ useUserStore().name }}
                </div>
    
                <!-- <el-icon class=""><caret-bottom /></el-icon> -->
              </div>
              
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="setLayout" v-if="settingsStore.showSettings">
                    <span>布局设置</span>
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <span>退出登录</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessageBox } from "element-plus";
import Breadcrumb from "@/components/Breadcrumb";
import TopNav from "@/components/TopNav";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import HeaderSearch from "@/components/HeaderSearch";
import useAppStore from "@/store/modules/app";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";

const appStore = useAppStore();
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const router = useRouter();

function toggleSideBar() {
  appStore.toggleSideBar();
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        // location.href = "/login";
        router.push('/login')
      });
    })
    .catch(() => { });
}

const emits = defineEmits(["setLayout"]);
function setLayout() {
  emits("setLayout");
}

const skipFn = () => {
  router.push("/data-screen");
  // window.open(
  //   "http://*************:8000/vfusion/#/bigscreen/code/bigScreen_3gEmHpDMiF"
  // );
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 64px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;

  .skip {
    width: 28px;
    height: 28px;
    cursor: pointer;
  }

  .top-logo-container {
    display: flex;
    height: 100%;
    // width: 100%;

    ::v-deep(.svg-icon) {
      width: 24px;
      height: 24px;
    }
    .top-logoDiv{
      background-color: #060746;
      width: 232px;
      display: flex;
      align-items: center;
      padding: 0 15px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.24);
    }
    .logo {
      width: 49px;
      height: 32px;
      background-image: url(../../assets/logo/logo1.svg);
      background-size: 100% 100%;
      margin-right: 12px;
    }

    .sys-name {
      color: #fff;
      line-height: 20px;
      font-size: 18px;
      font-weight: bold;
      font-family: SourceHanSansCN-Bold;
      // margin-top: 18px;
      // padding-right: 10px;
      // max-width: 200px;
      // min-width: 130px;
    }

    .line {
      width: 1px;
      height: 16px;
      background: #ffffff;
      margin: 24px 16px 0 0;
    }

    .el-menu {
      // width: 100%;
      height: 100%;
    }
  }

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    // position: absolute;
    // left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    height: 100%;
    line-height: 64px;
    display: flex;
    margin-left: auto;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #3275F9;
      // vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      .user-name {
        width: 50px;
        line-height: 40px;
        margin-right: 10px;
        color:#000;
        font-weight: bold;
      }

      .avatar-wrapper {
        margin-top: 12px;
        position: relative;
        display: flex;
        font-size: 14px;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        // i {
        //   cursor: pointer;
        //   position: absolute;
        //   right: -20px;
        //   top: 25px;
        //   font-size: 12px;
        // }
      }
    }

    .right-menu-item:last-child {
      padding: 0;
    }
  }
}
</style>
