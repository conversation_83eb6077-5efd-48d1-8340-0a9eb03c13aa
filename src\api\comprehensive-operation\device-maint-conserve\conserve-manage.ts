import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = 'monitor';


// 养护管理-新增
export function add(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintain/addTMaintain',
    method: 'post',
    data: data
  })
}



//养护管理-列表
export function getList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintain/byPage',
    method: 'post',
    data: {...query}
  })
}
//获取设备
export function getDevice() {
  return request({
    url: baseRouter + apiPrefix + '/equipmentInfo/getAllList',
    method: 'get',
  })
}
//根据设备gid获取养时限和上次养护时间

export function getEquiInfo(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintain/getEquiInfo',
    method: 'get',
    params: {...query}
  })
}






