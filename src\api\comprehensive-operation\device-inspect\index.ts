import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';

// 获取设备
export function getDeviceTree(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/deviceInfo/getDeviceTree',
    method: 'get',
    params: {...query},
  })
}

// 获取人员树
export function getPersonnelTree(query: any) {
  return request({
    url: baseRouter + '/system/user/deptUserTree',
    method: 'get',
    params: {...query},
  })
}

// 获取小组
export function getGroupOptions(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMeInspectionteam/byPage',
    method: 'post',
    data: {...query}
  })
}

// 获取巡检项筛选列表
export function getInspectItemList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tInspectionItems/byPage',
    method: 'post',
    data: {...query}
  })
}

// 巡检计划-新增
export function add(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMbInvestigationPlanJob/addTMbInvestigationPlanJob',
    method: 'post',
    data: data
  })
}

// 巡检计划-修改
export function update(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMbInvestigationPlanJob/editTMbInvestigationPlanJob',
    method: 'post',
    data: data
  })
}

// 巡检计划-删除
export function del(delIds: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMbInvestigationPlanJob/deleteByIds',
    method: 'delete',
    params: { ids: delIds },
  })
}

// 巡检计划-列表
export function getList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMbInvestigationPlanJob/byPage',
    method: 'post',
    data: {...query}
  })
}


// 查询安全评估-技术状况报告详细
// export function getReportInfo(gid: any) {
//   return request({
//     url: baseRouter + '/business/deviceInspect/getInfo',
//     method: 'get',
//     params: { gid },
//   })
// }


