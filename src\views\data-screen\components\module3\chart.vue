<template>
  <div ref="refChart" class="chart-box"></div>
</template>
<script setup>
import { ref, watch } from "vue";
import * as echarts from "echarts";
const props = defineProps({
  datas: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
const refChart = ref();

// onMounted(() => {
//   initEcharts();
// });
watch(
  () => props.datas,
  (newVal) => {
    initEcharts();
  },
  { deep: true }
);

let myChart = null;

const initEcharts = () => {
  if (!refChart.value) return;

  // 销毁已有实例
  if (myChart) {
    myChart.dispose();
  }

  try {
    myChart = echarts.init(refChart.value);

    const maxValue = Math.max(...props.datas.map((item) => item.value));
    let xAxisData = [];
    let barData = [];
    let lineData = [];
    props.datas.forEach((item) => {
      xAxisData.push(item.label);
      barData.push(item.value);
      lineData.push(item.onYear);
    });
    let legendData = ["耗能", "同比"];
    const option = {
      backgroundColor: "transparent",
      legend: {
        itemWidth: 15,
        itemHeight: 8,
        data: legendData,
        textStyle: {
          color: "#f9f9f9",
        },
        top: 10,
      },
      tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(2,7,22,0.8)",
        borderWidth: 0,
        textStyle: {
          color: "#fff",
          fontSize: 12,
        },
        axisPointer: {
          type: "line",
          lineStyle: {
            color: "#fff",
            width: 1,
          },
          position: {
            alignWithLabel: true,
            labelOffset: [0, 0],
          },
        },
        formatter: function (params) {
          let result = "";
          params.forEach((item, index) => {
            result +=
              item.marker +
              item.seriesName +
              "：" +
              item.value +
              (item.seriesType == "bar" ? "<br/>" : "%");
          });
          return result;
        },
      },
      grid: {
        left: "2%",
        right: "2%",
        bottom: "5%",
        top: "30%",
        containLabel: true,
      },
      xAxis: [
        {
          type: "category",
          data: xAxisData,
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(255, 255, 255, 0.24)",
              width: 1,
            },
          },
          axisTick: {
            show: false,
            alignWithLabel: true,
          },
          axisLabel: {
            show: true,
            color: "rgba(199, 214, 255, 0.64)",
            fontSize: 12,
            interval: 0,
          },
        },
      ],
      yAxis: [
        {
          min: 0,
          max: maxValue,
          type: "value",
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: "rgba(255, 255, 255, 0.24)",
              type: "solid",
            },
          },
          axisLabel: {
            show: true,
            color: "rgba(199, 214, 255, 0.64)",
            fontSize: 12,
          },
        },
        {
          type: "value",
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: "背景",
          type: "bar",
          barWidth: 22,
          barGap: "-70%",
          data: Array(props.datas.length).fill(maxValue),
          itemStyle: {
            color: "rgba(255,255,255,0.039)",
          },
          tooltip: {
            show: false,
          },
          zlevel: 9,
        },
        {
          type: "bar",
          name: legendData[0],
          barWidth: 8,
          data: barData,
          z: 2,
          itemStyle: {
            barBorderRadius: [8, 8, 0, 0],
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(64, 249, 255, 1)" },
              { offset: 1, color: "rgba(217, 254, 255, 1)" },
            ]),
          },
        },
        {
          type: "line",
          name: legendData[1],
          yAxisIndex: 1,
          smooth: true,
          lineStyle: {
            width: 2,
          },
          symbol: "circle",
          symbolSize: 2,
          itemStyle: {
            color: "rgba(218, 182, 115, 1)",
          },
          data: lineData,
        },
      ],
    };

    myChart.setOption(option);

    window.addEventListener("resize", () => {
      myChart?.resize();
    });
  } catch (error) {
    console.error("ECharts初始化失败:", error);
  }
};

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
});
</script>
<style scoped lang="scss">
.chart-box {
  width: 424px;
  height: 196px;
  position: relative;
}
</style>
