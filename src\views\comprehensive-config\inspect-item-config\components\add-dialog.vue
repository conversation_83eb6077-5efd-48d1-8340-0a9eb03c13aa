<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form label-width="112" :inline="false" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <el-form-item label="巡检项名称:" prop="itemName">
            <el-input v-model="formData.itemName" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="描述:" prop="remark" style="display: flex;">
            <el-input type="textarea" v-model="formData.remark" placeholder="请输入" :rows="3" maxlength="500" resize="none" :show-word-limit="true" />
          </el-form-item>
          <div>
            <el-form-item label="启用状态:" prop="status">
              <el-switch width="50" v-model="formData.status" :active-value="1" :inactive-value="0" active-text="启用" inactive-text="停用" inline-prompt />
            </el-form-item>
          </div>

          <el-form-item label="选择设备:" prop="deviceIdsArr" style="display: flex;">
            <el-transfer v-model="formData.deviceIdsArr" :data="deviceTransfer" :props="{label:'equipmentName',key:'id'}" :titles="['设备列表', '已选设备']" @change="fun_deviceChange">
              <template #right-empty>
                <span>未选择</span>
              </template>
            </el-transfer>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

import {
  getDeviceOptions,
  addInspectItem,
  updateInspectItem,
} from "@/api/comprehensive-config/inspect-item-config/index";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage, ElMessageBox } from "element-plus";
import _ from "lodash";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
}

// 表单数据-回显
const formData = computed(() => {
  console.log('props.data:',props.data);
  //设置初始默认状态
  if(props.data.status===undefined)props.data.status = 1;//启用状态
  //设备列表回显
  if(props.data.deviceIds===undefined){
    props.data.deviceIdsArr = [];
  }else{
    props.data.deviceIdsArr = props.data.deviceIds.split(',');
  }

  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});


onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getDeviceTransfer();//获取设备
});

// 获取字典
// const fun_getDicts = async () => {
//   const {data:cgqlxOptionData} = await getDict('cgqLX');//获取传感器类型
//   cgqlxOption.value = cgqlxOptionData.list;
//   console.log('传感器类型cgqlxOption:',cgqlxOption.value);
// }

// 获取设备
const deviceTransfer = ref([
  // { equipmentName: '设备1', id: '1'},
  // { equipmentName: '设备2', id: '2'},
  // { equipmentName: '设备3', id: '3'},
  // { equipmentName: '设备4', id: '4'},
  // { equipmentName: '设备5', id: '5'},
])
const fun_getDeviceTransfer = async () => {
  const {data} = await getDeviceOptions()
  console.log('获取设备列表devicesTransfer:',data);
  deviceTransfer.value = data;
}
// 设备选择
const fun_deviceChange = (val) => {
  console.log('设备选择:',val);
  formData.value.deviceIds = val.join(',');
  formData.value.deviceNames = val.map(v => deviceTransfer.value.find(item => item.id === v).equipmentName).join(',')
}

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  itemName: [
    { required: true, message: "请输入", trigger: "blur" },
  ],
  deviceIdsArr: [
    { required: true, message: "请选择", trigger: "blur" },
    { validator: (rule, value, callback)=>{
        if(value.length==0){
          callback(new Error('请选择设备'));
        }else{
          callback();
        }
      }, 
      trigger: 'change' 
    },
  ]
});


// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增") {
        const res = await addInspectItem(formData.value);
        console.log('新增:',res);
        ElMessage({
          message: res.message,
          type: "success",
        });
      } else {
        const res = await updateInspectItem(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
      }
      emits("refreshList");
      emits("onChangeVisible");
    }
  });
};

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
