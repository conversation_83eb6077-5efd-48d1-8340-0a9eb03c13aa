<template>
  <el-dialog v-model="isVisible" width="950" :show-close="false" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main over-auto-y" style="max-height: 60vh;">
      <div class="left">
        <el-form label-width="160" :inline="true" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <div class="title-l-blue-box marg-b-10">
            <span>基本信息</span>
          </div>
          <!-- :prop="item.key" -->
          <el-form-item :label="item.title+':'" :prop="index===0?item.key:''" v-for="(item,index) in baseColumns" :key="index">
            <div class="width-all flex">
              <el-input v-if="item.config.option==='input'" :type="item.config.type" v-model="formData[item.key]" placeholder="请输入" clearable />
              <el-date-picker v-else-if="item.config.option==='datePicker'" :type="item.config.type" v-model="formData[item.key]" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择" clearable />
              <el-select v-else-if="item.config.option==='select'" v-model="formData[item.key]" placeholder="请选择"  clearable >
                <el-option v-for="(item, index) in selectOption[item.key]" :key="index" :label="item.label" :value="item.value" />
              </el-select>
              <span class="flex-item-shrink-0 padd-l-5" v-if="item.unit">{{item.unit}}</span>
            </div>
          </el-form-item>

          <div class="title-l-blue-box marg-b-10">
            <span>监测信息</span>
          </div>
          <!-- :prop="item.key" -->
          <el-form-item :label="item.title+':'" v-for="(item,index) in monitorColumns" :key="index">
            <div class="width-all flex">
              <el-input v-if="item.config.option==='input'" :type="item.config.type" v-model="formData[item.key]" placeholder="请输入" :disabled="true" />
              <el-date-picker v-else-if="item.config.option==='datePicker'" :type="item.config.type" v-model="formData[item.key]" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择" :disabled="true" />
              <el-select v-else-if="item.config.option==='select'" v-model="formData[item.key]" placeholder="请选择" :disabled="true" >
                <el-option v-for="(item, index) in selectOption[item.key]" :key="index" :label="item.label" :value="item.value" />
              </el-select>
              <span class="flex-item-shrink-0 padd-l-5" v-if="item.unit">{{item.unit}}</span>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";
import { useRoute } from "vue-router";
const route = useRoute();
import { Columns } from "../table";

import {
  // getQlOptions,
  // addDeviceInfo,
  updateDeviceInfo,
} from "@/api/device-manage/index";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage } from "element-plus";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
}

// 表单数据-回显
const formData = computed(() => {
  console.log('props.data:',props.data);
  //设置初始默认状态
  // if(props.data.sbzt===undefined)props.data.sbzt = '0';//0:备用,1:在用

  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});


// 表格项
const headerColumns = new Columns();
const baseColumns = ref(headerColumns.columns);
const monitorColumns = ref(headerColumns[`columns_${route.name.toLowerCase().split('-').join('_')}`]);

// selectOption对象
const selectOption = ref({
  //设备状态
  status: [
    { label: "在用", value: "1" },
    { label: "备用", value: "0" },
  ],
  // 设备类型
  sblxfs: [
    { label: "供热", value: "1" },
    { label: "供冷", value: "0" },
  ],
});

onMounted(async () => {
  // fun_getDicts();//获取字典
});

// 获取字典
const fun_getDicts = async () => {
  // const {data:xjlxOptionData} = await getDict('XJLX');//获取巡检类型
  // xjlxOption.value = xjlxOptionData.list;
  // console.log('巡检类型xjlxOption:',xjlxOption.value);
}


// 表单验证
const ruleFormRef = ref();
const ruleObj = {};
const totalColumns = [...baseColumns.value,...monitorColumns.value];
totalColumns.forEach(item => {
  if(item.config.option === 'input') {
    if(item.config&&!item.config.ruleType) {
      ruleObj[item.key] = [{ required: item.config?.ruleRequired===false?false:true, message: "请输入", trigger: "blur" }];
    } else if(item.config&&item.config.ruleType==='positiveInteger') {
      ruleObj[item.key] = [{ required: item.config?.ruleRequired===false?false:true, message: "请输入", trigger: "blur" },{ validator: fun_isPositiveInteger }];
    } else if(item.config&&item.config.ruleType==='positiveNumber') {
      ruleObj[item.key] = [{ required: item.config?.ruleRequired===false?false:true, message: "请输入", trigger: "blur" },{ validator: fun_isPositiveNumber }];
    }
  }
  else if(item.config.option === 'datePicker') {
    ruleObj[item.key] = [{ required: item.config?.ruleRequired===false?false:true, message: "请选择", trigger: "blur" }];
  }
  else if(item.config.option === 'select') {
    ruleObj[item.key] = [{ required: item.config?.ruleRequired===false?false:true, message: "请选择", trigger: "blur" }];
  }
});
const rules = reactive(ruleObj);
// 正则正整数
function fun_isPositiveInteger(rule, value, callback) {
  if (value!=''&&!/^[1-9]\d*$/.test(value)) {
    callback(new Error('请输入正整数'));
  } else {
    callback();
  }
}
// 正则正数
function fun_isPositiveNumber(rule, value, callback) {
  if (value!=''&&!/^([1-9]\d*(\.\d+)?|0\.\d*[1-9])$/.test(value)) {
    callback(new Error('请输入正数'));
  } else {
    callback();
  }
}

// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      let res = {};
      if (props.title == "新增") {
        // res = await updateDeviceInfo(formData.value);
        // if(res.code === 200) {
        //   ElMessage({
        //     message: res.message,
        //     type: "success",
        //   });
        //   emits("refreshList");
        //   emits("onChangeVisible");
        // }
      } else {
        res = await updateDeviceInfo(formData.value);
        if(res.code == '0') {
          ElMessage({
            message: res.message,
            type: "success",
          });
          emits("refreshList");
          emits("onChangeVisible");
        }else{
          ElMessage({
            message: res.message,
            type: "error",
          });
        }
      }
    }
  });
};


// 上传附件
/* const action = import.meta.env.VITE_APP_IMG_URL + "/api/platform/system/fileInfo/uploadFile";
const headers = { Authorization: "Bearer " + getToken() };
const beforeAvatarUpload = (file) => {
  const fileExtensions = ["doc", "docx", "pdf"];
  if (formData.value.fileAllList) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传doc,docx,pdf格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("文件大小不能超过5MB");
    return false;
  }
}; */
/**文件上传成功回调 */
// const handleAvatarSuccess = (response, file) => {
//   formData.value.fileList = formData.value.fileAllList.map((e) => {return {fileGid:e.response.data.gid ?? e.gid}});
//   // formData.value.fj = formData.value.fileAllList.map((e) => e.response.data.originalName ?? e.originalName).toString();
// };

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
