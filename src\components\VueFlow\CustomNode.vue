<template>
  <div class="custom-node text-center font-0" :class="{ 'outline-ddd':isEdit,selected:selected&&isEdit }">

    <img width="100" :style="data.style" :src="data.imgUrl" alt="">
    <!-- <div class="text-center font-14" style="color:#eee;" v-if="data.label">{{ data.label }}</div> -->
    <Handle id="source-0" type="source" :position="Position.Left" style="transform: translate(0%, 0%);width: 6px;height: 6px;top: 20%;" :style="{opacity:isEdit?1:0}" :connectable="isEdit" />
    <Handle id="source-1" type="source" :position="Position.Left" style="transform: translate(0%, 0%);width: 6px;height: 6px;top:calc(50% - 3px)" :style="{opacity:isEdit?1:0}" :connectable="isEdit" />
    <Handle id="source-2" type="source" :position="Position.Left" style="transform: translate(0%, 0%);width: 6px;height: 6px; top:auto;bottom: 20%;" :style="{opacity:isEdit?1:0}" :connectable="isEdit" />

    <Handle id="source-3" type="source" :position="Position.Right" style="transform: translate(0%, 0%);width: 6px;height: 6px;top: 20%;" :style="{opacity:isEdit?1:0}" :connectable="isEdit" />
    <Handle id="source-4" type="source" :position="Position.Right" style="transform: translate(0%, 0%);width: 6px;height: 6px;top:calc(50% - 3px)" :style="{opacity:isEdit?1:0}" :connectable="isEdit" />
    <Handle id="source-5" type="source" :position="Position.Right" style="transform: translate(0%, 0%);width: 6px;height: 6px;top: auto;bottom: 20%;" :style="{opacity:isEdit?1:0}" :connectable="isEdit" />

    <Handle id="source-6" type="source" :position="Position.Top" style="transform: translate(0%, 0%);width: 6px;height: 6px;left: 20%;" :style="{opacity:isEdit?1:0}" :connectable="isEdit" />
    <Handle id="source-7" type="source" :position="Position.Top" style="transform: translate(0%, 0%);width: 6px;height: 6px;left:calc(50% - 3px)" :style="{opacity:isEdit?1:0}" :connectable="isEdit" />
    <Handle id="source-8" type="source" :position="Position.Top" style="transform: translate(0%, 0%);width: 6px;height: 6px;left: auto;right: 20%;" :style="{opacity:isEdit?1:0}" :connectable="isEdit" />
    
    <Handle id="source-9" type="source" :position="Position.Bottom" style="transform: translate(0%, 0%);width: 6px;height: 6px;left: 20%;" :style="{opacity:isEdit?1:0}" :connectable="isEdit" />
    <Handle id="source-10" type="source" :position="Position.Bottom" style="transform: translate(0%, 0%);width: 6px;height: 6px;left:calc(50% - 3px)" :style="{opacity:isEdit?1:0}" :connectable="isEdit" />
    <Handle id="source-11" type="source" :position="Position.Bottom" style="transform: translate(0%, 0%);width: 6px;height: 6px;left: auto;right: 20%;" :style="{opacity:isEdit?1:0}" :connectable="isEdit" />
  </div>
</template>

<script setup>
import { Handle, Position } from '@vue-flow/core';

defineProps({
  id: String,
  data: Object,
  selected: Boolean,
  isEdit: {
    type: Boolean,
    default: true,
  },
});


</script>

<style scoped>
.custom-node {
  /* border: 1px solid #ccc; */
  border-radius: 5px;
  /* background-color: #f5f5ff; */
  /* box-shadow: 0 2px 4px rgba(0,0,0,0.1); */
  transition: all 0.3s ease;
}

.custom-node.selected {
  /* border: 2px solid #7f56ff; */
  outline: 2px solid #7f56ff;
  box-shadow: 0 0 10px rgba(127, 86, 255, 0.5);
}

.custom-node-header {
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
}

.node-icon {
  margin-right: 8px;
  font-size: 16px;
}

.node-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-node-content {
  padding: 8px 0;
  min-height: 40px;
  word-break: break-word;
}

.custom-node-footer {
  border-top: 1px solid #e0e0e0;
  padding-top: 8px;
  display: flex;
  justify-content: space-between;
}

.node-type {
  font-size: 10px;
  background-color: #eaeaff;
  padding: 2px 6px;
  border-radius: 10px;
}
</style> 