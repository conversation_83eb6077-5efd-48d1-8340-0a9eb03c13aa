<template>
  <el-dialog v-model="dialogShow" width="860" class="dialog" title="详情">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
      </div>
    </template>
    <div class="dialog-main">
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="基本信息" name="first">
          <el-row :gutter="10" class="marg-b-10">
            <el-col :span="4" class="text-right">设备编码：</el-col>
            <el-col :span="8">{{ formData.gid }}</el-col>
            <el-col :span="4" class="text-right">设备名称：</el-col>
            <el-col :span="8">{{ formData.deviceName }}</el-col>
          </el-row>
          <el-row :gutter="10" class="marg-b-10">
            <el-col :span="4" class="text-right">监测项名称：</el-col>
            <el-col :span="8">{{ formData.monitorItemName }}</el-col>
            <el-col :span="4" class="text-right">监测项编码：</el-col>
            <el-col :span="8">{{ formData.monitorItemCode }}</el-col>
          </el-row>
          <el-row :gutter="10" class="marg-b-10">
            <el-col :span="4" class="text-right">单位：</el-col>
            <el-col :span="8">{{ formData.monitorItemMark }}</el-col>
            <el-col :span="4" class="text-right">设备状态：</el-col>
            <el-col :span="8">{{ Number(formData.status) === 1 ? '在线' : '离线' }}</el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="物联数据" name="second">
          <el-table :data="mockList" style="width: 100%;" :max-height="500" :header-cell-style="{ textAlign: 'center' }" :cell-style="{ textAlign: 'center' }">
            <el-table-column type="index" label="序号" width="100" />
            <el-table-column prop="name" label="监测项"  />
            <el-table-column prop="monitoringItem" label="监测项值"  width="200"/>
            <el-table-column prop="dataTime" label="监测上报时间" width="200"/>
          </el-table>
          <el-pagination
            class="pagination"
            background
            layout="total, sizes, prev, pager, next"
            :total="total"
            @size-change="onSizeChange"
            @current-change="onCurrentChange"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { defineProps, defineEmits, computed, ref, reactive, watch } from 'vue';
  import { getMockData } from '@/api/mockDeviceManager/deviceManager';

  const props = defineProps({
    isVisible: {
      type: Boolean,
      default: false,
    },
    viewFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const total = ref(0);
  const activeName = ref('first');
  const mockList = ref([]);
  const emits = defineEmits(['changeDialog']);
  const formData: any = computed(() => props.viewFormData);

  const listQuery = reactive({
    pageNum: 1,
    pageSize: 10,
    deviceGid: '',
  });

  const dialogShow = computed({
    get: () => props.isVisible,
    set: (newValue: any) => {
      emits('changeDialog', { flag: newValue, mode: 'view' });
    },
  });

  const getListData = async () => {
    listQuery.deviceGid = formData.value.gid;
    const result = await getMockData(listQuery);
    mockList.value = result.data.list;
    mockList.value.forEach((item: any) => {
      item.name = `${formData.value.monitorItemName}(${formData.value.monitorItemMark})`;
    });
    total.value = Number(result.data.total);
  };

  const onSizeChange = (evt: any) => {
    listQuery.pageSize = evt;
    getListData();
  };

  const onCurrentChange = (evt: any) => {
    listQuery.pageNum = evt;
    getListData();
  };

  watch(dialogShow, async (n) => {
    if (n) {
      await getListData();
    }
  });
</script>
<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
:deep(.el-input .el-input__wrapper) {
  width: 200px !important;
}
.pagination {
  margin-top: 10px;
  float: right;
}
</style>
