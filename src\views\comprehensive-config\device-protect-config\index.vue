<template>
  <div class="pointManage">
    <div class="main-contanier" style="height: 100%;">
      <div class="text-center marg-auto padd-20_0 max-width-all" style="width: 3rem;">
        <div class="title-l-blue-box marg-b-20 text-left font-18"><span>保护参数设置</span></div>
        
        <el-form label-width="182" :inline="false" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <el-form-item label="冷冻低压保护保护值:" prop="lddybhbhz" class=" marg-b-20">
            <div class="width-all flex">
              <el-input type="number" v-model="formData.lddybhbhz" placeholder="请输入" clearable />
              <span class="padd-l-10">Bar</span>
            </div>
          </el-form-item>
          <el-form-item label="冷冻低压保护恢复值:" prop="lddybhhfz" class=" marg-b-20">
            <div class="width-all flex">
              <el-input type="number" v-model="formData.lddybhhfz" placeholder="请输入" clearable />
              <span class="padd-l-10">Bar</span>
            </div>
          </el-form-item>
          <el-form-item label="冷冻高压保护保护值:" prop="ldgybhbhz" class=" marg-b-20">
            <div class="width-all flex">
              <el-input type="number" v-model="formData.ldgybhbhz" placeholder="请输入" clearable />
              <span class="padd-l-10">Bar</span>
            </div>
          </el-form-item>
          <el-form-item label="冷冻高压保护恢复值:" prop="ldgybhhfz" class=" marg-b-20">
            <div class="width-all flex">
              <el-input type="number" v-model="formData.ldgybhhfz" placeholder="请输入" clearable />
              <span class="padd-l-10">Bar</span>
            </div>
          </el-form-item>
          <el-form-item label="保护延时时间:" prop="bhyssj" class=" marg-b-20">
            <div class="width-all flex">
              <el-input type="number" v-model="formData.bhyssj" placeholder="请输入" clearable />
              <span class="padd-l-10">s<i class="hide">kk</i></span>
            </div>
          </el-form-item>
        </el-form>
        <!-- <div>
          说明：
        </div> -->

        <div class="padd-t-20">
          <!-- <el-button @click="cancelBtn">取消</el-button> -->
          <el-button type="primary" @click="submitBtn(ruleFormRef)">提交</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";


// import { getDict } from "@/api/common";
import {
  getDeviceProtectNewInfo,
  addDeviceProtect,
  // delDeviceProtect,
} from "@/api/comprehensive-config/device-protect-config/index";


// 获取字典
const fun_getDicts = async () => {
  // const {data} = await getDict('BGLX')
  // bglxOption.value = data.list;
  // console.log('报告类型bglxOption:',bglxOption.value);
}

// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getList();
});


// 数据
const formData = ref({});
const fun_getList = async () => {
  const { data } = await getDeviceProtectNewInfo();
  console.log('设备保护详情:',data);
  formData.value = data;
};


// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  lddybhbhz: [
    { required: true, message: "请输入冷冻低压保护保护值", trigger: "blur" },
  ],
  lddybhhfz: [
    { required: true, message: "请输入冷冻低压保护恢复值", trigger: "blur" },
  ],
  ldgybhbhz: [
    { required: true, message: "请输入冷冻高压保护保护值", trigger: "blur" },
  ],
  ldgybhhfz: [
    { required: true, message: "请输入冷冻高压保护恢复值", trigger: "blur" },
  ],
  bhyssj: [
    { required: true, message: "请输入保护延时时间", trigger: "blur" },
  ],
});

// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      delete formData.value.gid;
      const res = await addDeviceProtect(formData.value);
      console.log('新增:',res);
      ElMessage({
        message: res.message,
        type: "success",
      });
      fun_getList();
    }
  });
};


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
  .main-contanier{
    height: 100%;
    padding: 15px;
  }
}
</style>
