<template>
  <el-dialog v-model="isVisible" width="950" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main over-auto-y" style="max-height: 60vh;">
      <div class="title-l-blue-box marg-b-10">
        <span>基本信息</span>
      </div>
      <el-row :gutter="10" class="marg-b-10">
        <template v-for="item in baseColumns" :key="item.prop">
          <el-col :span="3" class="text-right">{{ item.title }}:</el-col>
          <el-col :span="5" v-if="item.config?.option!=='select'">{{ data[item.key] }}<span class="padd-l-5" v-if="item.unit&&data[item.key]">{{ item.unit }}</span></el-col>
          <el-col :span="5" v-else-if="item.config?.option==='select'">{{ data[item.key]!==null&&selectOption[item.key].find(v => v.value == data[item.key])?.label }}</el-col>
        </template>
      </el-row>

      <div class="title-l-blue-box marg-b-10">
        <span>监测信息</span>
      </div>
      <el-row :gutter="10" class="marg-b-10">
        <template v-for="item in monitorColumns" :key="item.prop">
          <el-col :span="5" class="text-right">{{ item.title }}:</el-col>
          <el-col :span="3" v-if="item.config?.option!=='select'">{{ data[item.key] }}<span class="padd-l-5" v-if="item.unit&&data[item.key]">{{ item.unit }}</span></el-col>
          <el-col :span="3" v-else-if="item.config?.option==='select'">{{ data[item.slot]!==null&&selectOption[item.key].find(v => v.value == data[item.slot])?.label }}</el-col>
        </template>
      </el-row>

      <div class="title-l-blue-box marg-b-10 marg-t-10">
        <span>养护信息</span>
      </div>
      <div class="padd-10 padd-t-0">
        <el-table :data="conserveList" max-height="400px">
          <el-table-column prop="yhTime" label="养护时间" align="center" width="180" />
          <el-table-column prop="createUserName" label="养护人员" align="center" width="180" />
          <el-table-column prop="remark" label="描述" align="center" />
        </el-table>
        <el-pagination class="pageBox flex flex-right border-t-ddd padd-t-10" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>

      <div class="title-l-blue-box marg-b-10 marg-t-10">
        <span>维修信息</span>
      </div>
      <div class="padd-10 padd-t-0">
        <el-table :data="maintList" max-height="400px">
          <el-table-column prop="level" label="故障等级" align="center">
            <template #default="scope">
              <el-tag :type="deviceFaultLevel.find(item => item.value === scope.row.level)?.type">{{ deviceFaultLevel.find(item => item.value === scope.row.level)?.label }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="maintenanceMsg" label="故障描述" align="center" />
          <el-table-column prop="status" label="维修状态" align="center">
            <template #default="scope">
              <el-tag :type="deviceMaintenanceStatus.find(item => item.value === scope.row.status)?.type">{{ deviceMaintenanceStatus.find(item => item.value === scope.row.status)?.label }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="maintUserName" label="维修人员" align="center" width="180" />
          <el-table-column prop="createTime" label="维修时间" align="center" width="180" />
        </el-table>
        <el-pagination class="pageBox flex flex-right border-t-ddd padd-t-10" v-model:current-page="filter2.pageNum" v-model:page-size="filter2.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total2" background
          @size-change="handleSizeChange2" @current-change="handleCurrentChange2" />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive, watch } from "vue";
import { useRoute } from "vue-router";
const route = useRoute();
import { Columns } from "../table";

import { 
  // getDeviceInspectList,
  getDeviceConserveList,
  getDeviceMaintList,
} from "@/api/device-manage/index";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {},
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};

// 监听
watch([() => props.data.gid], ([newGid], [oldGid]) => {
  // 若新值和旧值相同，则不执行请求
  if (newGid === oldGid) return; 
  fun_getList();
});

// 分页条件-养护
const filter = ref({
  pageNum: 1,
  pageSize: 10,
});
// 分页条件-维修
const filter2 = ref({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const conserveList = ref([]);
const maintList = ref([]);
const total = ref(0);//养护
const total2 = ref(0);//维修

const fun_getList = async () => {
  const {data : conserveData} = await getDeviceConserveList({deviceId: props.data.gid, ...filter.value});
  console.log('设备养护详情:',conserveData);
  conserveList.value = conserveData.list;
  total.value = conserveData.total;
  const {data : maintData} = await getDeviceMaintList({deviceId: props.data.gid, ...filter2.value});
  console.log('设备维修详情:',maintData);
  maintList.value = maintData.list;
  total2.value = maintData.total;
};

// 分页-养护
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};

// 分页-维修
const handleCurrentChange2 = (val) => {
  filter2.value.pageNum = val;
  fun_getList();
};
const handleSizeChange2 = (val) => {
  filter2.value.pageNum = 1;
  filter2.value.pageSize = val;
  fun_getList();
};


// 表格项-基本信息
const headerColumns = new Columns();
const baseColumns = ref(headerColumns.columns);
// 表格项-监测信息
const monitorColumns = ref(headerColumns[`columns_${route.name.toLowerCase().split('-').join('_')}`]);

// selectOption对象
const selectOption = ref({
  //设备状态
  status: [
    { label: "在用", value: "1" },
    { label: "备用", value: "0" },
  ],
  // 设备类型
  sblxfs: [
    { label: "供热", value: "1" },
    { label: "供冷", value: "0" },
  ],
});


// 故障等级
const deviceFaultLevel = ref([
  {label: '轻微',value: 1, type: 'success'},
  {label: '一般',value: 2, type: 'warning'},
  {label: '严重',value: 3, type: 'danger'},
]);

// 维修状态
const deviceMaintenanceStatus = ref([
  {label: '待维修',value: 1, type: 'danger'},
  {label: '维修中',value: 2, type: 'warning'},
  {label: '已完成',value: 3, type: 'success'},
]);

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
