<template>
  <div class="gird-box">
    <div class="gird-item" v-for="(item, index) in props.datas" :key="index">
      <div class="gird-title">
        <div class="gird-title-icon"></div>
        <div class="gird-title-text">{{ item.deviceName }}</div>
      </div>
      <div class="gird-main">
        <div class="gird-content">
          <div class="content-label">{{ item.address }}</div>
          <div class="content-date">{{ item.date }}</div>
        </div>
        <div class="gird-value">
          <div class="value-label">报警值</div>
          <div class="value-box">
            <div class="value-nums">{{ item.value }}</div>
            <div class="value-unit">M</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  datas: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
</script>
<style lang="scss" scoped>
.gird-box {
  display: grid;
  width: 100%;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 4px;
  overflow: hidden;
  .gird-item {
    width: 209px;
    height: 104px;
    background: url(@/assets/data-screen/module-6-gird-bg.png) no-repeat;
    background-size: 100% 100%;
    padding: 11px 16px;
    .gird-title {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 4px;
      .gird-title-icon {
        width: 32px;
        height: 32px;
        background: url(@/assets/data-screen/module-6-gird-icon.png) no-repeat;
        background-size: 100% 100%;
        margin-right: 11px;
      }
      .gird-title-text {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #40f9ff;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .gird-main {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .gird-content {
      display: flex;
      flex-direction: column;
      .content-label {
        height: 20px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #ffffff;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 6px;
      }
      .content-date {
        height: 20px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: rgba(199, 214, 255, 0.8);
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .gird-value {
      .value-label {
        height: 20px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .value-box {
        display: flex;
        align-items: baseline;
        justify-content: center;
        .value-nums {
          line-height: 24px;
          font-family: D-DIN-PRO, D-DIN-PRO;
          font-weight: bold;
          font-size: 24px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          background: linear-gradient(180deg, #ffffff 20%, #ff332c 80%);
          -webkit-background-clip: text;
          color: transparent;
        }
        .value-unit {
          margin-left: 2px;
          line-height: 20px;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: rgba(199, 214, 255, 0.8);
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
}
</style>
