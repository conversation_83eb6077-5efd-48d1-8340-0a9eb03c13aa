<template>
  <div class="data-screen-wrapper">
    <div class="header-wrapper z-index-9">
      <div class="header-left">
        <div class="weatcher-wrapper">
          <div class="weather-icon"></div>
          <div class="weather-info">
            <div class="weather-temp">{{weatherData.temperature}}</div>
            <div class="weather-detail">{{weatherData.weather}}</div>
          </div>
        </div>
        <div class="line"></div>
        <div class="date">{{ time }}</div>
      </div>
      <div class="header-center">楼宇能源管控平台</div>
      <div
        class="screen-building-floor hover"
        @click="fun_goPage('/screen-building-floor')"
      >
        楼层大屏
      </div>
      <div class="header-right" @click="handleGoIndex">
        <img class="icon-out" src="@/assets/data-screen/icon-out.png" />
        <div class="right-text">进入系统后台</div>
      </div>
    </div>
    <div class="posit-fixed left-0 top-0 right-0 bottom-0">
      <threejs
        ref="threejsRef"
        :config="{
          scaleHeight: 1080,
          axesHelperShow: false,
          camera: { x: 400, y: 240, z: 400, lookAt: [0, 100, 0], far: 100000 },
        }"
        @fun_loaded="fun_threejsOnload"
        @fun_clickModelBack="fun_clickModelBack"
      ></threejs>
    </div>
    <div class="left-wrapper">
      <module title="系统实时数据" :height="303">
        <module1 :datas="moduleData1"></module1>
      </module>
      <module title="近一年分项电耗" :height="256">
        <module2 :datas="moduleData2"></module2>
      </module>
      <module title="能耗分析" :height="353">
        <module3 :datas="moduleData3"> </module3>
      </module>
    </div>
    <div class="right-wrapper">
      <module title="客流统计" :height="265">
        <module4 :datas="moduleData4"></module4>
      </module>
      <module title="运维统计" :height="287">
        <module5 :datas="moduleData5"></module5>
      </module>
      <module title="系统实时数据" :height="360">
        <module6 :datas="moduleData6"></module6>
      </module>
    </div>
    <div class="bottom-wrapper">
      <select-box @select="handleSelect" @choose="handleChoose"></select-box>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";

import module from "./components/module.vue";
import selectBox from "./components/select.vue";
import module1 from "./components/module1/index.vue";
import module2 from "./components/module2/index.vue";
import module3 from "./components/module3/index.vue";
import module4 from "./components/module4/index.vue";
import module5 from "./components/module5/index.vue";
import module6 from "./components/module6/index.vue";
import dayjs from "dayjs";
import threejs from "@/components/threejsComm/threejsComm.vue";
import axios from "axios";
import {
  fun_addGeometryModelTest1,
  fun_animateTest1,
  fun_addModelTweenTest1,
  fun_addImageModel,
  fun_addTextModel,
  fun_addGltfModel,
  fun_addHdr,
  fun_fullScreen,
  fun_addGui,
} from "@/components/threejsComm/threejsComm";
import {
  getEquipmentInfoScreen,
  getSchedulingPersonnelList,
  getCount1,
  getCount2,
  getCount3,
  getDeviceAlarmList,
} from "@/api/screen-data/index";

const router = useRouter();
//系统实时数据
const moduleData1 = ref({});
//近一年分项电耗数据
const moduleData2 = ref([]);
//能耗分析数据
const moduleData3 = ref([]);
//客流统计数据
const moduleData4 = ref({});
//运维统计数据
const moduleData5 = ref({});
//系统实时数据
const moduleData6 = ref({});

//模拟系统实时数据
let mockData1 = {
  realTimePower: 2350, //实时功率
  realTimeLoad: 2200, //实时负荷
  coolerNums: 12, //冷机数量
  coolerTotal: 20, //冷机总数
  coolingTowerNums: 10, //冷却塔数量
  coolingTowerTotal: 20, //冷却塔总数
  chilledWaterPumpNums: 15, //冷冻水泵数量
  chilledWaterPumTotal: 20, //冷冻水泵总数
  coolingWaterPumpNums: 18, //冷却水泵数量
  coolingWaterPumTotal: 20, //冷却水泵总数
  secondaryPumpNums: 15, //二次泵数量
  secondaryPumTotal: 20, //二次泵总数
};
//模拟近一年分项电耗数据
let mockData2 = [
  {
    name: "制冷机房",
    value: 200,
    progress: 20,
  },
  {
    name: "热风幕",
    value: 200,
    progress: 20,
  },
  {
    name: "空调机组",
    value: 200,
    progress: 20,
  },
  {
    name: "照明",
    value: 200,
    progress: 20,
  },
  {
    name: "制热机组",
    value: 200,
    progress: 20,
  },
];
//模拟能耗分析数据
let mockData3 = {
  total: [
    {
      label: "耗电量",
      value: "584",
      unit: "度",
    },
    {
      label: "耗水量",
      value: "584",
      unit: "吨",
    },
    {
      label: "总能耗",
      value: "19.4",
      unit: "",
    },
  ],
  chartData: [
    {
      label: "1.1",
      value: 400,
      onYear: 3,
    },
    {
      label: "1.2",
      value: 300,
      onYear: 1,
    },
    {
      label: "1.3",
      value: 420,
      onYear: 0.2,
    },
    {
      label: "1.4",
      value: 520,
      onYear: 0.5,
    },
    {
      label: "1.5",
      value: 310,
      onYear: 0.7,
    },
    {
      label: "1.6",
      value: 480,
      onYear: 0.3,
    },
    {
      label: "1.7",
      value: 300,
      onYear: 0.8,
    },
  ],
};
//模拟客流统计数据
let mockData4 = {
  todayNumberOfPeople: 1234,
  currentNumberOfPeople: 1222,
  listData: [
    {
      label: "室外温度",
      value: 25,
      unit: "℃",
    },
    {
      label: "室内平均温度",
      value: "23.5",
      unit: "℃",
    },
    {
      label: "室外湿度",
      value: 64,
      unit: "RH%",
    },
    {
      label: "室内平均湿度",
      value: 33,
      unit: "RH%",
    },
  ],
};
//模拟运维统计数据
let mockData5 = ref({
  personData: [
    {
      name: "张三",
    },
    {
      name: "张三",
    },
    {
      name: "张三",
    },
  ],
  orderData: {
    orderCount: 2481,
    orderRate: 80,
  },
  patrolData: {
    patrolCount: 2481,
    patrolDoneCount: 2281,
    patrolUnDoneCount: 200,
    patrolRate: 95,
  },
});
//模拟系统实时数据
let mockData6 = {
  totalData: [
    {
      label: "告警总数",
      value: 1234,
    },
    {
      label: "待处理",
      value: 1234,
    },
    {
      label: "处理中",
      value: 1234,
    },
    {
      label: "已处理",
      value: 1234,
    },
  ],
  deviceData: [
    {
      deviceName: "1号设备",
      address: "3栋A座入口",
      date: "10/20 12:40",
      value: 30,
    },
    {
      deviceName: "1号设备",
      address: "3栋A座入口",
      date: "10/20 12:40",
      value: 30,
    },
    {
      deviceName: "1号设备",
      address: "3栋A座入口",
      date: "10/20 12:40",
      value: 30,
    },
    {
      deviceName: "1号设备",
      address: "3栋A座入口",
      date: "10/20 12:40",
      value: 30,
    },
  ],
};
//获取系统实时数据
const getData1 = async () => {
  let res = await getEquipmentInfoScreen();
  console.log(111, res);
  if (res.code == 0) {
    let datas = res.data;
    moduleData1.value = {
      realTimePower: 2350, //实时功率
      realTimeLoad: 2200, //实时负荷
      coolerNums: datas.find((item) => item.sblx == "冷水机组").open_count, //冷机数量
      coolerTotal: datas.find((item) => item.sblx == "冷水机组")
        .equipment_count, //冷机总数
      coolingTowerNums: datas.find((item) => item.sblx == "冷却塔").open_count, //冷却塔数量
      coolingTowerTotal: datas.find((item) => item.sblx == "冷却塔")
        .equipment_count, //冷却塔总数
      chilledWaterPumpNums: datas.find((item) => item.sblx == "冷冻水泵")
        .open_count, //冷冻水泵数量
      chilledWaterPumTotal: datas.find((item) => item.sblx == "冷冻水泵")
        .equipment_count, //冷冻水泵总数
      coolingWaterPumpNums: datas.find((item) => item.sblx == "冷却水泵")
        .open_count, //冷却水泵数量
      coolingWaterPumTotal: datas.find((item) => item.sblx == "冷却水泵")
        .equipment_count, //冷却水泵总数
      secondaryPumpNums: 15, //二次泵数量
      secondaryPumTotal: 20, //二次泵总数
    };
  }
  // let res = await new Promise((resolve) => {
  //   setTimeout(() => {
  //     let datas = {
  //       code: 200,
  //       data: mockData1,
  //     };
  //     resolve(datas);
  //   }, 200);
  // });
  // if (res.code === 200 && res.data) {
  //   moduleData1.value = res.data;
  // }
};

const getData2 = async () => {
  let res = await new Promise((resolve) => {
    setTimeout(() => {
      let datas = {
        code: 200,
        data: mockData2,
      };
      resolve(datas);
    }, 200);
  });
  if (res.code === 200 && res.data) {
    moduleData2.value = res.data;
  }
};
//获取能耗分析数据
const getData3 = async () => {
  let res = await new Promise((resolve) => {
    setTimeout(() => {
      let datas = {
        code: 200,
        data: mockData3,
      };
      resolve(datas);
    }, 200);
  });
  if (res.code === 200 && res.data) {
    moduleData3.value = res.data;
  }
};
//获取客流统计数据
const getData4 = async () => {
  let res = await new Promise((resolve) => {
    setTimeout(() => {
      let datas = {
        code: 200,
        data: mockData4,
      };
      resolve(datas);
    }, 200);
  });
  if (res.code === 200 && res.data) {
    moduleData4.value = res.data;
  }
};

//获取运维统计数据
const getData5 = async () => {
  //获取当前时间，并格式化年月日时分秒为YYYY-MM-DD格式
  let curDate = dayjs().format("YYYY-MM-DD");
  let params = {
    startTime: curDate + " 00:00",
    endTime: curDate + " 23:59",
  };
  //做一个promise.all
  let [res1, res2, res3] = await Promise.all([
    getSchedulingPersonnelList(params),
    getCount1(),
    getCount2(),
  ]);
  console.log("res1", res1);
  console.log("res2", res2);
  console.log("res3", res3);

  console.log(params, res1);
  if (res1.code == 0) {
    moduleData5.value.personData = res1.data.list.map((item) => {
      return {
        name: item.personName,
      };
    });
  }
  if (res2.code == 0) {
    moduleData5.value.orderData = {
      orderCount: res2.data.doneNum,
      orderRate: ((res2.data.doneNum / res2.data.total) * 100).toFixed(2),
    };
  }
  if (res3.code == 0) {
    moduleData5.value.patrolData = {
      patrolCount: res3.data.total,
      patrolDoneCount: res3.data.doneNum,
      patrolUnDoneCount: res3.data.total - res3.data.doneNum,
      patrolRate: ((res3.data.doneNum / res3.data.total) * 100).toFixed(2),
    };
  }
  console.log(moduleData5);
};
//获取系统实时数据
const getData6 = async () => {
  let curDate = dayjs().format("YYYY-MM-DD");
  let params = {
    // startTime: curDate + " 00:00",
    // endTime: curDate + " 23:59",
    pageSize: 4, //每页条数
    pageNum: 1, //当前页
    assembleIds: [1],
  };
  let [res1, res2] = await Promise.all([
    getCount3(),
    getDeviceAlarmList(params),
  ]);
  console.log(res1);
  if (res1.code == 0) {
    moduleData6.value.totalData = [
      {
        label: "告警总数",
        value: res1.data.total,
      },
      {
        label: "待处理",
        value: res1.data.total - res1.data.doneNum - res1.data.doingNum,
      },
      {
        label: "处理中",
        value: res1.data.doingNum,
      },
      {
        label: "已处理",
        value: res1.data.doneNum,
      },
    ];
  }
  if (res2.code == 0) {
    moduleData6.value.deviceData = res2.data.list.map((item) => {
      return {
        deviceName: item.sblxmc,
        address: item.jcxmc,
        date: dayjs(item.createTime).format("YYYY/MM/DD HH:mm"),
        value: item.dqbjz,
      };
    });
  }
};
let time = ref(dayjs().format("HH:mm:ss YYYY/MM/DD"));
const timer = ref(null);
const getTime = () => {
  timer.value = setInterval(() => {
    time.value = dayjs().format("HH:mm:ss YYYY/MM/DD");
  }, 1000);
};
const weatherData = ref({
  temperature:"",
  weather:""
});
const getWeather = async () => {
  // 基本参数配置
  const apiUrl = "/my-api/simpleWeather/query"; // 接口请求URL
  const apiKey = "6da0cad031f632db4615e4f5c99341f2"; // 在个人中心->我的数据,接口名称上方查看
  const city = "哈尔滨";
  // 接口请求入参配置
  const requestParams = {
    key: apiKey,
    city: city, //url encode处理
  };
  // 发起接口网络请求
  axios
    .get(apiUrl, { params: requestParams })
    .then((response) => {
      // 解析响应结果
      if (response.status === 200) {
        const responseResult = response.data;
        // 网络请求成功。可依据业务逻辑和接口文档说明自行处理。
        console.log(responseResult);
        if (responseResult.error_code == 0) {
          moduleData4.value.listData[0].value =
            responseResult?.result?.realtime?.temperature;
            weatherData.value.temperature = responseResult?.result?.future[0]?.temperature;
            weatherData.value.weather = responseResult?.result?.future[0]?.weather;
            console.log(weatherData.value,111);
        }
      } else {
        // 网络异常等因素，解析结果异常。可依据业务逻辑自行处理。
        console.log("请求异常");
      }
    })
    .catch((error) => {
      // 网络请求失败，可以根据实际情况进行处理
      console.log("网络请求失败:", error);
    });
};
onMounted(async () => {
  getTime();
  //获取数据
  await getData1();
  await getData2();
  await getData3();
  await getData4();
  await getData5();
  await getData6();
  await getWeather();
});
onUnmounted(() => {
  clearInterval(timer.value);
});

//栋楼选择事件处理函数和楼层选择事件处理函数
const handleSelect = (val) => {
  //栋楼选择事件处理函数
  console.log(val);
};
const handleChoose = (val) => {
  //楼层选择事件处理函数
  console.log(val);
};

//threejs
const threejsRef = ref();

// 监听threejs组件加载完成
let rendererThis = "",
  sceneThis = "",
  cameraThis = "";
function fun_threejsOnload(renderer, scene, camera) {
  // console.log('fun_threejsOnload:renderer,scene,camera',renderer,scene,camera);
  rendererThis = renderer;
  sceneThis = scene;
  cameraThis = camera;
  //添加几何体模型测试
  // const {cube,cylinder} = fun_addGeometryModelTest1({renderer,scene,camera});
  // fun_animateTest1({renderer,scene,camera},{cube,cylinder});
  // fun_addModelTweenTest1({renderer,scene,camera});

  //添加图片模型(平面模型)
  // fun_addImageModel({renderer,scene,camera},{path:'/static/threejsData/test1.png',position:{x:-25,y:0,z:0},lookAtCamera:true});
  //添加文字模型
  // fun_addTextModel({renderer,scene,camera},{text:'你好',position:{x:10,y:0,z:0},color:'#ff00ff',size:2,depth:0.1,weight:0.05,lookAtCamera:true});

  //添加Gltf模型-城市1
  // fun_addGltfModel({scene},{path:'/static/threejsData/empire_state_building.glb',position:{x:20,y:30,z:-50},scale:3});
  //添加Gltf模型-城市2
  // fun_addGltfModel({scene},{path:'/static/threejsData/empire_state_building_1k.glb',position:{x:-30,y:10,z:0},scale:5});

  //添加Gltf模型-简体几个楼
  // fun_addGltfModel({scene},{path:'/static/threejsData/modern_city_block.glb',position:{x:-80,y:0,z:-60},scale:0.005});
  //添加Gltf模型-楼群
  // fun_addGltfModel({scene},{path:'/static/threejsData/low_city.glb',position:{x:0,y:0,z:0},scale:1});

  //添加Gltf模型-楼群-哈尔滨灰模
  // fun_addGltfModel({scene},{path:'/static/threejsData/harbin.glb',position:{x:0,y:0,z:0},scale:1});
  //添加Gltf模型-楼群-哈尔滨灰模带地面
  fun_addGltfModel(
    { scene },
    {
      path: "/static/threejsData/harbinMap.glb",
      position: { x: -100, y: 0, z: -100 },
      scale: 1,
    }
  );

  //添加Hdr环境贴图
  fun_addHdr(
    { renderer, scene, camera },
    { path: "/static/threejsData/sky.hdr" }
  );

  //添加Gui自定义调试工具
  // fun_addGui({renderer,scene,camera},{cube,cylinder});
}

// 模型点击回调
function fun_clickModelBack({ modelThis, _isSelect }) {
  console.log("fun_clickModelBack:", modelThis, _isSelect);
  // router.push({path:'/screen-building-floor'});
}

// 全屏
function fun_fullScreenClick() {
  // fun_fullScreen({renderer:rendererThis});
}

//跳转页面
const fun_goPage = (path) => {
  router.push({ path: path });
};

const handleGoIndex = () => {
  //返回首页
  router.push({ path: "/index" });
};
const handleBack = () => {
  //返回按钮事件处理函数
  router.go(-1);
};
</script>

<style lang="scss" scoped>
.data-screen-wrapper {
  width: 1920px;
  height: 1080px;
  background: #061b3a;
  position: relative;
  .header-wrapper {
    width: 100%;
    height: 104px;
    position: relative;
    background: url(@/assets/data-screen/header-bg.png) no-repeat center;
    background-size: auto 100%;
    display: flex;
    padding: 16px 0 16px 24px;
    align-items: flex-start;
    justify-content: space-between;
    .header-left {
      display: flex;
      height: 40px;
      align-items: center;
      .weatcher-wrapper {
        width: 120px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .icon-weather {
          height: 40px;
          width: 40px;
        }
        .weather-info {
          .weather-temp {
            height: 20px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: bold;
            font-size: 16px;
            color: #ebf8ff;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }
          .weather-detail {
            height: 18px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #ebf8ff;
            text-align: ce;
            font-style: normal;
            text-transform: none;
          }
        }
      }
      .line {
        width: 0px;
        height: 32px;
        border-radius: 0px 0px 0px 0px;
        border: 2px solid;
        border-image: linear-gradient(
            150deg,
            rgba(255, 255, 255, 0),
            rgba(255, 255, 255, 0.5),
            rgba(255, 255, 255, 0)
          )
          2 2;
        margin: 0 16px;
      }
      .date {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 18px;
        color: #ebf8ff;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .header-center {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);

      font-size: 48px;
      color: #ffffff;
      letter-spacing: 7px;
      text-shadow: 2px 4px 0px rgba(14, 26, 66, 0.56);
    }
    .header-right {
      display: flex;
      align-items: center;
      justify-content: center;
      background: url(@/assets/data-screen/header-right-bg.png) no-repeat center;
      background-size: 100% 100%;
      width: 208px;
      height: 46px;
      cursor: pointer;
      .icon-out {
        width: 24px;
        margin-right: 8px;
        height: 24px;
      }
      .right-text {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 18px;
        color: rgba(199, 214, 255, 0.8);
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .left-wrapper {
    position: absolute;
    top: 124px;
    left: 24px;
    width: 456px;
    bottom: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .right-wrapper {
    position: absolute;
    top: 124px;
    right: 24px;
    width: 456px;
    bottom: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .bottom-wrapper {
    position: absolute;
    left: 492px;
    right: 492px;
    bottom: 24px;
    height: 48px;
  }
}

.screen-building-floor {
  position: absolute;
  right: 238px;
  font-size: 20px;
  top: 20px;
  color: rgba(199, 214, 255, 0.8);
  border: 1px solid #414148;
  border-radius: 5px;
  box-shadow: 1px 1px 7px #666;
  padding: 2px 10px;
}
</style>
