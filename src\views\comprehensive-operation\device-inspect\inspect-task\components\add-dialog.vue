<template>
  <el-dialog
    v-model="isVisible"
    width="860"
    :show-close="false"
    :close-on-click-modal="false"
    class="dialog"
    @open="fun_open"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form
          label-width="112"
          :inline="true"
          :model="formData"
          ref="ruleFormRef"
          :rules="rules"
          status-icon
        >
          <el-form-item label="任务名称:" prop="rwmc">
            <el-input v-model="formData.rwmc" placeholder="请输入" clearable />
            <!-- <el-select v-model="formData.qlGid" placeholder="请选择" clearable @change="fun_qlbmChange">
              <el-option v-for="(item, index) in qlOption" :key="index" :label="item.qlmc" :value="item.gid" />
            </el-select> -->
          </el-form-item>
          <el-form-item label="巡检类型:" prop="xjlxbm">
            <el-select
              v-model="formData.xjlxbm"
              placeholder="请选择"
              clearable
              @change="fun_xjlxChange"
            >
              <el-option
                v-for="(item, index) in taskTypeOption"
                :key="index"
                :label="item.businessLabel"
                :value="item.businessValue"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="巡检设备:" prop="xjsbbm">
            <el-select v-model="formData.xjsbbm" placeholder="请选择" clearable @change="fun_xjsbChange">
              <el-option v-for="(item, index) in xjsbOption" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item> -->
          <el-form-item label="巡检项:" prop="xjxbm">
            <el-select
              v-model="formData.xjxbm"
              placeholder="请选择"
              clearable
              @change="fun_xjxChange"
              value-key="gid"
            >
              <el-option
                v-for="(item, index) in xjxOption"
                :key="index"
                :label="item.itemName"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="任务时间:" prop="dateRange">
            <el-date-picker
              v-model="formData.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="fun_dateChange"
            />
          </el-form-item>
          <!-- <el-form-item label="巡检周期:" prop="xjzqUnit">
            <div class="width-all flex flex-both">
              <el-input-number v-model="formData.xjzqNum" :min="1" :max="99" :precision="1" :step-strictly="true" />
              <el-select style="width: 80px;" v-model="formData.xjzqUnit" placeholder="请选择" @change="fun_xjzqUnitChange">
                <el-option v-for="(item, index) in xjzqUnitOption" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </el-form-item> -->
          <el-form-item label="巡检时限:" prop="xjsxType">
            <el-select
              v-model="formData.xjsxType"
              placeholder="请选择"
              @change="fun_xjsxTypeChange"
            >
              <el-option
                v-for="(item, index) in xjsxTypeOption"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div
              class="width-all flex flex-both padd-t-5"
              v-if="formData.xjsxType == '3'"
            >
              <el-input-number
                v-model="formData.xjsxNum"
                :min="1"
                :max="99"
                :precision="1"
                :step-strictly="true"
              />
              天
              <!--- <el-select
                style="width: 80px"
                v-model="formData.xjsxUnit"
                placeholder="请选择"
                @change="fun_xjsxUnitChange"
              >
                <el-option
                  v-for="(item, index) in xjsxUnitOption"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>-->
            </div>
          </el-form-item>
          <el-form-item
            label="巡检人员:"
            :prop="formData.xjryType == 1 ? 'xjrybm' : 'xjxzbm'"
          >
            <el-radio-group v-model="formData.xjryType">
              <el-radio :value="1">选择人员</el-radio>
              <el-radio :value="2">选择小组</el-radio>
            </el-radio-group>
            <el-cascader
              v-if="formData.xjryType == 1"
              class="width-all"
              ref="xjryTreeRef"
              v-model="formData.xjrybm"
              :options="xjryTree"
              :props="xjryTreeProps"
              show-all-levels
              collapse-tags
              :collapse-tags-tooltip="true"
              clearable
              @change="fun_xjryChange"
              placeholder="请选择"
            />

            <el-select
              v-else
              class="width-all"
              v-model="formData.xjxzbm"
              clearable
              placeholder="请选择"
              @change="fun_groupChange"
              value-key="inspectorId"
            >
              <el-option
                v-for="(item, index) in xjxzTree"
                :key="index"
                :label="item.inspectionTeamName"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <!-- <div>
            <el-form-item label="跳过节假日:" prop="tgjjr">
              <el-switch width="50" v-model="formData.tgjjr" :active-value="1" :inactive-value="0" active-text="是" inactive-text="否" inline-prompt />
            </el-form-item>
          </div>
          <div>
            <el-form-item label="启用状态:" prop="sfqy">
              <el-switch width="50" v-model="formData.sfqy" :active-value="1" :inactive-value="0" active-text="启用" inactive-text="停用" inline-prompt />
            </el-form-item>
          </div> -->
          <!-- <el-form-item label="上传附件:" prop="fileAllList">
            <el-upload
              v-model:file-list="formData.fileAllList"
              class="upload-demo"
              :action="action"
              :headers="headers"
              :limit="1"
              :before-upload="beforeAvatarUpload"
              :on-success="handleAvatarSuccess"
            >
              <el-button type="primary" :disabled="formData.fileAllList?.length >= 1">点击上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  文件大小不超过5MB,支持扩展名:doc,docx,pdf
                </div>
              </template>
            </el-upload>
          </el-form-item> -->
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive, watch } from "vue";

import {
  // getDeviceTree,
  getPersonnelTree,
  getGroupOptions,
  add,
  update,
  getInspectionItem,
} from "@/api/comprehensive-operation/device-inspect/task";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage } from "element-plus";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log("打开fun_open:", props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
};

watch(
  () => props.data,
  (val) => {
    console.log("data:", val);
    if (val?.planId) {
      formData.value.rwmc = val?.inspectionPlanName;
      formData.value.xjlxbm = val?.taskType;
      formData.value.dateRange = [val?.startTime, val?.endTime];
      formData.value.startTime = val?.startTime;
      formData.value.endTime = val?.endTime;
      formData.value.xjxbm = {
        gid: val?.inspectionItemId,
        itemName: val?.inspectionItemName,
      };
      formData.value.xjsxType = Number(val?.limitType);
      formData.value.xjsxNum = Number(val?.limitDay);
      if (val?.inspectionPersonnelId) {
        formData.value.xjrybm = val?.inspectionPersonnelId;
        formData.value.xjryType = 1;
        formData.value.xjrybmStr = {
          id: val?.inspectionPersonnelId,
          label: val?.inspectionPersonnel,
        };
      }
      if (val?.groupId) {
        formData.value.xjryType = 2;
        formData.value.xjxzbm = {
          inspectorId: val?.groupId,
          inspectionTeamName: val?.groupName,
        };
      }
    } else {
      formData.value = {
        xjsxNum: 1,
      };
    }
  },
  {
    deep: true,
  }
);
// 表单数据-回显
const formData = ref({});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});

onMounted(async () => {
  fun_getDicts(); //获取字典
  fun_getxjsbOptions(); //获取巡检设备
  fun_getxjryTree(); //获取巡检人员
  fun_getxjxzTree(); //获取巡检小组
  fun_getInspectionItem(); //获取巡检项
});
const cycleTypeOption = ref([]);
const limitTypeOption = ref([]);
// 获取字典
const fun_getDicts = async () => {
  const { data: taskTypeOptionData } = await getDict("task_type"); //获取巡检类型
  taskTypeOption.value = taskTypeOptionData.list;
  console.log("巡检类型taskTypeOption:", taskTypeOption.value);
  const { data: cycleTypeOptionData } = await getDict("cycle_type"); //获取巡检周期
  cycleTypeOption.value = cycleTypeOptionData.list;
  console.log("巡检周期cycleTypeOption:", cycleTypeOption.value);
  const { data: limitTypeOptionData } = await getDict("limit_type"); //获取巡检时限
  limitTypeOption.value = limitTypeOptionData.list.filter(
    (item) => item.businessValue !== "2"
  );
  console.log("巡检时限limitTypeOption:", limitTypeOption.value);
};

// 巡检类型
const taskTypeOption = ref([]);
// 巡检类型选择
const fun_xjlxChange = (val) => {
  console.log("巡检类型选择:", val);
  //formData.value.bglxmc = taskTypeOption.value.find(item => item.businessValue == val).businessLabel;
};
//巡检周期单位
const xjzqUnitOption = ref([
  { label: "天", value: "1" },
  { label: "周", value: "2" },
  { label: "月", value: "3" },
  { label: "季", value: "4" },
  { label: "年", value: "5" },
]);
// 巡检周期单位选择
const fun_xjzqUnitChange = (val) => {
  console.log("巡检周期单位选择:", val);
  formData.value.bglxmc = xjzqUnitOption.value.find(
    (item) => item.businessValue == val
  ).businessLabel;
};

// 巡检时限类型
const xjsxTypeOption = ref([
  { label: "当天", value: 1 },
  // { label: "下次周期开始前", value: "2" },
  { label: "自定义", value: 3 },
]);
//巡检时限单位
const xjsxUnitOption = ref([
  { label: "天", value: "1" },
  { label: "周", value: "2" },
  { label: "月", value: "3" },
  { label: "季", value: "4" },
  { label: "年", value: "5" },
]);
// 巡检时限单位选择
const fun_xjsxUnitChange = (val) => {
  console.log("巡检时限单位选择:", val);
  formData.value.bglxmc = xjsxUnitOption.value.find(
    (item) => item.businessValue == val
  ).businessLabel;
};

// 巡检项
const xjxOption = ref([]);
// 巡检项选择
const fun_xjxChange = (val) => {
  console.log("巡检项选择:", val);
  // formData.value.bglxmc = xjxOption.value.find(item => item.businessValue == val).businessLabel;
};

// 计划时间
const fun_dateChange = (val) => {
  console.log("计划时间选择:", val);
  if (val) {
    formData.value.startTime = val[0] + " 00:00:00";
    formData.value.endTime = val[1] + " 23:59:59";
  } else {
    formData.value.startTime = "";
    formData.value.endTime = "";
  }
};

// 获取巡检设备
const xjsbOption = ref([]);
const fun_getxjsbOptions = async () => {
  // const {data} = await getQlOptions()
  // xjsbOption.value = data;
  console.log("巡检设备列表xjsbOption:", xjsbOption.value);
};
// 巡检设备选择
const fun_xjsbChange = (val) => {
  console.log("巡检设备选择:", val);
  formData.value.xjsbmc = xjsbOption.value.find(
    (item) => item.value == val
  ).label;
};

// 获取巡检人员
const xjryTreeProps = {
  label: "label",
  value: "id",
  children: "childrenList",
  multiple: false, //是否多选
  emitPath: false, //是否返回路径
};
const xjryTree = ref([
  {
    name: "巡检人员",
    gid: "0",
    children: [
      { name: "巡检人员1", gid: "1" },
      { name: "巡检人员2", gid: "2" },
      { name: "巡检人员3", gid: "3" },
      { name: "巡检人员4", gid: "4" },
    ],
  },
]);
const fun_getxjryTree = async () => {
  const { data } = await getPersonnelTree();
  // const {data} = await getQlOptions()
  xjryTree.value = data;
  console.log("巡检人员列表xjryTree:", xjryTree.value);
};
const fun_getInspectionItem = async () => {
  const { data } = await getInspectionItem({ pageNum: 1, pageSize: 999999 });
  xjxOption.value = data.list;
  console.log("巡检项列表xjxOption:", xjxOption.value);
};
// 巡检人员选择
const xjryTreeRef = ref();
const fun_xjryChange = (data) => {
  const checkedNodes = xjryTreeRef.value.getCheckedNodes(true); //获取选中的叶子节点
  console.log("巡检人员-节点点击:", data, checkedNodes);
  formData.value.xjrybmStr = checkedNodes[0].data;
  // formData.value.xjrybmStr = checkedNodes.map(item => item.gid).join(',');
  // formData.value.xjrymcStr = checkedNodes.map(item => item.name).join(',');
};

// 获取巡检小组
const xjxzTree = ref([
  {
    name: "巡检小组",
    gid: "0",
    children: [
      { name: "巡检小组1", gid: "1" },
      { name: "巡检小组2", gid: "2" },
      { name: "巡检小组3", gid: "3" },
      { name: "巡检小组4", gid: "4" },
    ],
  },
]);
const fun_getxjxzTree = async () => {
  const { data } = await getGroupOptions({ pageNum: 1, pageSize: 999999 });

  xjxzTree.value = data.list;
  console.log("巡检小组列表xjxzTree:", xjxzTree.value);
};
// 巡检小组选择
const xjxzTreeRef = ref();
const fun_xjxzChange = (data) => {
  const checkedNodes = xjxzTreeRef.value.getCheckedNodes(true); //获取选中的叶子节点
  console.log("巡检小组-节点点击:", data, checkedNodes);
  formData.value.xjxzbmStr = checkedNodes.map((item) => item.gid).join(",");
  formData.value.xjxzmcStr = checkedNodes.map((item) => item.name).join(",");
};

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  rwmc: [{ required: true, message: "请输入", trigger: "blur" }],
  xjlxbm: [{ required: true, message: "请选择", trigger: "blur" }],
  xjsbbm: [{ required: true, message: "请选择", trigger: "blur" }],
  xjrybm: [{ required: true, message: "请选择", trigger: "blur" }],
  xjxzbm: [{ required: true, message: "请选择", trigger: "blur" }],
  dateRange: [{ required: true, message: "请选择", trigger: "change" }],
  xjzqUnit: [{ required: true, message: "请选择", trigger: "blur" }],
  xjsxType: [{ required: true, message: "请选择", trigger: "blur" }],
  xjsxUnit: [{ required: true, message: "请选择", trigger: "blur" }],
  xjxbm: [{ required: true, message: "请选择", trigger: "blur" }],

  // fileAllList: [
  //   { required: true, message: "请上传附件", trigger: "blur" },
  // ],
});

// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增") {
        console.log("新增", formData.value);
        let params = {
          inspectionPlanName: formData.value.rwmc, //任务名称
          taskType: formData.value.xjlxbm, //任务类型
          startTime: formData.value.startTime, //开始时间
          endTime: formData.value.endTime, //结束时间
          limitType: formData.value.xjsxType, //巡检时限
          limitDay: formData.value.xjsxNum,
          inspectionItemId: formData.value.xjxbm.gid, //巡检项id
          inspectionItemName: formData.value.xjxbm.itemName, //巡检项名称
          inspectionPersonnelId: formData.value?.xjrybmStr?.id, //巡检人员id
          inspectionPersonnel: formData.value?.xjrybmStr?.label, //巡检人员名称
          groupName: formData.value?.xjxzbm?.inspectionTeamName, //巡检小组名称
          groupId: formData.value?.xjxzbm?.inspectorId, //巡检小组id
        };
        const res = await add(params);
        ElMessage({
          message: res.message,
          type: "success",
        });

        emits("refreshList");
        emits("onChangeVisible");
      } else {
        let params = {
          inspectionPlanName: formData.value.rwmc, //任务名称
          taskType: formData.value.xjlxbm, //任务类型
          startTime: formData.value.startTime, //开始时间
          endTime: formData.value.endTime, //结束时间
          limitType: formData.value.xjsxType, //巡检时限
          limitDay: formData.value.xjsxNum,
          inspectionItemId: formData.value.xjxbm.gid, //巡检项id
          inspectionItemName: formData.value.xjxbm.itemName, //巡检项名称
          inspectionPersonnelId: formData.value?.xjrybmStr?.id, //巡检人员id
          inspectionPersonnel: formData.value?.xjrybmStr?.label, //巡检人员名称
          groupName: formData.value?.xjxzbm?.inspectionTeamName, //巡检小组名称
          groupId: formData.value?.xjxzbm?.inspectorId, //巡检小组id
          planId: props.data?.planId, //巡检任务id
        };
        console.log(1111,props.data,formData.value)
        const res = await update(params);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      }
    }
  });
};

// 上传附件
/* const action = import.meta.env.VITE_APP_IMG_URL + "/api/platform/system/fileInfo/uploadFile";
const headers = { Authorization: "Bearer " + getToken() };
const beforeAvatarUpload = (file) => {
  const fileExtensions = ["doc", "docx", "pdf"];
  if (formData.value.fileAllList) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传doc,docx,pdf格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("文件大小不能超过5MB");
    return false;
  }
}; */
/**文件上传成功回调 */
// const handleAvatarSuccess = (response, file) => {
//   formData.value.fileList = formData.value.fileAllList.map((e) => {return {fileGid:e.response.data.gid ?? e.gid}});
//   // formData.value.fj = formData.value.fileAllList.map((e) => e.response.data.originalName ?? e.originalName).toString();
// };
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
