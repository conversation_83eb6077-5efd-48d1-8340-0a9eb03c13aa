<template>
  <el-dialog
    v-model="isVisible"
    width="600"
    :show-close="false"
    class="dialog"
    @open="open"
    :append-to-body="true"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="mapMain" id="map"></div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";
import { useGlobalConfig } from "@/utils/useGlobConfig";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  title: {
    type: String,
    default: "",
  },
});
const emits = defineEmits(["onChangeVisible"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
// 地图
const mapUrl = useGlobalConfig().mapUrl;
const HGZH2D = window["HGZH2D"];
let map = null;
const open = () => {
  if (!map) {
    const layerUrl = mapUrl;
    const layer = new HGZH2D.layer.Tile({
      source: new HGZH2D.source.XYZ({
        url: layerUrl,
        projection: "EPSG:4326",
      }),
    });
    map = new HGZH2D.Map({
      target: "map",
      layers: [layer],
      view: new HGZH2D.View({
        zoom: 13,
        projection: "EPSG:4326",
        center: [114.7915, 30.31875],
      }),
    });
  }
};
const cancelBtn = () => {
  emits("onChangeVisible");
};
</script>

<style lang="scss" scoped>
.dialog {
  height: auto;
  .mapMain {
    width: 100%;
    height: 300px;
    padding: 12px;
    box-sizing: border-box;
  }
}
</style>
