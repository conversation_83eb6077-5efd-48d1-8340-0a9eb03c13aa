<template>
  <div class="pointManage">
    <div class="formBox">
      <el-form :inline="true" :model="filter">
        <el-form-item label="安装时间:">
          <el-date-picker v-model="filter.azsj" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" clearable placeholder="请选择" />
        </el-form-item>
        <el-form-item label="厂家:">
          <el-input v-model="filter.cjmc" placeholder="请输入" clearable />
        </el-form-item>
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier">
      <div class="main-btns">
        <el-button type="primary" @click="addBtn" :icon="Plus">新增</el-button>
        <!-- <el-button type="danger" @click="delAllBtn" :icon="Delete">批量删除</el-button> -->
      </div>
      <div class="tableBox">
        <Table :data="tableData" :columns="columns" row-key="gid" @listSelectChange="handleSelectionChange">
          <template #yhsx="{ row }">
            <div>
              <span>{{row.yhsx}} 天/次</span>
            </div>
          </template>
          <template #operations="{ row }">
            <div class="buttons">
              <div class="btn" @click="handleLook(row)">查看</div>
              <div class="btn" @click="handleEdit(row)">编辑</div>
              <div class="btn" @click="handleDelete(row)">删除</div>
              <div class="btn" @click="handleEdit(row,'onlyEditDevice')">关联设备</div>
              <!-- <div class="btn" @click="handleDownload(row)">下载</div> -->
            </div>
          </template>
        </Table>
        <el-pagination class="pageBox" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增-编辑 -->
    <addDialog :dialogVisible="addVisiblty" :title="addTitle" :data="addData" @onChangeVisible="addVisiblty = false"
      @refreshList="fun_getList()"></addDialog>
    <!-- 查看 -->
    <lookDialog :dialogVisible="lookVisiblty" :data="lookData" @onChangeVisible="lookVisiblty = false"></lookDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
import addDialog from "./components/add-dialog.vue";
import lookDialog from "./components/look-dialog.vue";

import { getDict } from "@/api/common";
import {
  getDeviceConnectList,
  delDeviceConnect,
  // addDeviceConnect,
  // updateDeviceConnect,
} from "@/api/device-manage/index";

// 搜索条件
const filter = ref({
  cjmc: '',
  azsj: '',
  dateRange: '',
  startTime: '',
  endTime: '',
  pageSize: 10,
  pageNum: 1,
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    cjmc: "",
    azsj: "",
    dateRange: "",
    startTime: "",
    endTime: "",
    pageSize: 10,
    pageNum: 1,
  };
  fun_getList();
};

//监测项
// const xjxOption = ref([]);

// 获取字典
const fun_getDicts = async () => {
  const {data} = await getDict('BGLX')
  bglxOption.value = data.list;
  console.log('报告类型bglxOption:',bglxOption.value);
}

// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getList();
});

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns);
// 表格数据
const total = ref(0);
const tableData = ref([
  // {gid:1,azsj:'2024-09-25 10:10:10',cjmc:'厂家名称',yhsx:100,equipmentInfos:[{xh:1,sbmc:'设备名称'},{xh:2,sbmc:'设备名称'}]},
  // {gid:2,azsj:'2024-09-25 10:10:10',cjmc:'厂家名称',yhsx:100,equipmentInfos:[{xh:1,sbmc:'设备名称'},{xh:2,sbmc:'设备名称'}]},
]);
const fun_getList = async () => {
  const { data } = await getDeviceConnectList(filter.value);
  console.log('设备关联列表:',data);
  tableData.value = data.list;
  total.value = data.total;
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};


// 日期范围-切换
// const fun_dateChange = (val) => {
//   console.log('时间范围-时间选择:',val);
//   if (val) {
//     filter.value.startTime = val[0] + " 00:00:00";
//     filter.value.endTime = val[1] + " 23:59:59";
//   } else {
//     filter.value.startTime = "";
//     filter.value.endTime = "";
//   }
// };

// 新增
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref([]);
const addBtn = () => {
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row,type) => {
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "编辑";
  addVisiblty.value = true;
  if(type){
    addData.value.type = type;//关联设备
  }
};

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row) => {
  lookVisiblty.value = true;
  lookData.value = row;
  // lookData.value = {}
  // const res = await getReportInfo(row.gid)
  // lookData.value = res.data;
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("删除后不可恢复,确定吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await delDeviceConnect(row.gid);
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    fun_getList();
  })
};
// 批量删除
const delIds = ref("");
const handleSelectionChange = (data) => {
  delIds.value = data.map((item) => item.gid).join(",");
};
const delAllBtn = () => {
  if (delIds.value) {
    ElMessageBox.confirm("批量删除后不可恢复,确定吗?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        const res = await delDeviceConnect(delIds.value);
        if (res.code === 0) {
          fun_getList();
          ElMessage({
            type: "success",
            message: "删除成功",
          });
        } else {
          ElMessage({
            message: res.message,
            type: "warning",
          });
        }
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "取消删除",
        });
      });
  } else {
    ElMessage({
      message: "请选择需要删除的选项",
      type: "warning",
    });
  }
};

// 附件下载
// import { preImageUrl } from '@/utils/index'
// const handleDownload = (row) => {
//   console.log('附件下载:',preImageUrl,row);
//   if (row.fileList && row.fileList.length > 0) {
//     window.open( import.meta.env.VITE_APP_IMG_URL+preImageUrl+row.fileList[0].fileGid);
//   } else {
//     ElMessage({
//       message: "没有附件",
//       type: "warning",
//     });
//   }
// }


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
