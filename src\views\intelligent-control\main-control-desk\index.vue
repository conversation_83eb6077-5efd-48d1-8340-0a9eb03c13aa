<template>
  <div class="pointManage">
    <div class="max-height-all font-16 grid grid-temp-col-12 grid-gap-10 over-auto-y">
      <div class="grid-span-4 bg-color-fff border-radius-5 padd-20" style="height:1.80rem;" v-for="(item,key) of dataList" :key="key">
        <div>
          <div class="flex flex-both border-b-ddd">
            <div>{{ item.sbmc }}</div>
            <div class="padd-b-5">
              <el-button type="primary" @click="submitBtn(ruleFormRef,item.attributeList,key)">保存</el-button>
            </div>
          </div>
          <!-- <ul class="padd-20_0 grid grid-temp-col-12">
            <li class="grid-span-4 padd-5_10">总台数：{{ item.totalNum }}</li>
            <li class="grid-span-4 padd-5_10">开启台数：{{ item.openNum }}</li>
            <li class="grid-span-4 padd-5_10">故障台数：{{ item.troubleNum }}</li>
            <li class="grid-span-4 padd-5_10">远程控制台数：{{ item.remoteControlNum }}</li>
            <li class="grid-span-4 padd-5_10">本地控制台数：{{ item.localControlNum }}</li>
          </ul> -->
          <div class="padd-t-20">
            <el-form label-width="160" :inline="true" :model="item" ref="ruleFormRef" :rules="rules">
              <el-form-item :label="attr.attributeDesc" v-for="(attr,index) of item.attributeList" :key="index" style="width:48%;margin:0 1%;" class="padd-b-20">
                <div class="width-all flex" v-if="attr.attributeMark.substring(0,1)=='B'">
                  <el-select v-model="attr.attributeValue" placeholder="请选择">
                    <el-option v-for="(status, index2) in triggerStatusOption" :key="index2" :label="status.label" :value="status.value" />
                  </el-select>
                </div>
                <div class="width-all flex" v-else>
                  <el-input v-model.number="attr.attributeValue" type="text" placeholder="请输入" />
                  <span class="flex-item-shrink-0 padd-l-5" v-if="attr.attributeUnit">{{ attr.attributeUnit }}</span>
                </div>
              </el-form-item>
              <!-- <el-form-item label="启停设定:" style="width:40%;" class="padd-b-20">
                <el-select v-model="item.triggerStatus" placeholder="请选择">
                  <el-option v-for="(attr, index) in triggerStatusOption" :key="index" :label="attr.label" :value="attr.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="回风温度设定:" prop="backWindTemperature" style="width:40%;" class="padd-b-20" v-if="item.type=='2'">
                <div class="width-all flex flex-both">
                  <el-input type="text" v-model.number="item.backWindTemperature" placeholder="请输入" clearable />
                  <span>℃</span>
                </div>
              </el-form-item> -->
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";


import { getDict } from "@/api/common";
import {
  getDeviceConsoleList,
  updateDeviceAttribute,
} from "@/api/intelligent-control/index";


//启停状态
const triggerStatusOption = ref([
  { label: "启动", value: '1' },
  { label: "关闭", value: '0' },
]);

//季节模式
const seasonModeOption = ref([
  { label: "冬季", value: '1' },
  { label: "夏季", value: '2' },
  { label: "过渡季", value: '3' },
]);

// 获取字典
const fun_getDicts = async () => {
  // const {data:cycleTypeOptionData} = await getDict('cycle_type');//获取巡检周期
  // cycleTypeOption.value = cycleTypeOptionData.list;
  // console.log('巡检周期cycleTypeOption:',cycleTypeOption.value);
}


// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getList();
});

const dataList = ref([
  // {
  //   name:'新风机组',type:'1',
  //   totalNum:100,openNum:10,troubleNum:10,remoteControlNum:20,localControlNum:8,
  //   triggerStatus:1,seasonMode:'1',
  // },
  // {
  //   name:'组合式空调机组',type:'2',
  //   totalNum:100,openNum:10,troubleNum:10,remoteControlNum:20,localControlNum:8,
  //   triggerStatus:1,seasonMode:'1',
  // },
]);
const fun_getList = async () => {
  const { data } = await getDeviceConsoleList({pageNum:1,pageSize:1000});
  console.log('设备控制台列表:',data);
  dataList.value = data?.list;
};


// 启用停用
const handleState = (row) => {
  ElMessageBox.confirm(`确定${row.triggerStatus==1?'停用':'启用'}吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await update({id:row.id,triggerStatus:row.triggerStatus==1?0:1});
    ElMessage({
      type: "success",
      message: `${row.triggerStatus==1?'停用':'启用'}成功`,
    });
    row.triggerStatus=row.triggerStatus==1?0:1;//启用停用
    fun_getList();
  })
};

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  // goWindTemperature: [
  //   { required: false, message: "请输入", trigger: ["blur","change"] },
  //   { type: 'number', message: '请输入数字类型' },
  // ],
  // backWindTemperature: [
  //   { required: false, message: "请输入", trigger: ["blur","change"] },
  //   { type: 'number', message: '请输入数字类型' },
  // ],
  // co2Density: [
  //   { required: false, message: "请输入", trigger: ["blur","change"] },
  //   { type: 'number', message: '请输入数字类型' },
  // ],
});

// 提交
const submitBtn = async (formEls,attrInfoList,index) => {
  if (!formEls) return;
  await formEls[index].validate(async (valid, fields) => {
    if (valid) {
      ElMessageBox.confirm("确定保存吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const res = await updateDeviceAttribute({list:attrInfoList});
        ElMessage({
          message: res.message,
          type: "success",
        });
        fun_getList();
      })
    }else{
      console.log('表单验证未通过');
    }
  });
};


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
  :deep(.el-form-item__label){
    // color: #fff;
    font-size: 14px;
    line-height: 1;
    align-items: center;
  }
}
</style>
