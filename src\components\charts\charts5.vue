<template>
  <div ref="refChart" class="chart-container"></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, watch, markRaw } from "vue";
import * as echarts from "echarts";
import { getTransferPx } from "@/utils/px2rem";

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

const refChart = ref<any>();
const chartInstance = ref();
const chartsData: any = ref({});

onMounted(() => {
  watch(
    () => props.data,
    (val) => {
      if (val) {
        chartsData.value = val;
        initChart();
      }
    },
    {
      deep: true,
      immediate: true,
    }
  );
});

onBeforeUnmount(() => {
  // 销毁图表
  chartInstance.value.dispose();
  chartInstance.value = null;
});

const initChart = () => {
  chartInstance.value = markRaw(echarts.init(refChart.value));

  const initOption = {
    title: {
      text: chartsData.value.unit,
      top: 0,
      left: 16,
      textStyle: {
        fontSize: getTransferPx(15),
        color: "#858B99",
      },
      subtextStyle: {
        fontSize: getTransferPx(16),
      },
    },
    xAxis: {
      type: "category",
      axisTick: {
        show: false,
      },

      data: chartsData.value.category,
      axisLine: {
        show: true,
        lineStyle: {
          color: "#e5e5e5",
        },
      },
      axisLabel: {
        fontSize: 15,
        color: "#858B99",
      },
    },
    grid: {
      top: getTransferPx(30),
      left: getTransferPx(70),
      right: getTransferPx(50),
      bottom: getTransferPx(30),
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
      },
    },
    yAxis: {
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: getTransferPx(15),
        color: "#858B99",
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "rgba(0,0,0,0.08)",
        },
      },
    },
    legend: {
      top: getTransferPx(80),
      right: getTransferPx(20),
    },
    series: [
      {
        type: "line",
        data: chartsData.value.value,
        symbolSize: 10,
        symbol: "none",
        smooth: true,
        itemStyle: {
          normal: {
            color: "#50a7ff",
          },
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(51,153,255,0.4)", // 阴影的上部颜色
              },
              {
                offset: 0.8,
                color: "rgba(51,153,255,0)", // 阴影的下部颜色
              },
            ]),
          },
        },
      },
    ],
  };

  // 图表初始化配置
  chartInstance.value.setOption(initOption);

  setTimeout(function () {
    window.addEventListener("resize", () => {
      chartInstance.value?.resize();
    });
  }, 200);
};
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
