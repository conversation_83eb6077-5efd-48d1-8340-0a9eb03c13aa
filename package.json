{"name": "ruoyi", "version": "3.6.3", "description": "基础平台系统", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Cloud.git"}, "dependencies": {"@dvgis/dc-sdk": "^4.2.0", "@dvgis/vite-plugin-dc": "^4.0.1", "@element-plus/icons-vue": "2.3.1", "@hgzh/hgzh2d-sdk": "^1.0.0", "@hgzh/hgzh2d-sdk-types": "^1.0.2", "@tresjs/cientos": "^4.3.1", "@tresjs/core": "^4.3.6", "@turf/turf": "^7.2.0", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue-flow/background": "^1.3.2", "@vue-flow/core": "^1.45.0", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "^10.6.1", "amfe-flexible": "^2.2.1", "axios": "0.27.2", "axios-mapper": "^0.5.9", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "echarts": "5.4.3", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.8.0", "esno": "^4.7.0", "file-saver": "2.0.5", "fs-extra": "^11.2.0", "fuse.js": "6.6.2", "gojs": "^3.0.22", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "lodash": "^4.17.21", "mammoth": "^1.8.0", "mitt": "^3.0.1", "mockjs": "^1.1.0", "moment": "^2.30.1", "nprogress": "0.2.0", "pdfjs-dist": "^2.16.105", "pinia": "2.1.7", "postcss-pxtorem": "^6.1.0", "proj4": "^2.15.0", "qs": "^6.14.0", "ruoyi": "file:", "three": "^0.177.0", "vite-plugin-html": "^3.2.2", "vue": "^3.4.37", "vue-countup-v3": "^1.4.2", "vue-cropper": "1.1.1", "vue-pdf-embed": "^2.1.2", "vue-router": "4.2.5", "vue3-pdfjs": "^0.1.6", "vue3-seamless-scroll": "^3.0.2"}, "devDependencies": {"@hgzh/vite-plugin-hgzh2d": "^1.0.0", "@types/js-cookie": "^3.0.6", "@types/three": "^0.177.0", "@vitejs/plugin-vue": "4.5.0", "@vue/compiler-sfc": "3.3.9", "sass": "1.69.5", "typescript": "^5.5.4", "unplugin-auto-import": "0.17.1", "unplugin-vue-setup-extend-plus": "1.0.0", "vite": "5.0.4", "vite-plugin-compression": "0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svg-icons": "2.0.1"}}