<template>
  <div class="pointManage">
    <div class="formBox">
      <el-form :inline="true" :model="filter">
        <el-form-item label="场景名称:">
          <el-input v-model="filter.cjmc" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="策略名称:">
          <el-input v-model="filter.clmc" placeholder="请输入" clearable />
        </el-form-item>
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier">
      <div class="raido-btns">
        <el-radio-group v-model="radioValue" @change="resetBtn" size="large">
          <el-radio-button label="群控" value="1" />
          <el-radio-button label="AI" value="2" />
        </el-radio-group>
      </div>
      <div class="main-btns">
        <el-button type="primary" @click="addBtn" :icon="Plus">新增</el-button>
      </div>
      <div class="tableBox">
        <Table :data="tableData" :columns="columns" row-key="gid" @listSelectChange="handleSelectionChange">
          <template #cjId="{ row }">
            <span>{{ row.cjId&&sceneList.find(item => item.gid === row.cjId)?.cjmc }}</span>
          </template>
          <template #status="{ row }">
            <div>
              <el-switch width="50" v-model="row.status" active-value="1" inactive-value="0" active-text="开启" inactive-text="关闭" inline-prompt :before-change="()=>{return false;}" @click="handleState(row)"  />
            </div>
          </template>
          <template #operations="{ row }">
            <div class="buttons">
              <div class="btn" @click="handleLook(row)">查看</div>
              <div class="btn" @click="handleEdit(row)">编辑</div>
              <!-- <div class="btn" @click="handleDelete(row)">删除</div> -->
            </div>
          </template>
        </Table>
        <el-pagination class="pageBox" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增-编辑-查看 -->
    <addDialog :dialogVisible="addVisiblty" :title="addTitle" :data="addData" :sceneList="sceneList" @onChangeVisible="addVisiblty = false"
      @refreshList="fun_getList()" :isLook="isLook"></addDialog>
    <!-- 查看 -->
    <!-- <lookDialog :dialogVisible="lookVisiblty" :data="lookData" @onChangeVisible="lookVisiblty = false"></lookDialog> -->
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
import addDialog from "./components/add-dialog.vue";
import lookDialog from "./components/look-dialog.vue";

import { getDict } from "@/api/common";
import {
  getStrategyList,
  getSceneList,
  editStrategyStatus,
} from "@/api/energy-manage/index";

// 搜索条件
const filter = ref({
  cjmc: '',
  clmc: '',
  pageSize: 10,
  pageNum: 1,
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    cjmc: "",
    clmc: "",
    pageSize: 10,
    pageNum: 1,
  };
  fun_getList();
};

const radioValue = ref('1')

// 获取字典
const fun_getDicts = async () => {
  
}

// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getSceneList();//获取场景下拉列表
  fun_getList();
});

// 场景名称回显列表
const sceneList = ref([]);
const fun_getSceneList = async () => {
  const { data } = await getSceneList();
  sceneList.value = data.list;
}

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns);
// 表格数据
const total = ref(0);
const tableData = ref([
  // {
  //   id: 1,
  //   clmc: "平日策略",
  //   sceneId: 1,
  //   cjmc: "平日模式",
  //   status: 1,
  //   createTime: "2025-01-01 12:00:00",
  //   updateTime: "2025-01-01 12:00:00",
  //   todayRecommendIndex: 5,
  //   energyConsumption: 100,
  //   supplyAirTemp: 25,
  //   returnAirTemp: 20,
  //   co2Concentration: 400,
  //   deviceData:[
  //     {
  //       id:11,
  //       deviceType:'冷机',
  //       timeList: [{
  //         startTime: '08:00:00',
  //         endTime: '18:00:00'
  //       }],
  //       firstNum: 2,
  //       firstLoadTime: 10,
  //       minNum: 1,
  //       minUnloadTime: 10,
  //       maxNum: 3,
  //       minTemp: 15,
  //       maxTemp: [{
  //         timeRange: ['08:00:00','18:00:00'],
  //         temp: 25
  //       }]
  //     }
  //   ]
  // },
  // {
  //   id: 2,
  //   clmc: "周末策略",
  //   sceneId: 2,
  //   cjmc: "周末模式",
  //   status: 1,
  //   createTime: "2025-01-01 12:00:00",
  //   updateTime: "2025-01-01 12:00:00",
  //   todayRecommendIndex: 4,
  //   energyConsumption: 100,
  //   supplyAirTemp: 25,
  //   returnAirTemp: 20,
  //   co2Concentration: 400,
  // },
  // {
  //   id: 3,
  //   clmc: "节假日策略",
  //   sceneId: 3,
  //   cjmc: "节假日模式",
  //   status: 0,
  //   createTime: "2025-01-01 12:00:00",
  //   updateTime: "2025-01-01 12:00:00",
  //   todayRecommendIndex: 3,
  //   energyConsumption: 100,
  //   supplyAirTemp: 25,
  //   returnAirTemp: 20,
  //   co2Concentration: 400,
  // }
]);
const fun_getList = async () => {
  if(radioValue.value=='1'){
    // 群控
    const { data } = await getStrategyList(filter.value);
    tableData.value = data.list;
    total.value = data.total;
  }else{
    // AI
    tableData.value = [];
    total.value = 0;
  }
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};

// 新增
const isLook = ref(false);//是否查看
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref([]);
const addBtn = () => {
  isLook.value = false;
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row) => {
  isLook.value = false;
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "编辑";
  addVisiblty.value = true;
};

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row) => {
  isLook.value = true;
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "详情";
  addVisiblty.value = true;
  // lookVisiblty.value = true;
  // lookData.value = row;
  // lookData.value = {}
  // const res = await getReportInfo(row.gid)
  // lookData.value = res.data;
};
// 启用停用
const handleState =async (row) => {
  if(row.status===undefined)return false;
  ElMessageBox.confirm(`确定${row.status==1?'关闭':'开启'}吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await editStrategyStatus({gid:row.gid,status:row.status==1?0:1});
    ElMessage({
      type: "success",
      message: `${row.status==1?'关闭':'开启'}成功`,
    });
    row.status=row.status==1?0:1;//启用停用
    fun_getList();
  })
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("删除后不可恢复,确定吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    // const res = await delReport(row.gid);
    // ElMessage({
    //   type: "success",
    //   message: "删除成功",
    // });
    // fun_getList();
  })
};
// 批量删除
const delIds = ref("");
const handleSelectionChange = (data) => {
  delIds.value = data.map((item) => item.gid).join(",");
};


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
.raido-btns {
  margin-bottom: 16px;
  ::v-deep(.el-radio-button__inner) {
    padding: 10px 36px;
  }
}
.main-contanier .tableBox {
  height: calc(100% - 165px);
}
</style>
