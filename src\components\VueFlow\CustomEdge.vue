<template>
  <svg class="vue-flow__edge vue-flow__edge-custom">
    <!-- 透明较宽背景 -->
    <path
      :d="edgePath"
      class="path-bg"
      :class="{ selected:selected&&isEdit }"
    />
    <!-- 动画线条 -->
    <path
      :d="edgePath"
      class="path-line"
      :class="{ selected:selected&&isEdit }"
      :marker-end="markerEnd"
    />
  </svg>
</template>

<script setup>
import { computed } from 'vue';
import { getBezierPath, getStraightPath,getSmoothStepPath } from '@vue-flow/core';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  sourceX: {
    type: Number,
    required: true,
  },
  sourceY: {
    type: Number,
    required: true,
  },
  targetX: {
    type: Number,
    required: true,
  },
  targetY: {
    type: Number,
    required: true,
  },
  sourcePosition: {
    type: String,
    required: true,
  },
  targetPosition: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: false,
  },
  markerEnd: {
    type: String,
    required: false,
  },
  style: {
    type: Object,
    required: false,
  },
  sourceNode: {
    type: Object,
    required: false,
  },
  targetNode: {
    type: Object,
    required: false,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: true,
  }
});

// 获取基础颜色
function getBaseColor() {
  // 如果有自定义颜色，优先使用自定义颜色
  if (props.data && props.data.customColor) {
    return props.data.customColor;
  }

  // 默认颜色
  return '#00f';//蓝色
}

// 计算曲线路径
const edgePath = computed(() => {
  return getSmoothStepPath({
    sourceX: props.sourceX,
    sourceY: props.sourceY,
    sourcePosition: props.sourcePosition,
    targetX: props.targetX,
    targetY: props.targetY,
    targetPosition: props.targetPosition,
  })[0];
});
</script>

<style scoped>
.path-bg {
  stroke: rgba(191, 229, 247, 0.5);
  stroke-width: 10px;
  fill: none;
  pointer-events: stroke;
}

.path-line {
  stroke: v-bind('getBaseColor()');
  stroke-width: 2px;
  stroke-dasharray: 5 3;
  fill: none;
  animation: flow 30s linear infinite;
  pointer-events: none;
}

.path-bg.selected {
  stroke: rgba(255, 200, 200, 0.5);
  stroke-width: 12px;
}

.path-line.selected {
  stroke-width: 3px;
}

@keyframes flow {
  from {
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dashoffset: 0;
  }
}
</style> 