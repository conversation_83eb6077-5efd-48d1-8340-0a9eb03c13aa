<template>
  <el-dialog v-model="isVisible" width="1140" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>历史报警</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main over-auto-y" style="height: 60vh;">
      <div class="height-all flex flex-col">
        <div class="flex-item-shrink-0 flex flex-both">
            <el-form :inline="true" :model="filter">
              <el-form-item label="时间范围:">
                <el-date-picker v-model="filter.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="fun_dateChange" />
              </el-form-item>
              <el-form-item label="报警状态:">
                <el-select v-model="filter.bjztbm" placeholder="请选择" @change="fun_bjztChange">
                  <el-option v-for="item in bjztOption" :key="item.businessValue" :label="item.businessLabel" :value="item.businessValue" />
                </el-select>
              </el-form-item>
            </el-form>

            <div class="btns flex-item-shrink-0">
              <el-button type="primary" @click="searchBtn">查询</el-button>
              <el-button @click="resetBtn">重置</el-button>
            </div>
        </div>
        <div class="marg-10_0" style="height: 2px;background-color: #eee;width: 100%;"></div>

        <div class="height-all tableBox flex flex-col">
          <Table :data="tableData" :columns="columns" row-key="id">
            <template #bjztbm="{ row }">
              {{bjztOption.length>0?bjztOption.find(item => item.businessValue == row.bjztbm).businessLabel:'-'}}
            </template>
            <template #operations="{ row }">
              <div class="buttons flex flex-around">
                <div class="color-blue hover" @click="handleLook(row)">处置跟踪</div>
              </div>
            </template>
          </Table>
          <el-pagination class="pageBox padd-t-10 flex-right border-t-ddd" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
            :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
            @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </div>
      <!-- 查看 -->
      <lookDialog :dialogVisible="lookVisiblty" :data="lookData" @onChangeVisible="lookVisiblty = false" append-to-body></lookDialog>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

import Table from "@/components/Table/index.vue";
import { Columns } from "../table";
import lookDialog from "./look-dialog.vue";

import { getDict } from "@/api/common";
// import {
//   listReport,
// } from "@/api/comprehensive-operation/device-inspect/index";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {return {}},
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};
// 查询条件
const filter = ref({
  dateRange: [],
  startTime: '',
  endTime: '',
  pageNum: 1,
  pageSize: 10,
});
// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    dateRange: [],
    startTime: '',
    endTime: '',
    pageNum: 1,
    pageSize: 10,
  };
  fun_getList();
};

// 获取字典
const fun_getDicts = async () => {
  const {data} = await getDict('BJZT');//获取报警状态
  bjztOption.value = data.list;
  console.log('报警状态bjztOption:',bjztOption.value);
}

// 获取报警状态
const bjztOption = ref([
  // {businessLabel:'待研判',businessValue:'1'},
  // {businessLabel:'待核查',businessValue:'2'},
  // {businessLabel:'待处置',businessValue:'3'},
  // {businessLabel:'待办结',businessValue:'4'},
  // {businessLabel:'已办结',businessValue:'5'},
  // {businessLabel:'系统误报',businessValue:'6'},
]);
// 报警状态选择
const fun_bjztChange = (val) => {
  console.log('报警状态选择:',val);
  filter.value.bjztmc = bjztOption.value.find(item => item.businessValue == val).businessLabel;
}

// 日期范围-切换
const fun_dateChange = (val) => {
  console.log('时间范围-时间选择:',val);
  if (val) {
    filter.value.startTime = val[0] + " 00:00:00";
    filter.value.endTime = val[1] + " 23:59:59";
  } else {
    filter.value.startTime = "";
    filter.value.endTime = "";
  }
};

// 监听
watch(isVisible, (val) => {
  if(val){
    fun_getList();//获取列表
  }
});

// 挂载
onMounted(async () => {
  fun_getDicts();//获取字典
  // fun_getList();//获取列表
});

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns3_1);

// 表格数据
const total = ref(0);
const tableData = ref([
  {id:1,jcxbm:'监测项代码',jcxmc:'监测项',bjz:'报警值',bjsj:'2025-05-14 10:00:00',bjztbm:'2'},
  {id:2,jcxbm:'监测项代码',jcxmc:'监测项',bjz:'报警值',bjsj:'2025-05-14 10:00:00',bjztbm:'3'},
]);
const fun_getList = async () => {
  // const { data } = await listReport({...filter.value,gid:props.data.gid});
  // tableData.value = data.list;
  // total.value = data.total;
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};


// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row,type) => {
  lookVisiblty.value = true;
  lookData.value = row;
};

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
