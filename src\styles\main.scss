:root {
  --el-menu-bg-color: rgba(0, 0, 0, 0);
  // --el-menu-text-color: rgba(255, 255, 255, 0.75);
  --el-menu-text-color: rgba(0, 0, 0, 0.75);
  // --el-menu-active-color: #fff;
  --el-menu-active-color: #000000;
  --el-menu-border-color: rgba(0, 0, 0, 0);
  --el-menu-hover-text-color: #fff;
  --el-menu-item-font-size: 16px;
}

.topmenu-container.el-menu--horizontal > .is-active {
  // font-weight: bold;
  // background: linear-gradient(
  //   180deg,
  //   rgba(232, 247, 255, 0) 0%,
  //   rgba(232, 247, 255, 0.45) 100%
  // );
  // border-bottom: 4px solid #ebf5ff !important;
  color: #3275F9!important;
}
.topmenu-container.el-menu--horizontal > .is-active .svg-icon {
  fill: #fff !important;
}
.sidebar-container .is-active {
  background: linear-gradient(
    180deg,
    rgba(235, 245, 255, 1) 0%,
    rgba(235, 245, 255, 1) 100%
  );
}

.sidebar-container .el-sub-menu .el-menu-item.is-active {
  // border-right: 6px solid #3399ff;
  // background: linear-gradient(
  //   180deg,
  //   rgba(235, 245, 255, 1) 0%,
  //   rgba(235, 245, 255, 1) 100%
  // );
  // background: #3275F9;
  background: linear-gradient(
    180deg,
    #3275F9 0%,
    #3275F9 100%
  );
  border-radius: 10px;

  .menu-title {
    color: #fff !important;
  }
}
.sidebar-container .el-menu-item.is-active {
  color: #fff !important;
  // border-right: 6px solid #3399ff;
  background: #3275F9;
  border-radius: 10px;

}
.el-sub-menu.is-active .el-sub-menu__title .menu-title {
  color: #fff !important;
}

.el-sub-menu.is-active .el-sub-menu__title .svg-icon {
  fill: #3399ff !important;
}

.sidebar-container .submenu-title-noDropdown {
  font-size: 14px;
}

#app .sidebar-container .el-menu {
  --el-menu-hover-bg-color: #ff0000 !important;
}
.el-popper {
  z-index: 999999999999 !important;
}
