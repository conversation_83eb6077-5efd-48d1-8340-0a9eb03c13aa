<template>
  <div class="grid-container">
    <gird1 :datas="props.datas"></gird1>
    <gird2 :datas="props.datas"></gird2>
    <gird3 :datas="props.datas"></gird3>
  </div>
</template>
<script setup>
import gird1 from "./gird1.vue";
import gird2 from "./gird2.vue";
import gird3 from "./gird3.vue";
const props = defineProps({
  datas: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
</script>
