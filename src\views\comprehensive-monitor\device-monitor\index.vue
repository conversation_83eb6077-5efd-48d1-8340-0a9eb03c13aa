<template>
  <div class="pointManage">
    <div class="formBox" style="height: auto;">
      <el-form :inline="true" :model="filter">
        <el-form-item label="传感器名称:">
          <el-input v-model="filter.cgqmc" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="所属设备:">
          <el-select v-model="filter.sssbbm" placeholder="请选择" clearable @change="fun_sssbChange">
            <el-option v-for="(item, index) in sssbOption" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="传感器类型:">
          <el-cascader ref="cgqTypeTreeRef" v-model="filter.cgqType" :options="cgqTypeTree" :props="cgqTypeTreeProps" :show-all-levels="false" collapse-tags :collapse-tags-tooltip="true" clearable @change="fun_cgqTypeChange" placeholder="请选择" />
        </el-form-item>
        <!-- <el-form-item label="时间范围:">
          <el-date-picker v-model="filter.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="fun_dateChange" />
        </el-form-item> -->
      </el-form>

      <div class="btns flex-item-shrink-0">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier flex flex-col">
      <div class="main-btns flex-item-shrink-0">
        <!-- <el-button type="primary" @click="addBtn" :icon="Plus">新增</el-button>
        <el-button type="primary" @click="handleEditAll" :icon="Finished">批量报警处置</el-button>
        <el-button type="danger" @click="delAllBtn" :icon="Delete">批量误报忽略</el-button> -->
      </div>
      <div class="tableBox height-all-impt flex flex-col">
        <Table :data="tableData" :columns="columns" row-key="id" @listSelectChange="handleSelectionChange">
          <template #sbzt="{ row }">
            <el-tag v-if="row.sbzt == 1" type="success">在线</el-tag>
            <el-tag v-else type="danger">离线</el-tag>
          </template>
          <template #operations="{ row }">
            <div class="buttons">
              <div class="btn" @click="handleLook(row,1)">监测数据</div>
              <div class="btn" @click="handleLook(row,2)">报警数据</div>
              <!-- <div class="btn" @click="handleDelete(row)">误报忽略</div>
              <div class="btn" @click="handleEdit(row)">报警处置</div> -->
              <!-- <div class="btn" @click="handleDownload(row)">下载</div> -->
            </div>
          </template>
        </Table>
        <el-pagination class="pageBox" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增-编辑 -->
    <!-- <addDialog :dialogVisible="addVisiblty" :title="addTitle" :data="addData" @onChangeVisible="addVisiblty = false" @refreshList="fun_getList()"></addDialog> -->
    <!-- 查看 -->
    <!-- <lookDialog :dialogVisible="lookVisiblty" :data="lookData" @onChangeVisible="lookVisiblty = false"></lookDialog> -->
    <!-- 监测数据 -->
    <monitorDialog :dialogVisible="monitorVisiblty" :data="monitorData" @onChangeVisible="monitorVisiblty = false"></monitorDialog>
    <!-- 报警数据 -->
    <alarmDialog :dialogVisible="alarmVisiblty" :data="alarmData" @onChangeVisible="alarmVisiblty = false"></alarmDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete, Finished } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
import addDialog from "./components/add-dialog.vue";
import lookDialog from "./components/look-dialog.vue";
import monitorDialog from "./components/look-monitor-dialog.vue";
import alarmDialog from "./components/look-alarm-dialog.vue";

import { getDict } from "@/api/common";
// import {
//   listReport,
//   getReportInfo,
//   // addReport,
//   // updateReport,
//   delReport,
// } from "@/api/comprehensive-operation/device-inspect/index";

// 搜索条件
const filter = ref({
  cgqmc: '',//传感器名称
  sssbbm: '',//所属设备
  sssbmc: '',//所属设备名称
  cgqType: '',//传感器类型
  cgqTypeLabel: '',//传感器类型名称
  dateRange: '',//时间范围
  startTime: '',//开始时间
  endTime: '',//结束时间
  pageSize: 10,//每页条数
  pageNum: 1,//当前页
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    cgqmc: "",//传感器名称
    sssbbm: '',//所属设备
    sssbmc: "",//所属设备名称
    cgqType: '',//传感器类型
    cgqTypeLabel: '',//传感器类型名称
    dateRange: "",//时间范围
    startTime: "",//开始时间
    endTime: "",//结束时间
    pageSize: 10,//每页条数
    pageNum: 1,//当前页
  };
  fun_getList();
};

// 获取字典
const fun_getDicts = async () => {
  const {data} = await getDict('BGLX')
  bglxOption.value = data.list;
  console.log('报告类型bglxOption:',bglxOption.value);
}

// 获取所属设备
const sssbOption = ref([]);
const fun_getsssbOptions = async () => {
  // const {data} = await getQlOptions()
  // sssbOption.value = data;
  console.log('设备列表sssbOption:',sssbOption.value);
}
// 所属设备选择
const fun_sssbChange = (val) => {
  console.log('所属设备选择:',val);
  formData.value.sssbmc = sssbOption.value.find(item => item.value == val).label;
}

// 传感器类型
const cgqTypeTreeProps = {
  label: 'name',
  value: 'gid',
  children: 'children',
  multiple: false,//是否多选
  emitPath: false,//是否返回路径
}
const cgqTypeTree = ref([
  {
    name: '传感器类型1',
    gid: '1',
    children: [
      { name: '传感器类型1-1', gid: '11' },
      { name: '传感器类型1-2', gid: '12' },
      { name: '传感器类型1-3', gid: '13' },
      { name: '传感器类型1-4', gid: '14' },
    ]
  },
  {
    name: '传感器类型2',
    gid: '2',
    children: [
      { name: '传感器类型2-1', gid: '21' },
      { name: '传感器类型2-2', gid: '22' },
      { name: '传感器类型2-3', gid: '23' },
      { name: '传感器类型2-4', gid: '24' },
    ]
  }
]);
const fun_getcgqTypeTree = async () => {
  // const {data} = await getQlOptions()
  // xjryTree.value = data;
  console.log('传感器类型列表cgqTypeTree:',cgqTypeTree.value);
}
// 传感器类型选择
const cgqTypeTreeRef = ref();
const fun_cgqTypeChange = (data) => {
  // const checkedNodes = cgqTypeTreeRef.value.getCheckedNodes(true);//获取选中的叶子节点
  // console.log('传感器类型-节点点击:',data,checkedNodes);
  // formData.value.cgqTypeStr = checkedNodes.map(item => item.gid).join(',');
  // formData.value.cgqTypeLabelStr = checkedNodes.map(item => item.name).join(',');
}


// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getsssbOptions();//获取设备
  fun_getcgqTypeTree();//获取传感器类型
  fun_getList();//获取列表
});

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns1);

// 表格数据
const total = ref(0);
const tableData = ref([
  // {id:1,cgqbm:'传感器编号',cgqTypeLabel:'传感器类型',sssbmc:'所属设备',csxx:'厂商信息',sbzt:1},
  // {id:2,cgqbm:'传感器编号',cgqTypeLabel:'传感器类型',sssbmc:'所属设备',csxx:'厂商信息',sbzt:2},
]);
const fun_getList = async () => {
  // const { data } = await listReport(filter.value);
  // tableData.value = data.list;
  // total.value = data.total;
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};

// 日期范围-切换
const fun_dateChange = (val) => {
  console.log('时间范围-时间选择:',val);
  if (val) {
    filter.value.startTime = val[0] + " 00:00:00";
    filter.value.endTime = val[1] + " 23:59:59";
  } else {
    filter.value.startTime = "";
    filter.value.endTime = "";
  }
};

// 新增
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref({});
const addBtn = () => {
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row) => {
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "报警处置";
  addVisiblty.value = true;
};

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
// 监测数据
const monitorVisiblty = ref(false);
const monitorData = ref({});
// 报警数据
const alarmVisiblty = ref(false);
const alarmData = ref({});
const handleLook = async (row,type) => {
  if(type == 1){
    monitorVisiblty.value = true;
    monitorData.value = row;
  }else if(type == 2){
    alarmVisiblty.value = true;
    alarmData.value = row;
  }
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定删除吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await delReport(row.id);
    ElMessage({
      type: "success",
      message: `删除成功`,
    });
    fun_getList();
  })
};
// 批量删除
const delIds = ref("");
const handleSelectionChange = (data) => {
  delIds.value = data.map((item) => item.id).join(",");
};
const delAllBtn = () => {
  if (delIds.value) {
    ElMessageBox.confirm("批量删除后不可恢复,确定吗?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        const res = await delReport(delIds.value);
        if (res.code === 0) {
          fun_getList();
          ElMessage({
            type: "success",
            message: "删除成功",
          });
        } else {
          ElMessage({
            message: res.message,
            type: "warning",
          });
        }
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "取消删除",
        });
      });
  } else {
    ElMessage({
      message: "请选择需要删除的选项",
      type: "warning",
    });
  }
};

// 附件下载
// import { preImageUrl } from '@/utils/index'
// const handleDownload = (row) => {
//   console.log('附件下载:',preImageUrl,row);
//   if (row.fileList && row.fileList.length > 0) {
//     window.open( import.meta.env.VITE_APP_IMG_URL+preImageUrl+row.fileList[0].fileGid);
//   } else {
//     ElMessage({
//       message: "没有附件",
//       type: "warning",
//     });
//   }
// }


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
