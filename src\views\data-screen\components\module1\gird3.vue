<template>
  <div class="gird-box">
    <div class="gird-item">
      <div class="gird-content">
        <div class="gird-nums">{{ props.datas.coolingWaterPumpNums }}</div>
        <div class="gird-total">/{{ props.datas.coolingWaterPumTotal }}</div>
        <div class="gird-unit">个</div>
      </div>
      <div class="gird-label">冷却水泵</div>
    </div>
    <div class="gird-item">
      <div class="gird-content">
        <div class="gird-nums">{{ props.datas.secondaryPumpNums }}</div>
        <div class="gird-total">/{{ props.datas.secondaryPumTotal }}</div>
        <div class="gird-unit">个</div>
      </div>
      <div class="gird-label">二次泵</div>
    </div>
  </div>
</template>
<script setup>
import nums from "../count-up.vue";
const props = defineProps({
  datas: Object,
});
</script>
<style lang="scss" scoped>
.gird-box {
  display: grid;

  width: 100%;
  grid-template-columns: 1fr 1fr;
  grid-gap:56px;
  overflow: hidden;
  .gird-item {
    width: 180px;
    position: relative;
    .gird-content {
      display: flex;
      align-items: baseline;

      justify-content: center;
      height: 28px;
      padding-bottom: 4px;
      margin-bottom: 4px;
      .gird-nums {
        font-family: D-DIN-PRO, D-DIN-PRO;
        font-weight: bold;
        font-size: 24px;
        line-height: 24px;
      }
      .gird-total,
      .gird-unit {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: rgba(199, 214, 255, 0.64);
      }
      .gird-unit {
        margin-left: 4px;
      }
    }
    .gird-label {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
    }
    &:nth-child(1) {
      .gird-content {
        background: url(@/assets/data-screen/module-1-gird-bg-6.png) no-repeat;
        background-size: 100% 100%;
        .gird-nums {
          color: #ffb7bf;
        }
      }
    }
    &:nth-child(2) {
      .gird-content {
        background: url(@/assets/data-screen/module-1-gird-bg-7.png) no-repeat;
        background-size: 100% 100%;
        .gird-nums {
          color: #ffb7bf;
        }
      }
    }
  }
}
</style>
