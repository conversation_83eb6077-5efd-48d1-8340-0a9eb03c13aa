<template>
  <el-dialog
    v-model="isVisible"
    width="860"
    :show-close="false"
    :close-on-click-modal="false"
    class="dialog"
    @open="fun_open"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main over-auto-y" style="max-height: 60vh">
      <div class="left">
        <el-form
          label-width="112"
          :inline="true"
          :model="formData"
          ref="ruleFormRef"
          :rules="rules"
          status-icon
        >
          <el-form-item label="设备:" prop="xjsbbm">
            <el-select
              v-model="formData.xjsbbm"
              :collapse-tags="true"
              collapse-tags-tooltip
              placeholder="请选择"
              clearable
              value-key="gid"
              @change="fun_xjsbChange"
              :disabled="formData.inspectTaskDialogType"
            >
              <el-option
                v-for="(item, index) in xjsbOption"
                :key="index"
                :label="item.sbmc"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="养护时限:" prop="yhsx">
            <el-input
              v-model="formData.yhsx"
              placeholder="请输入"
              :disabled="true"
            />
          </el-form-item>
          <el-form-item label="本次养护时间:" prop="yhtime">
            <el-date-picker v-model="formData.yhtime" type="datetime" />
          </el-form-item>
          <el-form-item label="上次养护时间:" prop="lastYhtime">
            <el-date-picker
              v-model="formData.lastYhtime"
              type="datetime"
              :disabled="true"
            />
          </el-form-item>
          <el-form-item label="养护说明:" prop="remark" style="display: flex">
            <el-input
              type="textarea"
              v-model="formData.remark"
              placeholder="请输入"
              :rows="3"
              maxlength="500"
              resize="none"
              :show-word-limit="true"
            />
          </el-form-item>
          <el-form-item label="图片:" prop="fileListEdit" style="display: flex">
            <el-upload
              v-model:file-list="formData.fileListEdit"
              :action="action"
              :headers="headers"
              list-type="picture-card"
              :limit="9"
              :on-success="handlePictureCardSuccess"
              :before-upload="handlePictureCardBeforeUpload"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>
          </el-form-item>
          <!-- <el-form-item label="养护后:" prop="fileListEdit2" style="display: flex;">
            <el-upload
              v-model:file-list="formData.fileListEdit2"
              :action="action"
              :headers="headers"
              list-type="picture-card"
              :limit="9"
              :before-upload="handlePictureCardBeforeUpload2"
              :on-success="handlePictureCardSuccess2"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>
          </el-form-item> -->

          <!-- <el-form-item label="上传附件:" prop="fileAllList">
            <el-upload
              v-model:file-list="formData.fileAllList"
              class="upload-demo"
              :action="action"
              :headers="headers"
              :limit="1"
              :before-upload="beforeAvatarUpload"
              :on-success="handleAvatarSuccess"
            >
              <el-button type="primary" :disabled="formData.fileAllList?.length >= 1">点击上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  文件大小不超过5MB,支持扩展名:doc,docx,pdf
                </div>
              </template>
            </el-upload>
          </el-form-item> -->
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

// import {
//   getQlOptions,
//   addReport,
//   updateReport,
// } from "@/api/comprehensive-operation/device-inspect/index";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";
import {
  getDevice,
  getEquiInfo,
  add,
} from "@/api/comprehensive-operation/device-maint-conserve/conserve-manage";

import { ElMessage } from "element-plus";
import moment from "moment";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log("打开fun_open:", props.data);
  // 回显附件
  if (props.data.fileList) {
    props.data.fileListEdit = props.data.fileList.map((e) => {
      return {
        name: e.fileName,
        url: import.meta.env.VITE_APP_IMG_URL + e.filePath,
      };
    });
  }
  // 回显附件2
  // if (props.data.fileList2) {
  //   props.data.fileListEdit2 = props.data.fileList2.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
  nextTick(() => {
    ruleFormRef.value && ruleFormRef.value.clearValidate();
  });
};

// 表单数据-回显
const formData = computed(() => {
  console.log("props.data:", props.data);
  //设置初始默认状态
  if (props.data.yhtime === undefined)
    props.data.yhtime = moment().format("YYYY-MM-DD HH:mm:ss"); //当前时间
  //巡检详情跳转过来的
  if (props.data.inspectTaskDialogType){
     props.data.xjsbbm = xjsbOption.value.find(item=>item.gid==props.data.inspect_sbbms)
     
     fun_xjsbChange(props.data.xjsbbm)
  }

  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});

onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getxjsbOptions(); //获取设备
});

// 获取字典
const fun_getDicts = async () => {
  const { data: xjlxOptionData } = await getDict("XJLX"); //获取隐患类型
  xjlxOption.value = xjlxOptionData.list;
  console.log("隐患类型xjlxOption:", xjlxOption.value);
  const { data: xjxOptionData } = await getDict("XJX"); //获取隐患等级
  xjxOption.value = xjxOptionData.list;
  console.log("隐患等级xjxOption:", xjxOption.value);
};

// 获取设备
const xjsbOption = ref([]);
const fun_getxjsbOptions = async () => {
  let res = await getDevice();
  // const {data} = await getQlOptions()
  // xjsbOption.value = data;
  if (res.code === 0) {
    xjsbOption.value = res.data.list;
  }

  console.log("设备列表xjsbOption:", xjsbOption.value);
};
// 设备选择
const fun_xjsbChange = async (val) => {
  console.log("设备选择:", val);
  //formData.value.xjsbmc = val?.sbmc;
  let res = await getEquiInfo({ gid: val?.gid });
  console.log("设备详情res:", res);
  if (res.code == 0) {
    //养护时限
    formData.value.yhsx = res.data?.yhsx;
    //上次养护时间
    formData.value.lastYhtime = res.data?.lastCreateTime;
  } else {
    formData.value.yhsx = "";
    formData.value.lastYhtime = "";
    ElMessage.error(res.message);
  }
};

// 隐患类型
const xjlxOption = ref([]);
// 隐患类型选择
const fun_xjlxChange = (val) => {
  console.log("隐患类型选择:", val);
  formData.value.bglxmc = xjlxOption.value.find(
    (item) => item.businessValue == val
  ).businessLabel;
};

// 隐患等级
const xjxOption = ref([
  { label: "一级", value: "1" },
  { label: "二级", value: "2" },
  { label: "三级", value: "3" },
]);
// 隐患等级选择
const fun_xjxChange = (val) => {
  console.log("隐患等级选择:", val);
  formData.value.bglxmc = xjxOption.value.find(
    (item) => item.businessValue == val
  ).businessLabel;
};

const action =
  import.meta.env.VITE_APP_IMG_URL + "/api/platform/system/fileInfo/uploadFile";
const headers = { Authorization: "Bearer " + getToken() };
// 上传图片前
const handlePictureCardBeforeUpload = (file) => {
  console.log("上传图片前:", file);
  const fileExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
  if (formData.value.fileListEdit) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传jpg,jpeg,png,gif,bmp,webp格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("图片大小不能超过5MB");
    return false;
  }
};
// 上传图片成功
const handlePictureCardSuccess = (file) => {
  console.log("上传图片成功:", file);
  formData.value.fileList = formData.value.fileListEdit.map((e) => ({
    maintainid: formData.value.gid,
    fileGid: e.response.data.gid ?? e.gid,
    // originalName: e.response.data.originalName ?? e.originalName,
  }));
};
// 上传图片前2
/* const handlePictureCardBeforeUpload2 = (file) => {
  console.log('上传图片前:',file);
  const fileExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
  if (formData.value.fileListEdit2) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传jpg,jpeg,png,gif,bmp,webp格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("图片大小不能超过5MB");
    return false;
  }
}; */
// 上传图片成功2
/* const handlePictureCardSuccess2 = (file) => {
  console.log('上传图片成功:',file);
  formData.value.fileList2 = formData.value.fileListEdit2
    .map((e) => ({
      gid: e.gid,
      recordDetailsGid: formData.value.gid,
      fileGid: e.response.data.gid ?? e.gid,
      // originalName: e.response.data.originalName ?? e.originalName,
    }));
}; */

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  xjsbbm: [{ required: true, message: "请选择", trigger: "blur" }],
  // yhsx: [{ required: true, message: "请输入", trigger: "blur" }],
  // lastYhtime: [{ required: true, message: "请选择", trigger: "blur" }],
  yhtime: [{ required: true, message: "请选择", trigger: "blur" }],
  // remark: [
  //   { required: true, message: "请输入", trigger: "blur" },
  // ],

  // fileListEdit: [
  //   { required: true, message: "请上传图片", trigger: "change" },
  //   { validator: (rule, value, callback) => {
  //     if (value.length > 0) {
  //       callback();
  //     } else {
  //       callback(new Error("请上传图片"));
  //     }
  //   }, trigger: "change" },
  // ],
  // fileListEdit2: [
  //   { required: true, message: "请上传维修后图片", trigger: "change" },
  //   { validator: (rule, value, callback) => {
  //     if (value.length > 0) {
  //       callback();
  //     } else {
  //       callback(new Error("请上传维修后图片"));
  //     }
  //   }, trigger: "change" },
  // ],

  // fileAllList: [
  //   { required: true, message: "请上传附件", trigger: "blur" },
  // ],
});

// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增" || props.title == "养护上报") {
        let res = {};
        let params = {
            deviceId: formData.value.xjsbbm.gid,
            deviceName: formData.value.xjsbbm.sbmc,
            descMsg: formData.value.remark,
            yhTime: formData.value.yhtime,
            fileList: formData.value.fileList,
            planId:props?.data?.planId
          };
          console.log(params,formData.value);
           res = await add(params);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      } else {
        const res = await updateReport(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      }
      formData.value.fileListEdit = [];
      // formData.value.fileListEdit2=[];
    }
  });
};

// 上传附件
/* const action = import.meta.env.VITE_APP_IMG_URL + "/api/platform/system/fileInfo/uploadFile";
const headers = { Authorization: "Bearer " + getToken() };
const beforeAvatarUpload = (file) => {
  const fileExtensions = ["doc", "docx", "pdf"];
  if (formData.value.fileAllList) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传doc,docx,pdf格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("文件大小不能超过5MB");
    return false;
  }
}; */
/**文件上传成功回调 */
// const handleAvatarSuccess = (response, file) => {
//   formData.value.fileList = formData.value.fileAllList.map((e) => {return {fileGid:e.response.data.gid ?? e.gid}});
//   // formData.value.fj = formData.value.fileAllList.map((e) => e.response.data.originalName ?? e.originalName).toString();
// };
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
