const validateEnums = {
    blur: 'blur',
    change: 'change'
}

export const validateTypeEnums = {
    telPhone: 'telPhone',
    phone: 'phone',
    vehicle: 'vehicle',
    serialNum: 'serialNum',
    number: 'number',
    positiveNumber: 'positiveNumber'
}

const validateRulesEnums = {
    [validateTypeEnums.telPhone]: {
        // 座机校验
        pattern: /^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$/,
        message: '请输入正确的',
        triggle: 'blur'
    },
    [validateTypeEnums.phone]: {
        // 手机号校验
        pattern:
            /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        message: '请输入正确的',
        triggle: 'blur'
    },
    [validateTypeEnums.vehicle]: {
        // 车牌号校验
        pattern:
            /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$/,
        message: '请输入正确的',
        triggle: 'blur'
    },
    [validateTypeEnums.serialNum]: {
        // 编号校验
        pattern: /^[A-Za-z0-9]+$/,
        message: '只能输入字母或数字',
        triggle: 'blur'
    },
    [validateTypeEnums.number]: {
        // 数字校验
        pattern: /^[0-9]+$/,
        message: '只能输入数字',
        triggle: 'blur'
    },
    [validateTypeEnums.positiveNumber]: {
        // 正数
        pattern: /^[1-9]\d*$/,
        message: '只能输入大于0的数字',
        triggle: 'blur'
    }
}

export function generateRulesByColumns(columns) {
    const allRules = {}
    if (!Array.isArray(columns)) {
        throw Error('请传入数组类型')
    }
    const noGroupColumn = columns.filter(column => !column.group)
    const flatGroupColumn = columns
        .filter(column => column.group)
        .reduce((pre, item) => [...pre, ...item.children], [])
    columns = [...noGroupColumn, ...flatGroupColumn]

    function getBlurOrChange(type) {
        const blurArr = ['input', 'input-number']
        const changeArr = [
            'radio',
            'radio-group',
            'checkbox',
            'checkbox-group',
            'select',
            'cascader',
            'switch',
            'slider',
            'time-select',
            'date-picker',
            'rate',
            'color-picker',
            'transfer',
            'input-point',
            'tree-select'
        ]
        if (blurArr.includes(type)) return validateEnums.blur
        else if (changeArr.includes(type)) return validateEnums.change
    }
    function getTipPrefix(type) {
        return getBlurOrChange(type) === validateEnums.blur
            ? '请输入'
            : '请选择'
    }

    function setPatternRulesMessage(rules, rulesConfig, columnConfig) {
        if (rulesConfig.patternMessage) {
            rules.message = rulesConfig.patternMessage.replace(':', '')
            return
        }
        switch (rulesConfig.validateType) {
            case validateTypeEnums.telPhone:
            case validateTypeEnums.phone:
            case validateTypeEnums.vehicle:
                rules.message =
                    rules.message + columnConfig.label.replace(':', '')
                return
            case validateTypeEnums.serialNum:
            case validateTypeEnums.number:
            case validateTypeEnums.positiveNumber:
                rules.message =
                    columnConfig.label.replace(':', '') + rules.message
        }
    }

    columns.forEach(item => {
        const { rules } = item
        if (rules && item.key) {
            // 判断rules是数组还是对象
            // 如果是数组则不处理
            // 如果是对象，并且判断是否需要必填，如果需要必填则进行下面步骤
            // 1.判断message是否传入，如果没传入则根据label生成
            // 2.判断trigger是否传入， 如果没传入则根据type生成
            if (Array.isArray(rules)) {
                allRules[item.key] = rules
            } else {
                const { required, message, trigger, validateType } = {
                    ...rules
                }
                if (required) {
                    if (!message) {
                        rules.message =
                            getTipPrefix(item.type) +
                            item.label.replace(':', '')
                    }
                    if (!trigger) {
                        rules.trigger = getBlurOrChange(item.type)
                    }
                }
                if (
                    (rules.required && rules.message) ||
                    rules.pattern ||
                    rules.message ||
                    (rules.validator && typeof rules.validator === 'function')
                )
                    allRules[item.key] = [rules]

                if (validateType) {
                    const patternRules = { ...validateRulesEnums[validateType] }
                    setPatternRulesMessage(patternRules, rules, item)
                    allRules[item.key] = allRules[item.key] || []
                    allRules[item.key].push(patternRules)
                }
            }
            const isInput = getBlurOrChange(item.type) === validateEnums.blur
            // if (isInput) {
            //     allRules[item.key] = allRules[item.key] || []
            //     allRules[item.key].push({
            //         message: '输入的内容不能以空格开头或空格结尾',
            //         pattern: /^(\S.*\S{1}|\S{1})$/,
            //         transform(value) {
            //             return value
            //         },
            //         triggle: 'blur'
            //     })
            // }
        }
    })
    return allRules
}
