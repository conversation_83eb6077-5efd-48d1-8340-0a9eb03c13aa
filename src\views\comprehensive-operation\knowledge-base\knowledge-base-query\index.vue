<template>
  <div class="pointManage">
    <div class="formBox">
      <el-form :inline="true" :model="filter">
        <el-form-item label="标题:">
          <el-input v-model="filter.title" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="描述:">
          <el-input v-model="filter.description" placeholder="请输入" clearable />
        </el-form-item>
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier">
      <!-- <div class="main-btns">
        <el-button type="primary" @click="addBtn" :icon="Plus">新增</el-button>
      </div> -->
      <div class="tableBox">
        <Table :data="tableData" :columns="columns" row-key="gid">
          <template #operations="{ row }">
            <div class="buttons">
              <div class="btn" @click="handleLook(row)">查看</div>
            </div>
          </template>
        </Table>
        <el-pagination class="pageBox" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增-编辑 -->
    <!-- <addDialog :dialogVisible="addVisiblty" :title="addTitle" :data="addData" @onChangeVisible="addVisiblty = false"
      @refreshList="fun_getList()"></addDialog> -->
    <!-- 查看 -->
    <lookDialog :dialogVisible="lookVisiblty" :data="lookData" @onChangeVisible="lookVisiblty = false"></lookDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
// import addDialog from "./components/add-dialog.vue";
import lookDialog from "./components/look-dialog.vue";

import { getBusinessDict } from "@/api/common";
import {
  getList,
  del,
  update,
} from "@/api/comprehensive-operation/knowledge-base/index";
import { template } from "lodash";

// 搜索条件
const filter = ref({
  title: '',
  description: '',
  status: '1',
  pageSize: 10,
  pageNum: 1,
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    title: "",
    description: "",
    status: '1',
    pageSize: 10,
    pageNum: 1,
  };
  fun_getList();
};

const typeOption = ref([]);
// 获取字典
const fun_getDicts = async () => {
  const {data:typeOptionData} = await getBusinessDict('knowledge_type');//获取知识库类型
  typeOption.value = typeOptionData;
}
const formatType = (row) => {
  const res = typeOption.value.find(item => item.businessValue === row.type);
  return res ? res.businessLabel : row.type;
}

// 挂载
onMounted(async () => {
  fun_getDicts();//获取字典
  fun_getList();
});

// 表格项
const headerColumns = new Columns({
  typeFormatter: formatType
});
const columns = ref(headerColumns.columns);
// 表格数据
const total = ref(0);
const tableData = ref([]);
const fun_getList = async () => {
  const { data } = await getList(filter.value);
  console.log('知识库列表:',data);
  tableData.value = data.list;
  total.value = data.total;
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};

// 新增
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref([]);
const addBtn = () => {
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row) => {
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "编辑";
  addVisiblty.value = true;
};

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row) => {
  lookVisiblty.value = true;
  lookData.value = {...row, typeName: formatType(row)};
};
</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
