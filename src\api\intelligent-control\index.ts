import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';


/* 主控台页面 */

// 设备控制台-列表
export function getDeviceConsoleList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentInfo/consoleListByPage',
    method: 'post',
    data: {...query}
  })
}

/* 暖通空调页面 */

// 设备属性-详情
export function getDeviceAttributeList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentAttribute/getListByParam',
    method: 'post',
    data: {...query}
  })
}
// 设备属性(带手自动启动属性)-详情
export function getDeviceAttributeAutoHandleList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentAttribute/getListByAutoHandle',
    method: 'post',
    data: {...query}
  })
}

// 设备属性-启停控制
export function updateDeviceAttribute(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentAttribute/remoteControl',
    method: 'post',
    data: data
  })
}

// 设备扎点-列表
export function getDevicePositionList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentInfo/getEquipmentInfoList',
    method: 'post',
    data: {...query}
  })
}




/* 平面图绘制页面 */

// 面板-设备列表
export function getPanelDeviceList(type: string) {
  if(type=='1937014595413544961'){ //冷源
    return request({
      url: baseRouter + apiPrefix + '/equipmentInfo/getAllList',
      method: 'get',
      // params: {...query}
    })
  }else if(type=='-1'){ //热源(暂时没有)
    return {data:''}
    // return request({
    //   url: baseRouter + apiPrefix + '/equipmentInfo/getAllList',
    //   method: 'get',
    //   // params: {...query}
    // })
  }
}

// 暖通空调的冷源/热源的工艺图获取(一共就两个工艺图,新增之后就用数据库里的主键ID获取工艺图json数据)
export function getFlowChart(gid: string) {
  return request({
    url: baseRouter + apiPrefix + '/tWfProcdef/getInfo',
    method: 'get',
    params: {gid:gid},//gid:'1937014595413544961'(冷源)
  })
}

// 暖通空调的冷源/热源的工艺图新增(一共就两个工艺图,新增之后就用数据库里的主键ID获取工艺图json数据和走修改接口)
export function addFlowChart(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tWfProcdef/addTWfProcdef',
    method: 'post',
    data: data
  })
}

// 暖通空调的冷源/热源的工艺图修改(一共就两个工艺图,新增之后就用数据库里的主键ID获取工艺图json数据和走新增接口)
export function updateFlowChart(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tWfProcdef/editTWfProcdef',
    method: 'post',
    data: data //{gid:'',procddata:jsonData}
  })
}






