import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = 'monitor';


// 维修管理-自己维修
export function add(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintenance/addSelfTMaintenance',
    method: 'post',
    data: data
  })
}

// 维修管理-巡检维修
export function addTMaintenance(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintenance/addTMaintenance',
    method: 'post',
    data: data
  })
}

// 维修管理-获取单条信息
export function getInfo(data: any) {
  console.log(111,data)
  return request({
    url: baseRouter + apiPrefix + '/tMaintenance/getInfo',
    method: 'get',
    params: data
  })
}


//维修管理-列表
export function getList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintenance/byPage',
    method: 'post',
    data: {...query}
  })
}
//维修代办-列表
export function getMyList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintenance/byMyPage',
    method: 'post',
    data: {...query}
  })
}
//维修代办-人员列表
export function getPersonList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/personnelScheduling/getSchedulingPersonnelList',
    method: 'post',
    data: {...query}
  })
}
//维修代办-流程
export function addTMaintenanceStep(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintenanceStep/addTMaintenanceStep',
    method: 'post',
    data: {...query}
  })
}

//获取设备
export function getDevice() {
  return request({
    url: baseRouter + apiPrefix + '/equipmentInfo/getAllList',
    method: 'get',
  })
}
export function getEquiInfo(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintain/getEquiInfo',
    method: 'get',
    params: {...query}
  })
}






