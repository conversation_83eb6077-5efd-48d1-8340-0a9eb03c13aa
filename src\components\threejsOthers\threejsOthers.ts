
// 导入threejs
import * as THREE from 'three';
// 导入轨道控制器
// import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
// import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
// 导入gltf加载器(加载gltf模型)
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
// 导入draco解压缩gltf模型加载器
import { DRACOLoader } from 'three/addons/loaders/DRACOLoader.js';
// 导入hdr加载器(加载hdr贴图)
import { RGBELoader } from 'three/addons/loaders/RGBELoader.js';
// 导入tween补间动画库
import * as TWEEN from 'three/addons/libs/tween.module.js';
// 导入字体加载器(加载字体)
import { FontLoader } from 'three/addons/loaders/FontLoader.js';
// 导入文本几何体加载器(加载文本)
import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';
// 导入lil.gui(调试开发工具)
import { GUI } from 'three/addons/libs/lil-gui.module.min.js';


// 添加几何体模型测试1
function fun_addGeometryModelTest1({scene,renderer,camera}:any){
    //创建几何体-立方体
    const geometryCube = new THREE.BoxGeometry(1,1,1);//立方体-参数:width,height,depth,...
    //创建材质(基础材质(不需要光源):MeshBasicMaterial,标准材质(需要有光源):MeshStandardMaterial)-立方体
    const materialCube = new THREE.MeshBasicMaterial({ wireframe:false });//基础材质-参数:color,wireframe,...
    // const materialCube = new THREE.MeshStandardMaterial();//标准材质-参数:color,wireframe,...
    // materialCube.color = new THREE.Color(0xff0000);//设置立方体颜色
    // materialCube.side = THREE.DoubleSide;//设置材质两面可见(适用于平面,默认背面不可见)
    //添加纹理
    const texture = new THREE.TextureLoader().load('/static/threejsData/logo.svg');
    texture.colorSpace = THREE.SRGBColorSpace;//设置纹理颜色空间-SRGBColorSpace(人类感知色彩光谱范围值空间)
    //设置贴图(map:颜色贴图,alphaMap:透明度贴图,envMap:环境贴图,lightMap:光照贴图,aoMap:环境光遮蔽贴图,gradientMap:渐变贴图,bumpmap:凹凸贴图,normalMap:法线贴图,specularMap:镜面反射贴图,displacementMap:位移贴图)
    materialCube.map = texture;//设置颜色贴图
    // materialCube.alphaMap = texture;//设置透明度贴图
    // materialCube.transparent = true;//设置材质透明
    // materialCube.opacity = 0.3;//设置材质透明度
    //创建网格-立方体
    const cube = new THREE.Mesh(geometryCube, materialCube);
    //设置立方体在网格(父元素网格或场景网格)的位置
    cube.position.x=3;//x轴
    // cube.position.y=2;//y轴
    // cube.position.z=2;//z轴
    // cube.position.set(3,2,2);//设置立方体在网格的位置:x,y,z轴
    //设置立方体的网格的缩放
    // cube.scale.set(2,2,2);//设置立方体在网格的缩放:x,y,z轴
    //添加立方体-到场景网格中
    // scene.add(cube);

    //创建几何体-圆柱体
    const geometryCylinder = new THREE.CylinderGeometry(1,1,1);//圆柱体-参数:radiusTop,radiusBottom,height,...
    //创建材质(基础材质(不需要光源):MeshBasicMaterial,标准材质(需要有光源):MeshStandardMaterial)-圆柱体
    // const materialCylinder = new THREE.MeshBasicMaterial({ color: 0x0000ff,wireframe:true });//基础材质-参数:color,wireframe,...
    const materialCylinder = new THREE.MeshStandardMaterial({ color: 0x0000ff,wireframe:false });//标准材质-参数:color,wireframe,...
    //创建网格-圆柱体
    const cylinder = new THREE.Mesh(geometryCylinder, materialCylinder);
    //设置圆柱体在网格(父元素网格或场景网格)的位置
    cylinder.position.x=-2;//x轴
    //设置圆柱体的网格的缩放
    // cylinder.scale.set(2,2,2);//设置圆柱体在网格的缩放:x,y,z轴
    //添加圆柱体-到场景网格中
    // scene.add(cylinder);

    // 添加绑定1
    scene.add(cylinder);//添加圆柱体-到场景网格中
    cylinder.add(cube);//添加立方体-到圆柱体网格中
    // 添加绑定2
    // scene.add(cube);//添加立方体-到场景网格中
    // cube.add(cylinder);//添加圆柱体-到立方体网格中
    
    renderer.render(scene, camera);//渲染到标签中
    return {cube,cylinder};
}

// 添加图片模型(平面模型)
function fun_addImageModel({scene,renderer,camera}:any,config:{path:string,width?:number,height?:number,position?:{x:number,y:number,z:number},scale?:number,lookAtCamera?:boolean}){
  //创建几何体-平面
  const geometryPlane = new THREE.PlaneGeometry(config.width||20,config.height||20);//平面-参数:width,height,depth,...
  //创建材质(基础材质(不需要光源):MeshBasicMaterial,标准材质(需要有光源):MeshStandardMaterial)-平面
  const materialPlane = new THREE.MeshBasicMaterial({ wireframe:false });//基础材质-参数:color,wireframe,...
  // const materialPlane = new THREE.MeshStandardMaterial();//标准材质-参数:color,wireframe,...
  // materialPlane.color = new THREE.Color(0xff0000);//设置平面颜色
  materialPlane.side = THREE.DoubleSide;//设置材质两面可见(适用于平面,默认背面不可见)
  //添加纹理
  const texture = new THREE.TextureLoader().load(config.path);
  texture.colorSpace = THREE.SRGBColorSpace;//设置纹理颜色空间-SRGBColorSpace(人类感知色彩光谱范围值空间)
  //设置贴图(map:颜色贴图,alphaMap:透明度贴图,envMap:环境贴图,lightMap:光照贴图,aoMap:环境光遮蔽贴图,gradientMap:渐变贴图,bumpmap:凹凸贴图,normalMap:法线贴图,specularMap:镜面反射贴图,displacementMap:位移贴图)
  materialPlane.map = texture;//设置颜色贴图
  // materialPlane.alphaMap = texture;//设置透明度贴图
  materialPlane.transparent = true;//设置材质透明
  // materialPlane.opacity = 0.3;//设置材质透明度
  //创建网格-平面
  const plane = new THREE.Mesh(geometryPlane, materialPlane);
  //设置平面在网格(父元素网格或场景网格)的位置(x,y,z轴)
  plane.position.set(config.position?.x||0,config.position?.y||0,config.position?.z||0);//x,y,z轴
  // plane.position.x=config.position?.x||0;//x轴
  // plane.position.y=config.position?.x||0;//y轴
  // plane.position.z=config.position?.x||0;//z轴
  //设置平面的网格的缩放(x,y,z轴)
  const modelScale = config.scale||1;
  plane.scale.set(modelScale,modelScale,modelScale);//x,y,z轴
  //设置平面的网格的旋转(x,y,z轴)
  // plane.rotation.x = -Math.PI/2;//x轴
  // plane.rotation.y = -Math.PI/2;//y轴
  // plane.rotation.z = -Math.PI/2;//z轴
  //设置平面的网格的旋转:始终正对相机
  // if(config.lookAtCamera)fun_imageAnimate({scene,renderer,camera},{plane});//持续更新平面模型动画函数(保证始终正对相机)
  //添加平面-到场景网格中
  scene.add(plane);

  renderer.render(scene, camera);//渲染到标签中
  return {plane};
}
// 持续更新平面模型动画函数(保证始终正对相机)
function fun_imageAnimate({scene,renderer,camera}:any,{plane}:any){
  //设置平面的网格的旋转:正对相机
  plane.lookAt(camera.position);
  renderer.clear();
  renderer.render(scene, camera);//渲染到标签中
  requestAnimationFrame(()=>fun_imageAnimate({scene,renderer,camera},{plane}));//重复调用当前函数渲染
}

// 添加文字模型
const fontLoader = new FontLoader();//创建字体加载器
function fun_addTextModel({scene,renderer,camera}:any,config:{text:string,fontPath?:string,position?:{x:number,y:number,z:number},scale?:number,color?:string|number,size?:number,depth?:number,weight?:number,lookAtCamera?:boolean}){
  // '/static/threejsData/fonts/helvetiker_regular.typeface.json'
  fontLoader.load(config.fontPath||'/static/threejsData/fonts/OPPOSansL_Regular.typeface.json', function (font) {//加载字体
    // console.log('font:',font);
    // 创建文本几何体
    const textGeometry = new TextGeometry(config.text, {
      font: font,//字体
      size: config.size||2,//字号
      depth: config.depth||0.1,//厚度
      curveSegments: 12,//曲线上的分段数
      bevelEnabled: true,//是否开启斜角
      bevelThickness: 0.03,//斜角厚度
      bevelSize: config.weight||0.05,//斜角大小(加粗)
      bevelOffset: 0,//斜角偏移
      bevelSegments: 5//斜角分段数
    })
    // 优化几何体，合并缓冲区
    textGeometry.computeBoundingBox();
    // textGeometry.center();
    // 创建材质
    const textMaterial = new THREE.MeshBasicMaterial({ color: config.color||'#ffffff', wireframe: false })//基础材质-参数:color,wireframe,...
    // 创建文本
    const text = new THREE.Mesh(textGeometry, textMaterial)
    // 设置文本位置
    text.position.set(config.position?.x||0,config.position?.y||0,config.position?.z||0)
    // 设置文本缩放
    const modelScale = config.scale || 1;
    text.scale.set(modelScale, modelScale, modelScale);
    // 设置文本正对相机
    // if(config.lookAtCamera)fun_textAnimate({scene,renderer,camera},{text});//持续更新文本模型动画函数(保证始终正对相机)

    // 添加文本
    scene.add(text);
    // 渲染
    renderer.render(scene, camera);
    return {text};
  })
}
// 持续更新文本模型动画函数(保证始终正对相机)
function fun_textAnimate({scene,renderer,camera}:any,{text}:any){
  // 设置文本正对相机
  text.lookAt(camera.position);
  renderer.clear();
  renderer.render(scene, camera);//渲染到标签中
  requestAnimationFrame(()=>fun_textAnimate({scene,renderer,camera},{text}));//重复调用当前函数渲染
}

// 添加建筑楼层模型-A1-1(A1栋1层)
function fun_addBuildingFloorModel_A1_1({scene,renderer,camera}:any,config:{position?:{x:number,y:number,z:number},scale?:number,lookAtCamera?:boolean}){
  //创建几何体-立方体-楼层平面
  const geometryCube = new THREE.BoxGeometry(1840,20,1000);//立方体-参数:width(x轴),height(y轴),depth(z轴),...
  //创建材质(基础材质(不需要光源):MeshBasicMaterial,标准材质(需要有光源):MeshStandardMaterial)-立方体
  const materialCube = new THREE.MeshBasicMaterial({ wireframe:false });//基础材质-参数:color,wireframe,...
  // const materialCube = new THREE.MeshStandardMaterial();//标准材质-参数:color,wireframe,...
  materialCube.color = new THREE.Color('#455D89');//设置立方体颜色
  //创建网格-立方体
  const cube = new THREE.Mesh(geometryCube, materialCube);
  //设置立方体在网格(父元素网格或场景网格)的位置
  // cube.position.x=3;//x轴
  cube.position.y=0;//y轴
  // cube.position.z=2;//z轴
  // cube.position.set(3,2,2);//设置立方体在网格的位置:x,y,z轴
  //设置立方体的网格的缩放
  // cube.scale.set(2,2,2);//设置立方体在网格的缩放:x,y,z轴
  
  //添加立方体-到场景网格中
  scene.add(cube);

  //创建几何体-立方体-楼层外围墙-上
  const geometryCube_wall_up = new THREE.BoxGeometry(1840,200,20);//立方体-参数:width(x轴),height(y轴),depth(z轴),...
  const materialCube_wall_up = new THREE.MeshBasicMaterial({color: '#ddd', transparent: true, opacity: 0.8});//基础材质
  //创建网格-立方体
  const cube_wall_up = new THREE.Mesh(geometryCube_wall_up, materialCube_wall_up);
  //设置立方体在网格(父元素网格或场景网格)的位置
  cube_wall_up.position.z = -490;
  cube_wall_up.position.y = 90;
  //添加立方体-到场景网格中
  cube.add(cube_wall_up);
  //创建几何体-立方体-楼层外围墙-下
  const geometryCube_wall_down = new THREE.BoxGeometry(1840,200,20);//立方体-参数:width(x轴),height(y轴),depth(z轴),...
  const materialCube_wall_down = new THREE.MeshBasicMaterial({color: '#ddd', transparent: true, opacity: 0.8});//基础材质
  //创建网格-立方体
  const cube_wall_down = new THREE.Mesh(geometryCube_wall_down, materialCube_wall_down);
  //设置立方体在网格(父元素网格或场景网格)的位置
  cube_wall_down.position.z = 490;
  cube_wall_down.position.y = 90;
  //添加立方体-到场景网格中
  cube.add(cube_wall_down);
  //创建几何体-立方体-楼层外围墙-左
  const geometryCube_wall_left = new THREE.BoxGeometry(20,200,1000);//立方体-参数:width(x轴),height(y轴),depth(z轴),...
  const materialCube_wall_left = new THREE.MeshBasicMaterial({color: '#ddd', transparent: true, opacity: 0.8});//基础材质
  //创建网格-立方体
  const cube_wall_left = new THREE.Mesh(geometryCube_wall_left, materialCube_wall_left);
  //设置立方体在网格(父元素网格或场景网格)的位置
  cube_wall_left.position.x = -910;
  cube_wall_left.position.y = 90;
  //添加立方体-到场景网格中
  cube.add(cube_wall_left);
  //创建几何体-立方体-楼层外围墙-右
  const geometryCube_wall_right = new THREE.BoxGeometry(20,200,1000);//立方体-参数:width(x轴),height(y轴),depth(z轴),...
  const materialCube_wall_right = new THREE.MeshBasicMaterial({color: '#ddd', transparent: true, opacity: 0.8});//基础材质
  //创建网格-立方体
  const cube_wall_right = new THREE.Mesh(geometryCube_wall_right, materialCube_wall_right);
  //设置立方体在网格(父元素网格或场景网格)的位置
  cube_wall_right.position.x = 910;
  cube_wall_right.position.y = 90;
  //添加立方体-到场景网格中
  cube.add(cube_wall_right);


  // 添加文字模型
  fontLoader.load('/static/threejsData/fonts/OPPOSansL_Regular.typeface.json', function (font) {//加载字体
    // console.log('font:',font);
    // 创建材质
    const textMaterial = new THREE.MeshBasicMaterial({ color: '#ffffff', wireframe: false })//基础材质-参数:color,wireframe,...
    // 创建文本几何体
    const textGeometry1 = new TextGeometry('全日制餐厅', {
      font: font,//字体
      size: 30,//字号
      depth: 2,//厚度
      curveSegments: 12,//曲线上的分段数
      bevelOffset: 0,//斜角偏移
      bevelEnabled: true,//是否开启斜角
      bevelThickness: 1,//斜角厚度
      bevelSize: 1,//斜角大小(加粗)
      bevelSegments: 3//斜角分段数
    })
    // 优化几何体，合并缓冲区
    textGeometry1.computeBoundingBox();
    // textGeometry.center();
    // 创建文本
    const text1 = new THREE.Mesh(textGeometry1, textMaterial)
    // 设置文本旋转
    text1.rotation.x = -Math.PI/2;
    // 设置文本位置
    text1.position.set(-500,10,0)
    // 设置文本正对相机
    // if(config.lookAtCamera)fun_textAnimate({scene,renderer,camera},{text});//持续更新文本模型动画函数(保证始终正对相机)

    // 添加文本
    cube.add(text1);

    // 创建文本几何体
    const textGeometry2 = new TextGeometry('大堂吧', {
      font: font,//字体
      size: 30,//字号
      depth: 2,//厚度
      curveSegments: 12,//曲线上的分段数
      bevelOffset: 0,//斜角偏移
      bevelEnabled: true,//是否开启斜角
      bevelThickness: 1,//斜角厚度
      bevelSize: 1,//斜角大小(加粗)
      bevelSegments: 3//斜角分段数
    })
    // 优化几何体，合并缓冲区
    textGeometry2.computeBoundingBox();
    // textGeometry.center();
    // 创建文本
    const text2 = new THREE.Mesh(textGeometry2, textMaterial)
    // 设置文本旋转
    text2.rotation.x = -Math.PI/2;
    // 设置文本位置
    text2.position.set(500,10,0)
    // 设置文本正对相机
    // if(config.lookAtCamera)fun_textAnimate({scene,renderer,camera},{text});//持续更新文本模型动画函数(保证始终正对相机)

    // 添加文本
    cube.add(text2);

    // 渲染
    // renderer.render(scene, camera);
  })


  renderer.render(scene, camera);//渲染到标签中
  // return {cube};
}


export {
  fun_addGeometryModelTest1,//添加几何体模型测试1

  fun_addImageModel,//添加图片模型(平面模型)
  fun_addTextModel,//添加文字模型

  fun_addBuildingFloorModel_A1_1,//添加建筑楼层模型-A1-1(A1栋1层)
}

