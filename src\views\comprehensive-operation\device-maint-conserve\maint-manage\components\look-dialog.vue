<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main over-auto-y" style="height: 60vh;">
      <div class="title-l-blue-box marg-b-10">
        <span>隐患信息</span>
      </div>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">隐患名称:</el-col>
        <el-col :span="8">{{ data.maintenanceName }}</el-col>
        <el-col :span="4" class="text-right">上报时间:</el-col>
        <el-col :span="8">{{ data.createTime }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">隐患类型:</el-col>
        <el-col :span="8">{{ data.maintenanceType }}</el-col>
        <el-col :span="4" class="text-right">隐患等级:</el-col>
        <el-col :span="8">{{ data.level }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">上报人:</el-col>
        <el-col :span="8">{{ data.createUserName }}</el-col>
        <el-col :span="4" class="text-right">所属设备:</el-col>
        <el-col :span="8">{{ data.deviceName }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">隐患描述:</el-col>
        <el-col :span="20">{{ data.descMsg }}</el-col>
      </el-row>
      
      <!-- <el-row :gutter="10" class="marg-b-10 padd-t-20">
        <el-col :span="24" class="">
          <div style="padding-left: 20px;">
            
            <div class="">
              <img class="inblock marg-r-10 marg-b-10" style="width:120px;" :src="`/src/assets/images/card-icon1.png`" alt="">
              <img class="inblock marg-r-10 marg-b-10" style="width:120px;" :src="`/src/assets/images/card-icon1.png`" alt="">
              <img class="inblock marg-r-10 marg-b-10" style="width:120px;" :src="`/src/assets/images/card-icon1.png`" alt="">
              <img class="inblock marg-r-10 marg-b-10" style="width:120px;" :src="`/src/assets/images/card-icon1.png`" alt="">
            </div>
          </div>
        </el-col>
      </el-row> -->

      <div class="title-l-blue-box marg-b-10">
        <span>报警信息</span>
      </div>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">报警编号:</el-col>
        <el-col :span="8">{{ data?.rskAlarmDetails?.bjbh }}</el-col>
        <el-col :span="4" class="text-right">监测项:</el-col>
        <el-col :span="8">{{ data?.rskAlarmDetails?.jcxdm }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">当前报警值:</el-col>
        <el-col :span="8">{{  data?.rskAlarmDetails?.dqbjz }}</el-col>
        <el-col :span="4" class="text-right">当前报警时间:</el-col>
        <el-col :span="8">{{ data?.rskAlarmDetails?.dqbjsj }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">所属设备:</el-col>
        <el-col :span="8">{{ data?.rskAlarmDetails?.sblxmc }}</el-col>
        <el-col :span="4" class="text-right">监测设备名称:</el-col>
        <el-col :span="8">{{ data?.rskAlarmDetails?.sbmc }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">报警位置:</el-col>
        <el-col :span="8">{{ data?.rskAlarmDetails?.bjwz }}</el-col>
        <el-col :span="4" class="text-right">处置状态:</el-col>
        <el-col :span="8">{{ data?.rskAlarmDetails?.bjczjdmc }}</el-col>
      </el-row>

      <div class="title-l-blue-box marg-b-10 marg-t-20">
        <span>处理记录</span>
      </div>
      <div>
        <el-timeline style="max-width: 600px" v-if="stepList.length>0">
          <el-timeline-item v-for="(item, index) in stepList" :key="index" :timestamp="item.createTime" color="#0bbd87" :hide-timestamp="true">
            <div class="line-2">
              <div class="">
                <span class="inblock">{{ item.maintenanceStep }}</span>
                <span class="inblock marg-0_20">{{ item.createTime }}</span>
                <span class="inblock">处理人：{{ item.createUserName }}</span>
                 <span class="inblock marg-0_20" v-if="item.remark">处置状态：{{ item.remark}}</span>
              </div>
              <div class="" v-if="item.handleResult">研判结果：{{ item.handleResult }}</div>
              <div class="bg-color-ddd padd-5 flex">
                <span class="flex-item-shrink-0">意见：</span>
                <span>{{ item.maintenanceMsg }}</span>
              </div>
              <div class="img-list" v-if="item.fileList&&item.fileList.length > 0">
                <el-image class="marg-r-10 marg-t-10" v-for="(img, index) in item.fileList" :key="index" :src="img" :preview-src-list="item.fileList" :initial-index="index" show-progress :preview-teleported="true" style="width: 100px; height: 100px" />
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive, watch } from "vue";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      {
      }
    },
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};

//处理记录
const stepList = ref([]);
const handleProcessList = [
  // {
  //   createTime: '2018-04-15 10:10:10',
  //   typeValue: '1',
  //   typeName: '报警研判',
  //   clr: '处理人',
  //   remark: '设备故障，需要处理',
  //   handleResult: '报警处置',
  //   imagesList:['/static/images/construction/zh.png','/static/images/router/router.png','/static/images/emergency.png'],
  // },
  // {
  //   createTime: '2018-04-13',
  //   typeValue: '2',
  //   typeName: '故障维修',
  //   clr: '处理人',
  //   remark: '备注',
  // },
  // {
  //   createTime: '2018-04-11',
  //   typeValue: '3',
  //   typeName: '处置办结',
  //   clr: '处理人',
  //   remark: '备注',
  // },
]

watch(()=>props.data, (val) => {
  if (val) {
    let list  = val?.stepList?JSON.parse(JSON.stringify(val?.stepList)):null;
    if(list&& list.length>0){
      list.forEach(item=>{
        let imgList = []
        if(item.fileList.length>0){
          for(let i=0;i<item.fileList.length;i++){
            imgList.push( item.fileList[i].fileInfo.path)
          }
          item.fileList = imgList;
        }
      })
    }
    stepList.value = list;
    console.log(list);
  }
}, {
  immediate: true,
  deep: true,
})
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
