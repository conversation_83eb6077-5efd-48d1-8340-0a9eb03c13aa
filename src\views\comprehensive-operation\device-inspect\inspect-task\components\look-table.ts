export class Columns {
  get columns() {
    return [
      // {
      //   title: "",
      //   align: "center",
      //   type: "selection",
      //   width: 60,
      // },
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "检查项",
        key: "deviceName",
        align: "center",
      },
      {
        title: "设备状态",
        key: "status",
        slot: "status",
        align: "center",
      },
      {
        title: "巡检人",
        key: "termUserName",
        align: "center",
      },
      {
        title: "巡检时间",
        key: "updateTime",
        align: "center",
      },
      {
        title: "是否养护",
        key: "maintainStatus",
        slot: "maintainStatus",
        align: "center",
      },
      {
        title: "是否维修",
        key: "dealStatus",
        slot: "dealStatus",
        align: "center",
      },
      {
        title: "隐患上报",
        key: "dangerStatus",
        slot: "dangerStatus",
        align: "center",
      },
      // {
      //   title: "维修数",
      //   key: "wxNum",
      //   align: "center",
      // },
      // {
      //   title: "养护数",
      //   key: "hyNum",
      //   align: "center",
      // },
      // {
      //   title: "创建时间",
      //   key: "createTime",
      //   align: "center",
      // },
      // {
      //   title: "操作",
      //   slot: "operations",
      //   align: "center",
      //   width: 160,
      // },
    ];
  }
}
