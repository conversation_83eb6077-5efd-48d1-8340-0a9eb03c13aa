import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';


/* 巡检项配置页面 */

// 巡检项配置-获取设备列表
export function getDeviceOptions(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentInfo/getSelectList',
    method: 'get',
    params: {...query}
  })
}

// 巡检项配置-新增
export function addInspectItem(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tInspectionItems/addTInspectionItems',
    method: 'post',
    data: data
  })
}

// 巡检项配置-修改
export function updateInspectItem(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tInspectionItems/editTInspectionItems',
    method: 'post',
    data: data
  })
}

// 巡检项配置-删除
export function delInspectItems(delIds: any) {
  return request({
    url: baseRouter + apiPrefix + '/tInspectionItems/deleteByIds',
    method: 'delete',
    params: { ids: delIds },
  })
}

// 巡检项配置-列表
export function getInspectItemList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tInspectionItems/byPage',
    method: 'post',
    data: {...query}
  })
}

// 巡检项配置-详情
export function getInspectItemInfo(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tInspectionItems/getInfo',
    method: 'get',
    params: {...query}
  })
}










