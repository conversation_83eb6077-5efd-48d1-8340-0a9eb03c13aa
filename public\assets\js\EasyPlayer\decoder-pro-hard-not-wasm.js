!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";var e=1e-6,t="undefined"!=typeof Float32Array?Float32Array:Array;function i(){var e=new t(16);return t!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0),e[0]=1,e[5]=1,e[10]=1,e[15]=1,e}function r(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=1,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=1,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e}Math.hypot||(Math.hypot=function(){for(var e=0,t=arguments.length;t--;)e+=arguments[t]*arguments[t];return Math.sqrt(e)});var s,n=function(e,t,i,r,s,n,a){var o=1/(t-i),d=1/(r-s),l=1/(n-a);return e[0]=-2*o,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=-2*d,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=2*l,e[11]=0,e[12]=(t+i)*o,e[13]=(s+r)*d,e[14]=(a+n)*l,e[15]=1,e};function a(e,i,r){var s=new t(3);return s[0]=e,s[1]=i,s[2]=r,s}s=new t(3),t!=Float32Array&&(s[0]=0,s[1]=0,s[2]=0);var o=(t,s)=>{s&&t.pixelStorei(t.UNPACK_ALIGNMENT,1);const o=function(){const e=m(t.VERTEX_SHADER,"\n            attribute vec4 aVertexPosition;\n            attribute vec2 aTexturePosition;\n            uniform mat4 uModelMatrix;\n            uniform mat4 uViewMatrix;\n            uniform mat4 uProjectionMatrix;\n            varying lowp vec2 vTexturePosition;\n            void main(void) {\n              gl_Position = uProjectionMatrix * uViewMatrix * uModelMatrix * aVertexPosition;\n              vTexturePosition = aTexturePosition;\n            }\n        "),i=m(t.FRAGMENT_SHADER,"\n            precision highp float;\n            varying highp vec2 vTexturePosition;\n            uniform int isyuv;\n            uniform sampler2D rgbaTexture;\n            uniform sampler2D yTexture;\n            uniform sampler2D uTexture;\n            uniform sampler2D vTexture;\n\n            const mat4 YUV2RGB = mat4( 1.1643828125, 0, 1.59602734375, -.87078515625,\n                                       1.1643828125, -.39176171875, -.81296875, .52959375,\n                                       1.1643828125, 2.017234375, 0, -1.081390625,\n                                       0, 0, 0, 1);\n\n\n            void main(void) {\n\n                if (isyuv>0) {\n\n                    highp float y = texture2D(yTexture,  vTexturePosition).r;\n                    highp float u = texture2D(uTexture,  vTexturePosition).r;\n                    highp float v = texture2D(vTexture,  vTexturePosition).r;\n                    gl_FragColor = vec4(y, u, v, 1) * YUV2RGB;\n\n                } else {\n                    gl_FragColor =  texture2D(rgbaTexture, vTexturePosition);\n                }\n            }\n        "),r=t.createProgram();if(t.attachShader(r,e),t.attachShader(r,i),t.linkProgram(r),!t.getProgramParameter(r,t.LINK_STATUS))return console.log("Unable to initialize the shader program: "+t.getProgramInfoLog(r)),null;return r}();let d={program:o,attribLocations:{vertexPosition:t.getAttribLocation(o,"aVertexPosition"),texturePosition:t.getAttribLocation(o,"aTexturePosition")},uniformLocations:{projectionMatrix:t.getUniformLocation(o,"uProjectionMatrix"),modelMatrix:t.getUniformLocation(o,"uModelMatrix"),viewMatrix:t.getUniformLocation(o,"uViewMatrix"),rgbatexture:t.getUniformLocation(o,"rgbaTexture"),ytexture:t.getUniformLocation(o,"yTexture"),utexture:t.getUniformLocation(o,"uTexture"),vtexture:t.getUniformLocation(o,"vTexture"),isyuv:t.getUniformLocation(o,"isyuv")}},l=function(){const e=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,e);t.bufferData(t.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,-1,-1,1,1,-1,-1,1,-1]),t.STATIC_DRAW);var i=[];i=i.concat([0,1],[1,1],[1,0],[0,0]);const r=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,r),t.bufferData(t.ARRAY_BUFFER,new Float32Array(i),t.STATIC_DRAW);const s=t.createBuffer();t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,s);return t.bufferData(t.ELEMENT_ARRAY_BUFFER,new Uint16Array([0,1,2,0,2,3]),t.STATIC_DRAW),{position:e,texPosition:r,indices:s}}(),h=c(),f=c(),p=c(),u=c();function c(){let e=t.createTexture();return t.bindTexture(t.TEXTURE_2D,e),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),e}function m(e,i){const r=t.createShader(e);return t.shaderSource(r,i),t.compileShader(r),t.getShaderParameter(r,t.COMPILE_STATUS)?r:(console.log("An error occurred compiling the shaders: "+t.getShaderInfoLog(r)),t.deleteShader(r),null)}function _(s,o){t.viewport(0,0,s,o),t.clearColor(0,0,0,0),t.clearDepth(1),t.enable(t.DEPTH_TEST),t.depthFunc(t.LEQUAL),t.clear(t.COLOR_BUFFER_BIT|t.DEPTH_BUFFER_BIT);const h=i();n(h,-1,1,-1,1,.1,100);const c=i();r(c);const m=i();!function(t,i,s,n){var a,o,d,l,h,f,p,u,c,m,_=i[0],g=i[1],y=i[2],b=n[0],v=n[1],w=n[2],S=s[0],U=s[1],x=s[2];Math.abs(_-S)<e&&Math.abs(g-U)<e&&Math.abs(y-x)<e?r(t):(p=_-S,u=g-U,c=y-x,a=v*(c*=m=1/Math.hypot(p,u,c))-w*(u*=m),o=w*(p*=m)-b*c,d=b*u-v*p,(m=Math.hypot(a,o,d))?(a*=m=1/m,o*=m,d*=m):(a=0,o=0,d=0),l=u*d-c*o,h=c*a-p*d,f=p*o-u*a,(m=Math.hypot(l,h,f))?(l*=m=1/m,h*=m,f*=m):(l=0,h=0,f=0),t[0]=a,t[1]=l,t[2]=p,t[3]=0,t[4]=o,t[5]=h,t[6]=u,t[7]=0,t[8]=d,t[9]=f,t[10]=c,t[11]=0,t[12]=-(a*_+o*g+d*y),t[13]=-(l*_+h*g+f*y),t[14]=-(p*_+u*g+c*y),t[15]=1)}(m,a(0,0,0),a(0,0,-1),a(0,1,0));{const e=3,i=t.FLOAT,r=!1,s=0,n=0;t.bindBuffer(t.ARRAY_BUFFER,l.position),t.vertexAttribPointer(d.attribLocations.vertexPosition,e,i,r,s,n),t.enableVertexAttribArray(d.attribLocations.vertexPosition)}{const e=2,i=t.FLOAT,r=!1,s=0,n=0;t.bindBuffer(t.ARRAY_BUFFER,l.texPosition),t.vertexAttribPointer(d.attribLocations.texturePosition,e,i,r,s,n),t.enableVertexAttribArray(d.attribLocations.texturePosition)}t.activeTexture(t.TEXTURE0+3),t.bindTexture(t.TEXTURE_2D,f),t.activeTexture(t.TEXTURE0+4),t.bindTexture(t.TEXTURE_2D,p),t.activeTexture(t.TEXTURE0+5),t.bindTexture(t.TEXTURE_2D,u),t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,l.indices),t.useProgram(d.program),t.uniformMatrix4fv(d.uniformLocations.projectionMatrix,!1,h),t.uniformMatrix4fv(d.uniformLocations.modelMatrix,!1,c),t.uniformMatrix4fv(d.uniformLocations.viewMatrix,!1,m),t.uniform1i(d.uniformLocations.rgbatexture,2),t.uniform1i(d.uniformLocations.ytexture,3),t.uniform1i(d.uniformLocations.utexture,4),t.uniform1i(d.uniformLocations.vtexture,5),t.uniform1i(d.uniformLocations.isyuv,1);{const e=6,i=t.UNSIGNED_SHORT,r=0;t.drawElements(t.TRIANGLES,e,i,r)}}return{render:function(e,i,r,s,n){t.activeTexture(t.TEXTURE0),t.bindTexture(t.TEXTURE_2D,f),t.texImage2D(t.TEXTURE_2D,0,t.LUMINANCE,e,i,0,t.LUMINANCE,t.UNSIGNED_BYTE,r),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,p),t.texImage2D(t.TEXTURE_2D,0,t.LUMINANCE,e/2,i/2,0,t.LUMINANCE,t.UNSIGNED_BYTE,s),t.activeTexture(t.TEXTURE2),t.bindTexture(t.TEXTURE_2D,u),t.texImage2D(t.TEXTURE_2D,0,t.LUMINANCE,e/2,i/2,0,t.LUMINANCE,t.UNSIGNED_BYTE,n),_(e,i)},renderYUV:function(e,i,r){let s=r.slice(0,e*i),n=r.slice(e*i,e*i*5/4),a=r.slice(e*i*5/4,e*i*3/2);t.activeTexture(t.TEXTURE0),t.bindTexture(t.TEXTURE_2D,f),t.texImage2D(t.TEXTURE_2D,0,t.LUMINANCE,e,i,0,t.LUMINANCE,t.UNSIGNED_BYTE,s),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,p),t.texImage2D(t.TEXTURE_2D,0,t.LUMINANCE,e/2,i/2,0,t.LUMINANCE,t.UNSIGNED_BYTE,n),t.activeTexture(t.TEXTURE2),t.bindTexture(t.TEXTURE_2D,u),t.texImage2D(t.TEXTURE_2D,0,t.LUMINANCE,e/2,i/2,0,t.LUMINANCE,t.UNSIGNED_BYTE,a),_(e,i)},destroy:function(){t.deleteProgram(d.program),t.deleteBuffer(l.position),t.deleteBuffer(l.texPosition),t.deleteBuffer(l.indices),t.deleteTexture(h),t.deleteTexture(f),t.deleteTexture(p),t.deleteTexture(u),d=null,l=null,h=null,f=null,p=null,u=null}}};const d=1,l=2,h="fetch",f="websocket",p="player",u="playbackTF",c="mp4",m="debug",_="warn",g=36e5,y={playType:p,container:"",videoBuffer:1e3,videoBufferDelay:1e3,networkDelay:1e4,isResize:!0,isFullResize:!1,isFlv:!1,isHls:!1,isFmp4:!1,isFmp4Private:!1,isWebrtc:!1,isWebrtcForZLM:!1,isWebrtcForSRS:!1,isWebrtcForOthers:!0,isNakedFlow:!1,isMpeg4:!1,isAliyunRtc:!1,isTs:!1,debug:!1,debugLevel:_,debugUuid:"",isMulti:!0,multiIndex:-1,hotKey:!1,loadingTimeout:10,heartTimeout:10,timeout:10,pageVisibilityHiddenTimeout:300,loadingTimeoutReplay:!0,heartTimeoutReplay:!0,loadingTimeoutReplayTimes:3,heartTimeoutReplayTimes:3,heartTimeoutReplayUseLastFrameShow:!0,replayUseLastFrameShow:!0,replayShowLoadingIcon:!1,supportDblclickFullscreen:!1,showBandwidth:!1,showPerformance:!1,mseCorrectTimeDuration:20,keepScreenOn:!0,isNotMute:!1,hasAudio:!0,hasVideo:!0,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1,ptz:!1,quality:!1,zoom:!1,close:!1,scale:!1,performance:!1,logSave:!1,aiFace:!1,aiObject:!1,aiOcclusion:!1,fullscreenFn:null,fullscreenExitFn:null,screenshotFn:null,playFn:null,pauseFn:null,recordFn:null,recordStopFn:null},extendOperateBtns:[],contextmenuBtns:[],watermarkConfig:{},controlAutoHide:!1,hasControl:!1,loadingIcon:!0,loadingIconStyle:{},loadingText:"",background:"",backgroundLoadingShow:!0,loadingBackground:"",loadingBackgroundWidth:0,loadingBackgroundHeight:0,decoder:"decoder-pro.js",decoderAudio:"decoder-pro-audio.js",decoderHard:"decoder-pro-hard.js",decoderHardNotWasm:"decoder-pro-hard-not-wasm.js",wasmMp4RecorderDecoder:"easyplayer-pro-mp4-recorder-decoder.js",decoderWASM:"",isDecoderUseCDN:!1,url:"",rotate:0,mirrorRotate:"none",aspectRatio:"default",playbackConfig:{playList:[],fps:"",showControl:!0,controlType:"normal",duration:0,startTime:"",showRateBtn:!1,rateConfig:[],showPrecision:"",showPrecisionBtn:!0,isCacheBeforeDecodeForFpsRender:!1,uiUsePlaybackPause:!1,isPlaybackPauseClearCache:!0,isUseFpsRender:!1,isUseLocalCalculateTime:!1,localOneFrameTimestamp:40,supportWheel:!1,useWCS:!1,useMSE:!1},qualityConfig:[],defaultStreamQuality:"",scaleConfig:["拉伸","缩放","正常"],forceNoOffscreen:!0,hiddenAutoPause:!1,protocol:l,demuxType:"flv",useWasm:!1,useMSE:!1,useWCS:!1,useSIMD:!0,useMThreading:!1,wcsUseVideoRender:!0,wcsUseWebgl2Render:!0,wasmUseVideoRender:!0,mseUseCanvasRender:!1,hlsUseCanvasRender:!1,webrtcUseCanvasRender:!1,useOffscreen:!1,useWebGPU:!1,mseDecodeErrorReplay:!0,wcsDecodeErrorReplay:!0,wasmDecodeErrorReplay:!0,simdDecodeErrorReplay:!0,simdDecodeErrorReplayType:"wasm",autoWasm:!0,decoderErrorAutoWasm:!0,hardDecodingNotSupportAutoWasm:!0,webglAlignmentErrorReplay:!0,webglContextLostErrorReplay:!0,openWebglAlignment:!1,syncAudioAndVideo:!1,syncAudioAndVideoDiff:500,playbackDelayTime:1e3,playbackFps:25,playbackForwardMaxRateDecodeIFrame:4,playbackCurrentTimeMove:!0,useVideoRender:!0,useCanvasRender:!1,networkDelayTimeoutReplay:!1,recordType:c,checkFirstIFrame:!0,nakedFlowFps:25,audioEngine:null,isShowRecordingUI:!0,isShowZoomingUI:!0,useFaceDetector:!1,useObjectDetector:!1,useImageDetector:!1,useOcclusionDetector:!1,ptzClickType:"click",ptzStopEmitDelay:.3,ptzZoomShow:!1,ptzApertureShow:!1,ptzFocusShow:!1,ptzMoreArrowShow:!1,weiXinInAndroidAudioBufferSize:4800,isM7sCrypto:!1,m7sCryptoAudio:!1,isSm4Crypto:!1,isXorCrypto:!1,sm4CryptoKey:"",m7sCryptoKey:"",xorCryptoKey:"",cryptoKey:"",cryptoIV:"",cryptoKeyUrl:"",autoResize:!1,useWebFullScreen:!1,ptsMaxDiff:3600,aiFaceDetectLevel:2,aiFaceDetectWidth:240,aiFaceDetectShowRect:!0,aiFaceDetectInterval:1e3,aiFaceDetectRectConfig:{},aiObjectDetectLevel:2,aiObjectDetectWidth:240,aiObjectDetectShowRect:!0,aiObjectDetectInterval:1e3,aiObjectDetectRectConfig:{},aiOcclusionDetectInterval:1e3,aiImageDetectDrop:!1,aiImageDetectActive:!1,videoRenderSupportScale:!0,mediaSourceTsIsMaxDiffReplay:!0,controlHtml:"",isH265:!1,isWebrtcH265:!1,supportLockScreenPlayAudio:!0,supportHls265:!0,isEmitSEI:!1,pauseAndNextPlayUseLastFrameShow:!1,demuxUseWorker:!1,playFailedAndReplay:!0,showMessageConfig:{webglAlignmentError:"Webgl 渲染失败",webglContextLostError:"webgl 上下文丢失",mediaSourceH265NotSupport:"不支持硬解码H265",mediaSourceFull:"缓冲区已满",mediaSourceAppendBufferError:"初始化解码器失败",mseSourceBufferError:"解码失败",mseAddSourceBufferError:"初始化解码器失败",mediaSourceDecoderConfigurationError:"初始化解码器失败",mediaSourceTsIsMaxDiff:"流异常",mseWidthOrHeightChange:"流异常",mediaSourceAudioG711NotSupport:"硬解码不支持G711a/u音频格式",mediaSourceUseCanvasRenderPlayFailed:"MediaSource解码使用canvas渲染失败",webcodecsH265NotSupport:"不支持硬解码H265",webcodecsUnsupportedConfigurationError:"初始化解码器失败",webcodecsDecodeConfigureError:"初始化解码器失败",webcodecsDecodeError:"解码失败",wcsWidthOrHeightChange:"解码失败",wasmDecodeError:"解码失败",simdDecodeError:"解码失败",wasmWidthOrHeightChange:"流异常",wasmUseVideoRenderError:"video自动渲染失败",videoElementPlayingFailed:"video自动渲染失败",simdH264DecodeVideoWidthIsTooLarge:"不支持该分辨率的视频",networkDelayTimeout:"网络超时重播失败",fetchError:"请求失败",streamEnd:"请求结束",websocketError:"请求失败",webrtcError:"请求失败",hlsError:"请求失败",decoderWorkerInitError:"初始化worker失败",videoElementPlayingFailedForWebrtc:"video自动渲染失败",videoInfoError:"解析视频分辨率失败",webrtcStreamH265:"webrtc不支持H265",delayTimeout:"播放超时重播失败",loadingTimeout:"加载超时重播失败",loadingTimeoutRetryEnd:"加载超时重播失败",delayTimeoutRetryEnd:"播放超时重播失败"},videoElementPlayingFailedReplay:!0,mp4RecordUseWasm:!0,mseAutoCleanupSourceBuffer:!0,mseAutoCleanupMaxBackwardDuration:30,mseAutoCleanupMinBackwardDuration:10,widthOrHeightChangeReplay:!0,simdH264DecodeVideoWidthIsTooLargeReplay:!0,mediaSourceAudioG711NotSupportReplay:!0,mediaSourceAudioInitTimeoutReplay:!0,mediaSourceUseCanvasRenderPlayFailedReplay:!0,mediaSourceUseCanvasRenderPlayFailedReplayType:"video",widthOrHeightChangeReplayDelayTime:0,ghostWatermarkConfig:{on:5,off:5,content:"",fontSize:12,color:"white",opacity:.15,speed:.2},dynamicWatermarkConfig:{content:"",speed:.2,fontSize:12,color:"white",opacity:.15},isDropSameTimestampGop:!1,mseDecodeAudio:!1,nakedFlowH265DemuxUseNew:!0,extendDomConfig:{html:"",showBeforePlay:!1,showAfterLoading:!0},disableContextmenu:!1,websocket1006ErrorReplay:!1,websocket1006ErrorReplayDelayTime:0,mseDecoderUseWorker:!1},b="init",v="initVideo",w="render",S="playAudio",U="initAudio",x="audioCode",E="audioNalu",A="audioAACSequenceHeader",B="videoCode",T="videoCodec",k="videoNalu",C="videoPayload",D="audioPayload",I="workerFetch",F="iframeIntervalTs",L="isDropping",P="playbackStreamVideoFps",z="wasmWidthOrHeightChange",R="simdDecodeError",M="simdH264DecodeVideoWidthIsTooLarge",N="closeEnd",O="tempStream",G="videoSEI",H="flvScriptData",V="aacSequenceHeader",$="videoSequenceHeader",W="flvBufferData",Y="checkFirstIFrame",j=1,q=2,K=8,X=9,Z=18,J="init",Q="decode",ee="audioDecode",te="videoDecode",ie="close",re="updateConfig",se="clearBuffer",ne="fetchStream",ae="sendWsMessage",oe="streamEnd",de="streamRate",le="streamAbps",he="streamVbps",fe="streamDts",pe="streamSuccess",ue="streamStats",ce="networkDelayTimeout",me="websocketOpen",_e={playError:"playIsNotPauseOrUrlIsNull",fetchError:"fetchError",websocketError:"websocketError",webcodecsH265NotSupport:"webcodecsH265NotSupport",webcodecsDecodeError:"webcodecsDecodeError",webcodecsUnsupportedConfigurationError:"webcodecsUnsupportedConfigurationError",webcodecsDecodeConfigureError:"webcodecsDecodeConfigureError",mediaSourceH265NotSupport:"mediaSourceH265NotSupport",mediaSourceAudioG711NotSupport:"mediaSourceAudioG711NotSupport",mediaSourceAudioInitTimeout:"mediaSourceAudioInitTimeout",mediaSourceDecoderConfigurationError:"mediaSourceDecoderConfigurationError",mediaSourceFull:"mseSourceBufferFull",mseSourceBufferError:"mseSourceBufferError",mseAddSourceBufferError:"mseAddSourceBufferError",mediaSourceAppendBufferError:"mediaSourceAppendBufferError",mediaSourceTsIsMaxDiff:"mediaSourceTsIsMaxDiff",mediaSourceUseCanvasRenderPlayFailed:"mediaSourceUseCanvasRenderPlayFailed",mediaSourceBufferedIsZeroError:"mediaSourceBufferedIsZeroError",wasmDecodeError:"wasmDecodeError",wasmUseVideoRenderError:"wasmUseVideoRenderError",hlsError:"hlsError",webrtcError:"webrtcError",webrtcClosed:"webrtcClosed",webrtcIceCandidateError:"webrtcIceCandidateError",webglAlignmentError:"webglAlignmentError",wasmWidthOrHeightChange:"wasmWidthOrHeightChange",mseWidthOrHeightChange:"mseWidthOrHeightChange",wcsWidthOrHeightChange:"wcsWidthOrHeightChange",widthOrHeightChange:"widthOrHeightChange",tallWebsocketClosedByError:"tallWebsocketClosedByError",flvDemuxBufferSizeTooLarge:"flvDemuxBufferSizeTooLarge",wasmDecodeVideoNoResponseError:"wasmDecodeVideoNoResponseError",audioChannelError:"audioChannelError",simdH264DecodeVideoWidthIsTooLarge:"simdH264DecodeVideoWidthIsTooLarge",simdDecodeError:"simdDecodeError",webglContextLostError:"webglContextLostError",videoElementPlayingFailed:"videoElementPlayingFailed",videoElementPlayingFailedForWebrtc:"videoElementPlayingFailedForWebrtc",decoderWorkerInitError:"decoderWorkerInitError",videoInfoError:"videoInfoError",videoCodecIdError:"videoCodecIdError",streamEnd:oe,delayTimeout:"delayTimeout",loadingTimeout:"loadingTimeout",networkDelayTimeout:ce,aliyunRtcError:"aliyunRtcError",...{talkStreamError:"talkStreamError",talkStreamClose:"talkStreamClose"}},ge=1,ye=7,be=12,ve=99,we={h264:"H264(AVC)",h265:"H265(HEVC)"},Se={AAC:10,ALAW:7,MULAW:8,MP3:2},Ue={sps:7,pps:8,iFrame:5,kUnspecified:0,kSliceNonIDR:1,kSliceDPA:2,kSliceDPB:3,kSliceDPC:4,kSliceIDR:5,kSliceSEI:6,kSliceSPS:7,kSlicePPS:8,kSliceAUD:9,kEndOfSequence:10,kEndOfStream:11,kFiller:12,kSPSExt:13,kReserved0:14},xe={pFrame:1,iFrame:19,nLp:20,vps:32,sps:33,pps:34,sei:39,prefixSei:39,suffixSei:40},Ee="key",Ae="delta",Be={h264:"avc",h265:"hevc"},Te="AbortError",ke={sequenceHeader:0,nalu:1},Ce={keyFrame:1,interFrame:2},De=1,Ie="idle",Fe="buffering",Le="complete",Pe=0,ze=1,Re=3,Me=16,Ne=9e4,Oe=45e4;function Ge(e,t){return e(t={exports:{}},t.exports),t.exports}function He({profile:e,sampleRate:t,channel:i}){return new Uint8Array([175,0,e<<3|(14&t)>>1,(1&t)<<7|i<<3])}function Ve(e){return $e(e)&&e[1]===ke.sequenceHeader}function $e(e){return e[0]>>4===Se.AAC}Ge((function(e){!function(){var t="undefined"!=typeof window&&void 0!==window.document?window.document:{},i=e.exports,r=function(){for(var e,i=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],r=0,s=i.length,n={};r<s;r++)if((e=i[r])&&e[1]in t){for(r=0;r<e.length;r++)n[i[0][r]]=e[r];return n}return!1}(),s={change:r.fullscreenchange,error:r.fullscreenerror},n={request:function(e,i){return new Promise(function(s,n){var a=function(){this.off("change",a),s()}.bind(this);this.on("change",a);var o=(e=e||t.documentElement)[r.requestFullscreen](i);o instanceof Promise&&o.then(a).catch(n)}.bind(this))},exit:function(){return new Promise(function(e,i){if(this.isFullscreen){var s=function(){this.off("change",s),e()}.bind(this);this.on("change",s);var n=t[r.exitFullscreen]();n instanceof Promise&&n.then(s).catch(i)}else e()}.bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,i){var r=s[e];r&&t.addEventListener(r,i,!1)},off:function(e,i){var r=s[e];r&&t.removeEventListener(r,i,!1)},raw:r};r?(Object.defineProperties(n,{isFullscreen:{get:function(){return Boolean(t[r.fullscreenElement])}},element:{enumerable:!0,get:function(){return t[r.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(t[r.fullscreenEnabled])}}}),i?e.exports=n:window.screenfull=n):i?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}}()})).isEnabled;const We=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],Ye=We;function je(e,t){if("mp4a.40.2"===e){if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}}function qe(e,t=9e4){return 1024*t/e}function Ke(){return(new Date).getTime()}function Xe(){return performance&&"function"==typeof performance.now?performance.now():Date.now()}function Ze(e){let t=0,i=Xe();return r=>{if(s=r,"[object Number]"!==Object.prototype.toString.call(s))return;var s;t+=r;const n=Xe(),a=n-i;a>=1e3&&(e(t/a*1e3),i=n,t=0)}}function Je(){const e=window.navigator.userAgent;return!e.match(/Chrome/gi)&&!!e.match(/Safari/gi)}function Qe(e){return null==e}function et(e){e.close()}function tt(e,t){t&&(e=e.filter((e=>e.type&&e.type===t)));let i=e[0],r=null,s=1;if(e.length>0){let t=e[1];t&&t.ts-i.ts>1e5&&(i=t,s=2)}if(i)for(let n=s;n<e.length;n++){let s=e[n];if(t&&s.type&&s.type!==t&&(s=null),s){if(s.ts-i.ts>=1e3){e[n-1].ts-i.ts<1e3&&(r=n+1)}}}return r}function it(){return function(e){let t="";if("object"==typeof e)try{t=JSON.stringify(e),t=JSON.parse(t)}catch(i){t=e}else t=e;return t}(y)}function rt(e){return e[0]>>4===Ce.keyFrame&&e[1]===ke.sequenceHeader}function st(e){return!0===e||"true"===e}function nt(e){return!0!==e&&"true"!==e}(()=>{try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(e instanceof WebAssembly.Module)return new WebAssembly.Instance(e)instanceof WebAssembly.Instance}}catch(e){}})();var at=function(e,t,i,r){return new(i||(i=Promise))((function(s,n){function a(e){try{d(r.next(e))}catch(e){n(e)}}function o(e){try{d(r.throw(e))}catch(e){n(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,o)}d((r=r.apply(e,t||[])).next())}))};const ot=Symbol(32),dt=Symbol(16),lt=Symbol(8);class ht{constructor(e){this.g=e,this.consumed=0,e&&(this.need=e.next().value)}setG(e){this.g=e,this.demand(e.next().value,!0)}consume(){this.buffer&&this.consumed&&(this.buffer.copyWithin(0,this.consumed),this.buffer=this.buffer.subarray(0,this.buffer.length-this.consumed),this.consumed=0)}demand(e,t){return t&&this.consume(),this.need=e,this.flush()}read(e){return at(this,void 0,void 0,(function*(){return this.lastReadPromise&&(yield this.lastReadPromise),this.lastReadPromise=new Promise(((t,i)=>{var r;this.reject=i,this.resolve=e=>{delete this.lastReadPromise,delete this.resolve,delete this.need,t(e)};this.demand(e,!0)||null===(r=this.pull)||void 0===r||r.call(this,e)}))}))}readU32(){return this.read(ot)}readU16(){return this.read(dt)}readU8(){return this.read(lt)}close(){var e;this.g&&this.g.return(),this.buffer&&this.buffer.subarray(0,0),null===(e=this.reject)||void 0===e||e.call(this,new Error("EOF")),delete this.lastReadPromise}flush(){if(!this.buffer||!this.need)return;let e=null;const t=this.buffer.subarray(this.consumed);let i=0;const r=e=>t.length<(i=e);if("number"==typeof this.need){if(r(this.need))return;e=t.subarray(0,i)}else if(this.need===ot){if(r(4))return;e=t[0]<<24|t[1]<<16|t[2]<<8|t[3]}else if(this.need===dt){if(r(2))return;e=t[0]<<8|t[1]}else if(this.need===lt){if(r(1))return;e=t[0]}else if("buffer"in this.need){if("byteOffset"in this.need){if(r(this.need.byteLength-this.need.byteOffset))return;new Uint8Array(this.need.buffer,this.need.byteOffset).set(t.subarray(0,i)),e=this.need}else if(this.g)return void this.g.throw(new Error("Unsupported type"))}else{if(r(this.need.byteLength))return;new Uint8Array(this.need).set(t.subarray(0,i)),e=this.need}return this.consumed+=i,this.g?this.demand(this.g.next(e).value,!0):this.resolve&&this.resolve(e),e}write(e){if(e instanceof Uint8Array?this.malloc(e.length).set(e):"buffer"in e?this.malloc(e.byteLength).set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength)):this.malloc(e.byteLength).set(new Uint8Array(e)),!this.g&&!this.resolve)return new Promise((e=>this.pull=e));this.flush()}writeU32(e){this.malloc(4).set([e>>24&255,e>>16&255,e>>8&255,255&e]),this.flush()}writeU16(e){this.malloc(2).set([e>>8&255,255&e]),this.flush()}writeU8(e){this.malloc(1)[0]=e,this.flush()}malloc(e){if(this.buffer){const t=this.buffer.length,i=t+e;if(i<=this.buffer.buffer.byteLength-this.buffer.byteOffset)this.buffer=new Uint8Array(this.buffer.buffer,this.buffer.byteOffset,i);else{const e=new Uint8Array(i);e.set(this.buffer),this.buffer=e}return this.buffer.subarray(t,i)}return this.buffer=new Uint8Array(e),this.buffer}}ht.U32=ot,ht.U16=dt,ht.U8=lt;class ft{constructor(e){this.log=(t,...i)=>{if(e._opt.debug&&e._opt.debugLevel==m){const r=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";console.log(`EasyPro${r}[✅✅✅][${t}]`,...i)}},this.warn=(t,...i)=>{if(e._opt.debug&&(e._opt.debugLevel==m||e._opt.debugLevel==_)){const r=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";console.log(`EasyPro${r}[❗❗❗][${t}]`,...i)}},this.error=(t,...i)=>{const r=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";console.error(`EasyPro${r}[❌❌❌][${t}]`,...i)}}}class pt{constructor(e){this._buffer=e,this._buffer_index=0,this._total_bytes=e.byteLength,this._total_bits=8*e.byteLength,this._current_word=0,this._current_word_bits_left=0}destroy(){this._buffer=null}_fillCurrentWord(){let e=this._total_bytes-this._buffer_index;if(e<=0)return void console.error("ExpGolomb: _fillCurrentWord() but no bytes available",this._total_bytes,this._buffer_index);let t=Math.min(4,e),i=new Uint8Array(4);i.set(this._buffer.subarray(this._buffer_index,this._buffer_index+t)),this._current_word=new DataView(i.buffer).getUint32(0,!1),this._buffer_index+=t,this._current_word_bits_left=8*t}readBits(e){if(e>32&&console.error("ExpGolomb: readBits() bits exceeded max 32bits!"),e<=this._current_word_bits_left){let t=this._current_word>>>32-e;return this._current_word<<=e,this._current_word_bits_left-=e,t}let t=this._current_word_bits_left?this._current_word:0;t>>>=32-this._current_word_bits_left;let i=e-this._current_word_bits_left;this._fillCurrentWord();let r=Math.min(i,this._current_word_bits_left),s=this._current_word>>>32-r;return this._current_word<<=r,this._current_word_bits_left-=r,t=t<<r|s,t}readBool(){return 1===this.readBits(1)}readByte(){return this.readBits(8)}_skipLeadingZero(){let e;for(e=0;e<this._current_word_bits_left;e++)if(this._current_word&2147483648>>>e)return this._current_word<<=e,this._current_word_bits_left-=e,e;return this._fillCurrentWord(),e+this._skipLeadingZero()}readUEG(){let e=this._skipLeadingZero();return this.readBits(e+1)-1}readSEG(){let e=this.readUEG();return 1&e?e+1>>>1:-1*(e>>>1)}}class ut{static _ebsp2rbsp(e){let t=e,i=t.byteLength,r=new Uint8Array(i),s=0;for(let e=0;e<i;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(r[s]=t[e],s++);return new Uint8Array(r.buffer,0,s)}static parseSPS(e){let t=ut._ebsp2rbsp(e),i=new pt(t);i.readByte();let r=i.readByte();i.readByte();let s=i.readByte();i.readUEG();let n=ut.getProfileString(r),a=ut.getLevelString(s),o=1,d=420,l=[0,420,422,444],h=8;if((100===r||110===r||122===r||244===r||44===r||83===r||86===r||118===r||128===r||138===r||144===r)&&(o=i.readUEG(),3===o&&i.readBits(1),o<=3&&(d=l[o]),h=i.readUEG()+8,i.readUEG(),i.readBits(1),i.readBool())){let e=3!==o?8:12;for(let t=0;t<e;t++)i.readBool()&&(t<6?ut._skipScalingList(i,16):ut._skipScalingList(i,64))}i.readUEG();let f=i.readUEG();if(0===f)i.readUEG();else if(1===f){i.readBits(1),i.readSEG(),i.readSEG();let e=i.readUEG();for(let t=0;t<e;t++)i.readSEG()}let p=i.readUEG();i.readBits(1);let u=i.readUEG(),c=i.readUEG(),m=i.readBits(1);0===m&&i.readBits(1),i.readBits(1);let _=0,g=0,y=0,b=0;i.readBool()&&(_=i.readUEG(),g=i.readUEG(),y=i.readUEG(),b=i.readUEG());let v=1,w=1,S=0,U=!0,x=0,E=0;if(i.readBool()){if(i.readBool()){let e=i.readByte();e>0&&e<16?(v=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][e-1],w=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][e-1]):255===e&&(v=i.readByte()<<8|i.readByte(),w=i.readByte()<<8|i.readByte())}if(i.readBool()&&i.readBool(),i.readBool()&&(i.readBits(4),i.readBool()&&i.readBits(24)),i.readBool()&&(i.readUEG(),i.readUEG()),i.readBool()){let e=i.readBits(32),t=i.readBits(32);U=i.readBool(),x=t,E=2*e,S=x/E}}let A=1;1===v&&1===w||(A=v/w);let B=0,T=0;if(0===o)B=1,T=2-m;else{B=3===o?1:2,T=(1===o?2:1)*(2-m)}let k=16*(u+1),C=16*(c+1)*(2-m);k-=(_+g)*B,C-=(y+b)*T;let D=Math.ceil(k*A);return i.destroy(),i=null,{profile_string:n,level_string:a,bit_depth:h,ref_frames:p,chroma_format:d,chroma_format_string:ut.getChromaFormatString(d),frame_rate:{fixed:U,fps:S,fps_den:E,fps_num:x},sar_ratio:{width:v,height:w},codec_size:{width:k,height:C},present_size:{width:D,height:C}}}static parseSPS$2(e){let t=e.subarray(1,4),i="avc1.";for(let e=0;e<3;e++){let r=t[e].toString(16);r.length<2&&(r="0"+r),i+=r}let r=ut._ebsp2rbsp(e),s=new pt(r);s.readByte();let n=s.readByte();s.readByte();let a=s.readByte();s.readUEG();let o=ut.getProfileString(n),d=ut.getLevelString(a),l=1,h=420,f=[0,420,422,444],p=8,u=8;if((100===n||110===n||122===n||244===n||44===n||83===n||86===n||118===n||128===n||138===n||144===n)&&(l=s.readUEG(),3===l&&s.readBits(1),l<=3&&(h=f[l]),p=s.readUEG()+8,u=s.readUEG()+8,s.readBits(1),s.readBool())){let e=3!==l?8:12;for(let t=0;t<e;t++)s.readBool()&&(t<6?ut._skipScalingList(s,16):ut._skipScalingList(s,64))}s.readUEG();let c=s.readUEG();if(0===c)s.readUEG();else if(1===c){s.readBits(1),s.readSEG(),s.readSEG();let e=s.readUEG();for(let t=0;t<e;t++)s.readSEG()}let m=s.readUEG();s.readBits(1);let _=s.readUEG(),g=s.readUEG(),y=s.readBits(1);0===y&&s.readBits(1),s.readBits(1);let b=0,v=0,w=0,S=0;s.readBool()&&(b=s.readUEG(),v=s.readUEG(),w=s.readUEG(),S=s.readUEG());let U=1,x=1,E=0,A=!0,B=0,T=0;if(s.readBool()){if(s.readBool()){let e=s.readByte();e>0&&e<16?(U=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][e-1],x=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][e-1]):255===e&&(U=s.readByte()<<8|s.readByte(),x=s.readByte()<<8|s.readByte())}if(s.readBool()&&s.readBool(),s.readBool()&&(s.readBits(4),s.readBool()&&s.readBits(24)),s.readBool()&&(s.readUEG(),s.readUEG()),s.readBool()){let e=s.readBits(32),t=s.readBits(32);A=s.readBool(),B=t,T=2*e,E=B/T}}let k=1;1===U&&1===x||(k=U/x);let C=0,D=0;if(0===l)C=1,D=2-y;else{C=3===l?1:2,D=(1===l?2:1)*(2-y)}let I=16*(_+1),F=16*(g+1)*(2-y);I-=(b+v)*C,F-=(w+S)*D;let L=Math.ceil(I*k);return s.destroy(),s=null,{codec_mimetype:i,profile_idc:n,level_idc:a,profile_string:o,level_string:d,chroma_format_idc:l,bit_depth:p,bit_depth_luma:p,bit_depth_chroma:u,ref_frames:m,chroma_format:h,chroma_format_string:ut.getChromaFormatString(h),frame_rate:{fixed:A,fps:E,fps_den:T,fps_num:B},sar_ratio:{width:U,height:x},codec_size:{width:I,height:F},present_size:{width:L,height:F}}}static _skipScalingList(e,t){let i=8,r=8,s=0;for(let n=0;n<t;n++)0!==r&&(s=e.readSEG(),r=(i+s+256)%256),i=0===r?i:r}static getProfileString(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}}static getLevelString(e){return(e/10).toFixed(1)}static getChromaFormatString(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}}}class ct{constructor(e){this.buffer=e,this.buflen=e.length,this.bufpos=0,this.bufoff=0,this.iserro=!1}read(e){let t=0,i=0;for(;e;){if(e<0||this.bufpos>=this.buflen)return this.iserro=!0,0;this.iserro=!1,i=this.bufoff+e>8?8-this.bufoff:e,t<<=i,t+=this.buffer[this.bufpos]>>8-this.bufoff-i&255>>8-i,this.bufoff+=i,e-=i,8==this.bufoff&&(this.bufpos++,this.bufoff=0)}return t}look(e){let t=this.bufpos,i=this.bufoff,r=this.read(e);return this.bufpos=t,this.bufoff=i,r}read_golomb(){let e;for(e=0;0===this.read(1)&&!this.iserro;e++);return(1<<e)+this.read(e)-1}}function mt(e){const t={},i=new DataView(e.buffer);let r=i.getUint8(0),s=i.getUint8(1);if(i.getUint8(2),i.getUint8(3),1!==r||0===s)return{};const n=1+(3&i.getUint8(4));if(3!==n&&4!==n)return{};let a=31&i.getUint8(5);if(0===a)return{};let o=6;for(let r=0;r<a;r++){let s=i.getUint16(o,!1);if(o+=2,0===s)continue;let n=new Uint8Array(e.buffer,o,s);o+=s;let a=ut.parseSPS(n);if(0!==r)continue;t.sps=n,t.timescale=1e3,t.codecWidth=a.codec_size.width,t.codecHeight=a.codec_size.height,t.presentWidth=a.present_size.width,t.presentHeight=a.present_size.height,t.profile=a.profile_string,t.level=a.level_string,t.bitDepth=a.bit_depth,t.chromaFormat=a.chroma_format,t.sarRatio=a.sar_ratio,t.frameRate=a.frame_rate,!1!==a.frame_rate.fixed&&0!==a.frame_rate.fps_num&&0!==a.frame_rate.fps_den||(t.frameRate={fixed:!0,fps:25,fps_num:25e3,fps_den:1e3});let d=t.frameRate.fps_den,l=t.frameRate.fps_num;t.refSampleDuration=t.timescale*(d/l);let h=n.subarray(1,4),f="avc1.";for(let e=0;e<3;e++){let t=h[e].toString(16);t.length<2&&(t="0"+t),f+=t}t.codec=f}let d=i.getUint8(o);if(0===d)return{};o++;for(let r=0;r<d;r++){let r=i.getUint16(o,!1);if(o+=2,0===r)continue;let s=new Uint8Array(e.buffer,o,r);o+=r,t.pps=s}if(t.videoType="avc",t.sps){const e=t.sps.byteLength,i=new Uint8Array([e>>>24&255,e>>>16&255,e>>>8&255,255&e]),r=new Uint8Array(e+4);r.set(i,0),r.set(t.sps,4),t.sps=r}if(t.pps){const e=t.pps.byteLength,i=new Uint8Array([e>>>24&255,e>>>16&255,e>>>8&255,255&e]),r=new Uint8Array(e+4);r.set(i,0),r.set(t.pps,4),t.pps=r}return t}function _t({sps:e,pps:t}){const i=[23,0,0,0,0,1,66,0,30,255];i[0]=23,i[6]=e[1],i[7]=e[2],i[8]=e[3],i[10]=225,i[11]=e.byteLength>>8&255,i[12]=255&e.byteLength,i.push(...e,1,t.byteLength>>8&255,255&t.byteLength,...t);return new Uint8Array(i)}function gt(e,t){let i=[];i[0]=t?23:39,i[1]=1,i[2]=0,i[3]=0,i[4]=0;const r=new Uint8Array(i.length+e.byteLength);return r.set(i,0),r.set(e,i.length),r}function yt(e){return 31&e[0]}function bt(e){return e===Ue.kSliceSEI}function vt(e){return!function(e){return e===Ue.sps||e===Ue.pps}(e)&&!bt(e)}function wt(e){return e===Ue.iFrame}const St=e=>{let t=e,i=t.byteLength,r=new Uint8Array(i),s=0;for(let e=0;e<i;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(r[s]=t[e],s++);return new Uint8Array(r.buffer,0,s)},Ut=e=>{switch(e){case 0:return"4:0:0";case 1:return"4:2:0";case 2:return"4:2:2";case 3:return"4:4:4";default:return"Unknown"}},xt=e=>{let t=St(e),i=new pt(t);i.readByte(),i.readByte();let r=0,s=0,n=0,a=0;i.readBits(4);let o=i.readBits(3);i.readBool();let d=i.readBits(2),l=i.readBool(),h=i.readBits(5),f=i.readByte(),p=i.readByte(),u=i.readByte(),c=i.readByte(),m=i.readByte(),_=i.readByte(),g=i.readByte(),y=i.readByte(),b=i.readByte(),v=i.readByte(),w=i.readByte(),S=[],U=[];for(let e=0;e<o;e++)S.push(i.readBool()),U.push(i.readBool());if(o>0)for(let e=o;e<8;e++)i.readBits(2);for(let e=0;e<o;e++)S[e]&&(i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte()),S[e]&&i.readByte();i.readUEG();let x=i.readUEG();3==x&&i.readBits(1);let E=i.readUEG(),A=i.readUEG();i.readBool()&&(r+=i.readUEG(),s+=i.readUEG(),n+=i.readUEG(),a+=i.readUEG());let B=i.readUEG(),T=i.readUEG(),k=i.readUEG();for(let e=i.readBool()?0:o;e<=o;e++)i.readUEG(),i.readUEG(),i.readUEG();if(i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG(),i.readBool()){if(i.readBool())for(let e=0;e<4;e++)for(let t=0;t<(3===e?2:6);t++){if(i.readBool()){let t=Math.min(64,1<<4+(e<<1));e>1&&i.readSEG();for(let e=0;e<t;e++)i.readSEG()}else i.readUEG()}}i.readBool(),i.readBool(),i.readBool()&&(i.readByte(),i.readUEG(),i.readUEG(),i.readBool());let C=i.readUEG(),D=0;for(let e=0;e<C;e++){let t=!1;if(0!==e&&(t=i.readBool()),t){e===C&&i.readUEG(),i.readBool(),i.readUEG();let t=0;for(let e=0;e<=D;e++){let e=i.readBool(),r=!1;e||(r=i.readBool()),(e||r)&&t++}D=t}else{let e=i.readUEG(),t=i.readUEG();D=e+t;for(let t=0;t<e;t++)i.readUEG(),i.readBool();for(let e=0;e<t;e++)i.readUEG(),i.readBool()}}if(i.readBool()){let e=i.readUEG();for(let t=0;t<e;t++){for(let e=0;e<k+4;e++)i.readBits(1);i.readBits(1)}}let I=!1,F=0,L=1,P=1,z=!1,R=1,M=1;if(i.readBool(),i.readBool(),i.readBool()){if(i.readBool()){let e=i.readByte();e>0&&e<16?(L=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][e-1],P=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][e-1]):255===e&&(L=i.readBits(16),P=i.readBits(16))}if(i.readBool()&&i.readBool(),i.readBool()){i.readBits(3),i.readBool(),i.readBool()&&(i.readByte(),i.readByte(),i.readByte())}if(i.readBool()&&(i.readUEG(),i.readUEG()),i.readBool(),i.readBool(),i.readBool(),I=i.readBool(),I&&(r+=i.readUEG(),s+=i.readUEG(),n+=i.readUEG(),a+=i.readUEG()),i.readBool()){if(R=i.readBits(32),M=i.readBits(32),i.readBool()){if(i.readUEG(),i.readBool()){let e=!1,t=!1,r=!1;e=i.readBool(),t=i.readBool(),(e||t)&&(r=i.readBool(),r&&(i.readByte(),i.readBits(5),i.readBool(),i.readBits(5)),i.readBits(4),i.readBits(4),r&&i.readBits(4),i.readBits(5),i.readBits(5),i.readBits(5));for(let s=0;s<=o;s++){let s=i.readBool();z=s;let n=!1,a=1;s||(n=i.readBool());let o=!1;if(n?i.readSEG():o=i.readBool(),o||(cpbcnt=i.readUEG()+1),e)for(let e=0;e<a;e++)i.readUEG(),i.readUEG(),r&&(i.readUEG(),i.readUEG());if(t)for(let e=0;e<a;e++)i.readUEG(),i.readUEG(),r&&(i.readUEG(),i.readUEG())}}}}i.readBool()&&(i.readBool(),i.readBool(),i.readBool(),F=i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG())}i.readBool();let N=`hvc1.${h}.1.L${w}.B0`,O=E,G=A,H=1;return 1!==L&&1!==P&&(H=L/P),i.destroy(),i=null,{codec_mimetype:N,level_string:(V=w,(V/30).toFixed(1)),profile_idc:h,bit_depth:B+8,ref_frames:1,chroma_format:x,chroma_format_string:Ut(x),general_level_idc:w,general_profile_space:d,general_tier_flag:l,general_profile_idc:h,general_profile_compatibility_flags_1:f,general_profile_compatibility_flags_2:p,general_profile_compatibility_flags_3:u,general_profile_compatibility_flags_4:c,general_constraint_indicator_flags_1:m,general_constraint_indicator_flags_2:_,general_constraint_indicator_flags_3:g,general_constraint_indicator_flags_4:y,general_constraint_indicator_flags_5:b,general_constraint_indicator_flags_6:v,min_spatial_segmentation_idc:F,constant_frame_rate:0,chroma_format_idc:x,bit_depth_luma_minus8:B,bit_depth_chroma_minus8:T,frame_rate:{fixed:z,fps:M/R,fps_den:R,fps_num:M},sar_ratio:{width:L,height:P},codec_size:{width:O,height:G},present_size:{width:O*H,height:G}};var V},Et=e=>{let t=St(e),i=new pt(t);return i.readByte(),i.readByte(),i.readBits(4),i.readBits(2),i.readBits(6),{num_temporal_layers:i.readBits(3)+1,temporal_id_nested:i.readBool()}},At=e=>{let t=St(e),i=new pt(t);i.readByte(),i.readByte(),i.readUEG(),i.readUEG(),i.readBool(),i.readBool(),i.readBits(3),i.readBool(),i.readBool(),i.readUEG(),i.readUEG(),i.readSEG(),i.readBool(),i.readBool(),i.readBool()&&i.readUEG(),i.readSEG(),i.readSEG(),i.readBool(),i.readBool(),i.readBool(),i.readBool();let r=i.readBool(),s=i.readBool(),n=1;return s&&r?n=0:s?n=3:r&&(n=2),{parallelismType:n}};function Bt(e,t=0){return(e[t]<<24>>>0)+(e[t+1]<<16)+(e[t+2]<<8)+(e[t+3]||0)}function Tt(e,t=4){if(e.length<4)return;const i=e.length,r=[];let s,n=0;for(;n+t<i;)if(s=Bt(e,n),3===t&&(s>>>=8),n+=t,s){if(n+s>i)break;r.push(e.subarray(n,n+s)),n+=s}return r}function kt(e){const t=e.byteLength,i=new Uint8Array(4);i[0]=t>>>24&255,i[1]=t>>>16&255,i[2]=t>>>8&255,i[3]=255&t;const r=new Uint8Array(t+4);return r.set(i,0),r.set(e,4),r}function Ct(e,t){let i=null;return t?e.length>=28&&(i=1+(3&e[26])):e.length>=12&&(i=1+(3&e[9])),i}function Dt(e,t){let i={},r=e.length,s=[],n=new ct(e);n.read(1),n.read(6),n.read(6),n.read(3);for(let e=2;e<r;e++)e+2<r&&3==n.look(24)?(s.push(n.read(8)),s.push(n.read(8)),e+=2,n.read(8)):s.push(n.read(8));let a=new Uint8Array(s),o=new ct(a);if(i.sps_video_parameter_set_id=o.read(4),i.sps_max_sub_layers_minus1=o.read(3),i.sps_temporal_id_nesting_flag=o.read(1),i.profile_tier_level=function(e,t,i){let r={};r.profile_space=e.read(2),r.tier_flag=e.read(1),r.profile_idc=e.read(5),r.profile_compatibility_flags=e.read(32),r.general_progressive_source_flag=e.read(1),r.general_interlaced_source_flag=e.read(1),r.general_non_packed_constraint_flag=e.read(1),r.general_frame_only_constraint_flag=e.read(1),e.read(32),e.read(12),r.level_idc=e.read(8),r.sub_layer_profile_present_flag=[],r.sub_layer_level_present_flag=[];for(let t=0;t<i;t++)r.sub_layer_profile_present_flag[t]=e.read(1),r.sub_layer_level_present_flag[t]=e.read(1);if(i>0)for(let t=i;t<8;t++)e.read(2);r.sub_layer_profile_space=[],r.sub_layer_tier_flag=[],r.sub_layer_profile_idc=[],r.sub_layer_profile_compatibility_flag=[],r.sub_layer_progressive_source_flag=[],r.sub_layer_interlaced_source_flag=[],r.sub_layer_non_packed_constraint_flag=[],r.sub_layer_frame_only_constraint_flag=[],r.sub_layer_level_idc=[];for(let t=0;t<i;t++)r.sub_layer_profile_present_flag[t]&&(r.sub_layer_profile_space[t]=e.read(2),r.sub_layer_tier_flag[t]=e.read(1),r.sub_layer_profile_idc[t]=e.read(5),r.sub_layer_profile_compatibility_flag[t]=e.read(32),r.sub_layer_progressive_source_flag[t]=e.read(1),r.sub_layer_interlaced_source_flag[t]=e.read(1),r.sub_layer_non_packed_constraint_flag[t]=e.read(1),r.sub_layer_frame_only_constraint_flag[t]=e.read(1),e.read(32),e.read(12)),r.sub_layer_level_present_flag[t]?r.sub_layer_level_idc[t]=e.read(8):r.sub_layer_level_idc[t]=1;return r}(o,0,i.sps_max_sub_layers_minus1),i.sps_seq_parameter_set_id=o.read_golomb(),i.chroma_format_idc=o.read_golomb(),3==i.chroma_format_idc?i.separate_colour_plane_flag=o.read(1):i.separate_colour_plane_flag=0,i.pic_width_in_luma_samples=o.read_golomb(),i.pic_height_in_luma_samples=o.read_golomb(),i.conformance_window_flag=o.read(1),i.conformance_window_flag){let e=1+(i.chroma_format_idc<2),t=1+(i.chroma_format_idc<3);i.conf_win_left_offset=o.read_golomb()*t,i.conf_win_right_offset=o.read_golomb()*t,i.conf_win_top_offset=o.read_golomb()*e,i.conf_win_bottom_offset=o.read_golomb()*e}else i.conf_win_left_offset=0,i.conf_win_right_offset=0,i.conf_win_top_offset=0,i.conf_win_bottom_offset=0;return i}function It({vps:e,pps:t,sps:i}){let r={configurationVersion:1};const s=Et(e),n=xt(i),a=At(t);r=Object.assign(r,s,n,a);let o=23+(5+e.byteLength)+(5+i.byteLength)+(5+t.byteLength),d=new Uint8Array(o);d[0]=1,d[1]=(3&r.general_profile_space)<<6|(r.general_tier_flag?1:0)<<5|31&r.general_profile_idc,d[2]=r.general_profile_compatibility_flags_1||0,d[3]=r.general_profile_compatibility_flags_2||0,d[4]=r.general_profile_compatibility_flags_3||0,d[5]=r.general_profile_compatibility_flags_4||0,d[6]=r.general_constraint_indicator_flags_1||0,d[7]=r.general_constraint_indicator_flags_2||0,d[8]=r.general_constraint_indicator_flags_3||0,d[9]=r.general_constraint_indicator_flags_4||0,d[10]=r.general_constraint_indicator_flags_5||0,d[11]=r.general_constraint_indicator_flags_6||0,d[12]=60,d[13]=240|(3840&r.min_spatial_segmentation_idc)>>8,d[14]=255&r.min_spatial_segmentation_idc,d[15]=252|3&r.parallelismType,d[16]=252|3&r.chroma_format_idc,d[17]=248|7&r.bit_depth_luma_minus8,d[18]=248|7&r.bit_depth_chroma_minus8,d[19]=0,d[20]=0,d[21]=(3&r.constant_frame_rate)<<6|(7&r.num_temporal_layers)<<3|(r.temporal_id_nested?1:0)<<2|3,d[22]=3,d[23]=128|xe.vps,d[24]=0,d[25]=1,d[26]=(65280&e.byteLength)>>8,d[27]=255&e.byteLength,d.set(e,28),d[23+(5+e.byteLength)+0]=128|xe.sps,d[23+(5+e.byteLength)+1]=0,d[23+(5+e.byteLength)+2]=1,d[23+(5+e.byteLength)+3]=(65280&i.byteLength)>>8,d[23+(5+e.byteLength)+4]=255&i.byteLength,d.set(i,23+(5+e.byteLength)+5),d[23+(5+e.byteLength+5+i.byteLength)+0]=128|xe.pps,d[23+(5+e.byteLength+5+i.byteLength)+1]=0,d[23+(5+e.byteLength+5+i.byteLength)+2]=1,d[23+(5+e.byteLength+5+i.byteLength)+3]=(65280&t.byteLength)>>8,d[23+(5+e.byteLength+5+i.byteLength)+4]=255&t.byteLength,d.set(t,23+(5+e.byteLength+5+i.byteLength)+5);const l=[28,0,0,0,0],h=new Uint8Array(l.length+d.byteLength);return h.set(l,0),h.set(d,l.length),h}function Ft(e,t){let i=[];i[0]=t?28:44,i[1]=1,i[2]=0,i[3]=0,i[4]=0;const r=new Uint8Array(i.length+e.byteLength);return r.set(i,0),r.set(e,i.length),r}function Lt(e){return(126&e[0])>>1}function Pt(e){return!function(e){return e>=32&&e<=40}(e)}function zt(e){return e>=16&&e<=21}function Rt(e){return parseInt(e)===e}function Mt(e){if(!Rt(e.length))return!1;for(var t=0;t<e.length;t++)if(!Rt(e[t])||e[t]<0||e[t]>255)return!1;return!0}function Nt(e,t){if(e.buffer&&"Uint8Array"===e.name)return t&&(e=e.slice?e.slice():Array.prototype.slice.call(e)),e;if(Array.isArray(e)){if(!Mt(e))throw new Error("Array contains invalid value: "+e);return new Uint8Array(e)}if(Rt(e.length)&&Mt(e))return new Uint8Array(e);throw new Error("unsupported array-like object")}function Ot(e){return new Uint8Array(e)}function Gt(e,t,i,r,s){null==r&&null==s||(e=e.slice?e.slice(r,s):Array.prototype.slice.call(e,r,s)),t.set(e,i)}var Ht,Vt={toBytes:function(e){var t=[],i=0;for(e=encodeURI(e);i<e.length;){var r=e.charCodeAt(i++);37===r?(t.push(parseInt(e.substr(i,2),16)),i+=2):t.push(r)}return Nt(t)},fromBytes:function(e){for(var t=[],i=0;i<e.length;){var r=e[i];r<128?(t.push(String.fromCharCode(r)),i++):r>191&&r<224?(t.push(String.fromCharCode((31&r)<<6|63&e[i+1])),i+=2):(t.push(String.fromCharCode((15&r)<<12|(63&e[i+1])<<6|63&e[i+2])),i+=3)}return t.join("")}},$t=(Ht="0123456789abcdef",{toBytes:function(e){for(var t=[],i=0;i<e.length;i+=2)t.push(parseInt(e.substr(i,2),16));return t},fromBytes:function(e){for(var t=[],i=0;i<e.length;i++){var r=e[i];t.push(Ht[(240&r)>>4]+Ht[15&r])}return t.join("")}}),Wt={16:10,24:12,32:14},Yt=[1,2,4,8,16,32,64,128,27,54,108,216,171,77,154,47,94,188,99,198,151,53,106,212,179,125,250,239,197,145],jt=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],qt=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],Kt=[3328402341,4168907908,4000806809,4135287693,4294111757,3597364157,3731845041,2445657428,1613770832,33620227,3462883241,1445669757,3892248089,3050821474,1303096294,3967186586,2412431941,528646813,2311702848,4202528135,4026202645,2992200171,2387036105,4226871307,1101901292,3017069671,1604494077,1169141738,597466303,1403299063,3832705686,2613100635,1974974402,3791519004,1033081774,1277568618,1815492186,2118074177,4126668546,2211236943,1748251740,1369810420,3521504564,4193382664,3799085459,2883115123,1647391059,706024767,134480908,2512897874,1176707941,2646852446,806885416,932615841,168101135,798661301,235341577,605164086,461406363,3756188221,3454790438,1311188841,2142417613,3933566367,302582043,495158174,1479289972,874125870,907746093,3698224818,3025820398,1537253627,2756858614,1983593293,3084310113,2108928974,1378429307,3722699582,1580150641,327451799,2790478837,3117535592,0,3253595436,1075847264,3825007647,2041688520,3059440621,3563743934,2378943302,1740553945,1916352843,2487896798,2555137236,2958579944,2244988746,3151024235,3320835882,1336584933,3992714006,2252555205,2588757463,1714631509,293963156,2319795663,3925473552,67240454,4269768577,2689618160,2017213508,631218106,1269344483,2723238387,1571005438,2151694528,93294474,1066570413,563977660,1882732616,4059428100,1673313503,2008463041,2950355573,1109467491,537923632,3858759450,4260623118,3218264685,2177748300,403442708,638784309,3287084079,3193921505,899127202,2286175436,773265209,2479146071,1437050866,4236148354,2050833735,3362022572,3126681063,840505643,3866325909,3227541664,427917720,2655997905,2749160575,1143087718,1412049534,999329963,193497219,2353415882,3354324521,1807268051,672404540,2816401017,3160301282,369822493,2916866934,3688947771,1681011286,1949973070,336202270,2454276571,201721354,1210328172,3093060836,2680341085,3184776046,1135389935,3294782118,965841320,831886756,3554993207,4068047243,3588745010,2345191491,1849112409,3664604599,26054028,2983581028,2622377682,1235855840,3630984372,2891339514,4092916743,3488279077,3395642799,4101667470,1202630377,268961816,1874508501,4034427016,1243948399,1546530418,941366308,1470539505,1941222599,2546386513,3421038627,2715671932,3899946140,1042226977,2521517021,1639824860,227249030,260737669,3765465232,2084453954,1907733956,3429263018,2420656344,100860677,4160157185,470683154,3261161891,1781871967,2924959737,1773779408,394692241,2579611992,974986535,664706745,3655459128,3958962195,731420851,571543859,3530123707,2849626480,126783113,865375399,765172662,1008606754,361203602,3387549984,2278477385,2857719295,1344809080,2782912378,59542671,1503764984,160008576,437062935,1707065306,3622233649,2218934982,3496503480,2185314755,697932208,1512910199,504303377,2075177163,2824099068,1841019862,739644986],Xt=[2781242211,2230877308,2582542199,2381740923,234877682,3184946027,2984144751,1418839493,1348481072,50462977,2848876391,2102799147,434634494,1656084439,3863849899,2599188086,1167051466,2636087938,1082771913,2281340285,368048890,3954334041,3381544775,201060592,3963727277,1739838676,4250903202,3930435503,3206782108,4149453988,2531553906,1536934080,3262494647,484572669,2923271059,1783375398,1517041206,1098792767,49674231,1334037708,1550332980,4098991525,886171109,150598129,2481090929,1940642008,1398944049,1059722517,201851908,1385547719,1699095331,1587397571,674240536,2704774806,252314885,3039795866,151914247,908333586,2602270848,1038082786,651029483,1766729511,3447698098,2682942837,454166793,2652734339,1951935532,775166490,758520603,3000790638,4004797018,4217086112,4137964114,1299594043,1639438038,3464344499,2068982057,1054729187,1901997871,2534638724,4121318227,1757008337,0,750906861,1614815264,535035132,3363418545,3988151131,3201591914,1183697867,3647454910,1265776953,3734260298,3566750796,3903871064,1250283471,1807470800,717615087,3847203498,384695291,3313910595,3617213773,1432761139,2484176261,3481945413,283769337,100925954,2180939647,4037038160,1148730428,3123027871,3813386408,4087501137,4267549603,3229630528,2315620239,2906624658,3156319645,1215313976,82966005,3747855548,3245848246,1974459098,1665278241,807407632,451280895,251524083,1841287890,1283575245,337120268,891687699,801369324,3787349855,2721421207,3431482436,959321879,1469301956,4065699751,2197585534,1199193405,2898814052,3887750493,724703513,2514908019,2696962144,2551808385,3516813135,2141445340,1715741218,2119445034,2872807568,2198571144,3398190662,700968686,3547052216,1009259540,2041044702,3803995742,487983883,1991105499,1004265696,1449407026,1316239930,504629770,3683797321,168560134,1816667172,3837287516,1570751170,1857934291,4014189740,2797888098,2822345105,2754712981,936633572,2347923833,852879335,1133234376,1500395319,3084545389,2348912013,1689376213,3533459022,3762923945,3034082412,4205598294,133428468,634383082,2949277029,2398386810,3913789102,403703816,3580869306,2297460856,1867130149,1918643758,607656988,4049053350,3346248884,1368901318,600565992,2090982877,2632479860,557719327,3717614411,3697393085,2249034635,2232388234,2430627952,1115438654,3295786421,2865522278,3633334344,84280067,33027830,303828494,2747425121,1600795957,4188952407,3496589753,2434238086,1486471617,658119965,3106381470,953803233,334231800,3005978776,857870609,3151128937,1890179545,2298973838,2805175444,3056442267,574365214,2450884487,550103529,1233637070,4289353045,2018519080,2057691103,2399374476,4166623649,2148108681,387583245,3664101311,836232934,3330556482,3100665960,3280093505,2955516313,2002398509,287182607,3413881008,4238890068,3597515707,975967766],Zt=[1671808611,2089089148,2006576759,2072901243,4061003762,1807603307,1873927791,3310653893,810573872,16974337,1739181671,729634347,4263110654,3613570519,2883997099,1989864566,3393556426,2191335298,3376449993,2106063485,4195741690,1508618841,1204391495,4027317232,2917941677,3563566036,2734514082,2951366063,2629772188,2767672228,1922491506,3227229120,3082974647,4246528509,2477669779,644500518,911895606,1061256767,4144166391,3427763148,878471220,2784252325,3845444069,4043897329,1905517169,3631459288,827548209,356461077,67897348,3344078279,593839651,3277757891,405286936,2527147926,84871685,2595565466,118033927,305538066,2157648768,3795705826,3945188843,661212711,2999812018,1973414517,152769033,2208177539,745822252,439235610,455947803,1857215598,1525593178,2700827552,1391895634,994932283,3596728278,3016654259,695947817,3812548067,795958831,2224493444,1408607827,3513301457,0,3979133421,543178784,4229948412,2982705585,1542305371,1790891114,3410398667,3201918910,961245753,1256100938,1289001036,1491644504,3477767631,3496721360,4012557807,2867154858,4212583931,1137018435,1305975373,861234739,2241073541,1171229253,4178635257,33948674,2139225727,1357946960,1011120188,2679776671,2833468328,1374921297,2751356323,1086357568,2408187279,2460827538,2646352285,944271416,4110742005,3168756668,3066132406,3665145818,560153121,271589392,4279952895,4077846003,3530407890,3444343245,202643468,322250259,3962553324,1608629855,2543990167,1154254916,389623319,3294073796,2817676711,2122513534,1028094525,1689045092,1575467613,422261273,1939203699,1621147744,2174228865,1339137615,3699352540,577127458,712922154,2427141008,2290289544,1187679302,3995715566,3100863416,339486740,3732514782,1591917662,186455563,3681988059,3762019296,844522546,978220090,169743370,1239126601,101321734,611076132,1558493276,3260915650,3547250131,2901361580,1655096418,2443721105,2510565781,3828863972,2039214713,3878868455,3359869896,928607799,1840765549,2374762893,3580146133,1322425422,2850048425,1823791212,1459268694,4094161908,3928346602,1706019429,2056189050,2934523822,135794696,3134549946,2022240376,628050469,779246638,472135708,2800834470,3032970164,3327236038,3894660072,3715932637,1956440180,522272287,1272813131,3185336765,2340818315,2323976074,1888542832,1044544574,3049550261,1722469478,1222152264,50660867,4127324150,236067854,1638122081,895445557,1475980887,3117443513,2257655686,3243809217,489110045,2662934430,3778599393,4162055160,2561878936,288563729,1773916777,3648039385,2391345038,2493985684,2612407707,505560094,2274497927,3911240169,3460925390,1442818645,678973480,3749357023,2358182796,2717407649,2306869641,219617805,3218761151,3862026214,1120306242,1756942440,1103331905,2578459033,762796589,252780047,2966125488,1425844308,3151392187,372911126],Jt=[1667474886,2088535288,2004326894,2071694838,4075949567,1802223062,1869591006,3318043793,808472672,16843522,1734846926,724270422,4278065639,3621216949,2880169549,1987484396,3402253711,2189597983,3385409673,2105378810,4210693615,1499065266,1195886990,4042263547,2913856577,3570689971,2728590687,2947541573,2627518243,2762274643,1920112356,3233831835,3082273397,4261223649,2475929149,640051788,909531756,1061110142,4160160501,3435941763,875846760,2779116625,3857003729,4059105529,1903268834,3638064043,825316194,353713962,67374088,3351728789,589522246,3284360861,404236336,2526454071,84217610,2593830191,117901582,303183396,2155911963,3806477791,3958056653,656894286,2998062463,1970642922,151591698,2206440989,741110872,437923380,454765878,1852748508,1515908788,2694904667,1381168804,993742198,3604373943,3014905469,690584402,3823320797,791638366,2223281939,1398011302,3520161977,0,3991743681,538992704,4244381667,2981218425,1532751286,1785380564,3419096717,3200178535,960056178,1246420628,1280103576,1482221744,3486468741,3503319995,4025428677,2863326543,4227536621,1128514950,1296947098,859002214,2240123921,1162203018,4193849577,33687044,2139062782,1347481760,1010582648,2678045221,2829640523,1364325282,2745433693,1077985408,2408548869,2459086143,2644360225,943212656,4126475505,3166494563,3065430391,3671750063,555836226,269496352,4294908645,4092792573,3537006015,3452783745,202118168,320025894,3974901699,1600119230,2543297077,1145359496,387397934,3301201811,2812801621,2122220284,1027426170,1684319432,1566435258,421079858,1936954854,1616945344,2172753945,1330631070,3705438115,572679748,707427924,2425400123,2290647819,1179044492,4008585671,3099120491,336870440,3739122087,1583276732,185277718,3688593069,3772791771,842159716,976899700,168435220,1229577106,101059084,606366792,1549591736,3267517855,3553849021,2897014595,1650632388,2442242105,2509612081,3840161747,2038008818,3890688725,3368567691,926374254,1835907034,2374863873,3587531953,1313788572,2846482505,1819063512,1448540844,4109633523,3941213647,1701162954,2054852340,2930698567,134748176,3132806511,2021165296,623210314,774795868,471606328,2795958615,3031746419,3334885783,3907527627,3722280097,1953799400,522133822,1263263126,3183336545,2341176845,2324333839,1886425312,1044267644,3048588401,1718004428,1212733584,50529542,4143317495,235803164,1633788866,892690282,1465383342,3115962473,2256965911,3250673817,488449850,2661202215,3789633753,4177007595,2560144171,286339874,1768537042,3654906025,2391705863,2492770099,2610673197,505291324,2273808917,3924369609,3469625735,1431699370,673740880,3755965093,2358021891,2711746649,2307489801,218961690,3217021541,3873845719,1111672452,1751693520,1094828930,2576986153,757954394,252645662,2964376443,1414855848,3149649517,370555436],Qt=[1374988112,2118214995,437757123,975658646,1001089995,530400753,2902087851,1273168787,540080725,2910219766,2295101073,4110568485,1340463100,3307916247,641025152,3043140495,3736164937,632953703,1172967064,1576976609,3274667266,2169303058,2370213795,1809054150,59727847,361929877,3211623147,2505202138,3569255213,1484005843,1239443753,2395588676,1975683434,4102977912,2572697195,666464733,3202437046,4035489047,3374361702,2110667444,1675577880,3843699074,2538681184,1649639237,2976151520,3144396420,4269907996,4178062228,1883793496,2403728665,2497604743,1383856311,2876494627,1917518562,3810496343,1716890410,3001755655,800440835,2261089178,3543599269,807962610,599762354,33778362,3977675356,2328828971,2809771154,4077384432,1315562145,1708848333,101039829,3509871135,3299278474,875451293,2733856160,92987698,2767645557,193195065,1080094634,1584504582,3178106961,1042385657,2531067453,3711829422,1306967366,2438237621,1908694277,67556463,1615861247,429456164,3602770327,2302690252,1742315127,2968011453,126454664,3877198648,2043211483,2709260871,2084704233,4169408201,0,159417987,841739592,504459436,1817866830,4245618683,260388950,1034867998,908933415,168810852,1750902305,2606453969,607530554,202008497,2472011535,3035535058,463180190,2160117071,1641816226,1517767529,470948374,3801332234,3231722213,1008918595,303765277,235474187,4069246893,766945465,337553864,1475418501,2943682380,4003061179,2743034109,4144047775,1551037884,1147550661,1543208500,2336434550,3408119516,3069049960,3102011747,3610369226,1113818384,328671808,2227573024,2236228733,3535486456,2935566865,3341394285,496906059,3702665459,226906860,2009195472,733156972,2842737049,294930682,1206477858,2835123396,2700099354,1451044056,573804783,2269728455,3644379585,2362090238,2564033334,2801107407,2776292904,3669462566,1068351396,742039012,1350078989,1784663195,1417561698,4136440770,2430122216,775550814,2193862645,2673705150,1775276924,1876241833,3475313331,3366754619,270040487,3902563182,3678124923,3441850377,1851332852,3969562369,2203032232,3868552805,2868897406,566021896,4011190502,3135740889,1248802510,3936291284,699432150,832877231,708780849,3332740144,899835584,1951317047,4236429990,3767586992,866637845,4043610186,1106041591,2144161806,395441711,1984812685,1139781709,3433712980,3835036895,2664543715,1282050075,3240894392,1181045119,2640243204,25965917,4203181171,4211818798,3009879386,2463879762,3910161971,1842759443,2597806476,933301370,1509430414,3943906441,3467192302,3076639029,3776767469,2051518780,2631065433,1441952575,404016761,1942435775,1408749034,1610459739,3745345300,2017778566,3400528769,3110650942,941896748,3265478751,371049330,3168937228,675039627,4279080257,967311729,135050206,3635733660,1683407248,2076935265,3576870512,1215061108,3501741890],ei=[1347548327,1400783205,3273267108,2520393566,3409685355,4045380933,2880240216,2471224067,1428173050,4138563181,2441661558,636813900,4233094615,3620022987,2149987652,2411029155,1239331162,1730525723,2554718734,3781033664,46346101,310463728,2743944855,3328955385,3875770207,2501218972,3955191162,3667219033,768917123,3545789473,692707433,1150208456,1786102409,2029293177,1805211710,3710368113,3065962831,401639597,1724457132,3028143674,409198410,2196052529,1620529459,1164071807,3769721975,2226875310,486441376,2499348523,1483753576,428819965,2274680428,3075636216,598438867,3799141122,1474502543,711349675,129166120,53458370,2592523643,2782082824,4063242375,2988687269,3120694122,1559041666,730517276,2460449204,4042459122,2706270690,3446004468,3573941694,533804130,2328143614,2637442643,2695033685,839224033,1973745387,957055980,2856345839,106852767,1371368976,4181598602,1033297158,2933734917,1179510461,3046200461,91341917,1862534868,4284502037,605657339,2547432937,3431546947,2003294622,3182487618,2282195339,954669403,3682191598,1201765386,3917234703,3388507166,0,2198438022,1211247597,2887651696,1315723890,4227665663,1443857720,507358933,657861945,1678381017,560487590,3516619604,975451694,2970356327,261314535,3535072918,2652609425,1333838021,2724322336,1767536459,370938394,182621114,3854606378,1128014560,487725847,185469197,2918353863,3106780840,3356761769,2237133081,1286567175,3152976349,4255350624,2683765030,3160175349,3309594171,878443390,1988838185,3704300486,1756818940,1673061617,3403100636,272786309,1075025698,545572369,2105887268,4174560061,296679730,1841768865,1260232239,4091327024,3960309330,3497509347,1814803222,2578018489,4195456072,575138148,3299409036,446754879,3629546796,4011996048,3347532110,3252238545,4270639778,915985419,3483825537,681933534,651868046,2755636671,3828103837,223377554,2607439820,1649704518,3270937875,3901806776,1580087799,4118987695,3198115200,2087309459,2842678573,3016697106,1003007129,2802849917,1860738147,2077965243,164439672,4100872472,32283319,2827177882,1709610350,2125135846,136428751,3874428392,3652904859,3460984630,3572145929,3593056380,2939266226,824852259,818324884,3224740454,930369212,2801566410,2967507152,355706840,1257309336,4148292826,243256656,790073846,2373340630,1296297904,1422699085,3756299780,3818836405,457992840,3099667487,2135319889,77422314,1560382517,1945798516,788204353,1521706781,1385356242,870912086,325965383,2358957921,2050466060,2388260884,2313884476,4006521127,901210569,3990953189,1014646705,1503449823,1062597235,2031621326,3212035895,3931371469,1533017514,350174575,2256028891,2177544179,1052338372,741876788,1606591296,1914052035,213705253,2334669897,1107234197,1899603969,3725069491,2631447780,2422494913,1635502980,1893020342,1950903388,1120974935],ti=[2807058932,1699970625,2764249623,1586903591,1808481195,1173430173,1487645946,59984867,4199882800,1844882806,1989249228,1277555970,3623636965,3419915562,1149249077,2744104290,1514790577,459744698,244860394,3235995134,1963115311,4027744588,2544078150,4190530515,1608975247,2627016082,2062270317,1507497298,2200818878,567498868,1764313568,3359936201,2305455554,2037970062,1047239e3,1910319033,1337376481,2904027272,2892417312,984907214,1243112415,830661914,861968209,2135253587,2011214180,2927934315,2686254721,731183368,1750626376,4246310725,1820824798,4172763771,3542330227,48394827,2404901663,2871682645,671593195,3254988725,2073724613,145085239,2280796200,2779915199,1790575107,2187128086,472615631,3029510009,4075877127,3802222185,4107101658,3201631749,1646252340,4270507174,1402811438,1436590835,3778151818,3950355702,3963161475,4020912224,2667994737,273792366,2331590177,104699613,95345982,3175501286,2377486676,1560637892,3564045318,369057872,4213447064,3919042237,1137477952,2658625497,1119727848,2340947849,1530455833,4007360968,172466556,266959938,516552836,0,2256734592,3980931627,1890328081,1917742170,4294704398,945164165,3575528878,958871085,3647212047,2787207260,1423022939,775562294,1739656202,3876557655,2530391278,2443058075,3310321856,547512796,1265195639,437656594,3121275539,719700128,3762502690,387781147,218828297,3350065803,2830708150,2848461854,428169201,122466165,3720081049,1627235199,648017665,4122762354,1002783846,2117360635,695634755,3336358691,4234721005,4049844452,3704280881,2232435299,574624663,287343814,612205898,1039717051,840019705,2708326185,793451934,821288114,1391201670,3822090177,376187827,3113855344,1224348052,1679968233,2361698556,1058709744,752375421,2431590963,1321699145,3519142200,2734591178,188127444,2177869557,3727205754,2384911031,3215212461,2648976442,2450346104,3432737375,1180849278,331544205,3102249176,4150144569,2952102595,2159976285,2474404304,766078933,313773861,2570832044,2108100632,1668212892,3145456443,2013908262,418672217,3070356634,2594734927,1852171925,3867060991,3473416636,3907448597,2614737639,919489135,164948639,2094410160,2997825956,590424639,2486224549,1723872674,3157750862,3399941250,3501252752,3625268135,2555048196,3673637356,1343127501,4130281361,3599595085,2957853679,1297403050,81781910,3051593425,2283490410,532201772,1367295589,3926170974,895287692,1953757831,1093597963,492483431,3528626907,1446242576,1192455638,1636604631,209336225,344873464,1015671571,669961897,3375740769,3857572124,2973530695,3747192018,1933530610,3464042516,935293895,3454686199,2858115069,1863638845,3683022916,4085369519,3292445032,875313188,1080017571,3279033885,621591778,1233856572,2504130317,24197544,3017672716,3835484340,3247465558,2220981195,3060847922,1551124588,1463996600],ii=[4104605777,1097159550,396673818,660510266,2875968315,2638606623,4200115116,3808662347,821712160,1986918061,3430322568,38544885,3856137295,718002117,893681702,1654886325,2975484382,3122358053,3926825029,4274053469,796197571,1290801793,1184342925,3556361835,2405426947,2459735317,1836772287,1381620373,3196267988,1948373848,3764988233,3385345166,3263785589,2390325492,1480485785,3111247143,3780097726,2293045232,548169417,3459953789,3746175075,439452389,1362321559,1400849762,1685577905,1806599355,2174754046,137073913,1214797936,1174215055,3731654548,2079897426,1943217067,1258480242,529487843,1437280870,3945269170,3049390895,3313212038,923313619,679998e3,3215307299,57326082,377642221,3474729866,2041877159,133361907,1776460110,3673476453,96392454,878845905,2801699524,777231668,4082475170,2330014213,4142626212,2213296395,1626319424,1906247262,1846563261,562755902,3708173718,1040559837,3871163981,1418573201,3294430577,114585348,1343618912,2566595609,3186202582,1078185097,3651041127,3896688048,2307622919,425408743,3371096953,2081048481,1108339068,2216610296,0,2156299017,736970802,292596766,1517440620,251657213,2235061775,2933202493,758720310,265905162,1554391400,1532285339,908999204,174567692,1474760595,4002861748,2610011675,3234156416,3693126241,2001430874,303699484,2478443234,2687165888,585122620,454499602,151849742,2345119218,3064510765,514443284,4044981591,1963412655,2581445614,2137062819,19308535,1928707164,1715193156,4219352155,1126790795,600235211,3992742070,3841024952,836553431,1669664834,2535604243,3323011204,1243905413,3141400786,4180808110,698445255,2653899549,2989552604,2253581325,3252932727,3004591147,1891211689,2487810577,3915653703,4237083816,4030667424,2100090966,865136418,1229899655,953270745,3399679628,3557504664,4118925222,2061379749,3079546586,2915017791,983426092,2022837584,1607244650,2118541908,2366882550,3635996816,972512814,3283088770,1568718495,3499326569,3576539503,621982671,2895723464,410887952,2623762152,1002142683,645401037,1494807662,2595684844,1335535747,2507040230,4293295786,3167684641,367585007,3885750714,1865862730,2668221674,2960971305,2763173681,1059270954,2777952454,2724642869,1320957812,2194319100,2429595872,2815956275,77089521,3973773121,3444575871,2448830231,1305906550,4021308739,2857194700,2516901860,3518358430,1787304780,740276417,1699839814,1592394909,2352307457,2272556026,188821243,1729977011,3687994002,274084841,3594982253,3613494426,2701949495,4162096729,322734571,2837966542,1640576439,484830689,1202797690,3537852828,4067639125,349075736,3342319475,4157467219,4255800159,1030690015,1155237496,2951971274,1757691577,607398968,2738905026,499347990,3794078908,1011452712,227885567,2818666809,213114376,3034881240,1455525988,3414450555,850817237,1817998408,3092726480],ri=[0,235474187,470948374,303765277,941896748,908933415,607530554,708780849,1883793496,2118214995,1817866830,1649639237,1215061108,1181045119,1417561698,1517767529,3767586992,4003061179,4236429990,4069246893,3635733660,3602770327,3299278474,3400528769,2430122216,2664543715,2362090238,2193862645,2835123396,2801107407,3035535058,3135740889,3678124923,3576870512,3341394285,3374361702,3810496343,3977675356,4279080257,4043610186,2876494627,2776292904,3076639029,3110650942,2472011535,2640243204,2403728665,2169303058,1001089995,899835584,666464733,699432150,59727847,226906860,530400753,294930682,1273168787,1172967064,1475418501,1509430414,1942435775,2110667444,1876241833,1641816226,2910219766,2743034109,2976151520,3211623147,2505202138,2606453969,2302690252,2269728455,3711829422,3543599269,3240894392,3475313331,3843699074,3943906441,4178062228,4144047775,1306967366,1139781709,1374988112,1610459739,1975683434,2076935265,1775276924,1742315127,1034867998,866637845,566021896,800440835,92987698,193195065,429456164,395441711,1984812685,2017778566,1784663195,1683407248,1315562145,1080094634,1383856311,1551037884,101039829,135050206,437757123,337553864,1042385657,807962610,573804783,742039012,2531067453,2564033334,2328828971,2227573024,2935566865,2700099354,3001755655,3168937228,3868552805,3902563182,4203181171,4102977912,3736164937,3501741890,3265478751,3433712980,1106041591,1340463100,1576976609,1408749034,2043211483,2009195472,1708848333,1809054150,832877231,1068351396,766945465,599762354,159417987,126454664,361929877,463180190,2709260871,2943682380,3178106961,3009879386,2572697195,2538681184,2236228733,2336434550,3509871135,3745345300,3441850377,3274667266,3910161971,3877198648,4110568485,4211818798,2597806476,2497604743,2261089178,2295101073,2733856160,2902087851,3202437046,2968011453,3936291284,3835036895,4136440770,4169408201,3535486456,3702665459,3467192302,3231722213,2051518780,1951317047,1716890410,1750902305,1113818384,1282050075,1584504582,1350078989,168810852,67556463,371049330,404016761,841739592,1008918595,775550814,540080725,3969562369,3801332234,4035489047,4269907996,3569255213,3669462566,3366754619,3332740144,2631065433,2463879762,2160117071,2395588676,2767645557,2868897406,3102011747,3069049960,202008497,33778362,270040487,504459436,875451293,975658646,675039627,641025152,2084704233,1917518562,1615861247,1851332852,1147550661,1248802510,1484005843,1451044056,933301370,967311729,733156972,632953703,260388950,25965917,328671808,496906059,1206477858,1239443753,1543208500,1441952575,2144161806,1908694277,1675577880,1842759443,3610369226,3644379585,3408119516,3307916247,4011190502,3776767469,4077384432,4245618683,2809771154,2842737049,3144396420,3043140495,2673705150,2438237621,2203032232,2370213795],si=[0,185469197,370938394,487725847,741876788,657861945,975451694,824852259,1483753576,1400783205,1315723890,1164071807,1950903388,2135319889,1649704518,1767536459,2967507152,3152976349,2801566410,2918353863,2631447780,2547432937,2328143614,2177544179,3901806776,3818836405,4270639778,4118987695,3299409036,3483825537,3535072918,3652904859,2077965243,1893020342,1841768865,1724457132,1474502543,1559041666,1107234197,1257309336,598438867,681933534,901210569,1052338372,261314535,77422314,428819965,310463728,3409685355,3224740454,3710368113,3593056380,3875770207,3960309330,4045380933,4195456072,2471224067,2554718734,2237133081,2388260884,3212035895,3028143674,2842678573,2724322336,4138563181,4255350624,3769721975,3955191162,3667219033,3516619604,3431546947,3347532110,2933734917,2782082824,3099667487,3016697106,2196052529,2313884476,2499348523,2683765030,1179510461,1296297904,1347548327,1533017514,1786102409,1635502980,2087309459,2003294622,507358933,355706840,136428751,53458370,839224033,957055980,605657339,790073846,2373340630,2256028891,2607439820,2422494913,2706270690,2856345839,3075636216,3160175349,3573941694,3725069491,3273267108,3356761769,4181598602,4063242375,4011996048,3828103837,1033297158,915985419,730517276,545572369,296679730,446754879,129166120,213705253,1709610350,1860738147,1945798516,2029293177,1239331162,1120974935,1606591296,1422699085,4148292826,4233094615,3781033664,3931371469,3682191598,3497509347,3446004468,3328955385,2939266226,2755636671,3106780840,2988687269,2198438022,2282195339,2501218972,2652609425,1201765386,1286567175,1371368976,1521706781,1805211710,1620529459,2105887268,1988838185,533804130,350174575,164439672,46346101,870912086,954669403,636813900,788204353,2358957921,2274680428,2592523643,2441661558,2695033685,2880240216,3065962831,3182487618,3572145929,3756299780,3270937875,3388507166,4174560061,4091327024,4006521127,3854606378,1014646705,930369212,711349675,560487590,272786309,457992840,106852767,223377554,1678381017,1862534868,1914052035,2031621326,1211247597,1128014560,1580087799,1428173050,32283319,182621114,401639597,486441376,768917123,651868046,1003007129,818324884,1503449823,1385356242,1333838021,1150208456,1973745387,2125135846,1673061617,1756818940,2970356327,3120694122,2802849917,2887651696,2637442643,2520393566,2334669897,2149987652,3917234703,3799141122,4284502037,4100872472,3309594171,3460984630,3545789473,3629546796,2050466060,1899603969,1814803222,1730525723,1443857720,1560382517,1075025698,1260232239,575138148,692707433,878443390,1062597235,243256656,91341917,409198410,325965383,3403100636,3252238545,3704300486,3620022987,3874428392,3990953189,4042459122,4227665663,2460449204,2578018489,2226875310,2411029155,3198115200,3046200461,2827177882,2743944855],ni=[0,218828297,437656594,387781147,875313188,958871085,775562294,590424639,1750626376,1699970625,1917742170,2135253587,1551124588,1367295589,1180849278,1265195639,3501252752,3720081049,3399941250,3350065803,3835484340,3919042237,4270507174,4085369519,3102249176,3051593425,2734591178,2952102595,2361698556,2177869557,2530391278,2614737639,3145456443,3060847922,2708326185,2892417312,2404901663,2187128086,2504130317,2555048196,3542330227,3727205754,3375740769,3292445032,3876557655,3926170974,4246310725,4027744588,1808481195,1723872674,1910319033,2094410160,1608975247,1391201670,1173430173,1224348052,59984867,244860394,428169201,344873464,935293895,984907214,766078933,547512796,1844882806,1627235199,2011214180,2062270317,1507497298,1423022939,1137477952,1321699145,95345982,145085239,532201772,313773861,830661914,1015671571,731183368,648017665,3175501286,2957853679,2807058932,2858115069,2305455554,2220981195,2474404304,2658625497,3575528878,3625268135,3473416636,3254988725,3778151818,3963161475,4213447064,4130281361,3599595085,3683022916,3432737375,3247465558,3802222185,4020912224,4172763771,4122762354,3201631749,3017672716,2764249623,2848461854,2331590177,2280796200,2431590963,2648976442,104699613,188127444,472615631,287343814,840019705,1058709744,671593195,621591778,1852171925,1668212892,1953757831,2037970062,1514790577,1463996600,1080017571,1297403050,3673637356,3623636965,3235995134,3454686199,4007360968,3822090177,4107101658,4190530515,2997825956,3215212461,2830708150,2779915199,2256734592,2340947849,2627016082,2443058075,172466556,122466165,273792366,492483431,1047239e3,861968209,612205898,695634755,1646252340,1863638845,2013908262,1963115311,1446242576,1530455833,1277555970,1093597963,1636604631,1820824798,2073724613,1989249228,1436590835,1487645946,1337376481,1119727848,164948639,81781910,331544205,516552836,1039717051,821288114,669961897,719700128,2973530695,3157750862,2871682645,2787207260,2232435299,2283490410,2667994737,2450346104,3647212047,3564045318,3279033885,3464042516,3980931627,3762502690,4150144569,4199882800,3070356634,3121275539,2904027272,2686254721,2200818878,2384911031,2570832044,2486224549,3747192018,3528626907,3310321856,3359936201,3950355702,3867060991,4049844452,4234721005,1739656202,1790575107,2108100632,1890328081,1402811438,1586903591,1233856572,1149249077,266959938,48394827,369057872,418672217,1002783846,919489135,567498868,752375421,209336225,24197544,376187827,459744698,945164165,895287692,574624663,793451934,1679968233,1764313568,2117360635,1933530610,1343127501,1560637892,1243112415,1192455638,3704280881,3519142200,3336358691,3419915562,3907448597,3857572124,4075877127,4294704398,3029510009,3113855344,2927934315,2744104290,2159976285,2377486676,2594734927,2544078150],ai=[0,151849742,303699484,454499602,607398968,758720310,908999204,1059270954,1214797936,1097159550,1517440620,1400849762,1817998408,1699839814,2118541908,2001430874,2429595872,2581445614,2194319100,2345119218,3034881240,3186202582,2801699524,2951971274,3635996816,3518358430,3399679628,3283088770,4237083816,4118925222,4002861748,3885750714,1002142683,850817237,698445255,548169417,529487843,377642221,227885567,77089521,1943217067,2061379749,1640576439,1757691577,1474760595,1592394909,1174215055,1290801793,2875968315,2724642869,3111247143,2960971305,2405426947,2253581325,2638606623,2487810577,3808662347,3926825029,4044981591,4162096729,3342319475,3459953789,3576539503,3693126241,1986918061,2137062819,1685577905,1836772287,1381620373,1532285339,1078185097,1229899655,1040559837,923313619,740276417,621982671,439452389,322734571,137073913,19308535,3871163981,4021308739,4104605777,4255800159,3263785589,3414450555,3499326569,3651041127,2933202493,2815956275,3167684641,3049390895,2330014213,2213296395,2566595609,2448830231,1305906550,1155237496,1607244650,1455525988,1776460110,1626319424,2079897426,1928707164,96392454,213114376,396673818,514443284,562755902,679998e3,865136418,983426092,3708173718,3557504664,3474729866,3323011204,4180808110,4030667424,3945269170,3794078908,2507040230,2623762152,2272556026,2390325492,2975484382,3092726480,2738905026,2857194700,3973773121,3856137295,4274053469,4157467219,3371096953,3252932727,3673476453,3556361835,2763173681,2915017791,3064510765,3215307299,2156299017,2307622919,2459735317,2610011675,2081048481,1963412655,1846563261,1729977011,1480485785,1362321559,1243905413,1126790795,878845905,1030690015,645401037,796197571,274084841,425408743,38544885,188821243,3613494426,3731654548,3313212038,3430322568,4082475170,4200115116,3780097726,3896688048,2668221674,2516901860,2366882550,2216610296,3141400786,2989552604,2837966542,2687165888,1202797690,1320957812,1437280870,1554391400,1669664834,1787304780,1906247262,2022837584,265905162,114585348,499347990,349075736,736970802,585122620,972512814,821712160,2595684844,2478443234,2293045232,2174754046,3196267988,3079546586,2895723464,2777952454,3537852828,3687994002,3234156416,3385345166,4142626212,4293295786,3841024952,3992742070,174567692,57326082,410887952,292596766,777231668,660510266,1011452712,893681702,1108339068,1258480242,1343618912,1494807662,1715193156,1865862730,1948373848,2100090966,2701949495,2818666809,3004591147,3122358053,2235061775,2352307457,2535604243,2653899549,3915653703,3764988233,4219352155,4067639125,3444575871,3294430577,3746175075,3594982253,836553431,953270745,600235211,718002117,367585007,484830689,133361907,251657213,2041877159,1891211689,1806599355,1654886325,1568718495,1418573201,1335535747,1184342925];function oi(e){for(var t=[],i=0;i<e.length;i+=4)t.push(e[i]<<24|e[i+1]<<16|e[i+2]<<8|e[i+3]);return t}var di=function(e){if(!(this instanceof di))throw Error("AES must be instanitated with `new`");Object.defineProperty(this,"key",{value:Nt(e,!0)}),this._prepare()};di.prototype._prepare=function(){var e=Wt[this.key.length];if(null==e)throw new Error("invalid key size (must be 16, 24 or 32 bytes)");this._Ke=[],this._Kd=[];for(var t=0;t<=e;t++)this._Ke.push([0,0,0,0]),this._Kd.push([0,0,0,0]);var i,r=4*(e+1),s=this.key.length/4,n=oi(this.key);for(t=0;t<s;t++)i=t>>2,this._Ke[i][t%4]=n[t],this._Kd[e-i][t%4]=n[t];for(var a,o=0,d=s;d<r;){if(a=n[s-1],n[0]^=jt[a>>16&255]<<24^jt[a>>8&255]<<16^jt[255&a]<<8^jt[a>>24&255]^Yt[o]<<24,o+=1,8!=s)for(t=1;t<s;t++)n[t]^=n[t-1];else{for(t=1;t<s/2;t++)n[t]^=n[t-1];a=n[s/2-1],n[s/2]^=jt[255&a]^jt[a>>8&255]<<8^jt[a>>16&255]<<16^jt[a>>24&255]<<24;for(t=s/2+1;t<s;t++)n[t]^=n[t-1]}for(t=0;t<s&&d<r;)l=d>>2,h=d%4,this._Ke[l][h]=n[t],this._Kd[e-l][h]=n[t++],d++}for(var l=1;l<e;l++)for(var h=0;h<4;h++)a=this._Kd[l][h],this._Kd[l][h]=ri[a>>24&255]^si[a>>16&255]^ni[a>>8&255]^ai[255&a]},di.prototype.encrypt=function(e){if(16!=e.length)throw new Error("invalid plaintext size (must be 16 bytes)");for(var t=this._Ke.length-1,i=[0,0,0,0],r=oi(e),s=0;s<4;s++)r[s]^=this._Ke[0][s];for(var n=1;n<t;n++){for(s=0;s<4;s++)i[s]=Kt[r[s]>>24&255]^Xt[r[(s+1)%4]>>16&255]^Zt[r[(s+2)%4]>>8&255]^Jt[255&r[(s+3)%4]]^this._Ke[n][s];r=i.slice()}var a,o=Ot(16);for(s=0;s<4;s++)a=this._Ke[t][s],o[4*s]=255&(jt[r[s]>>24&255]^a>>24),o[4*s+1]=255&(jt[r[(s+1)%4]>>16&255]^a>>16),o[4*s+2]=255&(jt[r[(s+2)%4]>>8&255]^a>>8),o[4*s+3]=255&(jt[255&r[(s+3)%4]]^a);return o},di.prototype.decrypt=function(e){if(16!=e.length)throw new Error("invalid ciphertext size (must be 16 bytes)");for(var t=this._Kd.length-1,i=[0,0,0,0],r=oi(e),s=0;s<4;s++)r[s]^=this._Kd[0][s];for(var n=1;n<t;n++){for(s=0;s<4;s++)i[s]=Qt[r[s]>>24&255]^ei[r[(s+3)%4]>>16&255]^ti[r[(s+2)%4]>>8&255]^ii[255&r[(s+1)%4]]^this._Kd[n][s];r=i.slice()}var a,o=Ot(16);for(s=0;s<4;s++)a=this._Kd[t][s],o[4*s]=255&(qt[r[s]>>24&255]^a>>24),o[4*s+1]=255&(qt[r[(s+3)%4]>>16&255]^a>>16),o[4*s+2]=255&(qt[r[(s+2)%4]>>8&255]^a>>8),o[4*s+3]=255&(qt[255&r[(s+1)%4]]^a);return o};var li=function(e){if(!(this instanceof li))throw Error("AES must be instanitated with `new`");this.description="Electronic Code Block",this.name="ecb",this._aes=new di(e)};li.prototype.encrypt=function(e){if((e=Nt(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=Ot(e.length),i=Ot(16),r=0;r<e.length;r+=16)Gt(e,i,0,r,r+16),Gt(i=this._aes.encrypt(i),t,r);return t},li.prototype.decrypt=function(e){if((e=Nt(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=Ot(e.length),i=Ot(16),r=0;r<e.length;r+=16)Gt(e,i,0,r,r+16),Gt(i=this._aes.decrypt(i),t,r);return t};var hi=function(e,t){if(!(this instanceof hi))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Block Chaining",this.name="cbc",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=Ot(16);this._lastCipherblock=Nt(t,!0),this._aes=new di(e)};hi.prototype.encrypt=function(e){if((e=Nt(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=Ot(e.length),i=Ot(16),r=0;r<e.length;r+=16){Gt(e,i,0,r,r+16);for(var s=0;s<16;s++)i[s]^=this._lastCipherblock[s];this._lastCipherblock=this._aes.encrypt(i),Gt(this._lastCipherblock,t,r)}return t},hi.prototype.decrypt=function(e){if((e=Nt(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=Ot(e.length),i=Ot(16),r=0;r<e.length;r+=16){Gt(e,i,0,r,r+16),i=this._aes.decrypt(i);for(var s=0;s<16;s++)t[r+s]=i[s]^this._lastCipherblock[s];Gt(e,this._lastCipherblock,0,r,r+16)}return t};var fi=function(e,t,i){if(!(this instanceof fi))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Feedback",this.name="cfb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 size)")}else t=Ot(16);i||(i=1),this.segmentSize=i,this._shiftRegister=Nt(t,!0),this._aes=new di(e)};fi.prototype.encrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid plaintext size (must be segmentSize bytes)");for(var t,i=Nt(e,!0),r=0;r<i.length;r+=this.segmentSize){t=this._aes.encrypt(this._shiftRegister);for(var s=0;s<this.segmentSize;s++)i[r+s]^=t[s];Gt(this._shiftRegister,this._shiftRegister,0,this.segmentSize),Gt(i,this._shiftRegister,16-this.segmentSize,r,r+this.segmentSize)}return i},fi.prototype.decrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid ciphertext size (must be segmentSize bytes)");for(var t,i=Nt(e,!0),r=0;r<i.length;r+=this.segmentSize){t=this._aes.encrypt(this._shiftRegister);for(var s=0;s<this.segmentSize;s++)i[r+s]^=t[s];Gt(this._shiftRegister,this._shiftRegister,0,this.segmentSize),Gt(e,this._shiftRegister,16-this.segmentSize,r,r+this.segmentSize)}return i};var pi=function(e,t){if(!(this instanceof pi))throw Error("AES must be instanitated with `new`");if(this.description="Output Feedback",this.name="ofb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=Ot(16);this._lastPrecipher=Nt(t,!0),this._lastPrecipherIndex=16,this._aes=new di(e)};pi.prototype.encrypt=function(e){for(var t=Nt(e,!0),i=0;i<t.length;i++)16===this._lastPrecipherIndex&&(this._lastPrecipher=this._aes.encrypt(this._lastPrecipher),this._lastPrecipherIndex=0),t[i]^=this._lastPrecipher[this._lastPrecipherIndex++];return t},pi.prototype.decrypt=pi.prototype.encrypt;var ui=function(e){if(!(this instanceof ui))throw Error("Counter must be instanitated with `new`");0===e||e||(e=1),"number"==typeof e?(this._counter=Ot(16),this.setValue(e)):this.setBytes(e)};ui.prototype.setValue=function(e){if("number"!=typeof e||parseInt(e)!=e)throw new Error("invalid counter value (must be an integer)");if(e>Number.MAX_SAFE_INTEGER)throw new Error("integer value out of safe range");for(var t=15;t>=0;--t)this._counter[t]=e%256,e=parseInt(e/256)},ui.prototype.setBytes=function(e){if(16!=(e=Nt(e,!0)).length)throw new Error("invalid counter bytes size (must be 16 bytes)");this._counter=e},ui.prototype.increment=function(){for(var e=15;e>=0;e--){if(255!==this._counter[e]){this._counter[e]++;break}this._counter[e]=0}};var ci=function(e,t){if(!(this instanceof ci))throw Error("AES must be instanitated with `new`");this.description="Counter",this.name="ctr",t instanceof ui||(t=new ui(t)),this._counter=t,this._remainingCounter=null,this._remainingCounterIndex=16,this._aes=new di(e)};ci.prototype.encrypt=function(e){for(var t=Nt(e,!0),i=0;i<t.length;i++)16===this._remainingCounterIndex&&(this._remainingCounter=this._aes.encrypt(this._counter._counter),this._remainingCounterIndex=0,this._counter.increment()),t[i]^=this._remainingCounter[this._remainingCounterIndex++];return t},ci.prototype.decrypt=ci.prototype.encrypt;const mi={AES:di,Counter:ui,ModeOfOperation:{ecb:li,cbc:hi,cfb:fi,ofb:pi,ctr:ci},utils:{hex:$t,utf8:Vt},padding:{pkcs7:{pad:function(e){var t=16-(e=Nt(e,!0)).length%16,i=Ot(e.length+t);Gt(e,i);for(var r=e.length;r<i.length;r++)i[r]=t;return i},strip:function(e){if((e=Nt(e,!0)).length<16)throw new Error("PKCS#7 invalid length");var t=e[e.length-1];if(t>16)throw new Error("PKCS#7 padding byte out of range");for(var i=e.length-t,r=0;r<t;r++)if(e[i+r]!==t)throw new Error("PKCS#7 invalid padding byte");var s=Ot(i);return Gt(e,s,0,0,i),s}}},_arrayTest:{coerceArray:Nt,createArray:Ot,copyArray:Gt}};var _i=Ge((function(e,t){var i,r,s,n=(i=new Date,r=4,s={setLogLevel:function(e){r=e==this.debug?1:e==this.info?2:e==this.warn?3:(this.error,4)},debug:function(e,t){void 0===console.debug&&(console.debug=console.log),1>=r&&console.debug("["+n.getDurationString(new Date-i,1e3)+"]","["+e+"]",t)},log:function(e,t){this.debug(e.msg)},info:function(e,t){2>=r&&console.info("["+n.getDurationString(new Date-i,1e3)+"]","["+e+"]",t)},warn:function(e,t){3>=r&&console.warn("["+n.getDurationString(new Date-i,1e3)+"]","["+e+"]",t)},error:function(e,t){4>=r&&console.error("["+n.getDurationString(new Date-i,1e3)+"]","["+e+"]",t)}},s);n.getDurationString=function(e,t){var i;function r(e,t){for(var i=(""+e).split(".");i[0].length<t;)i[0]="0"+i[0];return i.join(".")}e<0?(i=!0,e=-e):i=!1;var s=e/(t||1),n=Math.floor(s/3600);s-=3600*n;var a=Math.floor(s/60),o=1e3*(s-=60*a);return o-=1e3*(s=Math.floor(s)),o=Math.floor(o),(i?"-":"")+n+":"+r(a,2)+":"+r(s,2)+"."+r(o,3)},n.printRanges=function(e){var t=e.length;if(t>0){for(var i="",r=0;r<t;r++)r>0&&(i+=","),i+="["+n.getDurationString(e.start(r))+","+n.getDurationString(e.end(r))+"]";return i}return"(empty)"},t.Log=n;var a=function(e){if(!(e instanceof ArrayBuffer))throw"Needs an array buffer";this.buffer=e,this.dataview=new DataView(e),this.position=0};a.prototype.getPosition=function(){return this.position},a.prototype.getEndPosition=function(){return this.buffer.byteLength},a.prototype.getLength=function(){return this.buffer.byteLength},a.prototype.seek=function(e){var t=Math.max(0,Math.min(this.buffer.byteLength,e));return this.position=isNaN(t)||!isFinite(t)?0:t,!0},a.prototype.isEos=function(){return this.getPosition()>=this.getEndPosition()},a.prototype.readAnyInt=function(e,t){var i=0;if(this.position+e<=this.buffer.byteLength){switch(e){case 1:i=t?this.dataview.getInt8(this.position):this.dataview.getUint8(this.position);break;case 2:i=t?this.dataview.getInt16(this.position):this.dataview.getUint16(this.position);break;case 3:if(t)throw"No method for reading signed 24 bits values";i=this.dataview.getUint8(this.position)<<16,i|=this.dataview.getUint8(this.position+1)<<8,i|=this.dataview.getUint8(this.position+2);break;case 4:i=t?this.dataview.getInt32(this.position):this.dataview.getUint32(this.position);break;case 8:if(t)throw"No method for reading signed 64 bits values";i=this.dataview.getUint32(this.position)<<32,i|=this.dataview.getUint32(this.position+4);break;default:throw"readInt method not implemented for size: "+e}return this.position+=e,i}throw"Not enough bytes in buffer"},a.prototype.readUint8=function(){return this.readAnyInt(1,!1)},a.prototype.readUint16=function(){return this.readAnyInt(2,!1)},a.prototype.readUint24=function(){return this.readAnyInt(3,!1)},a.prototype.readUint32=function(){return this.readAnyInt(4,!1)},a.prototype.readUint64=function(){return this.readAnyInt(8,!1)},a.prototype.readString=function(e){if(this.position+e<=this.buffer.byteLength){for(var t="",i=0;i<e;i++)t+=String.fromCharCode(this.readUint8());return t}throw"Not enough bytes in buffer"},a.prototype.readCString=function(){for(var e=[];;){var t=this.readUint8();if(0===t)break;e.push(t)}return String.fromCharCode.apply(null,e)},a.prototype.readInt8=function(){return this.readAnyInt(1,!0)},a.prototype.readInt16=function(){return this.readAnyInt(2,!0)},a.prototype.readInt32=function(){return this.readAnyInt(4,!0)},a.prototype.readInt64=function(){return this.readAnyInt(8,!1)},a.prototype.readUint8Array=function(e){for(var t=new Uint8Array(e),i=0;i<e;i++)t[i]=this.readUint8();return t},a.prototype.readInt16Array=function(e){for(var t=new Int16Array(e),i=0;i<e;i++)t[i]=this.readInt16();return t},a.prototype.readUint16Array=function(e){for(var t=new Int16Array(e),i=0;i<e;i++)t[i]=this.readUint16();return t},a.prototype.readUint32Array=function(e){for(var t=new Uint32Array(e),i=0;i<e;i++)t[i]=this.readUint32();return t},a.prototype.readInt32Array=function(e){for(var t=new Int32Array(e),i=0;i<e;i++)t[i]=this.readInt32();return t},t.MP4BoxStream=a;var o=function(e,t,i){this._byteOffset=t||0,e instanceof ArrayBuffer?this.buffer=e:"object"==typeof e?(this.dataView=e,t&&(this._byteOffset+=t)):this.buffer=new ArrayBuffer(e||0),this.position=0,this.endianness=null==i?o.LITTLE_ENDIAN:i};o.prototype={},o.prototype.getPosition=function(){return this.position},o.prototype._realloc=function(e){if(this._dynamicSize){var t=this._byteOffset+this.position+e,i=this._buffer.byteLength;if(t<=i)t>this._byteLength&&(this._byteLength=t);else{for(i<1&&(i=1);t>i;)i*=2;var r=new ArrayBuffer(i),s=new Uint8Array(this._buffer);new Uint8Array(r,0,s.length).set(s),this.buffer=r,this._byteLength=t}}},o.prototype._trimAlloc=function(){if(this._byteLength!=this._buffer.byteLength){var e=new ArrayBuffer(this._byteLength),t=new Uint8Array(e),i=new Uint8Array(this._buffer,0,t.length);t.set(i),this.buffer=e}},o.BIG_ENDIAN=!1,o.LITTLE_ENDIAN=!0,o.prototype._byteLength=0,Object.defineProperty(o.prototype,"byteLength",{get:function(){return this._byteLength-this._byteOffset}}),Object.defineProperty(o.prototype,"buffer",{get:function(){return this._trimAlloc(),this._buffer},set:function(e){this._buffer=e,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(o.prototype,"byteOffset",{get:function(){return this._byteOffset},set:function(e){this._byteOffset=e,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(o.prototype,"dataView",{get:function(){return this._dataView},set:function(e){this._byteOffset=e.byteOffset,this._buffer=e.buffer,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._byteOffset+e.byteLength}}),o.prototype.seek=function(e){var t=Math.max(0,Math.min(this.byteLength,e));this.position=isNaN(t)||!isFinite(t)?0:t},o.prototype.isEof=function(){return this.position>=this._byteLength},o.prototype.mapUint8Array=function(e){this._realloc(1*e);var t=new Uint8Array(this._buffer,this.byteOffset+this.position,e);return this.position+=1*e,t},o.prototype.readInt32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var i=new Int32Array(e);return o.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),o.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},o.prototype.readInt16Array=function(e,t){e=null==e?this.byteLength-this.position/2:e;var i=new Int16Array(e);return o.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),o.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},o.prototype.readInt8Array=function(e){e=null==e?this.byteLength-this.position:e;var t=new Int8Array(e);return o.memcpy(t.buffer,0,this.buffer,this.byteOffset+this.position,e*t.BYTES_PER_ELEMENT),this.position+=t.byteLength,t},o.prototype.readUint32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var i=new Uint32Array(e);return o.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),o.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},o.prototype.readUint16Array=function(e,t){e=null==e?this.byteLength-this.position/2:e;var i=new Uint16Array(e);return o.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),o.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},o.prototype.readUint8Array=function(e){e=null==e?this.byteLength-this.position:e;var t=new Uint8Array(e);return o.memcpy(t.buffer,0,this.buffer,this.byteOffset+this.position,e*t.BYTES_PER_ELEMENT),this.position+=t.byteLength,t},o.prototype.readFloat64Array=function(e,t){e=null==e?this.byteLength-this.position/8:e;var i=new Float64Array(e);return o.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),o.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},o.prototype.readFloat32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var i=new Float32Array(e);return o.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),o.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},o.prototype.readInt32=function(e){var t=this._dataView.getInt32(this.position,null==e?this.endianness:e);return this.position+=4,t},o.prototype.readInt16=function(e){var t=this._dataView.getInt16(this.position,null==e?this.endianness:e);return this.position+=2,t},o.prototype.readInt8=function(){var e=this._dataView.getInt8(this.position);return this.position+=1,e},o.prototype.readUint32=function(e){var t=this._dataView.getUint32(this.position,null==e?this.endianness:e);return this.position+=4,t},o.prototype.readUint16=function(e){var t=this._dataView.getUint16(this.position,null==e?this.endianness:e);return this.position+=2,t},o.prototype.readUint8=function(){var e=this._dataView.getUint8(this.position);return this.position+=1,e},o.prototype.readFloat32=function(e){var t=this._dataView.getFloat32(this.position,null==e?this.endianness:e);return this.position+=4,t},o.prototype.readFloat64=function(e){var t=this._dataView.getFloat64(this.position,null==e?this.endianness:e);return this.position+=8,t},o.endianness=new Int8Array(new Int16Array([1]).buffer)[0]>0,o.memcpy=function(e,t,i,r,s){var n=new Uint8Array(e,t,s),a=new Uint8Array(i,r,s);n.set(a)},o.arrayToNative=function(e,t){return t==this.endianness?e:this.flipArrayEndianness(e)},o.nativeToEndian=function(e,t){return this.endianness==t?e:this.flipArrayEndianness(e)},o.flipArrayEndianness=function(e){for(var t=new Uint8Array(e.buffer,e.byteOffset,e.byteLength),i=0;i<e.byteLength;i+=e.BYTES_PER_ELEMENT)for(var r=i+e.BYTES_PER_ELEMENT-1,s=i;r>s;r--,s++){var n=t[s];t[s]=t[r],t[r]=n}return e},o.prototype.failurePosition=0,String.fromCharCodeUint8=function(e){for(var t=[],i=0;i<e.length;i++)t[i]=e[i];return String.fromCharCode.apply(null,t)},o.prototype.readString=function(e,t){return null==t||"ASCII"==t?String.fromCharCodeUint8.apply(null,[this.mapUint8Array(null==e?this.byteLength-this.position:e)]):new TextDecoder(t).decode(this.mapUint8Array(e))},o.prototype.readCString=function(e){var t=this.byteLength-this.position,i=new Uint8Array(this._buffer,this._byteOffset+this.position),r=t;null!=e&&(r=Math.min(e,t));for(var s=0;s<r&&0!==i[s];s++);var n=String.fromCharCodeUint8.apply(null,[this.mapUint8Array(s)]);return null!=e?this.position+=r-s:s!=t&&(this.position+=1),n};var d=Math.pow(2,32);o.prototype.readInt64=function(){return this.readInt32()*d+this.readUint32()},o.prototype.readUint64=function(){return this.readUint32()*d+this.readUint32()},o.prototype.readInt64=function(){return this.readUint32()*d+this.readUint32()},o.prototype.readUint24=function(){return(this.readUint8()<<16)+(this.readUint8()<<8)+this.readUint8()},t.DataStream=o,o.prototype.save=function(e){var t=new Blob([this.buffer]);if(!window.URL||!URL.createObjectURL)throw"DataStream.save: Can't create object URL.";var i=window.URL.createObjectURL(t),r=document.createElement("a");document.body.appendChild(r),r.setAttribute("href",i),r.setAttribute("download",e),r.setAttribute("target","_self"),r.click(),window.URL.revokeObjectURL(i)},o.prototype._dynamicSize=!0,Object.defineProperty(o.prototype,"dynamicSize",{get:function(){return this._dynamicSize},set:function(e){e||this._trimAlloc(),this._dynamicSize=e}}),o.prototype.shift=function(e){var t=new ArrayBuffer(this._byteLength-e),i=new Uint8Array(t),r=new Uint8Array(this._buffer,e,i.length);i.set(r),this.buffer=t,this.position-=e},o.prototype.writeInt32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Int32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt32Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeInt32(e[i],t)},o.prototype.writeInt16Array=function(e,t){if(this._realloc(2*e.length),e instanceof Int16Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt16Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeInt16(e[i],t)},o.prototype.writeInt8Array=function(e){if(this._realloc(1*e.length),e instanceof Int8Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt8Array(e.length);else for(var t=0;t<e.length;t++)this.writeInt8(e[t])},o.prototype.writeUint32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Uint32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint32Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeUint32(e[i],t)},o.prototype.writeUint16Array=function(e,t){if(this._realloc(2*e.length),e instanceof Uint16Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint16Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeUint16(e[i],t)},o.prototype.writeUint8Array=function(e){if(this._realloc(1*e.length),e instanceof Uint8Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint8Array(e.length);else for(var t=0;t<e.length;t++)this.writeUint8(e[t])},o.prototype.writeFloat64Array=function(e,t){if(this._realloc(8*e.length),e instanceof Float64Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapFloat64Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeFloat64(e[i],t)},o.prototype.writeFloat32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Float32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapFloat32Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeFloat32(e[i],t)},o.prototype.writeInt32=function(e,t){this._realloc(4),this._dataView.setInt32(this.position,e,null==t?this.endianness:t),this.position+=4},o.prototype.writeInt16=function(e,t){this._realloc(2),this._dataView.setInt16(this.position,e,null==t?this.endianness:t),this.position+=2},o.prototype.writeInt8=function(e){this._realloc(1),this._dataView.setInt8(this.position,e),this.position+=1},o.prototype.writeUint32=function(e,t){this._realloc(4),this._dataView.setUint32(this.position,e,null==t?this.endianness:t),this.position+=4},o.prototype.writeUint16=function(e,t){this._realloc(2),this._dataView.setUint16(this.position,e,null==t?this.endianness:t),this.position+=2},o.prototype.writeUint8=function(e){this._realloc(1),this._dataView.setUint8(this.position,e),this.position+=1},o.prototype.writeFloat32=function(e,t){this._realloc(4),this._dataView.setFloat32(this.position,e,null==t?this.endianness:t),this.position+=4},o.prototype.writeFloat64=function(e,t){this._realloc(8),this._dataView.setFloat64(this.position,e,null==t?this.endianness:t),this.position+=8},o.prototype.writeUCS2String=function(e,t,i){null==i&&(i=e.length);for(var r=0;r<e.length&&r<i;r++)this.writeUint16(e.charCodeAt(r),t);for(;r<i;r++)this.writeUint16(0)},o.prototype.writeString=function(e,t,i){var r=0;if(null==t||"ASCII"==t)if(null!=i){var s=Math.min(e.length,i);for(r=0;r<s;r++)this.writeUint8(e.charCodeAt(r));for(;r<i;r++)this.writeUint8(0)}else for(r=0;r<e.length;r++)this.writeUint8(e.charCodeAt(r));else this.writeUint8Array(new TextEncoder(t).encode(e.substring(0,i)))},o.prototype.writeCString=function(e,t){var i=0;if(null!=t){var r=Math.min(e.length,t);for(i=0;i<r;i++)this.writeUint8(e.charCodeAt(i));for(;i<t;i++)this.writeUint8(0)}else{for(i=0;i<e.length;i++)this.writeUint8(e.charCodeAt(i));this.writeUint8(0)}},o.prototype.writeStruct=function(e,t){for(var i=0;i<e.length;i+=2){var r=e[i+1];this.writeType(r,t[e[i]],t)}},o.prototype.writeType=function(e,t,i){var r;if("function"==typeof e)return e(this,t);if("object"==typeof e&&!(e instanceof Array))return e.set(this,t,i);var s=null,n="ASCII",a=this.position;switch("string"==typeof e&&/:/.test(e)&&(r=e.split(":"),e=r[0],s=parseInt(r[1])),"string"==typeof e&&/,/.test(e)&&(r=e.split(","),e=r[0],n=parseInt(r[1])),e){case"uint8":this.writeUint8(t);break;case"int8":this.writeInt8(t);break;case"uint16":this.writeUint16(t,this.endianness);break;case"int16":this.writeInt16(t,this.endianness);break;case"uint32":this.writeUint32(t,this.endianness);break;case"int32":this.writeInt32(t,this.endianness);break;case"float32":this.writeFloat32(t,this.endianness);break;case"float64":this.writeFloat64(t,this.endianness);break;case"uint16be":this.writeUint16(t,o.BIG_ENDIAN);break;case"int16be":this.writeInt16(t,o.BIG_ENDIAN);break;case"uint32be":this.writeUint32(t,o.BIG_ENDIAN);break;case"int32be":this.writeInt32(t,o.BIG_ENDIAN);break;case"float32be":this.writeFloat32(t,o.BIG_ENDIAN);break;case"float64be":this.writeFloat64(t,o.BIG_ENDIAN);break;case"uint16le":this.writeUint16(t,o.LITTLE_ENDIAN);break;case"int16le":this.writeInt16(t,o.LITTLE_ENDIAN);break;case"uint32le":this.writeUint32(t,o.LITTLE_ENDIAN);break;case"int32le":this.writeInt32(t,o.LITTLE_ENDIAN);break;case"float32le":this.writeFloat32(t,o.LITTLE_ENDIAN);break;case"float64le":this.writeFloat64(t,o.LITTLE_ENDIAN);break;case"cstring":this.writeCString(t,s);break;case"string":this.writeString(t,n,s);break;case"u16string":this.writeUCS2String(t,this.endianness,s);break;case"u16stringle":this.writeUCS2String(t,o.LITTLE_ENDIAN,s);break;case"u16stringbe":this.writeUCS2String(t,o.BIG_ENDIAN,s);break;default:if(3==e.length){for(var d=e[1],l=0;l<t.length;l++)this.writeType(d,t[l]);break}this.writeStruct(e,t)}null!=s&&(this.position=a,this._realloc(s),this.position=a+s)},o.prototype.writeUint64=function(e){var t=Math.floor(e/d);this.writeUint32(t),this.writeUint32(4294967295&e)},o.prototype.writeUint24=function(e){this.writeUint8((16711680&e)>>16),this.writeUint8((65280&e)>>8),this.writeUint8(255&e)},o.prototype.adjustUint32=function(e,t){var i=this.position;this.seek(e),this.writeUint32(t),this.seek(i)},o.prototype.mapInt32Array=function(e,t){this._realloc(4*e);var i=new Int32Array(this._buffer,this.byteOffset+this.position,e);return o.arrayToNative(i,null==t?this.endianness:t),this.position+=4*e,i},o.prototype.mapInt16Array=function(e,t){this._realloc(2*e);var i=new Int16Array(this._buffer,this.byteOffset+this.position,e);return o.arrayToNative(i,null==t?this.endianness:t),this.position+=2*e,i},o.prototype.mapInt8Array=function(e){this._realloc(1*e);var t=new Int8Array(this._buffer,this.byteOffset+this.position,e);return this.position+=1*e,t},o.prototype.mapUint32Array=function(e,t){this._realloc(4*e);var i=new Uint32Array(this._buffer,this.byteOffset+this.position,e);return o.arrayToNative(i,null==t?this.endianness:t),this.position+=4*e,i},o.prototype.mapUint16Array=function(e,t){this._realloc(2*e);var i=new Uint16Array(this._buffer,this.byteOffset+this.position,e);return o.arrayToNative(i,null==t?this.endianness:t),this.position+=2*e,i},o.prototype.mapFloat64Array=function(e,t){this._realloc(8*e);var i=new Float64Array(this._buffer,this.byteOffset+this.position,e);return o.arrayToNative(i,null==t?this.endianness:t),this.position+=8*e,i},o.prototype.mapFloat32Array=function(e,t){this._realloc(4*e);var i=new Float32Array(this._buffer,this.byteOffset+this.position,e);return o.arrayToNative(i,null==t?this.endianness:t),this.position+=4*e,i};var l=function(e){this.buffers=[],this.bufferIndex=-1,e&&(this.insertBuffer(e),this.bufferIndex=0)};(l.prototype=new o(new ArrayBuffer,0,o.BIG_ENDIAN)).initialized=function(){var e;return this.bufferIndex>-1||(this.buffers.length>0?0===(e=this.buffers[0]).fileStart?(this.buffer=e,this.bufferIndex=0,n.debug("MultiBufferStream","Stream ready for parsing"),!0):(n.warn("MultiBufferStream","The first buffer should have a fileStart of 0"),this.logBufferLevel(),!1):(n.warn("MultiBufferStream","No buffer to start parsing from"),this.logBufferLevel(),!1))},ArrayBuffer.concat=function(e,t){n.debug("ArrayBuffer","Trying to create a new buffer of size: "+(e.byteLength+t.byteLength));var i=new Uint8Array(e.byteLength+t.byteLength);return i.set(new Uint8Array(e),0),i.set(new Uint8Array(t),e.byteLength),i.buffer},l.prototype.reduceBuffer=function(e,t,i){var r;return(r=new Uint8Array(i)).set(new Uint8Array(e,t,i)),r.buffer.fileStart=e.fileStart+t,r.buffer.usedBytes=0,r.buffer},l.prototype.insertBuffer=function(e){for(var t=!0,i=0;i<this.buffers.length;i++){var r=this.buffers[i];if(e.fileStart<=r.fileStart){if(e.fileStart===r.fileStart){if(e.byteLength>r.byteLength){this.buffers.splice(i,1),i--;continue}n.warn("MultiBufferStream","Buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+") already appended, ignoring")}else e.fileStart+e.byteLength<=r.fileStart||(e=this.reduceBuffer(e,0,r.fileStart-e.fileStart)),n.debug("MultiBufferStream","Appending new buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+")"),this.buffers.splice(i,0,e),0===i&&(this.buffer=e);t=!1;break}if(e.fileStart<r.fileStart+r.byteLength){var s=r.fileStart+r.byteLength-e.fileStart,a=e.byteLength-s;if(!(a>0)){t=!1;break}e=this.reduceBuffer(e,s,a)}}t&&(n.debug("MultiBufferStream","Appending new buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+")"),this.buffers.push(e),0===i&&(this.buffer=e))},l.prototype.logBufferLevel=function(e){var t,i,r,s,a,o=[],d="";for(r=0,s=0,t=0;t<this.buffers.length;t++)i=this.buffers[t],0===t?(a={},o.push(a),a.start=i.fileStart,a.end=i.fileStart+i.byteLength,d+="["+a.start+"-"):a.end===i.fileStart?a.end=i.fileStart+i.byteLength:((a={}).start=i.fileStart,d+=o[o.length-1].end-1+"], ["+a.start+"-",a.end=i.fileStart+i.byteLength,o.push(a)),r+=i.usedBytes,s+=i.byteLength;o.length>0&&(d+=a.end-1+"]");var l=e?n.info:n.debug;0===this.buffers.length?l("MultiBufferStream","No more buffer in memory"):l("MultiBufferStream",this.buffers.length+" stored buffer(s) ("+r+"/"+s+" bytes), continuous ranges: "+d)},l.prototype.cleanBuffers=function(){var e,t;for(e=0;e<this.buffers.length;e++)(t=this.buffers[e]).usedBytes===t.byteLength&&(n.debug("MultiBufferStream","Removing buffer #"+e),this.buffers.splice(e,1),e--)},l.prototype.mergeNextBuffer=function(){var e;if(this.bufferIndex+1<this.buffers.length){if((e=this.buffers[this.bufferIndex+1]).fileStart===this.buffer.fileStart+this.buffer.byteLength){var t=this.buffer.byteLength,i=this.buffer.usedBytes,r=this.buffer.fileStart;return this.buffers[this.bufferIndex]=ArrayBuffer.concat(this.buffer,e),this.buffer=this.buffers[this.bufferIndex],this.buffers.splice(this.bufferIndex+1,1),this.buffer.usedBytes=i,this.buffer.fileStart=r,n.debug("ISOFile","Concatenating buffer for box parsing (length: "+t+"->"+this.buffer.byteLength+")"),!0}return!1}return!1},l.prototype.findPosition=function(e,t,i){var r,s=null,a=-1;for(r=!0===e?0:this.bufferIndex;r<this.buffers.length&&(s=this.buffers[r]).fileStart<=t;)a=r,i&&(s.fileStart+s.byteLength<=t?s.usedBytes=s.byteLength:s.usedBytes=t-s.fileStart,this.logBufferLevel()),r++;return-1!==a&&(s=this.buffers[a]).fileStart+s.byteLength>=t?(n.debug("MultiBufferStream","Found position in existing buffer #"+a),a):-1},l.prototype.findEndContiguousBuf=function(e){var t,i,r,s=void 0!==e?e:this.bufferIndex;if(i=this.buffers[s],this.buffers.length>s+1)for(t=s+1;t<this.buffers.length&&(r=this.buffers[t]).fileStart===i.fileStart+i.byteLength;t++)i=r;return i.fileStart+i.byteLength},l.prototype.getEndFilePositionAfter=function(e){var t=this.findPosition(!0,e,!1);return-1!==t?this.findEndContiguousBuf(t):e},l.prototype.addUsedBytes=function(e){this.buffer.usedBytes+=e,this.logBufferLevel()},l.prototype.setAllUsedBytes=function(){this.buffer.usedBytes=this.buffer.byteLength,this.logBufferLevel()},l.prototype.seek=function(e,t,i){var r;return-1!==(r=this.findPosition(t,e,i))?(this.buffer=this.buffers[r],this.bufferIndex=r,this.position=e-this.buffer.fileStart,n.debug("MultiBufferStream","Repositioning parser at buffer position: "+this.position),!0):(n.debug("MultiBufferStream","Position "+e+" not found in buffered data"),!1)},l.prototype.getPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.position},l.prototype.getLength=function(){return this.byteLength},l.prototype.getEndPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.byteLength},t.MultiBufferStream=l;var h=function(){var e=[];e[3]="ES_Descriptor",e[4]="DecoderConfigDescriptor",e[5]="DecoderSpecificInfo",e[6]="SLConfigDescriptor",this.getDescriptorName=function(t){return e[t]};var t=this,i={};return this.parseOneDescriptor=function(t){var r,s,a,o=0;for(r=t.readUint8(),a=t.readUint8();128&a;)o=(127&a)<<7,a=t.readUint8();return o+=127&a,n.debug("MPEG4DescriptorParser","Found "+(e[r]||"Descriptor "+r)+", size "+o+" at position "+t.getPosition()),(s=e[r]?new i[e[r]](o):new i.Descriptor(o)).parse(t),s},i.Descriptor=function(e,t){this.tag=e,this.size=t,this.descs=[]},i.Descriptor.prototype.parse=function(e){this.data=e.readUint8Array(this.size)},i.Descriptor.prototype.findDescriptor=function(e){for(var t=0;t<this.descs.length;t++)if(this.descs[t].tag==e)return this.descs[t];return null},i.Descriptor.prototype.parseRemainingDescriptors=function(e){for(var i=e.position;e.position<i+this.size;){var r=t.parseOneDescriptor(e);this.descs.push(r)}},i.ES_Descriptor=function(e){i.Descriptor.call(this,3,e)},i.ES_Descriptor.prototype=new i.Descriptor,i.ES_Descriptor.prototype.parse=function(e){if(this.ES_ID=e.readUint16(),this.flags=e.readUint8(),this.size-=3,128&this.flags?(this.dependsOn_ES_ID=e.readUint16(),this.size-=2):this.dependsOn_ES_ID=0,64&this.flags){var t=e.readUint8();this.URL=e.readString(t),this.size-=t+1}else this.URL="";32&this.flags?(this.OCR_ES_ID=e.readUint16(),this.size-=2):this.OCR_ES_ID=0,this.parseRemainingDescriptors(e)},i.ES_Descriptor.prototype.getOTI=function(e){var t=this.findDescriptor(4);return t?t.oti:0},i.ES_Descriptor.prototype.getAudioConfig=function(e){var t=this.findDescriptor(4);if(!t)return null;var i=t.findDescriptor(5);if(i&&i.data){var r=(248&i.data[0])>>3;return 31===r&&i.data.length>=2&&(r=32+((7&i.data[0])<<3)+((224&i.data[1])>>5)),r}return null},i.DecoderConfigDescriptor=function(e){i.Descriptor.call(this,4,e)},i.DecoderConfigDescriptor.prototype=new i.Descriptor,i.DecoderConfigDescriptor.prototype.parse=function(e){this.oti=e.readUint8(),this.streamType=e.readUint8(),this.bufferSize=e.readUint24(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32(),this.size-=13,this.parseRemainingDescriptors(e)},i.DecoderSpecificInfo=function(e){i.Descriptor.call(this,5,e)},i.DecoderSpecificInfo.prototype=new i.Descriptor,i.SLConfigDescriptor=function(e){i.Descriptor.call(this,6,e)},i.SLConfigDescriptor.prototype=new i.Descriptor,this};t.MPEG4DescriptorParser=h;var f={ERR_INVALID_DATA:-1,ERR_NOT_ENOUGH_DATA:0,OK:1,BASIC_BOXES:["mdat","idat","free","skip","meco","strk"],FULL_BOXES:["hmhd","nmhd","iods","xml ","bxml","ipro","mere"],CONTAINER_BOXES:[["moov",["trak","pssh"]],["trak"],["edts"],["mdia"],["minf"],["dinf"],["stbl",["sgpd","sbgp"]],["mvex",["trex"]],["moof",["traf"]],["traf",["trun","sgpd","sbgp"]],["vttc"],["tref"],["iref"],["mfra",["tfra"]],["meco"],["hnti"],["hinf"],["strk"],["strd"],["sinf"],["rinf"],["schi"],["trgr"],["udta",["kind"]],["iprp",["ipma"]],["ipco"]],boxCodes:[],fullBoxCodes:[],containerBoxCodes:[],sampleEntryCodes:{},sampleGroupEntryCodes:[],trackGroupTypes:[],UUIDBoxes:{},UUIDs:[],initialize:function(){f.FullBox.prototype=new f.Box,f.ContainerBox.prototype=new f.Box,f.SampleEntry.prototype=new f.Box,f.TrackGroupTypeBox.prototype=new f.FullBox,f.BASIC_BOXES.forEach((function(e){f.createBoxCtor(e)})),f.FULL_BOXES.forEach((function(e){f.createFullBoxCtor(e)})),f.CONTAINER_BOXES.forEach((function(e){f.createContainerBoxCtor(e[0],null,e[1])}))},Box:function(e,t,i){this.type=e,this.size=t,this.uuid=i},FullBox:function(e,t,i){f.Box.call(this,e,t,i),this.flags=0,this.version=0},ContainerBox:function(e,t,i){f.Box.call(this,e,t,i),this.boxes=[]},SampleEntry:function(e,t,i,r){f.ContainerBox.call(this,e,t),this.hdr_size=i,this.start=r},SampleGroupEntry:function(e){this.grouping_type=e},TrackGroupTypeBox:function(e,t){f.FullBox.call(this,e,t)},createBoxCtor:function(e,t){f.boxCodes.push(e),f[e+"Box"]=function(t){f.Box.call(this,e,t)},f[e+"Box"].prototype=new f.Box,t&&(f[e+"Box"].prototype.parse=t)},createFullBoxCtor:function(e,t){f[e+"Box"]=function(t){f.FullBox.call(this,e,t)},f[e+"Box"].prototype=new f.FullBox,f[e+"Box"].prototype.parse=function(e){this.parseFullHeader(e),t&&t.call(this,e)}},addSubBoxArrays:function(e){if(e){this.subBoxNames=e;for(var t=e.length,i=0;i<t;i++)this[e[i]+"s"]=[]}},createContainerBoxCtor:function(e,t,i){f[e+"Box"]=function(t){f.ContainerBox.call(this,e,t),f.addSubBoxArrays.call(this,i)},f[e+"Box"].prototype=new f.ContainerBox,t&&(f[e+"Box"].prototype.parse=t)},createMediaSampleEntryCtor:function(e,t,i){f.sampleEntryCodes[e]=[],f[e+"SampleEntry"]=function(e,t){f.SampleEntry.call(this,e,t),f.addSubBoxArrays.call(this,i)},f[e+"SampleEntry"].prototype=new f.SampleEntry,t&&(f[e+"SampleEntry"].prototype.parse=t)},createSampleEntryCtor:function(e,t,i,r){f.sampleEntryCodes[e].push(t),f[t+"SampleEntry"]=function(i){f[e+"SampleEntry"].call(this,t,i),f.addSubBoxArrays.call(this,r)},f[t+"SampleEntry"].prototype=new f[e+"SampleEntry"],i&&(f[t+"SampleEntry"].prototype.parse=i)},createEncryptedSampleEntryCtor:function(e,t,i){f.createSampleEntryCtor.call(this,e,t,i,["sinf"])},createSampleGroupCtor:function(e,t){f[e+"SampleGroupEntry"]=function(t){f.SampleGroupEntry.call(this,e,t)},f[e+"SampleGroupEntry"].prototype=new f.SampleGroupEntry,t&&(f[e+"SampleGroupEntry"].prototype.parse=t)},createTrackGroupCtor:function(e,t){f[e+"TrackGroupTypeBox"]=function(t){f.TrackGroupTypeBox.call(this,e,t)},f[e+"TrackGroupTypeBox"].prototype=new f.TrackGroupTypeBox,t&&(f[e+"TrackGroupTypeBox"].prototype.parse=t)},createUUIDBox:function(e,t,i,r){f.UUIDs.push(e),f.UUIDBoxes[e]=function(r){t?f.FullBox.call(this,"uuid",r,e):i?f.ContainerBox.call(this,"uuid",r,e):f.Box.call(this,"uuid",r,e)},f.UUIDBoxes[e].prototype=t?new f.FullBox:i?new f.ContainerBox:new f.Box,r&&(f.UUIDBoxes[e].prototype.parse=t?function(e){this.parseFullHeader(e),r&&r.call(this,e)}:r)}};f.initialize(),f.TKHD_FLAG_ENABLED=1,f.TKHD_FLAG_IN_MOVIE=2,f.TKHD_FLAG_IN_PREVIEW=4,f.TFHD_FLAG_BASE_DATA_OFFSET=1,f.TFHD_FLAG_SAMPLE_DESC=2,f.TFHD_FLAG_SAMPLE_DUR=8,f.TFHD_FLAG_SAMPLE_SIZE=16,f.TFHD_FLAG_SAMPLE_FLAGS=32,f.TFHD_FLAG_DUR_EMPTY=65536,f.TFHD_FLAG_DEFAULT_BASE_IS_MOOF=131072,f.TRUN_FLAGS_DATA_OFFSET=1,f.TRUN_FLAGS_FIRST_FLAG=4,f.TRUN_FLAGS_DURATION=256,f.TRUN_FLAGS_SIZE=512,f.TRUN_FLAGS_FLAGS=1024,f.TRUN_FLAGS_CTS_OFFSET=2048,f.Box.prototype.add=function(e){return this.addBox(new f[e+"Box"])},f.Box.prototype.addBox=function(e){return this.boxes.push(e),this[e.type+"s"]?this[e.type+"s"].push(e):this[e.type]=e,e},f.Box.prototype.set=function(e,t){return this[e]=t,this},f.Box.prototype.addEntry=function(e,t){var i=t||"entries";return this[i]||(this[i]=[]),this[i].push(e),this},t.BoxParser=f,f.parseUUID=function(e){return f.parseHex16(e)},f.parseHex16=function(e){for(var t="",i=0;i<16;i++){var r=e.readUint8().toString(16);t+=1===r.length?"0"+r:r}return t},f.parseOneBox=function(e,t,i){var r,s,a,o=e.getPosition(),d=0;if(e.getEndPosition()-o<8)return n.debug("BoxParser","Not enough data in stream to parse the type and size of the box"),{code:f.ERR_NOT_ENOUGH_DATA};if(i&&i<8)return n.debug("BoxParser","Not enough bytes left in the parent box to parse a new box"),{code:f.ERR_NOT_ENOUGH_DATA};var l=e.readUint32(),h=e.readString(4),p=h;if(n.debug("BoxParser","Found box of type '"+h+"' and size "+l+" at position "+o),d=8,"uuid"==h){if(e.getEndPosition()-e.getPosition()<16||i-d<16)return e.seek(o),n.debug("BoxParser","Not enough bytes left in the parent box to parse a UUID box"),{code:f.ERR_NOT_ENOUGH_DATA};d+=16,p=a=f.parseUUID(e)}if(1==l){if(e.getEndPosition()-e.getPosition()<8||i&&i-d<8)return e.seek(o),n.warn("BoxParser",'Not enough data in stream to parse the extended size of the "'+h+'" box'),{code:f.ERR_NOT_ENOUGH_DATA};l=e.readUint64(),d+=8}else if(0===l)if(i)l=i;else if("mdat"!==h)return n.error("BoxParser","Unlimited box size not supported for type: '"+h+"'"),r=new f.Box(h,l),{code:f.OK,box:r,size:r.size};return 0!==l&&l<d?(n.error("BoxParser","Box of type "+h+" has an invalid size "+l+" (too small to be a box)"),{code:f.ERR_NOT_ENOUGH_DATA,type:h,size:l,hdr_size:d,start:o}):0!==l&&i&&l>i?(n.error("BoxParser","Box of type '"+h+"' has a size "+l+" greater than its container size "+i),{code:f.ERR_NOT_ENOUGH_DATA,type:h,size:l,hdr_size:d,start:o}):0!==l&&o+l>e.getEndPosition()?(e.seek(o),n.info("BoxParser","Not enough data in stream to parse the entire '"+h+"' box"),{code:f.ERR_NOT_ENOUGH_DATA,type:h,size:l,hdr_size:d,start:o}):t?{code:f.OK,type:h,size:l,hdr_size:d,start:o}:(f[h+"Box"]?r=new f[h+"Box"](l):"uuid"!==h?(n.warn("BoxParser","Unknown box type: '"+h+"'"),(r=new f.Box(h,l)).has_unparsed_data=!0):f.UUIDBoxes[a]?r=new f.UUIDBoxes[a](l):(n.warn("BoxParser","Unknown uuid type: '"+a+"'"),(r=new f.Box(h,l)).uuid=a,r.has_unparsed_data=!0),r.hdr_size=d,r.start=o,r.write===f.Box.prototype.write&&"mdat"!==r.type&&(n.info("BoxParser","'"+p+"' box writing not yet implemented, keeping unparsed data in memory for later write"),r.parseDataAndRewind(e)),r.parse(e),(s=e.getPosition()-(r.start+r.size))<0?(n.warn("BoxParser","Parsing of box '"+p+"' did not read the entire indicated box data size (missing "+-s+" bytes), seeking forward"),e.seek(r.start+r.size)):s>0&&(n.error("BoxParser","Parsing of box '"+p+"' read "+s+" more bytes than the indicated box data size, seeking backwards"),0!==r.size&&e.seek(r.start+r.size)),{code:f.OK,box:r,size:r.size})},f.Box.prototype.parse=function(e){"mdat"!=this.type?this.data=e.readUint8Array(this.size-this.hdr_size):0===this.size?e.seek(e.getEndPosition()):e.seek(this.start+this.size)},f.Box.prototype.parseDataAndRewind=function(e){this.data=e.readUint8Array(this.size-this.hdr_size),e.position-=this.size-this.hdr_size},f.FullBox.prototype.parseDataAndRewind=function(e){this.parseFullHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size),this.hdr_size-=4,e.position-=this.size-this.hdr_size},f.FullBox.prototype.parseFullHeader=function(e){this.version=e.readUint8(),this.flags=e.readUint24(),this.hdr_size+=4},f.FullBox.prototype.parse=function(e){this.parseFullHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size)},f.ContainerBox.prototype.parse=function(e){for(var t,i;e.getPosition()<this.start+this.size;){if((t=f.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==f.OK)return;if(i=t.box,this.boxes.push(i),this.subBoxNames&&-1!=this.subBoxNames.indexOf(i.type))this[this.subBoxNames[this.subBoxNames.indexOf(i.type)]+"s"].push(i);else{var r="uuid"!==i.type?i.type:i.uuid;this[r]?n.warn("Box of type "+r+" already stored in field of this type"):this[r]=i}}},f.Box.prototype.parseLanguage=function(e){this.language=e.readUint16();var t=[];t[0]=this.language>>10&31,t[1]=this.language>>5&31,t[2]=31&this.language,this.languageString=String.fromCharCode(t[0]+96,t[1]+96,t[2]+96)},f.SAMPLE_ENTRY_TYPE_VISUAL="Visual",f.SAMPLE_ENTRY_TYPE_AUDIO="Audio",f.SAMPLE_ENTRY_TYPE_HINT="Hint",f.SAMPLE_ENTRY_TYPE_METADATA="Metadata",f.SAMPLE_ENTRY_TYPE_SUBTITLE="Subtitle",f.SAMPLE_ENTRY_TYPE_SYSTEM="System",f.SAMPLE_ENTRY_TYPE_TEXT="Text",f.SampleEntry.prototype.parseHeader=function(e){e.readUint8Array(6),this.data_reference_index=e.readUint16(),this.hdr_size+=8},f.SampleEntry.prototype.parse=function(e){this.parseHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size)},f.SampleEntry.prototype.parseDataAndRewind=function(e){this.parseHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size),this.hdr_size-=8,e.position-=this.size-this.hdr_size},f.SampleEntry.prototype.parseFooter=function(e){f.ContainerBox.prototype.parse.call(this,e)},f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_HINT),f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_METADATA),f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SUBTITLE),f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SYSTEM),f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_TEXT),f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,(function(e){var t;this.parseHeader(e),e.readUint16(),e.readUint16(),e.readUint32Array(3),this.width=e.readUint16(),this.height=e.readUint16(),this.horizresolution=e.readUint32(),this.vertresolution=e.readUint32(),e.readUint32(),this.frame_count=e.readUint16(),t=Math.min(31,e.readUint8()),this.compressorname=e.readString(t),t<31&&e.readString(31-t),this.depth=e.readUint16(),e.readUint16(),this.parseFooter(e)})),f.createMediaSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,(function(e){this.parseHeader(e),e.readUint32Array(2),this.channel_count=e.readUint16(),this.samplesize=e.readUint16(),e.readUint16(),e.readUint16(),this.samplerate=e.readUint32()/65536,this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"avc1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"avc2"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"avc3"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"avc4"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"av01"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"hvc1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"hev1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"vvc1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"vvi1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"vvs1"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"vvcN"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"vp08"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"vp09"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"mp4a"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"ac-3"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"ec-3"),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"Opus"),f.createEncryptedSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_VISUAL,"encv"),f.createEncryptedSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_AUDIO,"enca"),f.createEncryptedSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SUBTITLE,"encu"),f.createEncryptedSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SYSTEM,"encs"),f.createEncryptedSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_TEXT,"enct"),f.createEncryptedSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_METADATA,"encm"),f.createBoxCtor("a1lx",(function(e){var t=16*(1+(1&(1&e.readUint8())));this.layer_size=[];for(var i=0;i<3;i++)this.layer_size[i]=16==t?e.readUint16():e.readUint32()})),f.createBoxCtor("a1op",(function(e){this.op_index=e.readUint8()})),f.createFullBoxCtor("auxC",(function(e){this.aux_type=e.readCString();var t=this.size-this.hdr_size-(this.aux_type.length+1);this.aux_subtype=e.readUint8Array(t)})),f.createBoxCtor("av1C",(function(e){var t=e.readUint8();if(t>>7&!1)n.error("av1C marker problem");else if(this.version=127&t,1===this.version)if(t=e.readUint8(),this.seq_profile=t>>5&7,this.seq_level_idx_0=31&t,t=e.readUint8(),this.seq_tier_0=t>>7&1,this.high_bitdepth=t>>6&1,this.twelve_bit=t>>5&1,this.monochrome=t>>4&1,this.chroma_subsampling_x=t>>3&1,this.chroma_subsampling_y=t>>2&1,this.chroma_sample_position=3&t,t=e.readUint8(),this.reserved_1=t>>5&7,0===this.reserved_1){if(this.initial_presentation_delay_present=t>>4&1,1===this.initial_presentation_delay_present)this.initial_presentation_delay_minus_one=15&t;else if(this.reserved_2=15&t,0!==this.reserved_2)return void n.error("av1C reserved_2 parsing problem");var i=this.size-this.hdr_size-4;this.configOBUs=e.readUint8Array(i)}else n.error("av1C reserved_1 parsing problem");else n.error("av1C version "+this.version+" not supported")})),f.createBoxCtor("avcC",(function(e){var t,i;for(this.configurationVersion=e.readUint8(),this.AVCProfileIndication=e.readUint8(),this.profile_compatibility=e.readUint8(),this.AVCLevelIndication=e.readUint8(),this.lengthSizeMinusOne=3&e.readUint8(),this.nb_SPS_nalus=31&e.readUint8(),i=this.size-this.hdr_size-6,this.SPS=[],t=0;t<this.nb_SPS_nalus;t++)this.SPS[t]={},this.SPS[t].length=e.readUint16(),this.SPS[t].nalu=e.readUint8Array(this.SPS[t].length),i-=2+this.SPS[t].length;for(this.nb_PPS_nalus=e.readUint8(),i--,this.PPS=[],t=0;t<this.nb_PPS_nalus;t++)this.PPS[t]={},this.PPS[t].length=e.readUint16(),this.PPS[t].nalu=e.readUint8Array(this.PPS[t].length),i-=2+this.PPS[t].length;i>0&&(this.ext=e.readUint8Array(i))})),f.createBoxCtor("btrt",(function(e){this.bufferSizeDB=e.readUint32(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32()})),f.createBoxCtor("clap",(function(e){this.cleanApertureWidthN=e.readUint32(),this.cleanApertureWidthD=e.readUint32(),this.cleanApertureHeightN=e.readUint32(),this.cleanApertureHeightD=e.readUint32(),this.horizOffN=e.readUint32(),this.horizOffD=e.readUint32(),this.vertOffN=e.readUint32(),this.vertOffD=e.readUint32()})),f.createBoxCtor("clli",(function(e){this.max_content_light_level=e.readUint16(),this.max_pic_average_light_level=e.readUint16()})),f.createFullBoxCtor("co64",(function(e){var t,i;if(t=e.readUint32(),this.chunk_offsets=[],0===this.version)for(i=0;i<t;i++)this.chunk_offsets.push(e.readUint64())})),f.createFullBoxCtor("CoLL",(function(e){this.maxCLL=e.readUint16(),this.maxFALL=e.readUint16()})),f.createBoxCtor("colr",(function(e){if(this.colour_type=e.readString(4),"nclx"===this.colour_type){this.colour_primaries=e.readUint16(),this.transfer_characteristics=e.readUint16(),this.matrix_coefficients=e.readUint16();var t=e.readUint8();this.full_range_flag=t>>7}else("rICC"===this.colour_type||"prof"===this.colour_type)&&(this.ICC_profile=e.readUint8Array(this.size-4))})),f.createFullBoxCtor("cprt",(function(e){this.parseLanguage(e),this.notice=e.readCString()})),f.createFullBoxCtor("cslg",(function(e){0===this.version&&(this.compositionToDTSShift=e.readInt32(),this.leastDecodeToDisplayDelta=e.readInt32(),this.greatestDecodeToDisplayDelta=e.readInt32(),this.compositionStartTime=e.readInt32(),this.compositionEndTime=e.readInt32())})),f.createFullBoxCtor("ctts",(function(e){var t,i;if(t=e.readUint32(),this.sample_counts=[],this.sample_offsets=[],0===this.version)for(i=0;i<t;i++){this.sample_counts.push(e.readUint32());var r=e.readInt32();r<0&&n.warn("BoxParser","ctts box uses negative values without using version 1"),this.sample_offsets.push(r)}else if(1==this.version)for(i=0;i<t;i++)this.sample_counts.push(e.readUint32()),this.sample_offsets.push(e.readInt32())})),f.createBoxCtor("dac3",(function(e){var t=e.readUint8(),i=e.readUint8(),r=e.readUint8();this.fscod=t>>6,this.bsid=t>>1&31,this.bsmod=(1&t)<<2|i>>6&3,this.acmod=i>>3&7,this.lfeon=i>>2&1,this.bit_rate_code=3&i|r>>5&7})),f.createBoxCtor("dec3",(function(e){var t=e.readUint16();this.data_rate=t>>3,this.num_ind_sub=7&t,this.ind_subs=[];for(var i=0;i<this.num_ind_sub+1;i++){var r={};this.ind_subs.push(r);var s=e.readUint8(),n=e.readUint8(),a=e.readUint8();r.fscod=s>>6,r.bsid=s>>1&31,r.bsmod=(1&s)<<4|n>>4&15,r.acmod=n>>1&7,r.lfeon=1&n,r.num_dep_sub=a>>1&15,r.num_dep_sub>0&&(r.chan_loc=(1&a)<<8|e.readUint8())}})),f.createFullBoxCtor("dfLa",(function(e){var t=[],i=["STREAMINFO","PADDING","APPLICATION","SEEKTABLE","VORBIS_COMMENT","CUESHEET","PICTURE","RESERVED"];for(this.parseFullHeader(e);;){var r=e.readUint8(),s=Math.min(127&r,i.length-1);if(s?e.readUint8Array(e.readUint24()):(e.readUint8Array(13),this.samplerate=e.readUint32()>>12,e.readUint8Array(20)),t.push(i[s]),128&r)break}this.numMetadataBlocks=t.length+" ("+t.join(", ")+")"})),f.createBoxCtor("dimm",(function(e){this.bytessent=e.readUint64()})),f.createBoxCtor("dmax",(function(e){this.time=e.readUint32()})),f.createBoxCtor("dmed",(function(e){this.bytessent=e.readUint64()})),f.createBoxCtor("dOps",(function(e){if(this.Version=e.readUint8(),this.OutputChannelCount=e.readUint8(),this.PreSkip=e.readUint16(),this.InputSampleRate=e.readUint32(),this.OutputGain=e.readInt16(),this.ChannelMappingFamily=e.readUint8(),0!==this.ChannelMappingFamily){this.StreamCount=e.readUint8(),this.CoupledCount=e.readUint8(),this.ChannelMapping=[];for(var t=0;t<this.OutputChannelCount;t++)this.ChannelMapping[t]=e.readUint8()}})),f.createFullBoxCtor("dref",(function(e){var t,i;this.entries=[];for(var r=e.readUint32(),s=0;s<r;s++){if((t=f.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==f.OK)return;i=t.box,this.entries.push(i)}})),f.createBoxCtor("drep",(function(e){this.bytessent=e.readUint64()})),f.createFullBoxCtor("elng",(function(e){this.extended_language=e.readString(this.size-this.hdr_size)})),f.createFullBoxCtor("elst",(function(e){this.entries=[];for(var t=e.readUint32(),i=0;i<t;i++){var r={};this.entries.push(r),1===this.version?(r.segment_duration=e.readUint64(),r.media_time=e.readInt64()):(r.segment_duration=e.readUint32(),r.media_time=e.readInt32()),r.media_rate_integer=e.readInt16(),r.media_rate_fraction=e.readInt16()}})),f.createFullBoxCtor("emsg",(function(e){1==this.version?(this.timescale=e.readUint32(),this.presentation_time=e.readUint64(),this.event_duration=e.readUint32(),this.id=e.readUint32(),this.scheme_id_uri=e.readCString(),this.value=e.readCString()):(this.scheme_id_uri=e.readCString(),this.value=e.readCString(),this.timescale=e.readUint32(),this.presentation_time_delta=e.readUint32(),this.event_duration=e.readUint32(),this.id=e.readUint32());var t=this.size-this.hdr_size-(16+(this.scheme_id_uri.length+1)+(this.value.length+1));1==this.version&&(t-=4),this.message_data=e.readUint8Array(t)})),f.createFullBoxCtor("esds",(function(e){var t=e.readUint8Array(this.size-this.hdr_size);if(void 0!==h){var i=new h;this.esd=i.parseOneDescriptor(new o(t.buffer,0,o.BIG_ENDIAN))}})),f.createBoxCtor("fiel",(function(e){this.fieldCount=e.readUint8(),this.fieldOrdering=e.readUint8()})),f.createBoxCtor("frma",(function(e){this.data_format=e.readString(4)})),f.createBoxCtor("ftyp",(function(e){var t=this.size-this.hdr_size;this.major_brand=e.readString(4),this.minor_version=e.readUint32(),t-=8,this.compatible_brands=[];for(var i=0;t>=4;)this.compatible_brands[i]=e.readString(4),t-=4,i++})),f.createFullBoxCtor("hdlr",(function(e){0===this.version&&(e.readUint32(),this.handler=e.readString(4),e.readUint32Array(3),this.name=e.readString(this.size-this.hdr_size-20),"\0"===this.name[this.name.length-1]&&(this.name=this.name.slice(0,-1)))})),f.createBoxCtor("hvcC",(function(e){var t,i,r,s;this.configurationVersion=e.readUint8(),s=e.readUint8(),this.general_profile_space=s>>6,this.general_tier_flag=(32&s)>>5,this.general_profile_idc=31&s,this.general_profile_compatibility=e.readUint32(),this.general_constraint_indicator=e.readUint8Array(6),this.general_level_idc=e.readUint8(),this.min_spatial_segmentation_idc=4095&e.readUint16(),this.parallelismType=3&e.readUint8(),this.chroma_format_idc=3&e.readUint8(),this.bit_depth_luma_minus8=7&e.readUint8(),this.bit_depth_chroma_minus8=7&e.readUint8(),this.avgFrameRate=e.readUint16(),s=e.readUint8(),this.constantFrameRate=s>>6,this.numTemporalLayers=(13&s)>>3,this.temporalIdNested=(4&s)>>2,this.lengthSizeMinusOne=3&s,this.nalu_arrays=[];var n=e.readUint8();for(t=0;t<n;t++){var a=[];this.nalu_arrays.push(a),s=e.readUint8(),a.completeness=(128&s)>>7,a.nalu_type=63&s;var o=e.readUint16();for(i=0;i<o;i++){var d={};a.push(d),r=e.readUint16(),d.data=e.readUint8Array(r)}}})),f.createFullBoxCtor("iinf",(function(e){var t;0===this.version?this.entry_count=e.readUint16():this.entry_count=e.readUint32(),this.item_infos=[];for(var i=0;i<this.entry_count;i++){if((t=f.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==f.OK)return;"infe"!==t.box.type&&n.error("BoxParser","Expected 'infe' box, got "+t.box.type),this.item_infos[i]=t.box}})),f.createFullBoxCtor("iloc",(function(e){var t;t=e.readUint8(),this.offset_size=t>>4&15,this.length_size=15&t,t=e.readUint8(),this.base_offset_size=t>>4&15,1===this.version||2===this.version?this.index_size=15&t:this.index_size=0,this.items=[];var i=0;if(this.version<2)i=e.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";i=e.readUint32()}for(var r=0;r<i;r++){var s={};if(this.items.push(s),this.version<2)s.item_ID=e.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";s.item_ID=e.readUint16()}switch(1===this.version||2===this.version?s.construction_method=15&e.readUint16():s.construction_method=0,s.data_reference_index=e.readUint16(),this.base_offset_size){case 0:s.base_offset=0;break;case 4:s.base_offset=e.readUint32();break;case 8:s.base_offset=e.readUint64();break;default:throw"Error reading base offset size"}var n=e.readUint16();s.extents=[];for(var a=0;a<n;a++){var o={};if(s.extents.push(o),1===this.version||2===this.version)switch(this.index_size){case 0:o.extent_index=0;break;case 4:o.extent_index=e.readUint32();break;case 8:o.extent_index=e.readUint64();break;default:throw"Error reading extent index"}switch(this.offset_size){case 0:o.extent_offset=0;break;case 4:o.extent_offset=e.readUint32();break;case 8:o.extent_offset=e.readUint64();break;default:throw"Error reading extent index"}switch(this.length_size){case 0:o.extent_length=0;break;case 4:o.extent_length=e.readUint32();break;case 8:o.extent_length=e.readUint64();break;default:throw"Error reading extent index"}}}})),f.createBoxCtor("imir",(function(e){var t=e.readUint8();this.reserved=t>>7,this.axis=1&t})),f.createFullBoxCtor("infe",(function(e){if(0!==this.version&&1!==this.version||(this.item_ID=e.readUint16(),this.item_protection_index=e.readUint16(),this.item_name=e.readCString(),this.content_type=e.readCString(),this.content_encoding=e.readCString()),1===this.version)return this.extension_type=e.readString(4),n.warn("BoxParser","Cannot parse extension type"),void e.seek(this.start+this.size);this.version>=2&&(2===this.version?this.item_ID=e.readUint16():3===this.version&&(this.item_ID=e.readUint32()),this.item_protection_index=e.readUint16(),this.item_type=e.readString(4),this.item_name=e.readCString(),"mime"===this.item_type?(this.content_type=e.readCString(),this.content_encoding=e.readCString()):"uri "===this.item_type&&(this.item_uri_type=e.readCString()))})),f.createFullBoxCtor("ipma",(function(e){var t,i;for(entry_count=e.readUint32(),this.associations=[],t=0;t<entry_count;t++){var r={};this.associations.push(r),this.version<1?r.id=e.readUint16():r.id=e.readUint32();var s=e.readUint8();for(r.props=[],i=0;i<s;i++){var n=e.readUint8(),a={};r.props.push(a),a.essential=(128&n)>>7==1,1&this.flags?a.property_index=(127&n)<<8|e.readUint8():a.property_index=127&n}}})),f.createFullBoxCtor("iref",(function(e){var t,i;for(this.references=[];e.getPosition()<this.start+this.size;){if((t=f.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==f.OK)return;(i=0===this.version?new f.SingleItemTypeReferenceBox(t.type,t.size,t.hdr_size,t.start):new f.SingleItemTypeReferenceBoxLarge(t.type,t.size,t.hdr_size,t.start)).write===f.Box.prototype.write&&"mdat"!==i.type&&(n.warn("BoxParser",i.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),i.parseDataAndRewind(e)),i.parse(e),this.references.push(i)}})),f.createBoxCtor("irot",(function(e){this.angle=3&e.readUint8()})),f.createFullBoxCtor("ispe",(function(e){this.image_width=e.readUint32(),this.image_height=e.readUint32()})),f.createFullBoxCtor("kind",(function(e){this.schemeURI=e.readCString(),this.value=e.readCString()})),f.createFullBoxCtor("leva",(function(e){var t=e.readUint8();this.levels=[];for(var i=0;i<t;i++){var r={};this.levels[i]=r,r.track_ID=e.readUint32();var s=e.readUint8();switch(r.padding_flag=s>>7,r.assignment_type=127&s,r.assignment_type){case 0:r.grouping_type=e.readString(4);break;case 1:r.grouping_type=e.readString(4),r.grouping_type_parameter=e.readUint32();break;case 2:case 3:break;case 4:r.sub_track_id=e.readUint32();break;default:n.warn("BoxParser","Unknown leva assignement type")}}})),f.createBoxCtor("lsel",(function(e){this.layer_id=e.readUint16()})),f.createBoxCtor("maxr",(function(e){this.period=e.readUint32(),this.bytes=e.readUint32()})),f.createBoxCtor("mdcv",(function(e){this.display_primaries=[],this.display_primaries[0]={},this.display_primaries[0].x=e.readUint16(),this.display_primaries[0].y=e.readUint16(),this.display_primaries[1]={},this.display_primaries[1].x=e.readUint16(),this.display_primaries[1].y=e.readUint16(),this.display_primaries[2]={},this.display_primaries[2].x=e.readUint16(),this.display_primaries[2].y=e.readUint16(),this.white_point={},this.white_point.x=e.readUint16(),this.white_point.y=e.readUint16(),this.max_display_mastering_luminance=e.readUint32(),this.min_display_mastering_luminance=e.readUint32()})),f.createFullBoxCtor("mdhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.parseLanguage(e),e.readUint16()})),f.createFullBoxCtor("mehd",(function(e){1&this.flags&&(n.warn("BoxParser","mehd box incorrectly uses flags set to 1, converting version to 1"),this.version=1),1==this.version?this.fragment_duration=e.readUint64():this.fragment_duration=e.readUint32()})),f.createFullBoxCtor("meta",(function(e){this.boxes=[],f.ContainerBox.prototype.parse.call(this,e)})),f.createFullBoxCtor("mfhd",(function(e){this.sequence_number=e.readUint32()})),f.createFullBoxCtor("mfro",(function(e){this._size=e.readUint32()})),f.createFullBoxCtor("mvhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.rate=e.readUint32(),this.volume=e.readUint16()>>8,e.readUint16(),e.readUint32Array(2),this.matrix=e.readUint32Array(9),e.readUint32Array(6),this.next_track_id=e.readUint32()})),f.createBoxCtor("npck",(function(e){this.packetssent=e.readUint32()})),f.createBoxCtor("nump",(function(e){this.packetssent=e.readUint64()})),f.createFullBoxCtor("padb",(function(e){var t=e.readUint32();this.padbits=[];for(var i=0;i<Math.floor((t+1)/2);i++)this.padbits=e.readUint8()})),f.createBoxCtor("pasp",(function(e){this.hSpacing=e.readUint32(),this.vSpacing=e.readUint32()})),f.createBoxCtor("payl",(function(e){this.text=e.readString(this.size-this.hdr_size)})),f.createBoxCtor("payt",(function(e){this.payloadID=e.readUint32();var t=e.readUint8();this.rtpmap_string=e.readString(t)})),f.createFullBoxCtor("pdin",(function(e){var t=(this.size-this.hdr_size)/8;this.rate=[],this.initial_delay=[];for(var i=0;i<t;i++)this.rate[i]=e.readUint32(),this.initial_delay[i]=e.readUint32()})),f.createFullBoxCtor("pitm",(function(e){0===this.version?this.item_id=e.readUint16():this.item_id=e.readUint32()})),f.createFullBoxCtor("pixi",(function(e){var t;for(this.num_channels=e.readUint8(),this.bits_per_channels=[],t=0;t<this.num_channels;t++)this.bits_per_channels[t]=e.readUint8()})),f.createBoxCtor("pmax",(function(e){this.bytes=e.readUint32()})),f.createFullBoxCtor("prft",(function(e){this.ref_track_id=e.readUint32(),this.ntp_timestamp=e.readUint64(),0===this.version?this.media_time=e.readUint32():this.media_time=e.readUint64()})),f.createFullBoxCtor("pssh",(function(e){if(this.system_id=f.parseHex16(e),this.version>0){var t=e.readUint32();this.kid=[];for(var i=0;i<t;i++)this.kid[i]=f.parseHex16(e)}var r=e.readUint32();r>0&&(this.data=e.readUint8Array(r))})),f.createFullBoxCtor("clef",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),f.createFullBoxCtor("enof",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),f.createFullBoxCtor("prof",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),f.createContainerBoxCtor("tapt",null,["clef","prof","enof"]),f.createBoxCtor("rtp ",(function(e){this.descriptionformat=e.readString(4),this.sdptext=e.readString(this.size-this.hdr_size-4)})),f.createFullBoxCtor("saio",(function(e){1&this.flags&&(this.aux_info_type=e.readUint32(),this.aux_info_type_parameter=e.readUint32());var t=e.readUint32();this.offset=[];for(var i=0;i<t;i++)0===this.version?this.offset[i]=e.readUint32():this.offset[i]=e.readUint64()})),f.createFullBoxCtor("saiz",(function(e){1&this.flags&&(this.aux_info_type=e.readUint32(),this.aux_info_type_parameter=e.readUint32()),this.default_sample_info_size=e.readUint8();var t=e.readUint32();if(this.sample_info_size=[],0===this.default_sample_info_size)for(var i=0;i<t;i++)this.sample_info_size[i]=e.readUint8()})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_METADATA,"mett",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_METADATA,"metx",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.namespace=e.readCString(),this.schema_location=e.readCString(),this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SUBTITLE,"sbtt",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SUBTITLE,"stpp",(function(e){this.parseHeader(e),this.namespace=e.readCString(),this.schema_location=e.readCString(),this.auxiliary_mime_types=e.readCString(),this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SUBTITLE,"stxt",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_SUBTITLE,"tx3g",(function(e){this.parseHeader(e),this.displayFlags=e.readUint32(),this.horizontal_justification=e.readInt8(),this.vertical_justification=e.readInt8(),this.bg_color_rgba=e.readUint8Array(4),this.box_record=e.readInt16Array(4),this.style_record=e.readUint8Array(12),this.parseFooter(e)})),f.createSampleEntryCtor(f.SAMPLE_ENTRY_TYPE_METADATA,"wvtt",(function(e){this.parseHeader(e),this.parseFooter(e)})),f.createSampleGroupCtor("alst",(function(e){var t,i=e.readUint16();for(this.first_output_sample=e.readUint16(),this.sample_offset=[],t=0;t<i;t++)this.sample_offset[t]=e.readUint32();var r=this.description_length-4-4*i;for(this.num_output_samples=[],this.num_total_samples=[],t=0;t<r/4;t++)this.num_output_samples[t]=e.readUint16(),this.num_total_samples[t]=e.readUint16()})),f.createSampleGroupCtor("avll",(function(e){this.layerNumber=e.readUint8(),this.accurateStatisticsFlag=e.readUint8(),this.avgBitRate=e.readUint16(),this.avgFrameRate=e.readUint16()})),f.createSampleGroupCtor("avss",(function(e){this.subSequenceIdentifier=e.readUint16(),this.layerNumber=e.readUint8();var t=e.readUint8();this.durationFlag=t>>7,this.avgRateFlag=t>>6&1,this.durationFlag&&(this.duration=e.readUint32()),this.avgRateFlag&&(this.accurateStatisticsFlag=e.readUint8(),this.avgBitRate=e.readUint16(),this.avgFrameRate=e.readUint16()),this.dependency=[];for(var i=e.readUint8(),r=0;r<i;r++){var s={};this.dependency.push(s),s.subSeqDirectionFlag=e.readUint8(),s.layerNumber=e.readUint8(),s.subSequenceIdentifier=e.readUint16()}})),f.createSampleGroupCtor("dtrt",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("mvif",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("prol",(function(e){this.roll_distance=e.readInt16()})),f.createSampleGroupCtor("rap ",(function(e){var t=e.readUint8();this.num_leading_samples_known=t>>7,this.num_leading_samples=127&t})),f.createSampleGroupCtor("rash",(function(e){if(this.operation_point_count=e.readUint16(),this.description_length!==2+(1===this.operation_point_count?2:6*this.operation_point_count)+9)n.warn("BoxParser","Mismatch in "+this.grouping_type+" sample group length"),this.data=e.readUint8Array(this.description_length-2);else{if(1===this.operation_point_count)this.target_rate_share=e.readUint16();else{this.target_rate_share=[],this.available_bitrate=[];for(var t=0;t<this.operation_point_count;t++)this.available_bitrate[t]=e.readUint32(),this.target_rate_share[t]=e.readUint16()}this.maximum_bitrate=e.readUint32(),this.minimum_bitrate=e.readUint32(),this.discard_priority=e.readUint8()}})),f.createSampleGroupCtor("roll",(function(e){this.roll_distance=e.readInt16()})),f.SampleGroupEntry.prototype.parse=function(e){n.warn("BoxParser","Unknown Sample Group type: "+this.grouping_type),this.data=e.readUint8Array(this.description_length)},f.createSampleGroupCtor("scif",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("scnm",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("seig",(function(e){this.reserved=e.readUint8();var t=e.readUint8();this.crypt_byte_block=t>>4,this.skip_byte_block=15&t,this.isProtected=e.readUint8(),this.Per_Sample_IV_Size=e.readUint8(),this.KID=f.parseHex16(e),this.constant_IV_size=0,this.constant_IV=0,1===this.isProtected&&0===this.Per_Sample_IV_Size&&(this.constant_IV_size=e.readUint8(),this.constant_IV=e.readUint8Array(this.constant_IV_size))})),f.createSampleGroupCtor("stsa",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("sync",(function(e){var t=e.readUint8();this.NAL_unit_type=63&t})),f.createSampleGroupCtor("tele",(function(e){var t=e.readUint8();this.level_independently_decodable=t>>7})),f.createSampleGroupCtor("tsas",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("tscl",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createSampleGroupCtor("vipr",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),f.createFullBoxCtor("sbgp",(function(e){this.grouping_type=e.readString(4),1===this.version?this.grouping_type_parameter=e.readUint32():this.grouping_type_parameter=0,this.entries=[];for(var t=e.readUint32(),i=0;i<t;i++){var r={};this.entries.push(r),r.sample_count=e.readInt32(),r.group_description_index=e.readInt32()}})),f.createFullBoxCtor("schm",(function(e){this.scheme_type=e.readString(4),this.scheme_version=e.readUint32(),1&this.flags&&(this.scheme_uri=e.readString(this.size-this.hdr_size-8))})),f.createBoxCtor("sdp ",(function(e){this.sdptext=e.readString(this.size-this.hdr_size)})),f.createFullBoxCtor("sdtp",(function(e){var t,i=this.size-this.hdr_size;this.is_leading=[],this.sample_depends_on=[],this.sample_is_depended_on=[],this.sample_has_redundancy=[];for(var r=0;r<i;r++)t=e.readUint8(),this.is_leading[r]=t>>6,this.sample_depends_on[r]=t>>4&3,this.sample_is_depended_on[r]=t>>2&3,this.sample_has_redundancy[r]=3&t})),f.createFullBoxCtor("senc"),f.createFullBoxCtor("sgpd",(function(e){this.grouping_type=e.readString(4),n.debug("BoxParser","Found Sample Groups of type "+this.grouping_type),1===this.version?this.default_length=e.readUint32():this.default_length=0,this.version>=2&&(this.default_group_description_index=e.readUint32()),this.entries=[];for(var t=e.readUint32(),i=0;i<t;i++){var r;r=f[this.grouping_type+"SampleGroupEntry"]?new f[this.grouping_type+"SampleGroupEntry"](this.grouping_type):new f.SampleGroupEntry(this.grouping_type),this.entries.push(r),1===this.version&&0===this.default_length?r.description_length=e.readUint32():r.description_length=this.default_length,r.write===f.SampleGroupEntry.prototype.write&&(n.info("BoxParser","SampleGroup for type "+this.grouping_type+" writing not yet implemented, keeping unparsed data in memory for later write"),r.data=e.readUint8Array(r.description_length),e.position-=r.description_length),r.parse(e)}})),f.createFullBoxCtor("sidx",(function(e){this.reference_ID=e.readUint32(),this.timescale=e.readUint32(),0===this.version?(this.earliest_presentation_time=e.readUint32(),this.first_offset=e.readUint32()):(this.earliest_presentation_time=e.readUint64(),this.first_offset=e.readUint64()),e.readUint16(),this.references=[];for(var t=e.readUint16(),i=0;i<t;i++){var r={};this.references.push(r);var s=e.readUint32();r.reference_type=s>>31&1,r.referenced_size=2147483647&s,r.subsegment_duration=e.readUint32(),s=e.readUint32(),r.starts_with_SAP=s>>31&1,r.SAP_type=s>>28&7,r.SAP_delta_time=268435455&s}})),f.SingleItemTypeReferenceBox=function(e,t,i,r){f.Box.call(this,e,t),this.hdr_size=i,this.start=r},f.SingleItemTypeReferenceBox.prototype=new f.Box,f.SingleItemTypeReferenceBox.prototype.parse=function(e){this.from_item_ID=e.readUint16();var t=e.readUint16();this.references=[];for(var i=0;i<t;i++)this.references[i]=e.readUint16()},f.SingleItemTypeReferenceBoxLarge=function(e,t,i,r){f.Box.call(this,e,t),this.hdr_size=i,this.start=r},f.SingleItemTypeReferenceBoxLarge.prototype=new f.Box,f.SingleItemTypeReferenceBoxLarge.prototype.parse=function(e){this.from_item_ID=e.readUint32();var t=e.readUint16();this.references=[];for(var i=0;i<t;i++)this.references[i]=e.readUint32()},f.createFullBoxCtor("SmDm",(function(e){this.primaryRChromaticity_x=e.readUint16(),this.primaryRChromaticity_y=e.readUint16(),this.primaryGChromaticity_x=e.readUint16(),this.primaryGChromaticity_y=e.readUint16(),this.primaryBChromaticity_x=e.readUint16(),this.primaryBChromaticity_y=e.readUint16(),this.whitePointChromaticity_x=e.readUint16(),this.whitePointChromaticity_y=e.readUint16(),this.luminanceMax=e.readUint32(),this.luminanceMin=e.readUint32()})),f.createFullBoxCtor("smhd",(function(e){this.balance=e.readUint16(),e.readUint16()})),f.createFullBoxCtor("ssix",(function(e){this.subsegments=[];for(var t=e.readUint32(),i=0;i<t;i++){var r={};this.subsegments.push(r),r.ranges=[];for(var s=e.readUint32(),n=0;n<s;n++){var a={};r.ranges.push(a),a.level=e.readUint8(),a.range_size=e.readUint24()}}})),f.createFullBoxCtor("stco",(function(e){var t;if(t=e.readUint32(),this.chunk_offsets=[],0===this.version)for(var i=0;i<t;i++)this.chunk_offsets.push(e.readUint32())})),f.createFullBoxCtor("stdp",(function(e){var t=(this.size-this.hdr_size)/2;this.priority=[];for(var i=0;i<t;i++)this.priority[i]=e.readUint16()})),f.createFullBoxCtor("sthd"),f.createFullBoxCtor("stri",(function(e){this.switch_group=e.readUint16(),this.alternate_group=e.readUint16(),this.sub_track_id=e.readUint32();var t=(this.size-this.hdr_size-8)/4;this.attribute_list=[];for(var i=0;i<t;i++)this.attribute_list[i]=e.readUint32()})),f.createFullBoxCtor("stsc",(function(e){var t,i;if(t=e.readUint32(),this.first_chunk=[],this.samples_per_chunk=[],this.sample_description_index=[],0===this.version)for(i=0;i<t;i++)this.first_chunk.push(e.readUint32()),this.samples_per_chunk.push(e.readUint32()),this.sample_description_index.push(e.readUint32())})),f.createFullBoxCtor("stsd",(function(e){var t,i,r,s;for(this.entries=[],r=e.readUint32(),t=1;t<=r;t++){if((i=f.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==f.OK)return;f[i.type+"SampleEntry"]?((s=new f[i.type+"SampleEntry"](i.size)).hdr_size=i.hdr_size,s.start=i.start):(n.warn("BoxParser","Unknown sample entry type: "+i.type),s=new f.SampleEntry(i.type,i.size,i.hdr_size,i.start)),s.write===f.SampleEntry.prototype.write&&(n.info("BoxParser","SampleEntry "+s.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),s.parseDataAndRewind(e)),s.parse(e),this.entries.push(s)}})),f.createFullBoxCtor("stsg",(function(e){this.grouping_type=e.readUint32();var t=e.readUint16();this.group_description_index=[];for(var i=0;i<t;i++)this.group_description_index[i]=e.readUint32()})),f.createFullBoxCtor("stsh",(function(e){var t,i;if(t=e.readUint32(),this.shadowed_sample_numbers=[],this.sync_sample_numbers=[],0===this.version)for(i=0;i<t;i++)this.shadowed_sample_numbers.push(e.readUint32()),this.sync_sample_numbers.push(e.readUint32())})),f.createFullBoxCtor("stss",(function(e){var t,i;if(i=e.readUint32(),0===this.version)for(this.sample_numbers=[],t=0;t<i;t++)this.sample_numbers.push(e.readUint32())})),f.createFullBoxCtor("stsz",(function(e){var t;if(this.sample_sizes=[],0===this.version)for(this.sample_size=e.readUint32(),this.sample_count=e.readUint32(),t=0;t<this.sample_count;t++)0===this.sample_size?this.sample_sizes.push(e.readUint32()):this.sample_sizes[t]=this.sample_size})),f.createFullBoxCtor("stts",(function(e){var t,i,r;if(t=e.readUint32(),this.sample_counts=[],this.sample_deltas=[],0===this.version)for(i=0;i<t;i++)this.sample_counts.push(e.readUint32()),(r=e.readInt32())<0&&(n.warn("BoxParser","File uses negative stts sample delta, using value 1 instead, sync may be lost!"),r=1),this.sample_deltas.push(r)})),f.createFullBoxCtor("stvi",(function(e){var t=e.readUint32();this.single_view_allowed=3&t,this.stereo_scheme=e.readUint32();var i,r,s=e.readUint32();for(this.stereo_indication_type=e.readString(s),this.boxes=[];e.getPosition()<this.start+this.size;){if((i=f.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==f.OK)return;r=i.box,this.boxes.push(r),this[r.type]=r}})),f.createBoxCtor("styp",(function(e){f.ftypBox.prototype.parse.call(this,e)})),f.createFullBoxCtor("stz2",(function(e){var t,i;if(this.sample_sizes=[],0===this.version)if(this.reserved=e.readUint24(),this.field_size=e.readUint8(),i=e.readUint32(),4===this.field_size)for(t=0;t<i;t+=2){var r=e.readUint8();this.sample_sizes[t]=r>>4&15,this.sample_sizes[t+1]=15&r}else if(8===this.field_size)for(t=0;t<i;t++)this.sample_sizes[t]=e.readUint8();else if(16===this.field_size)for(t=0;t<i;t++)this.sample_sizes[t]=e.readUint16();else n.error("BoxParser","Error in length field in stz2 box")})),f.createFullBoxCtor("subs",(function(e){var t,i,r,s;for(r=e.readUint32(),this.entries=[],t=0;t<r;t++){var n={};if(this.entries[t]=n,n.sample_delta=e.readUint32(),n.subsamples=[],(s=e.readUint16())>0)for(i=0;i<s;i++){var a={};n.subsamples.push(a),1==this.version?a.size=e.readUint32():a.size=e.readUint16(),a.priority=e.readUint8(),a.discardable=e.readUint8(),a.codec_specific_parameters=e.readUint32()}}})),f.createFullBoxCtor("tenc",(function(e){if(e.readUint8(),0===this.version)e.readUint8();else{var t=e.readUint8();this.default_crypt_byte_block=t>>4&15,this.default_skip_byte_block=15&t}this.default_isProtected=e.readUint8(),this.default_Per_Sample_IV_Size=e.readUint8(),this.default_KID=f.parseHex16(e),1===this.default_isProtected&&0===this.default_Per_Sample_IV_Size&&(this.default_constant_IV_size=e.readUint8(),this.default_constant_IV=e.readUint8Array(this.default_constant_IV_size))})),f.createFullBoxCtor("tfdt",(function(e){1==this.version?this.baseMediaDecodeTime=e.readUint64():this.baseMediaDecodeTime=e.readUint32()})),f.createFullBoxCtor("tfhd",(function(e){var t=0;this.track_id=e.readUint32(),this.size-this.hdr_size>t&&this.flags&f.TFHD_FLAG_BASE_DATA_OFFSET?(this.base_data_offset=e.readUint64(),t+=8):this.base_data_offset=0,this.size-this.hdr_size>t&&this.flags&f.TFHD_FLAG_SAMPLE_DESC?(this.default_sample_description_index=e.readUint32(),t+=4):this.default_sample_description_index=0,this.size-this.hdr_size>t&&this.flags&f.TFHD_FLAG_SAMPLE_DUR?(this.default_sample_duration=e.readUint32(),t+=4):this.default_sample_duration=0,this.size-this.hdr_size>t&&this.flags&f.TFHD_FLAG_SAMPLE_SIZE?(this.default_sample_size=e.readUint32(),t+=4):this.default_sample_size=0,this.size-this.hdr_size>t&&this.flags&f.TFHD_FLAG_SAMPLE_FLAGS?(this.default_sample_flags=e.readUint32(),t+=4):this.default_sample_flags=0})),f.createFullBoxCtor("tfra",(function(e){this.track_ID=e.readUint32(),e.readUint24();var t=e.readUint8();this.length_size_of_traf_num=t>>4&3,this.length_size_of_trun_num=t>>2&3,this.length_size_of_sample_num=3&t,this.entries=[];for(var i=e.readUint32(),r=0;r<i;r++)1===this.version?(this.time=e.readUint64(),this.moof_offset=e.readUint64()):(this.time=e.readUint32(),this.moof_offset=e.readUint32()),this.traf_number=e["readUint"+8*(this.length_size_of_traf_num+1)](),this.trun_number=e["readUint"+8*(this.length_size_of_trun_num+1)](),this.sample_number=e["readUint"+8*(this.length_size_of_sample_num+1)]()})),f.createFullBoxCtor("tkhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.track_id=e.readUint32(),e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.track_id=e.readUint32(),e.readUint32(),this.duration=e.readUint32()),e.readUint32Array(2),this.layer=e.readInt16(),this.alternate_group=e.readInt16(),this.volume=e.readInt16()>>8,e.readUint16(),this.matrix=e.readInt32Array(9),this.width=e.readUint32(),this.height=e.readUint32()})),f.createBoxCtor("tmax",(function(e){this.time=e.readUint32()})),f.createBoxCtor("tmin",(function(e){this.time=e.readUint32()})),f.createBoxCtor("totl",(function(e){this.bytessent=e.readUint32()})),f.createBoxCtor("tpay",(function(e){this.bytessent=e.readUint32()})),f.createBoxCtor("tpyl",(function(e){this.bytessent=e.readUint64()})),f.TrackGroupTypeBox.prototype.parse=function(e){this.parseFullHeader(e),this.track_group_id=e.readUint32()},f.createTrackGroupCtor("msrc"),f.TrackReferenceTypeBox=function(e,t,i,r){f.Box.call(this,e,t),this.hdr_size=i,this.start=r},f.TrackReferenceTypeBox.prototype=new f.Box,f.TrackReferenceTypeBox.prototype.parse=function(e){this.track_ids=e.readUint32Array((this.size-this.hdr_size)/4)},f.trefBox.prototype.parse=function(e){for(var t,i;e.getPosition()<this.start+this.size;){if((t=f.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==f.OK)return;(i=new f.TrackReferenceTypeBox(t.type,t.size,t.hdr_size,t.start)).write===f.Box.prototype.write&&"mdat"!==i.type&&(n.info("BoxParser","TrackReference "+i.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),i.parseDataAndRewind(e)),i.parse(e),this.boxes.push(i)}},f.createFullBoxCtor("trep",(function(e){for(this.track_ID=e.readUint32(),this.boxes=[];e.getPosition()<this.start+this.size;){if(ret=f.parseOneBox(e,!1,this.size-(e.getPosition()-this.start)),ret.code!==f.OK)return;box=ret.box,this.boxes.push(box)}})),f.createFullBoxCtor("trex",(function(e){this.track_id=e.readUint32(),this.default_sample_description_index=e.readUint32(),this.default_sample_duration=e.readUint32(),this.default_sample_size=e.readUint32(),this.default_sample_flags=e.readUint32()})),f.createBoxCtor("trpy",(function(e){this.bytessent=e.readUint64()})),f.createFullBoxCtor("trun",(function(e){var t=0;if(this.sample_count=e.readUint32(),t+=4,this.size-this.hdr_size>t&&this.flags&f.TRUN_FLAGS_DATA_OFFSET?(this.data_offset=e.readInt32(),t+=4):this.data_offset=0,this.size-this.hdr_size>t&&this.flags&f.TRUN_FLAGS_FIRST_FLAG?(this.first_sample_flags=e.readUint32(),t+=4):this.first_sample_flags=0,this.sample_duration=[],this.sample_size=[],this.sample_flags=[],this.sample_composition_time_offset=[],this.size-this.hdr_size>t)for(var i=0;i<this.sample_count;i++)this.flags&f.TRUN_FLAGS_DURATION&&(this.sample_duration[i]=e.readUint32()),this.flags&f.TRUN_FLAGS_SIZE&&(this.sample_size[i]=e.readUint32()),this.flags&f.TRUN_FLAGS_FLAGS&&(this.sample_flags[i]=e.readUint32()),this.flags&f.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?this.sample_composition_time_offset[i]=e.readUint32():this.sample_composition_time_offset[i]=e.readInt32())})),f.createFullBoxCtor("tsel",(function(e){this.switch_group=e.readUint32();var t=(this.size-this.hdr_size-4)/4;this.attribute_list=[];for(var i=0;i<t;i++)this.attribute_list[i]=e.readUint32()})),f.createFullBoxCtor("txtC",(function(e){this.config=e.readCString()})),f.createFullBoxCtor("url ",(function(e){1!==this.flags&&(this.location=e.readCString())})),f.createFullBoxCtor("urn ",(function(e){this.name=e.readCString(),this.size-this.hdr_size-this.name.length-1>0&&(this.location=e.readCString())})),f.createUUIDBox("********************************",!0,!1,(function(e){this.LiveServerManifest=e.readString(this.size-this.hdr_size).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})),f.createUUIDBox("********************************",!0,!1,(function(e){this.system_id=f.parseHex16(e);var t=e.readUint32();t>0&&(this.data=e.readUint8Array(t))})),f.createUUIDBox("********************************",!0,!1),f.createUUIDBox("********************************",!0,!1,(function(e){this.default_AlgorithmID=e.readUint24(),this.default_IV_size=e.readUint8(),this.default_KID=f.parseHex16(e)})),f.createUUIDBox("********************************",!0,!1,(function(e){this.fragment_count=e.readUint8(),this.entries=[];for(var t=0;t<this.fragment_count;t++){var i={},r=0,s=0;1===this.version?(r=e.readUint64(),s=e.readUint64()):(r=e.readUint32(),s=e.readUint32()),i.absolute_time=r,i.absolute_duration=s,this.entries.push(i)}})),f.createUUIDBox("********************************",!0,!1,(function(e){1===this.version?(this.absolute_time=e.readUint64(),this.duration=e.readUint64()):(this.absolute_time=e.readUint32(),this.duration=e.readUint32())})),f.createFullBoxCtor("vmhd",(function(e){this.graphicsmode=e.readUint16(),this.opcolor=e.readUint16Array(3)})),f.createFullBoxCtor("vpcC",(function(e){var t;1===this.version?(this.profile=e.readUint8(),this.level=e.readUint8(),t=e.readUint8(),this.bitDepth=t>>4,this.chromaSubsampling=t>>1&7,this.videoFullRangeFlag=1&t,this.colourPrimaries=e.readUint8(),this.transferCharacteristics=e.readUint8(),this.matrixCoefficients=e.readUint8(),this.codecIntializationDataSize=e.readUint16(),this.codecIntializationData=e.readUint8Array(this.codecIntializationDataSize)):(this.profile=e.readUint8(),this.level=e.readUint8(),t=e.readUint8(),this.bitDepth=t>>4&15,this.colorSpace=15&t,t=e.readUint8(),this.chromaSubsampling=t>>4&15,this.transferFunction=t>>1&7,this.videoFullRangeFlag=1&t,this.codecIntializationDataSize=e.readUint16(),this.codecIntializationData=e.readUint8Array(this.codecIntializationDataSize))})),f.createBoxCtor("vttC",(function(e){this.text=e.readString(this.size-this.hdr_size)})),f.createFullBoxCtor("vvcC",(function(e){var t,i,r={held_bits:void 0,num_held_bits:0,stream_read_1_bytes:function(e){this.held_bits=e.readUint8(),this.num_held_bits=8},stream_read_2_bytes:function(e){this.held_bits=e.readUint16(),this.num_held_bits=16},extract_bits:function(e){var t=this.held_bits>>this.num_held_bits-e&(1<<e)-1;return this.num_held_bits-=e,t}};if(r.stream_read_1_bytes(e),r.extract_bits(5),this.lengthSizeMinusOne=r.extract_bits(2),this.ptl_present_flag=r.extract_bits(1),this.ptl_present_flag){if(r.stream_read_2_bytes(e),this.ols_idx=r.extract_bits(9),this.num_sublayers=r.extract_bits(3),this.constant_frame_rate=r.extract_bits(2),this.chroma_format_idc=r.extract_bits(2),r.stream_read_1_bytes(e),this.bit_depth_minus8=r.extract_bits(3),r.extract_bits(5),r.stream_read_2_bytes(e),r.extract_bits(2),this.num_bytes_constraint_info=r.extract_bits(6),this.general_profile_idc=r.extract_bits(7),this.general_tier_flag=r.extract_bits(1),this.general_level_idc=e.readUint8(),r.stream_read_1_bytes(e),this.ptl_frame_only_constraint_flag=r.extract_bits(1),this.ptl_multilayer_enabled_flag=r.extract_bits(1),this.general_constraint_info=new Uint8Array(this.num_bytes_constraint_info),this.num_bytes_constraint_info){for(t=0;t<this.num_bytes_constraint_info-1;t++){var s=r.extract_bits(6);r.stream_read_1_bytes(e);var n=r.extract_bits(2);this.general_constraint_info[t]=s<<2|n}this.general_constraint_info[this.num_bytes_constraint_info-1]=r.extract_bits(6)}else r.extract_bits(6);for(r.stream_read_1_bytes(e),this.ptl_sublayer_present_mask=0,i=this.num_sublayers-2;i>=0;--i){var a=r.extract_bits(1);this.ptl_sublayer_present_mask|=a<<i}for(i=this.num_sublayers;i<=8&&this.num_sublayers>1;++i)r.extract_bits(1);for(i=this.num_sublayers-2;i>=0;--i)this.ptl_sublayer_present_mask&1<<i&&(this.sublayer_level_idc[i]=e.readUint8());if(this.ptl_num_sub_profiles=e.readUint8(),this.general_sub_profile_idc=[],this.ptl_num_sub_profiles)for(t=0;t<this.ptl_num_sub_profiles;t++)this.general_sub_profile_idc.push(e.readUint32());this.max_picture_width=e.readUint16(),this.max_picture_height=e.readUint16(),this.avg_frame_rate=e.readUint16()}this.nalu_arrays=[];var o=e.readUint8();for(t=0;t<o;t++){var d=[];this.nalu_arrays.push(d),r.stream_read_1_bytes(e),d.completeness=r.extract_bits(1),r.extract_bits(2),d.nalu_type=r.extract_bits(5);var l=1;for(13!=d.nalu_type&&12!=d.nalu_type&&(l=e.readUint16()),i=0;i<l;i++){var h=e.readUint16();d.push({data:e.readUint8Array(h),length:h})}}})),f.createFullBoxCtor("vvnC",(function(e){var t=strm.readUint8();this.lengthSizeMinusOne=3&t})),f.SampleEntry.prototype.isVideo=function(){return!1},f.SampleEntry.prototype.isAudio=function(){return!1},f.SampleEntry.prototype.isSubtitle=function(){return!1},f.SampleEntry.prototype.isMetadata=function(){return!1},f.SampleEntry.prototype.isHint=function(){return!1},f.SampleEntry.prototype.getCodec=function(){return this.type.replace(".","")},f.SampleEntry.prototype.getWidth=function(){return""},f.SampleEntry.prototype.getHeight=function(){return""},f.SampleEntry.prototype.getChannelCount=function(){return""},f.SampleEntry.prototype.getSampleRate=function(){return""},f.SampleEntry.prototype.getSampleSize=function(){return""},f.VisualSampleEntry.prototype.isVideo=function(){return!0},f.VisualSampleEntry.prototype.getWidth=function(){return this.width},f.VisualSampleEntry.prototype.getHeight=function(){return this.height},f.AudioSampleEntry.prototype.isAudio=function(){return!0},f.AudioSampleEntry.prototype.getChannelCount=function(){return this.channel_count},f.AudioSampleEntry.prototype.getSampleRate=function(){return this.samplerate},f.AudioSampleEntry.prototype.getSampleSize=function(){return this.samplesize},f.SubtitleSampleEntry.prototype.isSubtitle=function(){return!0},f.MetadataSampleEntry.prototype.isMetadata=function(){return!0},f.decimalToHex=function(e,t){var i=Number(e).toString(16);for(t=null==t?t=2:t;i.length<t;)i="0"+i;return i},f.avc1SampleEntry.prototype.getCodec=f.avc2SampleEntry.prototype.getCodec=f.avc3SampleEntry.prototype.getCodec=f.avc4SampleEntry.prototype.getCodec=function(){var e=f.SampleEntry.prototype.getCodec.call(this);return this.avcC?e+"."+f.decimalToHex(this.avcC.AVCProfileIndication)+f.decimalToHex(this.avcC.profile_compatibility)+f.decimalToHex(this.avcC.AVCLevelIndication):e},f.hev1SampleEntry.prototype.getCodec=f.hvc1SampleEntry.prototype.getCodec=function(){var e,t=f.SampleEntry.prototype.getCodec.call(this);if(this.hvcC){switch(t+=".",this.hvcC.general_profile_space){case 0:t+="";break;case 1:t+="A";break;case 2:t+="B";break;case 3:t+="C"}t+=this.hvcC.general_profile_idc,t+=".";var i=this.hvcC.general_profile_compatibility,r=0;for(e=0;e<32&&(r|=1&i,31!=e);e++)r<<=1,i>>=1;t+=f.decimalToHex(r,0),t+=".",0===this.hvcC.general_tier_flag?t+="L":t+="H",t+=this.hvcC.general_level_idc;var s=!1,n="";for(e=5;e>=0;e--)(this.hvcC.general_constraint_indicator[e]||s)&&(n="."+f.decimalToHex(this.hvcC.general_constraint_indicator[e],0)+n,s=!0);t+=n}return t},f.vvc1SampleEntry.prototype.getCodec=f.vvi1SampleEntry.prototype.getCodec=function(){var e,t=f.SampleEntry.prototype.getCodec.call(this);if(this.vvcC){t+="."+this.vvcC.general_profile_idc,this.vvcC.general_tier_flag?t+=".H":t+=".L",t+=this.vvcC.general_level_idc;var i="";if(this.vvcC.general_constraint_info){var r,s=[],n=0;for(n|=this.vvcC.ptl_frame_only_constraint<<7,n|=this.vvcC.ptl_multilayer_enabled<<6,e=0;e<this.vvcC.general_constraint_info.length;++e)n|=this.vvcC.general_constraint_info[e]>>2&63,s.push(n),n&&(r=e),n=this.vvcC.general_constraint_info[e]>>2&3;if(void 0===r)i=".CA";else{i=".C";var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",o=0,d=0;for(e=0;e<=r;++e)for(o=o<<8|s[e],d+=8;d>=5;){i+=a[o>>d-5&31],o&=(1<<(d-=5))-1}d&&(i+=a[31&(o<<=5-d)])}}t+=i}return t},f.mp4aSampleEntry.prototype.getCodec=function(){var e=f.SampleEntry.prototype.getCodec.call(this);if(this.esds&&this.esds.esd){var t=this.esds.esd.getOTI(),i=this.esds.esd.getAudioConfig();return e+"."+f.decimalToHex(t)+(i?"."+i:"")}return e},f.stxtSampleEntry.prototype.getCodec=function(){var e=f.SampleEntry.prototype.getCodec.call(this);return this.mime_format?e+"."+this.mime_format:e},f.vp08SampleEntry.prototype.getCodec=f.vp09SampleEntry.prototype.getCodec=function(){var e=f.SampleEntry.prototype.getCodec.call(this),t=this.vpcC.level;0==t&&(t="00");var i=this.vpcC.bitDepth;return 8==i&&(i="08"),e+".0"+this.vpcC.profile+"."+t+"."+i},f.av01SampleEntry.prototype.getCodec=function(){var e,t=f.SampleEntry.prototype.getCodec.call(this),i=this.av1C.seq_level_idx_0;return i<10&&(i="0"+i),2===this.av1C.seq_profile&&1===this.av1C.high_bitdepth?e=1===this.av1C.twelve_bit?"12":"10":this.av1C.seq_profile<=2&&(e=1===this.av1C.high_bitdepth?"10":"08"),t+"."+this.av1C.seq_profile+"."+i+(this.av1C.seq_tier_0?"H":"M")+"."+e},f.Box.prototype.writeHeader=function(e,t){this.size+=8,this.size>d&&(this.size+=8),"uuid"===this.type&&(this.size+=16),n.debug("BoxWriter","Writing box "+this.type+" of size: "+this.size+" at position "+e.getPosition()+(t||"")),this.size>d?e.writeUint32(1):(this.sizePosition=e.getPosition(),e.writeUint32(this.size)),e.writeString(this.type,null,4),"uuid"===this.type&&e.writeUint8Array(this.uuid),this.size>d&&e.writeUint64(this.size)},f.FullBox.prototype.writeHeader=function(e){this.size+=4,f.Box.prototype.writeHeader.call(this,e," v="+this.version+" f="+this.flags),e.writeUint8(this.version),e.writeUint24(this.flags)},f.Box.prototype.write=function(e){"mdat"===this.type?this.data&&(this.size=this.data.length,this.writeHeader(e),e.writeUint8Array(this.data)):(this.size=this.data?this.data.length:0,this.writeHeader(e),this.data&&e.writeUint8Array(this.data))},f.ContainerBox.prototype.write=function(e){this.size=0,this.writeHeader(e);for(var t=0;t<this.boxes.length;t++)this.boxes[t]&&(this.boxes[t].write(e),this.size+=this.boxes[t].size);n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},f.TrackReferenceTypeBox.prototype.write=function(e){this.size=4*this.track_ids.length,this.writeHeader(e),e.writeUint32Array(this.track_ids)},f.avcCBox.prototype.write=function(e){var t;for(this.size=7,t=0;t<this.SPS.length;t++)this.size+=2+this.SPS[t].length;for(t=0;t<this.PPS.length;t++)this.size+=2+this.PPS[t].length;for(this.ext&&(this.size+=this.ext.length),this.writeHeader(e),e.writeUint8(this.configurationVersion),e.writeUint8(this.AVCProfileIndication),e.writeUint8(this.profile_compatibility),e.writeUint8(this.AVCLevelIndication),e.writeUint8(this.lengthSizeMinusOne+252),e.writeUint8(this.SPS.length+224),t=0;t<this.SPS.length;t++)e.writeUint16(this.SPS[t].length),e.writeUint8Array(this.SPS[t].nalu);for(e.writeUint8(this.PPS.length),t=0;t<this.PPS.length;t++)e.writeUint16(this.PPS[t].length),e.writeUint8Array(this.PPS[t].nalu);this.ext&&e.writeUint8Array(this.ext)},f.co64Box.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.chunk_offsets.length,this.writeHeader(e),e.writeUint32(this.chunk_offsets.length),t=0;t<this.chunk_offsets.length;t++)e.writeUint64(this.chunk_offsets[t])},f.cslgBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20,this.writeHeader(e),e.writeInt32(this.compositionToDTSShift),e.writeInt32(this.leastDecodeToDisplayDelta),e.writeInt32(this.greatestDecodeToDisplayDelta),e.writeInt32(this.compositionStartTime),e.writeInt32(this.compositionEndTime)},f.cttsBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(e),e.writeUint32(this.sample_counts.length),t=0;t<this.sample_counts.length;t++)e.writeUint32(this.sample_counts[t]),1===this.version?e.writeInt32(this.sample_offsets[t]):e.writeUint32(this.sample_offsets[t])},f.drefBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++)this.entries[t].write(e),this.size+=this.entries[t].size;n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},f.elngBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=this.extended_language.length,this.writeHeader(e),e.writeString(this.extended_language)},f.elstBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+12*this.entries.length,this.writeHeader(e),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++){var i=this.entries[t];e.writeUint32(i.segment_duration),e.writeInt32(i.media_time),e.writeInt16(i.media_rate_integer),e.writeInt16(i.media_rate_fraction)}},f.emsgBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=16+this.message_data.length+(this.scheme_id_uri.length+1)+(this.value.length+1),this.writeHeader(e),e.writeCString(this.scheme_id_uri),e.writeCString(this.value),e.writeUint32(this.timescale),e.writeUint32(this.presentation_time_delta),e.writeUint32(this.event_duration),e.writeUint32(this.id),e.writeUint8Array(this.message_data)},f.ftypBox.prototype.write=function(e){this.size=8+4*this.compatible_brands.length,this.writeHeader(e),e.writeString(this.major_brand,null,4),e.writeUint32(this.minor_version);for(var t=0;t<this.compatible_brands.length;t++)e.writeString(this.compatible_brands[t],null,4)},f.hdlrBox.prototype.write=function(e){this.size=20+this.name.length+1,this.version=0,this.flags=0,this.writeHeader(e),e.writeUint32(0),e.writeString(this.handler,null,4),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeCString(this.name)},f.kindBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=this.schemeURI.length+1+(this.value.length+1),this.writeHeader(e),e.writeCString(this.schemeURI),e.writeCString(this.value)},f.mdhdBox.prototype.write=function(e){this.size=20,this.flags=0,this.version=0,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.timescale),e.writeUint32(this.duration),e.writeUint16(this.language),e.writeUint16(0)},f.mehdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.fragment_duration)},f.mfhdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.sequence_number)},f.mvhdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=96,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.timescale),e.writeUint32(this.duration),e.writeUint32(this.rate),e.writeUint16(this.volume<<8),e.writeUint16(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32Array(this.matrix),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(this.next_track_id)},f.SampleEntry.prototype.writeHeader=function(e){this.size=8,f.Box.prototype.writeHeader.call(this,e),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint16(this.data_reference_index)},f.SampleEntry.prototype.writeFooter=function(e){for(var t=0;t<this.boxes.length;t++)this.boxes[t].write(e),this.size+=this.boxes[t].size;n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},f.SampleEntry.prototype.write=function(e){this.writeHeader(e),e.writeUint8Array(this.data),this.size+=this.data.length,n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},f.VisualSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=70,e.writeUint16(0),e.writeUint16(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint16(this.width),e.writeUint16(this.height),e.writeUint32(this.horizresolution),e.writeUint32(this.vertresolution),e.writeUint32(0),e.writeUint16(this.frame_count),e.writeUint8(Math.min(31,this.compressorname.length)),e.writeString(this.compressorname,null,31),e.writeUint16(this.depth),e.writeInt16(-1),this.writeFooter(e)},f.AudioSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=20,e.writeUint32(0),e.writeUint32(0),e.writeUint16(this.channel_count),e.writeUint16(this.samplesize),e.writeUint16(0),e.writeUint16(0),e.writeUint32(this.samplerate<<16),this.writeFooter(e)},f.stppSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=this.namespace.length+1+this.schema_location.length+1+this.auxiliary_mime_types.length+1,e.writeCString(this.namespace),e.writeCString(this.schema_location),e.writeCString(this.auxiliary_mime_types),this.writeFooter(e)},f.SampleGroupEntry.prototype.write=function(e){e.writeUint8Array(this.data)},f.sbgpBox.prototype.write=function(e){this.version=1,this.flags=0,this.size=12+8*this.entries.length,this.writeHeader(e),e.writeString(this.grouping_type,null,4),e.writeUint32(this.grouping_type_parameter),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++){var i=this.entries[t];e.writeInt32(i.sample_count),e.writeInt32(i.group_description_index)}},f.sgpdBox.prototype.write=function(e){var t,i;for(this.flags=0,this.size=12,t=0;t<this.entries.length;t++)i=this.entries[t],1===this.version&&(0===this.default_length&&(this.size+=4),this.size+=i.data.length);for(this.writeHeader(e),e.writeString(this.grouping_type,null,4),1===this.version&&e.writeUint32(this.default_length),this.version>=2&&e.writeUint32(this.default_sample_description_index),e.writeUint32(this.entries.length),t=0;t<this.entries.length;t++)i=this.entries[t],1===this.version&&0===this.default_length&&e.writeUint32(i.description_length),i.write(e)},f.sidxBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20+12*this.references.length,this.writeHeader(e),e.writeUint32(this.reference_ID),e.writeUint32(this.timescale),e.writeUint32(this.earliest_presentation_time),e.writeUint32(this.first_offset),e.writeUint16(0),e.writeUint16(this.references.length);for(var t=0;t<this.references.length;t++){var i=this.references[t];e.writeUint32(i.reference_type<<31|i.referenced_size),e.writeUint32(i.subsegment_duration),e.writeUint32(i.starts_with_SAP<<31|i.SAP_type<<28|i.SAP_delta_time)}},f.smhdBox.prototype.write=function(e){this.version=0,this.flags=1,this.size=4,this.writeHeader(e),e.writeUint16(this.balance),e.writeUint16(0)},f.stcoBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+4*this.chunk_offsets.length,this.writeHeader(e),e.writeUint32(this.chunk_offsets.length),e.writeUint32Array(this.chunk_offsets)},f.stscBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+12*this.first_chunk.length,this.writeHeader(e),e.writeUint32(this.first_chunk.length),t=0;t<this.first_chunk.length;t++)e.writeUint32(this.first_chunk[t]),e.writeUint32(this.samples_per_chunk[t]),e.writeUint32(this.sample_description_index[t])},f.stsdBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=0,this.writeHeader(e),e.writeUint32(this.entries.length),this.size+=4,t=0;t<this.entries.length;t++)this.entries[t].write(e),this.size+=this.entries[t].size;n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},f.stshBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.shadowed_sample_numbers.length,this.writeHeader(e),e.writeUint32(this.shadowed_sample_numbers.length),t=0;t<this.shadowed_sample_numbers.length;t++)e.writeUint32(this.shadowed_sample_numbers[t]),e.writeUint32(this.sync_sample_numbers[t])},f.stssBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+4*this.sample_numbers.length,this.writeHeader(e),e.writeUint32(this.sample_numbers.length),e.writeUint32Array(this.sample_numbers)},f.stszBox.prototype.write=function(e){var t,i=!0;if(this.version=0,this.flags=0,this.sample_sizes.length>0)for(t=0;t+1<this.sample_sizes.length;){if(this.sample_sizes[t+1]!==this.sample_sizes[0]){i=!1;break}t++}else i=!1;this.size=8,i||(this.size+=4*this.sample_sizes.length),this.writeHeader(e),i?e.writeUint32(this.sample_sizes[0]):e.writeUint32(0),e.writeUint32(this.sample_sizes.length),i||e.writeUint32Array(this.sample_sizes)},f.sttsBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(e),e.writeUint32(this.sample_counts.length),t=0;t<this.sample_counts.length;t++)e.writeUint32(this.sample_counts[t]),e.writeUint32(this.sample_deltas[t])},f.tfdtBox.prototype.write=function(e){var t=Math.pow(2,32)-1;this.version=this.baseMediaDecodeTime>t?1:0,this.flags=0,this.size=4,1===this.version&&(this.size+=4),this.writeHeader(e),1===this.version?e.writeUint64(this.baseMediaDecodeTime):e.writeUint32(this.baseMediaDecodeTime)},f.tfhdBox.prototype.write=function(e){this.version=0,this.size=4,this.flags&f.TFHD_FLAG_BASE_DATA_OFFSET&&(this.size+=8),this.flags&f.TFHD_FLAG_SAMPLE_DESC&&(this.size+=4),this.flags&f.TFHD_FLAG_SAMPLE_DUR&&(this.size+=4),this.flags&f.TFHD_FLAG_SAMPLE_SIZE&&(this.size+=4),this.flags&f.TFHD_FLAG_SAMPLE_FLAGS&&(this.size+=4),this.writeHeader(e),e.writeUint32(this.track_id),this.flags&f.TFHD_FLAG_BASE_DATA_OFFSET&&e.writeUint64(this.base_data_offset),this.flags&f.TFHD_FLAG_SAMPLE_DESC&&e.writeUint32(this.default_sample_description_index),this.flags&f.TFHD_FLAG_SAMPLE_DUR&&e.writeUint32(this.default_sample_duration),this.flags&f.TFHD_FLAG_SAMPLE_SIZE&&e.writeUint32(this.default_sample_size),this.flags&f.TFHD_FLAG_SAMPLE_FLAGS&&e.writeUint32(this.default_sample_flags)},f.tkhdBox.prototype.write=function(e){this.version=0,this.size=80,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.track_id),e.writeUint32(0),e.writeUint32(this.duration),e.writeUint32(0),e.writeUint32(0),e.writeInt16(this.layer),e.writeInt16(this.alternate_group),e.writeInt16(this.volume<<8),e.writeUint16(0),e.writeInt32Array(this.matrix),e.writeUint32(this.width),e.writeUint32(this.height)},f.trexBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20,this.writeHeader(e),e.writeUint32(this.track_id),e.writeUint32(this.default_sample_description_index),e.writeUint32(this.default_sample_duration),e.writeUint32(this.default_sample_size),e.writeUint32(this.default_sample_flags)},f.trunBox.prototype.write=function(e){this.version=0,this.size=4,this.flags&f.TRUN_FLAGS_DATA_OFFSET&&(this.size+=4),this.flags&f.TRUN_FLAGS_FIRST_FLAG&&(this.size+=4),this.flags&f.TRUN_FLAGS_DURATION&&(this.size+=4*this.sample_duration.length),this.flags&f.TRUN_FLAGS_SIZE&&(this.size+=4*this.sample_size.length),this.flags&f.TRUN_FLAGS_FLAGS&&(this.size+=4*this.sample_flags.length),this.flags&f.TRUN_FLAGS_CTS_OFFSET&&(this.size+=4*this.sample_composition_time_offset.length),this.writeHeader(e),e.writeUint32(this.sample_count),this.flags&f.TRUN_FLAGS_DATA_OFFSET&&(this.data_offset_position=e.getPosition(),e.writeInt32(this.data_offset)),this.flags&f.TRUN_FLAGS_FIRST_FLAG&&e.writeUint32(this.first_sample_flags);for(var t=0;t<this.sample_count;t++)this.flags&f.TRUN_FLAGS_DURATION&&e.writeUint32(this.sample_duration[t]),this.flags&f.TRUN_FLAGS_SIZE&&e.writeUint32(this.sample_size[t]),this.flags&f.TRUN_FLAGS_FLAGS&&e.writeUint32(this.sample_flags[t]),this.flags&f.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?e.writeUint32(this.sample_composition_time_offset[t]):e.writeInt32(this.sample_composition_time_offset[t]))},f["url Box"].prototype.write=function(e){this.version=0,this.location?(this.flags=0,this.size=this.location.length+1):(this.flags=1,this.size=0),this.writeHeader(e),this.location&&e.writeCString(this.location)},f["urn Box"].prototype.write=function(e){this.version=0,this.flags=0,this.size=this.name.length+1+(this.location?this.location.length+1:0),this.writeHeader(e),e.writeCString(this.name),this.location&&e.writeCString(this.location)},f.vmhdBox.prototype.write=function(e){this.version=0,this.flags=1,this.size=8,this.writeHeader(e),e.writeUint16(this.graphicsmode),e.writeUint16Array(this.opcolor)},f.cttsBox.prototype.unpack=function(e){var t,i,r;for(r=0,t=0;t<this.sample_counts.length;t++)for(i=0;i<this.sample_counts[t];i++)e[r].pts=e[r].dts+this.sample_offsets[t],r++},f.sttsBox.prototype.unpack=function(e){var t,i,r;for(r=0,t=0;t<this.sample_counts.length;t++)for(i=0;i<this.sample_counts[t];i++)e[r].dts=0===r?0:e[r-1].dts+this.sample_deltas[t],r++},f.stcoBox.prototype.unpack=function(e){var t;for(t=0;t<this.chunk_offsets.length;t++)e[t].offset=this.chunk_offsets[t]},f.stscBox.prototype.unpack=function(e){var t,i,r,s,n;for(s=0,n=0,t=0;t<this.first_chunk.length;t++)for(i=0;i<(t+1<this.first_chunk.length?this.first_chunk[t+1]:1/0);i++)for(n++,r=0;r<this.samples_per_chunk[t];r++){if(!e[s])return;e[s].description_index=this.sample_description_index[t],e[s].chunk_index=n,s++}},f.stszBox.prototype.unpack=function(e){var t;for(t=0;t<this.sample_sizes.length;t++)e[t].size=this.sample_sizes[t]},f.DIFF_BOXES_PROP_NAMES=["boxes","entries","references","subsamples","items","item_infos","extents","associations","subsegments","ranges","seekLists","seekPoints","esd","levels"],f.DIFF_PRIMITIVE_ARRAY_PROP_NAMES=["compatible_brands","matrix","opcolor","sample_counts","sample_counts","sample_deltas","first_chunk","samples_per_chunk","sample_sizes","chunk_offsets","sample_offsets","sample_description_index","sample_duration"],f.boxEqualFields=function(e,t){if(e&&!t)return!1;var i;for(i in e)if(!(f.DIFF_BOXES_PROP_NAMES.indexOf(i)>-1||e[i]instanceof f.Box||t[i]instanceof f.Box||void 0===e[i]||void 0===t[i]||"function"==typeof e[i]||"function"==typeof t[i]||e.subBoxNames&&e.subBoxNames.indexOf(i.slice(0,4))>-1||t.subBoxNames&&t.subBoxNames.indexOf(i.slice(0,4))>-1||"data"===i||"start"===i||"size"===i||"creation_time"===i||"modification_time"===i||f.DIFF_PRIMITIVE_ARRAY_PROP_NAMES.indexOf(i)>-1||e[i]===t[i]))return!1;return!0},f.boxEqual=function(e,t){if(!f.boxEqualFields(e,t))return!1;for(var i=0;i<f.DIFF_BOXES_PROP_NAMES.length;i++){var r=f.DIFF_BOXES_PROP_NAMES[i];if(e[r]&&t[r]&&!f.boxEqual(e[r],t[r]))return!1}return!0};var p=function(){};p.prototype.parseSample=function(e){var t,i={};i.resources=[];var r=new a(e.data.buffer);if(e.subsamples&&0!==e.subsamples.length){if(i.documentString=r.readString(e.subsamples[0].size),e.subsamples.length>1)for(t=1;t<e.subsamples.length;t++)i.resources[t]=r.readUint8Array(e.subsamples[t].size)}else i.documentString=r.readString(e.data.length);return"undefined"!=typeof DOMParser&&(i.document=(new DOMParser).parseFromString(i.documentString,"application/xml")),i};var u=function(){};u.prototype.parseSample=function(e){return new a(e.data.buffer).readString(e.data.length)},u.prototype.parseConfig=function(e){var t=new a(e.buffer);return t.readUint32(),t.readCString()},t.XMLSubtitlein4Parser=p,t.Textin4Parser=u;var c=function(e){this.stream=e||new l,this.boxes=[],this.mdats=[],this.moofs=[],this.isProgressive=!1,this.moovStartFound=!1,this.onMoovStart=null,this.moovStartSent=!1,this.onReady=null,this.readySent=!1,this.onSegment=null,this.onSamples=null,this.onError=null,this.sampleListBuilt=!1,this.fragmentedTracks=[],this.extractedTracks=[],this.isFragmentationInitialized=!1,this.sampleProcessingStarted=!1,this.nextMoofNumber=0,this.itemListBuilt=!1,this.onSidx=null,this.sidxSent=!1};c.prototype.setSegmentOptions=function(e,t,i){var r=this.getTrackById(e);if(r){var s={};this.fragmentedTracks.push(s),s.id=e,s.user=t,s.trak=r,r.nextSample=0,s.segmentStream=null,s.nb_samples=1e3,s.rapAlignement=!0,i&&(i.nbSamples&&(s.nb_samples=i.nbSamples),i.rapAlignement&&(s.rapAlignement=i.rapAlignement))}},c.prototype.unsetSegmentOptions=function(e){for(var t=-1,i=0;i<this.fragmentedTracks.length;i++){this.fragmentedTracks[i].id==e&&(t=i)}t>-1&&this.fragmentedTracks.splice(t,1)},c.prototype.setExtractionOptions=function(e,t,i){var r=this.getTrackById(e);if(r){var s={};this.extractedTracks.push(s),s.id=e,s.user=t,s.trak=r,r.nextSample=0,s.nb_samples=1e3,s.samples=[],i&&i.nbSamples&&(s.nb_samples=i.nbSamples)}},c.prototype.unsetExtractionOptions=function(e){for(var t=-1,i=0;i<this.extractedTracks.length;i++){this.extractedTracks[i].id==e&&(t=i)}t>-1&&this.extractedTracks.splice(t,1)},c.prototype.parse=function(){var e,t;if(!this.restoreParsePosition||this.restoreParsePosition())for(;;){if(this.hasIncompleteMdat&&this.hasIncompleteMdat()){if(this.processIncompleteMdat())continue;return}if(this.saveParsePosition&&this.saveParsePosition(),(e=f.parseOneBox(this.stream,false)).code===f.ERR_NOT_ENOUGH_DATA){if(this.processIncompleteBox){if(this.processIncompleteBox(e))continue;return}return}var i;switch(i="uuid"!==(t=e.box).type?t.type:t.uuid,this.boxes.push(t),i){case"mdat":this.mdats.push(t);break;case"moof":this.moofs.push(t);break;case"moov":this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0);default:void 0!==this[i]&&n.warn("ISOFile","Duplicate Box of type: "+i+", overriding previous occurrence"),this[i]=t}this.updateUsedBytes&&this.updateUsedBytes(t,e)}},c.prototype.checkBuffer=function(e){if(null==e)throw"Buffer must be defined and non empty";if(void 0===e.fileStart)throw"Buffer must have a fileStart property";return 0===e.byteLength?(n.warn("ISOFile","Ignoring empty buffer (fileStart: "+e.fileStart+")"),this.stream.logBufferLevel(),!1):(n.info("ISOFile","Processing buffer (fileStart: "+e.fileStart+")"),e.usedBytes=0,this.stream.insertBuffer(e),this.stream.logBufferLevel(),!!this.stream.initialized()||(n.warn("ISOFile","Not ready to start parsing"),!1))},c.prototype.appendBuffer=function(e,t){var i;if(this.checkBuffer(e))return this.parse(),this.moovStartFound&&!this.moovStartSent&&(this.moovStartSent=!0,this.onMoovStart&&this.onMoovStart()),this.moov?(this.sampleListBuilt||(this.buildSampleLists(),this.sampleListBuilt=!0),this.updateSampleLists(),this.onReady&&!this.readySent&&(this.readySent=!0,this.onReady(this.getInfo())),this.processSamples(t),this.nextSeekPosition?(i=this.nextSeekPosition,this.nextSeekPosition=void 0):i=this.nextParsePosition,this.stream.getEndFilePositionAfter&&(i=this.stream.getEndFilePositionAfter(i))):i=this.nextParsePosition?this.nextParsePosition:0,this.sidx&&this.onSidx&&!this.sidxSent&&(this.onSidx(this.sidx),this.sidxSent=!0),this.meta&&(this.flattenItemInfo&&!this.itemListBuilt&&(this.flattenItemInfo(),this.itemListBuilt=!0),this.processItems&&this.processItems(this.onItem)),this.stream.cleanBuffers&&(n.info("ISOFile","Done processing buffer (fileStart: "+e.fileStart+") - next buffer to fetch should have a fileStart position of "+i),this.stream.logBufferLevel(),this.stream.cleanBuffers(),this.stream.logBufferLevel(!0),n.info("ISOFile","Sample data size in memory: "+this.getAllocatedSampleDataSize())),i},c.prototype.getInfo=function(){var e,t,i,r,s,n,a={},o=new Date("1904-01-01T00:00:00Z").getTime();if(this.moov)for(a.hasMoov=!0,a.duration=this.moov.mvhd.duration,a.timescale=this.moov.mvhd.timescale,a.isFragmented=null!=this.moov.mvex,a.isFragmented&&this.moov.mvex.mehd&&(a.fragment_duration=this.moov.mvex.mehd.fragment_duration),a.isProgressive=this.isProgressive,a.hasIOD=null!=this.moov.iods,a.brands=[],a.brands.push(this.ftyp.major_brand),a.brands=a.brands.concat(this.ftyp.compatible_brands),a.created=new Date(o+1e3*this.moov.mvhd.creation_time),a.modified=new Date(o+1e3*this.moov.mvhd.modification_time),a.tracks=[],a.audioTracks=[],a.videoTracks=[],a.subtitleTracks=[],a.metadataTracks=[],a.hintTracks=[],a.otherTracks=[],e=0;e<this.moov.traks.length;e++){if(n=(i=this.moov.traks[e]).mdia.minf.stbl.stsd.entries[0],r={},a.tracks.push(r),r.id=i.tkhd.track_id,r.name=i.mdia.hdlr.name,r.references=[],i.tref)for(t=0;t<i.tref.boxes.length;t++)s={},r.references.push(s),s.type=i.tref.boxes[t].type,s.track_ids=i.tref.boxes[t].track_ids;i.edts&&(r.edits=i.edts.elst.entries),r.created=new Date(o+1e3*i.tkhd.creation_time),r.modified=new Date(o+1e3*i.tkhd.modification_time),r.movie_duration=i.tkhd.duration,r.movie_timescale=a.timescale,r.layer=i.tkhd.layer,r.alternate_group=i.tkhd.alternate_group,r.volume=i.tkhd.volume,r.matrix=i.tkhd.matrix,r.track_width=i.tkhd.width/65536,r.track_height=i.tkhd.height/65536,r.timescale=i.mdia.mdhd.timescale,r.cts_shift=i.mdia.minf.stbl.cslg,r.duration=i.mdia.mdhd.duration,r.samples_duration=i.samples_duration,r.codec=n.getCodec(),r.kind=i.udta&&i.udta.kinds.length?i.udta.kinds[0]:{schemeURI:"",value:""},r.language=i.mdia.elng?i.mdia.elng.extended_language:i.mdia.mdhd.languageString,r.nb_samples=i.samples.length,r.size=i.samples_size,r.bitrate=8*r.size*r.timescale/r.samples_duration,n.isAudio()?(r.type="audio",a.audioTracks.push(r),r.audio={},r.audio.sample_rate=n.getSampleRate(),r.audio.channel_count=n.getChannelCount(),r.audio.sample_size=n.getSampleSize()):n.isVideo()?(r.type="video",a.videoTracks.push(r),r.video={},r.video.width=n.getWidth(),r.video.height=n.getHeight()):n.isSubtitle()?(r.type="subtitles",a.subtitleTracks.push(r)):n.isHint()?(r.type="metadata",a.hintTracks.push(r)):n.isMetadata()?(r.type="metadata",a.metadataTracks.push(r)):(r.type="metadata",a.otherTracks.push(r))}else a.hasMoov=!1;if(a.mime="",a.hasMoov&&a.tracks){for(a.videoTracks&&a.videoTracks.length>0?a.mime+='video/mp4; codecs="':a.audioTracks&&a.audioTracks.length>0?a.mime+='audio/mp4; codecs="':a.mime+='application/mp4; codecs="',e=0;e<a.tracks.length;e++)0!==e&&(a.mime+=","),a.mime+=a.tracks[e].codec;a.mime+='"; profiles="',a.mime+=this.ftyp.compatible_brands.join(),a.mime+='"'}return a},c.prototype.processSamples=function(e){var t,i;if(this.sampleProcessingStarted){if(this.isFragmentationInitialized&&null!==this.onSegment)for(t=0;t<this.fragmentedTracks.length;t++){var r=this.fragmentedTracks[t];for(i=r.trak;i.nextSample<i.samples.length&&this.sampleProcessingStarted;){n.debug("ISOFile","Creating media fragment on track #"+r.id+" for sample "+i.nextSample);var s=this.createFragment(r.id,i.nextSample,r.segmentStream);if(!s)break;if(r.segmentStream=s,i.nextSample++,(i.nextSample%r.nb_samples==0||e||i.nextSample>=i.samples.length)&&(n.info("ISOFile","Sending fragmented data on track #"+r.id+" for samples ["+Math.max(0,i.nextSample-r.nb_samples)+","+(i.nextSample-1)+"]"),n.info("ISOFile","Sample data size in memory: "+this.getAllocatedSampleDataSize()),this.onSegment&&this.onSegment(r.id,r.user,r.segmentStream.buffer,i.nextSample,e||i.nextSample>=i.samples.length),r.segmentStream=null,r!==this.fragmentedTracks[t]))break}}if(null!==this.onSamples)for(t=0;t<this.extractedTracks.length;t++){var a=this.extractedTracks[t];for(i=a.trak;i.nextSample<i.samples.length&&this.sampleProcessingStarted;){n.debug("ISOFile","Exporting on track #"+a.id+" sample #"+i.nextSample);var o=this.getSample(i,i.nextSample);if(!o)break;if(i.nextSample++,a.samples.push(o),(i.nextSample%a.nb_samples==0||i.nextSample>=i.samples.length)&&(n.debug("ISOFile","Sending samples on track #"+a.id+" for sample "+i.nextSample),this.onSamples&&this.onSamples(a.id,a.user,a.samples),a.samples=[],a!==this.extractedTracks[t]))break}}}},c.prototype.getBox=function(e){var t=this.getBoxes(e,!0);return t.length?t[0]:null},c.prototype.getBoxes=function(e,t){var i=[];return c._sweep.call(this,e,i,t),i},c._sweep=function(e,t,i){for(var r in this.type&&this.type==e&&t.push(this),this.boxes){if(t.length&&i)return;c._sweep.call(this.boxes[r],e,t,i)}},c.prototype.getTrackSamplesInfo=function(e){var t=this.getTrackById(e);return t?t.samples:void 0},c.prototype.getTrackSample=function(e,t){var i=this.getTrackById(e);return this.getSample(i,t)},c.prototype.releaseUsedSamples=function(e,t){var i=0,r=this.getTrackById(e);r.lastValidSample||(r.lastValidSample=0);for(var s=r.lastValidSample;s<t;s++)i+=this.releaseSample(r,s);n.info("ISOFile","Track #"+e+" released samples up to "+t+" (released size: "+i+", remaining: "+this.samplesDataSize+")"),r.lastValidSample=t},c.prototype.start=function(){this.sampleProcessingStarted=!0,this.processSamples(!1)},c.prototype.stop=function(){this.sampleProcessingStarted=!1},c.prototype.flush=function(){n.info("ISOFile","Flushing remaining samples"),this.updateSampleLists(),this.processSamples(!0),this.stream.cleanBuffers(),this.stream.logBufferLevel(!0)},c.prototype.seekTrack=function(e,t,i){var r,s,a,o,d=0,l=0;if(0===i.samples.length)return n.info("ISOFile","No sample in track, cannot seek! Using time "+n.getDurationString(0,1)+" and offset: 0"),{offset:0,time:0};for(r=0;r<i.samples.length;r++){if(s=i.samples[r],0===r)l=0,o=s.timescale;else if(s.cts>e*s.timescale){l=r-1;break}t&&s.is_sync&&(d=r)}for(t&&(l=d),e=i.samples[l].cts,i.nextSample=l;i.samples[l].alreadyRead===i.samples[l].size&&i.samples[l+1];)l++;return a=i.samples[l].offset+i.samples[l].alreadyRead,n.info("ISOFile","Seeking to "+(t?"RAP":"")+" sample #"+i.nextSample+" on track "+i.tkhd.track_id+", time "+n.getDurationString(e,o)+" and offset: "+a),{offset:a,time:e/o}},c.prototype.seek=function(e,t){var i,r,s,a=this.moov,o={offset:1/0,time:1/0};if(this.moov){for(s=0;s<a.traks.length;s++)i=a.traks[s],(r=this.seekTrack(e,t,i)).offset<o.offset&&(o.offset=r.offset),r.time<o.time&&(o.time=r.time);return n.info("ISOFile","Seeking at time "+n.getDurationString(o.time,1)+" needs a buffer with a fileStart position of "+o.offset),o.offset===1/0?o={offset:this.nextParsePosition,time:0}:o.offset=this.stream.getEndFilePositionAfter(o.offset),n.info("ISOFile","Adjusted seek position (after checking data already in buffer): "+o.offset),o}throw"Cannot seek: moov not received!"},c.prototype.equal=function(e){for(var t=0;t<this.boxes.length&&t<e.boxes.length;){var i=this.boxes[t],r=e.boxes[t];if(!f.boxEqual(i,r))return!1;t++}return!0},t.ISOFile=c,c.prototype.lastBoxStartPosition=0,c.prototype.parsingMdat=null,c.prototype.nextParsePosition=0,c.prototype.discardMdatData=!1,c.prototype.processIncompleteBox=function(e){var t;return"mdat"===e.type?(t=new f[e.type+"Box"](e.size),this.parsingMdat=t,this.boxes.push(t),this.mdats.push(t),t.start=e.start,t.hdr_size=e.hdr_size,this.stream.addUsedBytes(t.hdr_size),this.lastBoxStartPosition=t.start+t.size,this.stream.seek(t.start+t.size,!1,this.discardMdatData)?(this.parsingMdat=null,!0):(this.moovStartFound?this.nextParsePosition=this.stream.findEndContiguousBuf():this.nextParsePosition=t.start+t.size,!1)):("moov"===e.type&&(this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0)),!!this.stream.mergeNextBuffer&&this.stream.mergeNextBuffer()?(this.nextParsePosition=this.stream.getEndPosition(),!0):(e.type?this.moovStartFound?this.nextParsePosition=this.stream.getEndPosition():this.nextParsePosition=this.stream.getPosition()+e.size:this.nextParsePosition=this.stream.getEndPosition(),!1))},c.prototype.hasIncompleteMdat=function(){return null!==this.parsingMdat},c.prototype.processIncompleteMdat=function(){var e;return e=this.parsingMdat,this.stream.seek(e.start+e.size,!1,this.discardMdatData)?(n.debug("ISOFile","Found 'mdat' end in buffered data"),this.parsingMdat=null,!0):(this.nextParsePosition=this.stream.findEndContiguousBuf(),!1)},c.prototype.restoreParsePosition=function(){return this.stream.seek(this.lastBoxStartPosition,!0,this.discardMdatData)},c.prototype.saveParsePosition=function(){this.lastBoxStartPosition=this.stream.getPosition()},c.prototype.updateUsedBytes=function(e,t){this.stream.addUsedBytes&&("mdat"===e.type?(this.stream.addUsedBytes(e.hdr_size),this.discardMdatData&&this.stream.addUsedBytes(e.size-e.hdr_size)):this.stream.addUsedBytes(e.size))},c.prototype.add=f.Box.prototype.add,c.prototype.addBox=f.Box.prototype.addBox,c.prototype.init=function(e){var t=e||{};this.add("ftyp").set("major_brand",t.brands&&t.brands[0]||"iso4").set("minor_version",0).set("compatible_brands",t.brands||["iso4"]);var i=this.add("moov");return i.add("mvhd").set("timescale",t.timescale||600).set("rate",t.rate||65536).set("creation_time",0).set("modification_time",0).set("duration",t.duration||0).set("volume",t.width?0:256).set("matrix",[65536,0,0,0,65536,0,0,0,1073741824]).set("next_track_id",1),i.add("mvex"),this},c.prototype.addTrack=function(e){this.moov||this.init(e);var t=e||{};t.width=t.width||320,t.height=t.height||320,t.id=t.id||this.moov.mvhd.next_track_id,t.type=t.type||"avc1";var i=this.moov.add("trak");this.moov.mvhd.next_track_id=t.id+1,i.add("tkhd").set("flags",f.TKHD_FLAG_ENABLED|f.TKHD_FLAG_IN_MOVIE|f.TKHD_FLAG_IN_PREVIEW).set("creation_time",0).set("modification_time",0).set("track_id",t.id).set("duration",t.duration||0).set("layer",t.layer||0).set("alternate_group",0).set("volume",1).set("matrix",[0,0,0,0,0,0,0,0,0]).set("width",t.width<<16).set("height",t.height<<16);var r=i.add("mdia");r.add("mdhd").set("creation_time",0).set("modification_time",0).set("timescale",t.timescale||1).set("duration",t.media_duration||0).set("language",t.language||"und"),r.add("hdlr").set("handler",t.hdlr||"vide").set("name",t.name||"Track created with MP4Box.js"),r.add("elng").set("extended_language",t.language||"fr-FR");var s=r.add("minf");if(void 0!==f[t.type+"SampleEntry"]){var n=new f[t.type+"SampleEntry"];n.data_reference_index=1;var o="";for(var d in f.sampleEntryCodes)for(var l=f.sampleEntryCodes[d],h=0;h<l.length;h++)if(l.indexOf(t.type)>-1){o=d;break}switch(o){case"Visual":if(s.add("vmhd").set("graphicsmode",0).set("opcolor",[0,0,0]),n.set("width",t.width).set("height",t.height).set("horizresolution",72<<16).set("vertresolution",72<<16).set("frame_count",1).set("compressorname",t.type+" Compressor").set("depth",24),t.avcDecoderConfigRecord){var p=new f.avcCBox,u=new a(t.avcDecoderConfigRecord);p.parse(u),n.addBox(p)}break;case"Audio":s.add("smhd").set("balance",t.balance||0),n.set("channel_count",t.channel_count||2).set("samplesize",t.samplesize||16).set("samplerate",t.samplerate||65536);break;case"Hint":s.add("hmhd");break;case"Subtitle":if(s.add("sthd"),"stpp"===t.type)n.set("namespace",t.namespace||"nonamespace").set("schema_location",t.schema_location||"").set("auxiliary_mime_types",t.auxiliary_mime_types||"");break;default:s.add("nmhd")}t.description&&n.addBox(t.description),t.description_boxes&&t.description_boxes.forEach((function(e){n.addBox(e)})),s.add("dinf").add("dref").addEntry((new f["url Box"]).set("flags",1));var c=s.add("stbl");return c.add("stsd").addEntry(n),c.add("stts").set("sample_counts",[]).set("sample_deltas",[]),c.add("stsc").set("first_chunk",[]).set("samples_per_chunk",[]).set("sample_description_index",[]),c.add("stco").set("chunk_offsets",[]),c.add("stsz").set("sample_sizes",[]),this.moov.mvex.add("trex").set("track_id",t.id).set("default_sample_description_index",t.default_sample_description_index||1).set("default_sample_duration",t.default_sample_duration||0).set("default_sample_size",t.default_sample_size||0).set("default_sample_flags",t.default_sample_flags||0),this.buildTrakSampleLists(i),t.id}},f.Box.prototype.computeSize=function(e){var t=e||new o;t.endianness=o.BIG_ENDIAN,this.write(t)},c.prototype.addSample=function(e,t,i){var r=i||{},s={},n=this.getTrackById(e);if(null!==n){s.number=n.samples.length,s.track_id=n.tkhd.track_id,s.timescale=n.mdia.mdhd.timescale,s.description_index=r.sample_description_index?r.sample_description_index-1:0,s.description=n.mdia.minf.stbl.stsd.entries[s.description_index],s.data=t,s.size=t.byteLength,s.alreadyRead=s.size,s.duration=r.duration||1,s.cts=r.cts||0,s.dts=r.dts||0,s.is_sync=r.is_sync||!1,s.is_leading=r.is_leading||0,s.depends_on=r.depends_on||0,s.is_depended_on=r.is_depended_on||0,s.has_redundancy=r.has_redundancy||0,s.degradation_priority=r.degradation_priority||0,s.offset=0,s.subsamples=r.subsamples,n.samples.push(s),n.samples_size+=s.size,n.samples_duration+=s.duration,n.first_dts||(n.first_dts=r.dts),this.processSamples();var a=this.createSingleSampleMoof(s);return this.addBox(a),a.computeSize(),a.trafs[0].truns[0].data_offset=a.size+8,this.add("mdat").data=new Uint8Array(t),s}},c.prototype.createSingleSampleMoof=function(e){var t=0;t=e.is_sync?1<<25:65536;var i=new f.moofBox;i.add("mfhd").set("sequence_number",this.nextMoofNumber),this.nextMoofNumber++;var r=i.add("traf"),s=this.getTrackById(e.track_id);return r.add("tfhd").set("track_id",e.track_id).set("flags",f.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),r.add("tfdt").set("baseMediaDecodeTime",e.dts-(s.first_dts||0)),r.add("trun").set("flags",f.TRUN_FLAGS_DATA_OFFSET|f.TRUN_FLAGS_DURATION|f.TRUN_FLAGS_SIZE|f.TRUN_FLAGS_FLAGS|f.TRUN_FLAGS_CTS_OFFSET).set("data_offset",0).set("first_sample_flags",0).set("sample_count",1).set("sample_duration",[e.duration]).set("sample_size",[e.size]).set("sample_flags",[t]).set("sample_composition_time_offset",[e.cts-e.dts]),i},c.prototype.lastMoofIndex=0,c.prototype.samplesDataSize=0,c.prototype.resetTables=function(){var e,t,i,r,s,n;for(this.initial_duration=this.moov.mvhd.duration,this.moov.mvhd.duration=0,e=0;e<this.moov.traks.length;e++){(t=this.moov.traks[e]).tkhd.duration=0,t.mdia.mdhd.duration=0,(t.mdia.minf.stbl.stco||t.mdia.minf.stbl.co64).chunk_offsets=[],(i=t.mdia.minf.stbl.stsc).first_chunk=[],i.samples_per_chunk=[],i.sample_description_index=[],(t.mdia.minf.stbl.stsz||t.mdia.minf.stbl.stz2).sample_sizes=[],(r=t.mdia.minf.stbl.stts).sample_counts=[],r.sample_deltas=[],(s=t.mdia.minf.stbl.ctts)&&(s.sample_counts=[],s.sample_offsets=[]),n=t.mdia.minf.stbl.stss;var a=t.mdia.minf.stbl.boxes.indexOf(n);-1!=a&&(t.mdia.minf.stbl.boxes[a]=null)}},c.initSampleGroups=function(e,t,i,r,s){var n,a,o,d;function l(e,t,i){this.grouping_type=e,this.grouping_type_parameter=t,this.sbgp=i,this.last_sample_in_run=-1,this.entry_index=-1}for(t&&(t.sample_groups_info=[]),e.sample_groups_info||(e.sample_groups_info=[]),a=0;a<i.length;a++){for(d=i[a].grouping_type+"/"+i[a].grouping_type_parameter,o=new l(i[a].grouping_type,i[a].grouping_type_parameter,i[a]),t&&(t.sample_groups_info[d]=o),e.sample_groups_info[d]||(e.sample_groups_info[d]=o),n=0;n<r.length;n++)r[n].grouping_type===i[a].grouping_type&&(o.description=r[n],o.description.used=!0);if(s)for(n=0;n<s.length;n++)s[n].grouping_type===i[a].grouping_type&&(o.fragment_description=s[n],o.fragment_description.used=!0,o.is_fragment=!0)}if(t){if(s)for(a=0;a<s.length;a++)!s[a].used&&s[a].version>=2&&(d=s[a].grouping_type+"/0",(o=new l(s[a].grouping_type,0)).is_fragment=!0,t.sample_groups_info[d]||(t.sample_groups_info[d]=o))}else for(a=0;a<r.length;a++)!r[a].used&&r[a].version>=2&&(d=r[a].grouping_type+"/0",o=new l(r[a].grouping_type,0),e.sample_groups_info[d]||(e.sample_groups_info[d]=o))},c.setSampleGroupProperties=function(e,t,i,r){var s,n;for(s in t.sample_groups=[],r){var a;if(t.sample_groups[s]={},t.sample_groups[s].grouping_type=r[s].grouping_type,t.sample_groups[s].grouping_type_parameter=r[s].grouping_type_parameter,i>=r[s].last_sample_in_run&&(r[s].last_sample_in_run<0&&(r[s].last_sample_in_run=0),r[s].entry_index++,r[s].entry_index<=r[s].sbgp.entries.length-1&&(r[s].last_sample_in_run+=r[s].sbgp.entries[r[s].entry_index].sample_count)),r[s].entry_index<=r[s].sbgp.entries.length-1?t.sample_groups[s].group_description_index=r[s].sbgp.entries[r[s].entry_index].group_description_index:t.sample_groups[s].group_description_index=-1,0!==t.sample_groups[s].group_description_index)a=r[s].fragment_description?r[s].fragment_description:r[s].description,t.sample_groups[s].group_description_index>0?(n=t.sample_groups[s].group_description_index>65535?(t.sample_groups[s].group_description_index>>16)-1:t.sample_groups[s].group_description_index-1,a&&n>=0&&(t.sample_groups[s].description=a.entries[n])):a&&a.version>=2&&a.default_group_description_index>0&&(t.sample_groups[s].description=a.entries[a.default_group_description_index-1])}},c.process_sdtp=function(e,t,i){t&&(e?(t.is_leading=e.is_leading[i],t.depends_on=e.sample_depends_on[i],t.is_depended_on=e.sample_is_depended_on[i],t.has_redundancy=e.sample_has_redundancy[i]):(t.is_leading=0,t.depends_on=0,t.is_depended_on=0,t.has_redundancy=0))},c.prototype.buildSampleLists=function(){var e,t;for(e=0;e<this.moov.traks.length;e++)t=this.moov.traks[e],this.buildTrakSampleLists(t)},c.prototype.buildTrakSampleLists=function(e){var t,i,r,s,n,a,o,d,l,h,f,p,u,m,_,g,y,b,v,w,S,U,x,E;if(e.samples=[],e.samples_duration=0,e.samples_size=0,i=e.mdia.minf.stbl.stco||e.mdia.minf.stbl.co64,r=e.mdia.minf.stbl.stsc,s=e.mdia.minf.stbl.stsz||e.mdia.minf.stbl.stz2,n=e.mdia.minf.stbl.stts,a=e.mdia.minf.stbl.ctts,o=e.mdia.minf.stbl.stss,d=e.mdia.minf.stbl.stsd,l=e.mdia.minf.stbl.subs,p=e.mdia.minf.stbl.stdp,h=e.mdia.minf.stbl.sbgps,f=e.mdia.minf.stbl.sgpds,b=-1,v=-1,w=-1,S=-1,U=0,x=0,E=0,c.initSampleGroups(e,null,h,f),void 0!==s){for(t=0;t<s.sample_sizes.length;t++){var A={};A.number=t,A.track_id=e.tkhd.track_id,A.timescale=e.mdia.mdhd.timescale,A.alreadyRead=0,e.samples[t]=A,A.size=s.sample_sizes[t],e.samples_size+=A.size,0===t?(m=1,u=0,A.chunk_index=m,A.chunk_run_index=u,y=r.samples_per_chunk[u],g=0,_=u+1<r.first_chunk.length?r.first_chunk[u+1]-1:1/0):t<y?(A.chunk_index=m,A.chunk_run_index=u):(m++,A.chunk_index=m,g=0,m<=_||(_=++u+1<r.first_chunk.length?r.first_chunk[u+1]-1:1/0),A.chunk_run_index=u,y+=r.samples_per_chunk[u]),A.description_index=r.sample_description_index[A.chunk_run_index]-1,A.description=d.entries[A.description_index],A.offset=i.chunk_offsets[A.chunk_index-1]+g,g+=A.size,t>b&&(v++,b<0&&(b=0),b+=n.sample_counts[v]),t>0?(e.samples[t-1].duration=n.sample_deltas[v],e.samples_duration+=e.samples[t-1].duration,A.dts=e.samples[t-1].dts+e.samples[t-1].duration):A.dts=0,a?(t>=w&&(S++,w<0&&(w=0),w+=a.sample_counts[S]),A.cts=e.samples[t].dts+a.sample_offsets[S]):A.cts=A.dts,o?(t==o.sample_numbers[U]-1?(A.is_sync=!0,U++):(A.is_sync=!1,A.degradation_priority=0),l&&l.entries[x].sample_delta+E==t+1&&(A.subsamples=l.entries[x].subsamples,E+=l.entries[x].sample_delta,x++)):A.is_sync=!0,c.process_sdtp(e.mdia.minf.stbl.sdtp,A,A.number),A.degradation_priority=p?p.priority[t]:0,l&&l.entries[x].sample_delta+E==t&&(A.subsamples=l.entries[x].subsamples,E+=l.entries[x].sample_delta),(h.length>0||f.length>0)&&c.setSampleGroupProperties(e,A,t,e.sample_groups_info)}t>0&&(e.samples[t-1].duration=Math.max(e.mdia.mdhd.duration-e.samples[t-1].dts,0),e.samples_duration+=e.samples[t-1].duration)}},c.prototype.updateSampleLists=function(){var e,t,i,r,s,n,a,o,d,l,h,p,u,m,_;if(void 0!==this.moov)for(;this.lastMoofIndex<this.moofs.length;)if(d=this.moofs[this.lastMoofIndex],this.lastMoofIndex++,"moof"==d.type)for(l=d,e=0;e<l.trafs.length;e++){for(h=l.trafs[e],p=this.getTrackById(h.tfhd.track_id),u=this.getTrexById(h.tfhd.track_id),r=h.tfhd.flags&f.TFHD_FLAG_SAMPLE_DESC?h.tfhd.default_sample_description_index:u?u.default_sample_description_index:1,s=h.tfhd.flags&f.TFHD_FLAG_SAMPLE_DUR?h.tfhd.default_sample_duration:u?u.default_sample_duration:0,n=h.tfhd.flags&f.TFHD_FLAG_SAMPLE_SIZE?h.tfhd.default_sample_size:u?u.default_sample_size:0,a=h.tfhd.flags&f.TFHD_FLAG_SAMPLE_FLAGS?h.tfhd.default_sample_flags:u?u.default_sample_flags:0,h.sample_number=0,h.sbgps.length>0&&c.initSampleGroups(p,h,h.sbgps,p.mdia.minf.stbl.sgpds,h.sgpds),t=0;t<h.truns.length;t++){var g=h.truns[t];for(i=0;i<g.sample_count;i++){(m={}).moof_number=this.lastMoofIndex,m.number_in_traf=h.sample_number,h.sample_number++,m.number=p.samples.length,h.first_sample_index=p.samples.length,p.samples.push(m),m.track_id=p.tkhd.track_id,m.timescale=p.mdia.mdhd.timescale,m.description_index=r-1,m.description=p.mdia.minf.stbl.stsd.entries[m.description_index],m.size=n,g.flags&f.TRUN_FLAGS_SIZE&&(m.size=g.sample_size[i]),p.samples_size+=m.size,m.duration=s,g.flags&f.TRUN_FLAGS_DURATION&&(m.duration=g.sample_duration[i]),p.samples_duration+=m.duration,p.first_traf_merged||i>0?m.dts=p.samples[p.samples.length-2].dts+p.samples[p.samples.length-2].duration:(h.tfdt?m.dts=h.tfdt.baseMediaDecodeTime:m.dts=0,p.first_traf_merged=!0),m.cts=m.dts,g.flags&f.TRUN_FLAGS_CTS_OFFSET&&(m.cts=m.dts+g.sample_composition_time_offset[i]),_=a,g.flags&f.TRUN_FLAGS_FLAGS?_=g.sample_flags[i]:0===i&&g.flags&f.TRUN_FLAGS_FIRST_FLAG&&(_=g.first_sample_flags),m.is_sync=!(_>>16&1),m.is_leading=_>>26&3,m.depends_on=_>>24&3,m.is_depended_on=_>>22&3,m.has_redundancy=_>>20&3,m.degradation_priority=65535&_;var y=!!(h.tfhd.flags&f.TFHD_FLAG_BASE_DATA_OFFSET),b=!!(h.tfhd.flags&f.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),v=!!(g.flags&f.TRUN_FLAGS_DATA_OFFSET),w=0;w=y?h.tfhd.base_data_offset:b||0===t?l.start:o,m.offset=0===t&&0===i?v?w+g.data_offset:w:o,o=m.offset+m.size,(h.sbgps.length>0||h.sgpds.length>0||p.mdia.minf.stbl.sbgps.length>0||p.mdia.minf.stbl.sgpds.length>0)&&c.setSampleGroupProperties(p,m,m.number_in_traf,h.sample_groups_info)}}if(h.subs){p.has_fragment_subsamples=!0;var S=h.first_sample_index;for(t=0;t<h.subs.entries.length;t++)S+=h.subs.entries[t].sample_delta,(m=p.samples[S-1]).subsamples=h.subs.entries[t].subsamples}}},c.prototype.getSample=function(e,t){var i,r=e.samples[t];if(!this.moov)return null;if(r.data){if(r.alreadyRead==r.size)return r}else r.data=new Uint8Array(r.size),r.alreadyRead=0,this.samplesDataSize+=r.size,n.debug("ISOFile","Allocating sample #"+t+" on track #"+e.tkhd.track_id+" of size "+r.size+" (total: "+this.samplesDataSize+")");for(;;){var s=this.stream.findPosition(!0,r.offset+r.alreadyRead,!1);if(!(s>-1))return null;var a=(i=this.stream.buffers[s]).byteLength-(r.offset+r.alreadyRead-i.fileStart);if(r.size-r.alreadyRead<=a)return n.debug("ISOFile","Getting sample #"+t+" data (alreadyRead: "+r.alreadyRead+" offset: "+(r.offset+r.alreadyRead-i.fileStart)+" read size: "+(r.size-r.alreadyRead)+" full size: "+r.size+")"),o.memcpy(r.data.buffer,r.alreadyRead,i,r.offset+r.alreadyRead-i.fileStart,r.size-r.alreadyRead),i.usedBytes+=r.size-r.alreadyRead,this.stream.logBufferLevel(),r.alreadyRead=r.size,r;if(0===a)return null;n.debug("ISOFile","Getting sample #"+t+" partial data (alreadyRead: "+r.alreadyRead+" offset: "+(r.offset+r.alreadyRead-i.fileStart)+" read size: "+a+" full size: "+r.size+")"),o.memcpy(r.data.buffer,r.alreadyRead,i,r.offset+r.alreadyRead-i.fileStart,a),r.alreadyRead+=a,i.usedBytes+=a,this.stream.logBufferLevel()}},c.prototype.releaseSample=function(e,t){var i=e.samples[t];return i.data?(this.samplesDataSize-=i.size,i.data=null,i.alreadyRead=0,i.size):0},c.prototype.getAllocatedSampleDataSize=function(){return this.samplesDataSize},c.prototype.getCodecs=function(){var e,t="";for(e=0;e<this.moov.traks.length;e++){e>0&&(t+=","),t+=this.moov.traks[e].mdia.minf.stbl.stsd.entries[0].getCodec()}return t},c.prototype.getTrexById=function(e){var t;if(!this.moov||!this.moov.mvex)return null;for(t=0;t<this.moov.mvex.trexs.length;t++){var i=this.moov.mvex.trexs[t];if(i.track_id==e)return i}return null},c.prototype.getTrackById=function(e){if(void 0===this.moov)return null;for(var t=0;t<this.moov.traks.length;t++){var i=this.moov.traks[t];if(i.tkhd.track_id==e)return i}return null},c.prototype.items=[],c.prototype.itemsDataSize=0,c.prototype.flattenItemInfo=function(){var e,t,i,r=this.items,s=this.meta;if(null!=s&&void 0!==s.hdlr&&void 0!==s.iinf){for(e=0;e<s.iinf.item_infos.length;e++)(i={}).id=s.iinf.item_infos[e].item_ID,r[i.id]=i,i.ref_to=[],i.name=s.iinf.item_infos[e].item_name,s.iinf.item_infos[e].protection_index>0&&(i.protection=s.ipro.protections[s.iinf.item_infos[e].protection_index-1]),s.iinf.item_infos[e].item_type?i.type=s.iinf.item_infos[e].item_type:i.type="mime",i.content_type=s.iinf.item_infos[e].content_type,i.content_encoding=s.iinf.item_infos[e].content_encoding;if(s.iloc)for(e=0;e<s.iloc.items.length;e++){var a=s.iloc.items[e];switch(i=r[a.item_ID],0!==a.data_reference_index&&(n.warn("Item storage with reference to other files: not supported"),i.source=s.dinf.boxes[a.data_reference_index-1]),a.construction_method){case 0:break;case 1:case 2:n.warn("Item storage with construction_method : not supported")}for(i.extents=[],i.size=0,t=0;t<a.extents.length;t++)i.extents[t]={},i.extents[t].offset=a.extents[t].extent_offset+a.base_offset,i.extents[t].length=a.extents[t].extent_length,i.extents[t].alreadyRead=0,i.size+=i.extents[t].length}if(s.pitm&&(r[s.pitm.item_id].primary=!0),s.iref)for(e=0;e<s.iref.references.length;e++){var o=s.iref.references[e];for(t=0;t<o.references.length;t++)r[o.from_item_ID].ref_to.push({type:o.type,id:o.references[t]})}if(s.iprp)for(var d=0;d<s.iprp.ipmas.length;d++){var l=s.iprp.ipmas[d];for(e=0;e<l.associations.length;e++){var h=l.associations[e];for(void 0===(i=r[h.id]).properties&&(i.properties={},i.properties.boxes=[]),t=0;t<h.props.length;t++){var f=h.props[t];if(f.property_index>0&&f.property_index-1<s.iprp.ipco.boxes.length){var p=s.iprp.ipco.boxes[f.property_index-1];i.properties[p.type]=p,i.properties.boxes.push(p)}}}}}},c.prototype.getItem=function(e){var t,i;if(!this.meta)return null;if(!(i=this.items[e]).data&&i.size)i.data=new Uint8Array(i.size),i.alreadyRead=0,this.itemsDataSize+=i.size,n.debug("ISOFile","Allocating item #"+e+" of size "+i.size+" (total: "+this.itemsDataSize+")");else if(i.alreadyRead===i.size)return i;for(var r=0;r<i.extents.length;r++){var s=i.extents[r];if(s.alreadyRead!==s.length){var a=this.stream.findPosition(!0,s.offset+s.alreadyRead,!1);if(!(a>-1))return null;var d=(t=this.stream.buffers[a]).byteLength-(s.offset+s.alreadyRead-t.fileStart);if(!(s.length-s.alreadyRead<=d))return n.debug("ISOFile","Getting item #"+e+" extent #"+r+" partial data (alreadyRead: "+s.alreadyRead+" offset: "+(s.offset+s.alreadyRead-t.fileStart)+" read size: "+d+" full extent size: "+s.length+" full item size: "+i.size+")"),o.memcpy(i.data.buffer,i.alreadyRead,t,s.offset+s.alreadyRead-t.fileStart,d),s.alreadyRead+=d,i.alreadyRead+=d,t.usedBytes+=d,this.stream.logBufferLevel(),null;n.debug("ISOFile","Getting item #"+e+" extent #"+r+" data (alreadyRead: "+s.alreadyRead+" offset: "+(s.offset+s.alreadyRead-t.fileStart)+" read size: "+(s.length-s.alreadyRead)+" full extent size: "+s.length+" full item size: "+i.size+")"),o.memcpy(i.data.buffer,i.alreadyRead,t,s.offset+s.alreadyRead-t.fileStart,s.length-s.alreadyRead),t.usedBytes+=s.length-s.alreadyRead,this.stream.logBufferLevel(),i.alreadyRead+=s.length-s.alreadyRead,s.alreadyRead=s.length}}return i.alreadyRead===i.size?i:null},c.prototype.releaseItem=function(e){var t=this.items[e];if(t.data){this.itemsDataSize-=t.size,t.data=null,t.alreadyRead=0;for(var i=0;i<t.extents.length;i++){t.extents[i].alreadyRead=0}return t.size}return 0},c.prototype.processItems=function(e){for(var t in this.items){var i=this.items[t];this.getItem(i.id),e&&!i.sent&&(e(i),i.sent=!0,i.data=null)}},c.prototype.hasItem=function(e){for(var t in this.items){var i=this.items[t];if(i.name===e)return i.id}return-1},c.prototype.getMetaHandler=function(){return this.meta?this.meta.hdlr.handler:null},c.prototype.getPrimaryItem=function(){return this.meta&&this.meta.pitm?this.getItem(this.meta.pitm.item_id):null},c.prototype.itemToFragmentedTrackFile=function(e){var t=e||{},i=null;if(null==(i=t.itemId?this.getItem(t.itemId):this.getPrimaryItem()))return null;var r=new c;r.discardMdatData=!1;var s={type:i.type,description_boxes:i.properties.boxes};i.properties.ispe&&(s.width=i.properties.ispe.image_width,s.height=i.properties.ispe.image_height);var n=r.addTrack(s);return n?(r.addSample(n,i.data),r):null},c.prototype.write=function(e){for(var t=0;t<this.boxes.length;t++)this.boxes[t].write(e)},c.prototype.createFragment=function(e,t,i){var r=this.getTrackById(e),s=this.getSample(r,t);if(null==s)return s=r.samples[t],this.nextSeekPosition?this.nextSeekPosition=Math.min(s.offset+s.alreadyRead,this.nextSeekPosition):this.nextSeekPosition=r.samples[t].offset+s.alreadyRead,null;var a=i||new o;a.endianness=o.BIG_ENDIAN;var d=this.createSingleSampleMoof(s);d.write(a),d.trafs[0].truns[0].data_offset=d.size+8,n.debug("MP4Box","Adjusting data_offset with new value "+d.trafs[0].truns[0].data_offset),a.adjustUint32(d.trafs[0].truns[0].data_offset_position,d.trafs[0].truns[0].data_offset);var l=new f.mdatBox;return l.data=s.data,l.write(a),a},c.writeInitializationSegment=function(e,t,i,r){var s;n.debug("ISOFile","Generating initialization segment");var a=new o;a.endianness=o.BIG_ENDIAN,e.write(a);var d=t.add("mvex");for(i&&d.add("mehd").set("fragment_duration",i),s=0;s<t.traks.length;s++)d.add("trex").set("track_id",t.traks[s].tkhd.track_id).set("default_sample_description_index",1).set("default_sample_duration",r).set("default_sample_size",0).set("default_sample_flags",65536);return t.write(a),a.buffer},c.prototype.save=function(e){var t=new o;t.endianness=o.BIG_ENDIAN,this.write(t),t.save(e)},c.prototype.getBuffer=function(){var e=new o;return e.endianness=o.BIG_ENDIAN,this.write(e),e.buffer},c.prototype.initializeSegmentation=function(){var e,t,i,r;for(null===this.onSegment&&n.warn("MP4Box","No segmentation callback set!"),this.isFragmentationInitialized||(this.isFragmentationInitialized=!0,this.nextMoofNumber=0,this.resetTables()),t=[],e=0;e<this.fragmentedTracks.length;e++){var s=new f.moovBox;s.mvhd=this.moov.mvhd,s.boxes.push(s.mvhd),i=this.getTrackById(this.fragmentedTracks[e].id),s.boxes.push(i),s.traks.push(i),(r={}).id=i.tkhd.track_id,r.user=this.fragmentedTracks[e].user,r.buffer=c.writeInitializationSegment(this.ftyp,s,this.moov.mvex&&this.moov.mvex.mehd?this.moov.mvex.mehd.fragment_duration:void 0,this.moov.traks[e].samples.length>0?this.moov.traks[e].samples[0].duration:0),t.push(r)}return t},f.Box.prototype.printHeader=function(e){this.size+=8,this.size>d&&(this.size+=8),"uuid"===this.type&&(this.size+=16),e.log(e.indent+"size:"+this.size),e.log(e.indent+"type:"+this.type)},f.FullBox.prototype.printHeader=function(e){this.size+=4,f.Box.prototype.printHeader.call(this,e),e.log(e.indent+"version:"+this.version),e.log(e.indent+"flags:"+this.flags)},f.Box.prototype.print=function(e){this.printHeader(e)},f.ContainerBox.prototype.print=function(e){this.printHeader(e);for(var t=0;t<this.boxes.length;t++)if(this.boxes[t]){var i=e.indent;e.indent+=" ",this.boxes[t].print(e),e.indent=i}},c.prototype.print=function(e){e.indent="";for(var t=0;t<this.boxes.length;t++)this.boxes[t]&&this.boxes[t].print(e)},f.mvhdBox.prototype.print=function(e){f.FullBox.prototype.printHeader.call(this,e),e.log(e.indent+"creation_time: "+this.creation_time),e.log(e.indent+"modification_time: "+this.modification_time),e.log(e.indent+"timescale: "+this.timescale),e.log(e.indent+"duration: "+this.duration),e.log(e.indent+"rate: "+this.rate),e.log(e.indent+"volume: "+(this.volume>>8)),e.log(e.indent+"matrix: "+this.matrix.join(", ")),e.log(e.indent+"next_track_id: "+this.next_track_id)},f.tkhdBox.prototype.print=function(e){f.FullBox.prototype.printHeader.call(this,e),e.log(e.indent+"creation_time: "+this.creation_time),e.log(e.indent+"modification_time: "+this.modification_time),e.log(e.indent+"track_id: "+this.track_id),e.log(e.indent+"duration: "+this.duration),e.log(e.indent+"volume: "+(this.volume>>8)),e.log(e.indent+"matrix: "+this.matrix.join(", ")),e.log(e.indent+"layer: "+this.layer),e.log(e.indent+"alternate_group: "+this.alternate_group),e.log(e.indent+"width: "+this.width),e.log(e.indent+"height: "+this.height)};var m={createFile:function(e,t){var i=void 0===e||e,r=new c(t);return r.discardMdatData=!i,r}};t.createFile=m.createFile}));function gi(e){return e.reduce(((e,t)=>256*e+t))}function yi(e){const t=[101,103,119,99],i=e.length-28,r=e.slice(i,i+t.length);return t.every(((e,t)=>e===r[t]))}_i.Log,_i.MP4BoxStream,_i.DataStream,_i.MultiBufferStream,_i.MPEG4DescriptorParser,_i.BoxParser,_i.XMLSubtitlein4Parser,_i.Textin4Parser,_i.ISOFile,_i.createFile;class bi{constructor(){this.s=null,this.a=null,this.l=0,this.c=0,this.u=1/0,this.A=!1,this.d=!1,this.r=4194304,this.n=new Uint8Array([30,158,90,33,244,57,83,165,2,70,35,87,215,231,226,108]),this.t=this.n.slice().reverse()}destroy(){this.s=null,this.a=null,this.l=0,this.c=0,this.u=1/0,this.A=!1,this.d=!1,this.r=4194304,this.n=null,this.t=null}transport(e){if(!this.s&&this.l>50)return e;if(this.l++,this.d)return e;const t=new Uint8Array(e);if(this.A){if(!(this.c<this.u))return this.a&&this.s?(this.a.set(t,this.r),this.s.parse(null,this.r,t.byteLength),this.a.slice(this.r,this.r+t.byteLength)):(console.error("video_error_2"),this.d=!0,e);yi(t)&&this.c++}else{const i=function(e,t){const i=function(e,t){for(let i=0;i<e.byteLength-t.length;i++)for(let r=0;r<t.length&&e[i+r]===t[r];r++)if(r===t.length-1)return i;return null}(e,t);if(i){const t=gi(e.slice(i+16,i+16+8));return[t,gi(e.slice(i+24,i+24+8)),function(e){return e.map((e=>~e))}(e.slice(i+32,i+32+t))]}return null}(t,this.t);if(!i)return e;const r=function(e){try{if("object"!=typeof WebAssembly||"function"!=typeof WebAssembly.instantiate)throw null;{const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(!(e instanceof WebAssembly.Module&&new WebAssembly.Instance(e)instanceof WebAssembly.Instance))throw null}}catch(e){return new Error("video_error_4")}let t;try{t={env:{__handle_stack_overflow:()=>e(new Error("video_error_1")),memory:new WebAssembly.Memory({initial:256,maximum:256})}}}catch(e){return new Error("video_error_5")}return t}(e);if(r instanceof Error)return console.error(r.message),this.d=!0,e;this.A=!0,this.u=i[1],yi(t)&&this.c++,WebAssembly.instantiate(i[2],r).then((e=>{if("function"!=typeof(t=e.instance.exports).parse||"object"!=typeof t.memory)return this.d=!0,void console.error("video_error_3");var t;this.s=e.instance.exports,this.a=new Uint8Array(this.s.memory.buffer)})).catch((e=>{this.d=!0,console.error("video_error_6")}))}return e}}const vi=0,wi=32,Si=16,Ui=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72],xi=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257];function Ei(e){const t=[];for(let i=0,r=e.length;i<r;i+=2)t.push(parseInt(e.substr(i,2),16));return t}function Ai(e,t){const i=31&t;return e<<i|e>>>32-i}function Bi(e){return(255&Ui[e>>>24&255])<<24|(255&Ui[e>>>16&255])<<16|(255&Ui[e>>>8&255])<<8|255&Ui[255&e]}function Ti(e){return e^Ai(e,2)^Ai(e,10)^Ai(e,18)^Ai(e,24)}function ki(e){return e^Ai(e,13)^Ai(e,23)}function Ci(e,t,i){const r=new Array(4),s=new Array(4);for(let t=0;t<4;t++)s[0]=255&e[4*t],s[1]=255&e[4*t+1],s[2]=255&e[4*t+2],s[3]=255&e[4*t+3],r[t]=s[0]<<24|s[1]<<16|s[2]<<8|s[3];for(let e,t=0;t<32;t+=4)e=r[1]^r[2]^r[3]^i[t+0],r[0]^=Ti(Bi(e)),e=r[2]^r[3]^r[0]^i[t+1],r[1]^=Ti(Bi(e)),e=r[3]^r[0]^r[1]^i[t+2],r[2]^=Ti(Bi(e)),e=r[0]^r[1]^r[2]^i[t+3],r[3]^=Ti(Bi(e));for(let e=0;e<16;e+=4)t[e]=r[3-e/4]>>>24&255,t[e+1]=r[3-e/4]>>>16&255,t[e+2]=r[3-e/4]>>>8&255,t[e+3]=255&r[3-e/4]}function Di(e,t,i,{padding:r="pkcs#7",mode:s,iv:n=[],output:a="string"}={}){if("cbc"===s&&("string"==typeof n&&(n=Ei(n)),16!==n.length))throw new Error("iv is invalid");if("string"==typeof t&&(t=Ei(t)),16!==t.length)throw new Error("key is invalid");if(e="string"==typeof e?i!==vi?function(e){const t=[];for(let i=0,r=e.length;i<r;i++){const r=e.codePointAt(i);if(r<=127)t.push(r);else if(r<=2047)t.push(192|r>>>6),t.push(128|63&r);else if(r<=55295||r>=57344&&r<=65535)t.push(224|r>>>12),t.push(128|r>>>6&63),t.push(128|63&r);else{if(!(r>=65536&&r<=1114111))throw t.push(r),new Error("input is not supported");i++,t.push(240|r>>>18&28),t.push(128|r>>>12&63),t.push(128|r>>>6&63),t.push(128|63&r)}}return t}(e):Ei(e):[...e],("pkcs#5"===r||"pkcs#7"===r)&&i!==vi){const t=Si-e.length%Si;for(let i=0;i<t;i++)e.push(t)}const o=new Array(wi);!function(e,t,i){const r=new Array(4),s=new Array(4);for(let t=0;t<4;t++)s[0]=255&e[0+4*t],s[1]=255&e[1+4*t],s[2]=255&e[2+4*t],s[3]=255&e[3+4*t],r[t]=s[0]<<24|s[1]<<16|s[2]<<8|s[3];r[0]^=2746333894,r[1]^=1453994832,r[2]^=1736282519,r[3]^=2993693404;for(let e,i=0;i<32;i+=4)e=r[1]^r[2]^r[3]^xi[i+0],t[i+0]=r[0]^=ki(Bi(e)),e=r[2]^r[3]^r[0]^xi[i+1],t[i+1]=r[1]^=ki(Bi(e)),e=r[3]^r[0]^r[1]^xi[i+2],t[i+2]=r[2]^=ki(Bi(e)),e=r[0]^r[1]^r[2]^xi[i+3],t[i+3]=r[3]^=ki(Bi(e));if(i===vi)for(let e,i=0;i<16;i++)e=t[i],t[i]=t[31-i],t[31-i]=e}(t,o,i);const d=[];let l=n,h=e.length,f=0;for(;h>=Si;){const t=e.slice(f,f+16),r=new Array(16);if("cbc"===s)for(let e=0;e<Si;e++)i!==vi&&(t[e]^=l[e]);Ci(t,r,o);for(let e=0;e<Si;e++)"cbc"===s&&i===vi&&(r[e]^=l[e]),d[f+e]=r[e];"cbc"===s&&(l=i!==vi?r:t),h-=Si,f+=Si}if(("pkcs#5"===r||"pkcs#7"===r)&&i===vi){const e=d.length,t=d[e-1];for(let i=1;i<=t;i++)if(d[e-i]!==t)throw new Error("padding is invalid");d.splice(e-t,t)}return"array"!==a?i!==vi?d.map((e=>1===(e=e.toString(16)).length?"0"+e:e)).join(""):function(e){const t=[];for(let i=0,r=e.length;i<r;i++)e[i]>=240&&e[i]<=247?(t.push(String.fromCodePoint(((7&e[i])<<18)+((63&e[i+1])<<12)+((63&e[i+2])<<6)+(63&e[i+3]))),i+=3):e[i]>=224&&e[i]<=239?(t.push(String.fromCodePoint(((15&e[i])<<12)+((63&e[i+1])<<6)+(63&e[i+2]))),i+=2):e[i]>=192&&e[i]<=223?(t.push(String.fromCodePoint(((31&e[i])<<6)+(63&e[i+1]))),i++):t.push(String.fromCodePoint(e[i]));return t.join("")}(d):d}class Ii{on(e,t,i){const r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:i}),this}once(e,t,i){const r=this;function s(...n){r.off(e,s),t.apply(i,n)}return s._=t,this.on(e,s,i)}emit(e,...t){const i=((this.e||(this.e={}))[e]||[]).slice();for(let e=0;e<i.length;e+=1)i[e].fn.apply(i[e].ctx,t);return this}off(e,t){const i=this.e||(this.e={});if(!e)return Object.keys(i).forEach((e=>{delete i[e]})),void delete this.e;const r=i[e],s=[];if(r&&t)for(let e=0,i=r.length;e<i;e+=1)r[e].fn!==t&&r[e].fn._!==t&&s.push(r[e]);return s.length?i[e]=s:delete i[e],this}}const Fi={init:0,findFirstStartCode:1,findSecondStartCode:2};class Li extends Ii{constructor(e){super(),this.player=e,this.isDestroyed=!1,this.reset()}destroy(){this.isDestroyed=!1,this.off(),this.reset()}reset(){this.stats=Fi.init,this.tempBuffer=new Uint8Array(0),this.parsedOffset=0,this.versionLayer=0}dispatch(e,t){let i=new Uint8Array(this.tempBuffer.length+e.length);for(i.set(this.tempBuffer,0),i.set(e,this.tempBuffer.length),this.tempBuffer=i;!this.isDestroyed;){if(this.state==Fi.Init){let e=!1;for(;this.tempBuffer.length-this.parsedOffset>=2&&!this.isDestroyed;)if(255==this.tempBuffer[this.parsedOffset]){if(!(!1&this.tempBuffer[this.parsedOffset+1])){this.versionLayer=this.tempBuffer[this.parsedOffset+1],this.state=Fi.findFirstStartCode,this.fisrtStartCodeOffset=this.parsedOffset,this.parsedOffset+=2,e=!0;break}this.parsedOffset++}else this.parsedOffset++;if(e)continue;break}if(this.state==Fi.findFirstStartCode){let e=!1;for(;this.tempBuffer.length-this.parsedOffset>=2&&!this.isDestroyed;)if(255==this.tempBuffer[this.parsedOffset]){if(this.tempBuffer[this.parsedOffset+1]==this.versionLayer){this.state=Fi.findSecondStartCode,this.secondStartCodeOffset=this.parsedOffset,this.parsedOffset+=2,e=!0;break}this.parsedOffset++}else this.parsedOffset++;if(e)continue;break}if(this.state==Fi.findSecondStartCode){let e=this.tempBuffer.slice(this.fisrtStartCodeOffset,this.secondStartCodeOffset);this.emit("data",e,t),this.tempBuffer=this.tempBuffer.slice(this.secondStartCodeOffset),this.fisrtStartCodeOffset=0,this.parsedOffset=2,this.state=Fi.findFirstStartCode}}}}function Pi(e,t,i){for(let r=2;r<e.length;++r){const s=r-2,n=t[s%t.length],a=i[s%i.length];e[r]=e[r]^n^a}return e}function zi(...e){if((e=e.filter(Boolean)).length<2)return e[0];const t=new Uint8Array(e.reduce(((e,t)=>e+t.byteLength),0));let i=0;return e.forEach((e=>{t.set(e,i),i+=e.byteLength})),t}class Ri{static init(){Ri.types={avc1:[],avcC:[],hvc1:[],hvcC:[],av01:[],av1C:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[],".mp3":[],Opus:[],dOps:[],"ac-3":[],dac3:[],"ec-3":[],dec3:[]};for(let e in Ri.types)Ri.types.hasOwnProperty(e)&&(Ri.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);let e=Ri.constants={};e.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]),e.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),e.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),e.STSC=e.STCO=e.STTS,e.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),e.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),e.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),e.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),e.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),e.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}static box(e){let t=8,i=null,r=Array.prototype.slice.call(arguments,1),s=r.length;for(let e=0;e<s;e++)t+=r[e].byteLength;i=new Uint8Array(t),i[0]=t>>>24&255,i[1]=t>>>16&255,i[2]=t>>>8&255,i[3]=255&t,i.set(e,4);let n=8;for(let e=0;e<s;e++)i.set(r[e],n),n+=r[e].byteLength;return i}static generateInitSegment(e){let t=Ri.box(Ri.types.ftyp,Ri.constants.FTYP),i=Ri.moov(e),r=new Uint8Array(t.byteLength+i.byteLength);return r.set(t,0),r.set(i,t.byteLength),r}static moov(e){let t=Ri.mvhd(e.timescale,e.duration),i=Ri.trak(e),r=Ri.mvex(e);return Ri.box(Ri.types.moov,t,i,r)}static mvhd(e,t){return Ri.box(Ri.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))}static trak(e){return Ri.box(Ri.types.trak,Ri.tkhd(e),Ri.mdia(e))}static tkhd(e){let t=e.id,i=e.duration,r=e.presentWidth,s=e.presentHeight;return Ri.box(Ri.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,r>>>8&255,255&r,0,0,s>>>8&255,255&s,0,0]))}static mdia(e){return Ri.box(Ri.types.mdia,Ri.mdhd(e),Ri.hdlr(e),Ri.minf(e))}static mdhd(e){let t=e.timescale,i=e.duration;return Ri.box(Ri.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,i>>>24&255,i>>>16&255,i>>>8&255,255&i,85,196,0,0]))}static hdlr(e){let t=null;return t="audio"===e.type?Ri.constants.HDLR_AUDIO:Ri.constants.HDLR_VIDEO,Ri.box(Ri.types.hdlr,t)}static minf(e){let t=null;return t="audio"===e.type?Ri.box(Ri.types.smhd,Ri.constants.SMHD):Ri.box(Ri.types.vmhd,Ri.constants.VMHD),Ri.box(Ri.types.minf,t,Ri.dinf(),Ri.stbl(e))}static dinf(){return Ri.box(Ri.types.dinf,Ri.box(Ri.types.dref,Ri.constants.DREF))}static stbl(e){return Ri.box(Ri.types.stbl,Ri.stsd(e),Ri.box(Ri.types.stts,Ri.constants.STTS),Ri.box(Ri.types.stsc,Ri.constants.STSC),Ri.box(Ri.types.stsz,Ri.constants.STSZ),Ri.box(Ri.types.stco,Ri.constants.STCO))}static stsd(e){return"audio"===e.type?"mp3"===e.audioType?Ri.box(Ri.types.stsd,Ri.constants.STSD_PREFIX,Ri.mp3(e)):Ri.box(Ri.types.stsd,Ri.constants.STSD_PREFIX,Ri.mp4a(e)):"avc"===e.videoType?Ri.box(Ri.types.stsd,Ri.constants.STSD_PREFIX,Ri.avc1(e)):Ri.box(Ri.types.stsd,Ri.constants.STSD_PREFIX,Ri.hvc1(e))}static mp3(e){let t=e.channelCount,i=e.audioSampleRate,r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t,0,16,0,0,0,0,i>>>8&255,255&i,0,0]);return Ri.box(Ri.types[".mp3"],r)}static mp4a(e){let t=e.channelCount,i=e.audioSampleRate,r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t,0,16,0,0,0,0,i>>>8&255,255&i,0,0]);return Ri.box(Ri.types.mp4a,r,Ri.esds(e))}static esds(e){let t=e.config||[],i=t.length,r=new Uint8Array([0,0,0,0,3,23+i,0,1,0,4,15+i,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([i]).concat(t).concat([6,1,2]));return Ri.box(Ri.types.esds,r)}static avc1(e){let t=e.avcc;const i=e.codecWidth,r=e.codecHeight;let s=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,i>>>8&255,255&i,r>>>8&255,255&r,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return Ri.box(Ri.types.avc1,s,Ri.box(Ri.types.avcC,t))}static hvc1(e){let t=e.avcc;const i=e.codecWidth,r=e.codecHeight;let s=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,i>>>8&255,255&i,r>>>8&255,255&r,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return Ri.box(Ri.types.hvc1,s,Ri.box(Ri.types.hvcC,t))}static mvex(e){return Ri.box(Ri.types.mvex,Ri.trex(e))}static trex(e){let t=e.id,i=new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return Ri.box(Ri.types.trex,i)}static moof(e,t){return Ri.box(Ri.types.moof,Ri.mfhd(e.sequenceNumber),Ri.traf(e,t))}static mfhd(e){let t=new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e]);return Ri.box(Ri.types.mfhd,t)}static traf(e,t){let i=e.id,r=Ri.box(Ri.types.tfhd,new Uint8Array([0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i])),s=Ri.box(Ri.types.tfdt,new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t])),n=Ri.sdtp(e),a=Ri.trun(e,n.byteLength+16+16+8+16+8+8);return Ri.box(Ri.types.traf,r,s,a,n)}static sdtp(e){let t=new Uint8Array(5),i=e.flags;return t[4]=i.isLeading<<6|i.dependsOn<<4|i.isDependedOn<<2|i.hasRedundancy,Ri.box(Ri.types.sdtp,t)}static trun(e,t){let i=new Uint8Array(28);t+=36,i.set([0,0,15,1,0,0,0,1,t>>>24&255,t>>>16&255,t>>>8&255,255&t],0);let r=e.duration,s=e.size,n=e.flags,a=e.cts;return i.set([r>>>24&255,r>>>16&255,r>>>8&255,255&r,s>>>24&255,s>>>16&255,s>>>8&255,255&s,n.isLeading<<2|n.dependsOn,n.isDependedOn<<6|n.hasRedundancy<<4|n.isNonSync,0,0,a>>>24&255,a>>>16&255,a>>>8&255,255&a],12),Ri.box(Ri.types.trun,i)}static mdat(e){return Ri.box(Ri.types.mdat,e)}}Ri.init(),Date.now||(Date.now=function(){return(new Date).getTime()}),function(e={},t={},i=!1){let r=[],s=[],n={},a=new AbortController,m=null,_=null,y=null,ke=null,Ce=null,Ge=null,Xe=!1,at=!1,ot=!!st(i),dt=!1,lt=null,pt=null,ct=null,St=[],Ut=null,xt=null,Et=0,At=0,Bt=null,Rt=null,Mt=0,Nt=0,Ot=!1,Gt=!1,Ht=!1,Vt=null,$t=null,Wt=null,Yt=!1,jt=()=>{const e=it();return{debug:e.debug,debugLevel:e.debugLevel,debugUuid:e.debugUuid,useOffscreen:e.useOffscreen,useWCS:e.useWCS,useMSE:e.useMSE,videoBuffer:e.videoBuffer,videoBufferDelay:e.videoBufferDelay,openWebglAlignment:e.openWebglAlignment,playType:e.playType,hasAudio:e.hasAudio,hasVideo:e.hasVideo,playbackRate:1,playbackForwardMaxRateDecodeIFrame:e.playbackForwardMaxRateDecodeIFrame,playbackIsCacheBeforeDecodeForFpsRender:e.playbackConfig.isCacheBeforeDecodeForFpsRender,sampleRate:0,networkDelay:e.networkDelay,visibility:!0,useSIMD:e.useSIMD,isRecording:!1,recordType:e.recordType,isNakedFlow:e.isNakedFlow,checkFirstIFrame:e.checkFirstIFrame,audioBufferSize:1024,isM7sCrypto:e.isM7sCrypto,m7sCryptoAudio:e.m7sCryptoAudio,cryptoKey:e.cryptoKey,cryptoIV:e.cryptoIV,isSm4Crypto:e.isSm4Crypto,sm4CryptoKey:e.sm4CryptoKey,isXorCrypto:e.isXorCrypto,isHls265:!1,isFlv:e.isFlv,isFmp4:e.isFmp4,isMpeg4:e.isMpeg4,isTs:e.isTs,isFmp4Private:e.isFmp4Private,isEmitSEI:e.isEmitSEI,isRecordTypeFlv:!1,isWasmMp4:!1,isChrome:!1,isDropSameTimestampGop:e.isDropSameTimestampGop,mseDecodeAudio:e.mseDecodeAudio,nakedFlowH265DemuxUseNew:e.nakedFlowH265DemuxUseNew,mseDecoderUseWorker:e.mseDecoderUseWorker,mseAutoCleanupSourceBuffer:e.mseAutoCleanupSourceBuffer,mseAutoCleanupMaxBackwardDuration:e.mseAutoCleanupMaxBackwardDuration,mseAutoCleanupMinBackwardDuration:e.mseAutoCleanupMinBackwardDuration,mseCorrectTimeDuration:e.mseCorrectTimeDuration}};"VideoEncoder"in self&&(n={hasInit:!1,isEmitInfo:!1,offscreenCanvas:null,offscreenCanvasCtx:null,decoder:new VideoDecoder({output:function(e){if(n.isEmitInfo||(Qt.debug.log("worker","Webcodecs Video Decoder initSize"),postMessage({cmd:v,w:e.codedWidth,h:e.codedHeight}),n.isEmitInfo=!0,n.offscreenCanvas=new OffscreenCanvas(e.codedWidth,e.codedHeight),n.offscreenCanvasCtx=n.offscreenCanvas.getContext("2d")),"function"==typeof e.createImageBitmap)e.createImageBitmap().then((t=>{n.offscreenCanvasCtx.drawImage(t,0,0,e.codedWidth,e.codedHeight);let i=n.offscreenCanvas.transferToImageBitmap();postMessage({cmd:w,buffer:i,delay:Qt.delay,ts:0},[i]),et(e)}));else{n.offscreenCanvasCtx.drawImage(e,0,0,e.codedWidth,e.codedHeight);let t=n.offscreenCanvas.transferToImageBitmap();postMessage({cmd:w,buffer:t,delay:Qt.delay,ts:0},[t]),et(e)}},error:function(e){Qt.debug.error("worker","VideoDecoder error",e)}}),decode:function(e,t,i){const r=e[0]>>4==1;if(n.hasInit){const i=new EncodedVideoChunk({data:e.slice(5),timestamp:t,type:r?Ee:Ae});n.decoder.decode(i)}else if(r&&0===e[1]){const t=15&e[0];postMessage({cmd:B,code:t});const i=new Uint8Array(e);postMessage({cmd:T,buffer:i,codecId:t},[i.buffer]);const r=function(e){let t=e.subarray(1,4),i="avc1.";for(let e=0;e<3;e++){let r=t[e].toString(16);r.length<2&&(r="0"+r),i+=r}return{codec:i,description:e}}(e.slice(5));n.decoder.configure(r),n.hasInit=!0}},reset(){n.hasInit=!1,n.isEmitInfo=!1,n.offscreenCanvas=null,n.offscreenCanvasCtx=null}});let qt=function(){if(Yt=!0,Qt.fetchStatus!==Fe||nt(Qt._opt.isChrome)){if(a)try{a.abort(),a=null}catch(e){Qt.debug.log("worker","abort catch",e)}}else a=null,Qt.debug.log("worker",`abort() and not abortController.abort() _status is ${Qt.fetchStatus} and _isChrome is ${Qt._opt.isChrome}`)},Kt={init(){Kt.lastBuf=null,Kt.vps=null,Kt.sps=null,Kt.pps=null,Kt.streamType=null,Kt.localDts=0,Kt.isSendSeqHeader=!1},destroy(){Kt.lastBuf=null,Kt.vps=null,Kt.sps=null,Kt.pps=null,Kt.streamType=null,Kt.localDts=0,Kt.isSendSeqHeader=!1},dispatch(e){const t=new Uint8Array(e);Kt.extractNALu$2(t)},getNaluDts(){let e=Kt.localDts;return Kt.localDts=Kt.localDts+40,e},getNaluAudioDts(){const e=Qt._opt.sampleRate,t=Qt._opt.audioBufferSize;return Kt.localDts+parseInt(t/e*1e3)},extractNALu(e){let t,i,r=0,s=e.byteLength,n=0,a=[];for(;r<s;)switch(t=e[r++],n){case 0:0===t&&(n=1);break;case 1:n=0===t?2:0;break;case 2:case 3:0===t?n=3:1===t&&r<s?(i&&a.push(e.subarray(i,r-n-1)),i=r,n=0):n=0}return i&&a.push(e.subarray(i,s)),a},extractNALu$2(e){let t=null;if(!e||e.byteLength<1)return;Kt.lastBuf?(t=new Uint8Array(e.byteLength+Kt.lastBuf.length),t.set(Kt.lastBuf),t.set(new Uint8Array(e),Kt.lastBuf.length)):t=new Uint8Array(e);let i=0,r=-1,s=-2;const n=new Array;for(let e=0;e<t.length;e+=2){const i=t[e],a=t[e+1];0==r&&0==i&&0==a?n.push(e-1):1==a&&0==i&&0==r&&0==s&&n.push(e-2),s=i,r=a}if(n.length>1)for(let e=0;e<n.length-1;++e){const r=t.subarray(n[e],n[e+1]+1);Kt.handleNALu(r),i=n[e+1]}else i=n[0];if(0!=i&&i<t.length)Kt.lastBuf=t.subarray(i);else{Kt.lastBuf||(Kt.lastBuf=t);const i=new Uint8Array(Kt.lastBuf.length+e.byteLength);i.set(Kt.lastBuf),i.set(new Uint8Array(e),Kt.lastBuf.length),Kt.lastBuf=i}},handleNALu(e){e.byteLength<=4?Qt.debug.warn("worker",`handleNALu nalu byteLength is ${e.byteLength} <= 4`):(e=e.slice(4),Kt.handleVideoNalu(e))},handleVideoNalu(e){if(Kt.streamType||(Kt.streamType=function(e){let t=null,i=31&e[0];return i!==Ue.sps&&i!==Ue.pps||(t=we.h264),t||(i=(126&e[0])>>1,i!==xe.vps&&i!==xe.sps&&i!==xe.pps||(t=we.h265)),t}(e),Vt=Kt.streamType===we.h265),Kt.streamType===we.h264){const t=Kt.handleAddNaluStartCode(e),i=Kt.extractNALu(t);if(0===i.length)return void Qt.debug.warn("worker","handleVideoNalu","h264 naluList.length === 0");const r=[];if(i.forEach((e=>{const t=yt(e);t===Ue.pps||t===Ue.sps?Kt.handleVideoH264Nalu(e):vt(t)&&r.push(e)})),1===r.length)Kt.handleVideoH264Nalu(r[0]);else{const e=function(e){if(0===e.length)return!1;const t=yt(e[0]);for(let i=1;i<e.length;i++)if(t!==yt(e[i]))return!1;return!0}(r);if(e){const e=yt(r[0]),t=wt(e);Kt.handleVideoH264NaluList(r,t,e)}else r.forEach((e=>{Kt.handleVideoH264Nalu(e)}))}}else if(Kt.streamType===we.h265)if(Qt._opt.nakedFlowH265DemuxUseNew){const t=Kt.handleAddNaluStartCode(e),i=Kt.extractNALu(t);if(0===i.length)return void Qt.debug.warn("worker","handleVideoNalu","h265 naluList.length === 0");const r=[];if(i.forEach((e=>{const t=Lt(e);t===xe.pps||t===xe.sps||t===xe.vps?Kt.handleVideoH265Nalu(e):Pt(t)&&r.push(e)})),1===r.length)Kt.handleVideoH265Nalu(r[0]);else{const e=function(e){if(0===e.length)return!1;const t=Lt(e[0]);for(let i=1;i<e.length;i++)if(t!==Lt(e[i]))return!1;return!0}(r);if(e){const e=Lt(r[0]),t=zt(e);Kt.handleVideoH265NaluList(r,t,e)}else r.forEach((e=>{Kt.handleVideoH265Nalu(e)}))}}else{Lt(e)===xe.pps?Kt.extractH265PPS(e):Kt.handleVideoH265Nalu(e)}},extractH264PPS(e){const t=Kt.handleAddNaluStartCode(e);Kt.extractNALu(t).forEach((e=>{bt(yt(e))?Kt.extractH264SEI(e):Kt.handleVideoH264Nalu(e)}))},extractH265PPS(e){const t=Kt.handleAddNaluStartCode(e);Kt.extractNALu(t).forEach((e=>{const t=Lt(e);t===xe.sei?Kt.extractH265SEI(e):Kt.handleVideoH265Nalu(e)}))},extractH264SEI(e){const t=Kt.handleAddNaluStartCode(e);Kt.extractNALu(t).forEach((e=>{Kt.handleVideoH264Nalu(e)}))},extractH265SEI(e){const t=Kt.handleAddNaluStartCode(e);Kt.extractNALu(t).forEach((e=>{Kt.handleVideoH265Nalu(e)}))},handleAddNaluStartCode(e){const t=[0,0,0,1],i=new Uint8Array(e.length+t.length);return i.set(t),i.set(e,t.length),i},handleVideoH264Nalu(e){const t=yt(e);switch(t){case Ue.sps:Kt.sps=e;break;case Ue.pps:Kt.pps=e}if(Kt.isSendSeqHeader){if(Kt.sps&&Kt.pps){const e=_t({sps:Kt.sps,pps:Kt.pps}),t=Kt.getNaluDts();Qt.decode(e,{type:q,ts:t,isIFrame:!0,cts:0}),Kt.sps=null,Kt.pps=null}if(vt(t)){const i=wt(t),r=Kt.getNaluDts(),s=function(e,t){let i=[];i[0]=t?23:39,i[1]=1,i[2]=0,i[3]=0,i[4]=0,i[5]=e.byteLength>>24&255,i[6]=e.byteLength>>16&255,i[7]=e.byteLength>>8&255,i[8]=255&e.byteLength;const r=new Uint8Array(i.length+e.byteLength);return r.set(i,0),r.set(e,i.length),r}(e,i);Kt.doDecode(s,{type:q,ts:r,isIFrame:i,cts:0})}else Qt.debug.warn("work",`handleVideoH264Nalu Avc Seq Head is ${t}`)}else if(Kt.sps&&Kt.pps){Kt.isSendSeqHeader=!0;const e=_t({sps:Kt.sps,pps:Kt.pps});Qt.decode(e,{type:q,ts:0,isIFrame:!0,cts:0}),Kt.sps=null,Kt.pps=null}},handleVideoH264NaluList(e,t,i){if(Kt.isSendSeqHeader){const r=Kt.getNaluDts(),s=gt(e.reduce(((e,t)=>{const i=kt(e),r=kt(t),s=new Uint8Array(i.byteLength+r.byteLength);return s.set(i,0),s.set(r,i.byteLength),s})),t);Kt.doDecode(s,{type:q,ts:r,isIFrame:t,cts:0}),Qt.debug.log("worker",`handleVideoH264NaluList list size is ${e.length} package length is ${s.byteLength} isIFrame is ${t},nalu type is ${i}, dts is ${r}`)}else Qt.debug.warn("worker","handleVideoH264NaluList isSendSeqHeader is false")},handleVideoH265Nalu(e){const t=Lt(e);switch(t){case xe.vps:Kt.vps=e;break;case xe.sps:Kt.sps=e;break;case xe.pps:Kt.pps=e}if(Kt.isSendSeqHeader){if(Kt.vps&&Kt.sps&&Kt.pps){const e=It({vps:Kt.vps,sps:Kt.sps,pps:Kt.pps}),t=Kt.getNaluDts();Qt.decode(e,{type:q,ts:t,isIFrame:!0,cts:0}),Kt.vps=null,Kt.sps=null,Kt.pps=null}if(Pt(t)){const i=zt(t),r=Kt.getNaluDts(),s=function(e,t){let i=[];i[0]=t?28:44,i[1]=1,i[2]=0,i[3]=0,i[4]=0,i[5]=e.byteLength>>24&255,i[6]=e.byteLength>>16&255,i[7]=e.byteLength>>8&255,i[8]=255&e.byteLength;const r=new Uint8Array(i.length+e.byteLength);return r.set(i,0),r.set(e,i.length),r}(e,i);Kt.doDecode(s,{type:q,ts:r,isIFrame:i,cts:0})}else Qt.debug.warn("work",`handleVideoH265Nalu HevcSeqHead is ${t}`)}else if(Kt.vps&&Kt.sps&&Kt.pps){Kt.isSendSeqHeader=!0;const e=It({vps:Kt.vps,sps:Kt.sps,pps:Kt.pps});Qt.decode(e,{type:q,ts:0,isIFrame:!0,cts:0}),Kt.vps=null,Kt.sps=null,Kt.pps=null}},handleVideoH265NaluList(e,t,i){if(Kt.isSendSeqHeader){const r=Kt.getNaluDts(),s=Ft(e.reduce(((e,t)=>{const i=kt(e),r=kt(t),s=new Uint8Array(i.byteLength+r.byteLength);return s.set(i,0),s.set(r,i.byteLength),s})),t);Kt.doDecode(s,{type:q,ts:r,isIFrame:t,cts:0}),Qt.debug.log("worker",`handleVideoH265NaluList list size is ${e.length} package length is ${s.byteLength} isIFrame is ${t},nalu type is ${i}, dts is ${r}`)}else Qt.debug.warn("worker","handleVideoH265NaluList isSendSeqHeader is false")},doDecode(e,t){Qt.calcNetworkDelay(t.ts),t.isIFrame&&Qt.calcIframeIntervalTimestamp(t.ts),postMessage({cmd:I,type:he,value:e.byteLength}),postMessage({cmd:I,type:fe,value:t.ts}),Qt.decode(e,t)}},Xt={LOG_NAME:"worker fmp4Demuxer",mp4Box:_i.createFile(),offset:0,videoTrackId:null,audioTrackId:null,isHevc:!1,listenMp4Box(){Xt.mp4Box.onReady=Xt.onReady,Xt.mp4Box.onError=Xt.onError,Xt.mp4Box.onSamples=Xt.onSamples},initTransportDescarmber(){Xt.transportDescarmber=new bi},_getSeqHeader(e){const t=Xt.mp4Box.getTrackById(e.id);for(const e of t.mdia.minf.stbl.stsd.entries)if(e.avcC||e.hvcC){const t=new _i.DataStream(void 0,0,_i.DataStream.BIG_ENDIAN);let i=[];e.avcC?(e.avcC.write(t),i=[23,0,0,0,0]):(Xt.isHevc=!0,Vt=!0,e.hvcC.write(t),i=[28,0,0,0,0]);const r=new Uint8Array(t.buffer,8),s=new Uint8Array(i.length+r.length);return s.set(i,0),s.set(r,i.length),s}return null},onReady(e){Qt.debug.log(Xt.LOG_NAME,"onReady()",e);const t=e.videoTracks[0],i=e.audioTracks[0];if(t){Xt.videoTrackId=t.id;const e=Xt._getSeqHeader(t);e&&(Qt.debug.log(Xt.LOG_NAME,"seqHeader"),Qt.decodeVideo(e,0,!0,0)),Xt.mp4Box.setExtractionOptions(t.id)}if(i&&Qt._opt.hasAudio){Xt.audioTrackId=i.id;const e=i.audio||{},t=Ye.indexOf(e.sample_rate),r=i.codec.replace("mp4a.40.","");Xt.mp4Box.setExtractionOptions(i.id);const s=He({profile:parseInt(r,10),sampleRate:t,channel:e.channel_count});Qt.debug.log(Xt.LOG_NAME,"aacADTSHeader"),Qt.decodeAudio(s,0)}Xt.mp4Box.start()},onError(e){Qt.debug.error(Xt.LOG_NAME,"mp4Box onError",e)},onSamples(e,t,i){if(e===Xt.videoTrackId)for(const e of i){const t=e.data,i=e.is_sync,r=1e3*e.cts/e.timescale;e.duration,e.timescale,i&&Qt.calcIframeIntervalTimestamp(r);let s=null;s=Xt.isHevc?Ft(t,i):gt(t,i),postMessage({cmd:I,type:he,value:s.byteLength}),postMessage({cmd:I,type:fe,value:r}),Qt.decode(s,{type:q,ts:r,isIFrame:i,cts:0})}else if(e===Xt.audioTrackId){if(Qt._opt.hasAudio)for(const e of i){const t=e.data,i=1e3*e.cts/e.timescale;e.duration,e.timescale;const r=new Uint8Array(t.byteLength+2);r.set([175,1],0),r.set(t,2),postMessage({cmd:I,type:le,value:r.byteLength}),Qt.decode(r,{type:j,ts:i,isIFrame:!1,cts:0})}}else Qt.debug.warn(Xt.LOG_NAME,"onSamples() trackId error",e)},dispatch(e){let t=e;"string"!=typeof e?"object"==typeof e?(Xt.transportDescarmber&&(t=Xt.transportDescarmber.transport(t)),t.buffer.fileStart=Xt.offset,Xt.offset+=t.byteLength,Xt.mp4Box.appendBuffer(t.buffer)):Qt.debug.warn(Xt.LOG_NAME,"dispatch()","data is not object",e):Qt.debug.warn(Xt.LOG_NAME,"dispatch()","data is string",e)},destroy(){Xt.mp4Box&&(Xt.mp4Box.flush(),Xt.mp4Box=null),Xt.transportDescarmber&&(Xt.transportDescarmber.destroy(),Xt.transportDescarmber=null),Xt.offset=0,Xt.videoTrackId=null,Xt.audioTrackId=null,Xt.isHevc=!1}},Zt={LOG_NAME:"worker mpeg4Demuxer",lastBuffer:new Uint8Array(0),parsedOffset:0,firstStartCodeOffset:0,secondStartCodeOffset:0,state:"init",hasInitVideoCodec:!1,localDts:0,dispatch(e){const t=new Uint8Array(e);Zt.extractNALu(t)},destroy(){Zt.lastBuffer=new Uint8Array(0),Zt.parsedOffset=0,Zt.firstStartCodeOffset=0,Zt.secondStartCodeOffset=0,Zt.state="init",Zt.hasInitVideoCodec=!1,Zt.localDts=0},extractNALu(e){if(!e||e.byteLength<1)return void Qt.debug.warn(Zt.LOG_NAME,"extractNALu() buffer error",e);const t=new Uint8Array(Zt.lastBuffer.length+e.length);for(t.set(Zt.lastBuffer,0),t.set(new Uint8Array(e),Zt.lastBuffer.length),Zt.lastBuffer=t;;){if("init"===Zt.state){let e=!1;for(;Zt.lastBuffer.length-Zt.parsedOffset>=4;)if(0===Zt.lastBuffer[Zt.parsedOffset])if(0===Zt.lastBuffer[Zt.parsedOffset+1])if(1===Zt.lastBuffer[Zt.parsedOffset+2]){if(182===Zt.lastBuffer[Zt.parsedOffset+3]){Zt.state="findFirstStartCode",Zt.firstStartCodeOffset=Zt.parsedOffset,Zt.parsedOffset+=4,e=!0;break}Zt.parsedOffset++}else Zt.parsedOffset++;else Zt.parsedOffset++;else Zt.parsedOffset++;if(e)continue;break}if("findFirstStartCode"===Zt.state){let e=!1;for(;Zt.lastBuffer.length-Zt.parsedOffset>=4;)if(0===Zt.lastBuffer[Zt.parsedOffset])if(0===Zt.lastBuffer[Zt.parsedOffset+1])if(1===Zt.lastBuffer[Zt.parsedOffset+2]){if(182===Zt.lastBuffer[Zt.parsedOffset+3]){Zt.state="findSecondStartCode",Zt.secondStartCodeOffset=Zt.parsedOffset,Zt.parsedOffset+=4,e=!0;break}Zt.parsedOffset++}else Zt.parsedOffset++;else Zt.parsedOffset++;else Zt.parsedOffset++;if(e)continue;break}if("findSecondStartCode"===Zt.state){if(!(Zt.lastBuffer.length-Zt.parsedOffset>0))break;{let e,t,i=192&Zt.lastBuffer[Zt.parsedOffset];e=0==i?Zt.secondStartCodeOffset-14:Zt.secondStartCodeOffset;let r=0==(192&Zt.lastBuffer[Zt.firstStartCodeOffset+4]);if(r){if(Zt.firstStartCodeOffset-14<0)return void Qt.debug.warn(Zt.LOG_NAME,"firstStartCodeOffset -14 is",Zt.firstStartCodeOffset-14);Zt.hasInitVideoCodec||(Zt.hasInitVideoCodec=!0,Qt.debug.log(Zt.LOG_NAME,"setCodec"),ti.setCodec(ve,"")),t=Zt.lastBuffer.subarray(Zt.firstStartCodeOffset-14,e)}else t=Zt.lastBuffer.subarray(Zt.firstStartCodeOffset,e);let s=Zt.getNaluDts();Zt.hasInitVideoCodec?(postMessage({cmd:I,type:he,value:t.byteLength}),postMessage({cmd:I,type:fe,value:s}),ti.decode(t,r?1:0,s)):Qt.debug.warn(Zt.LOG_NAME,"has not init video codec"),Zt.lastBuffer=Zt.lastBuffer.subarray(e),Zt.firstStartCodeOffset=0==i?14:0,Zt.parsedOffset=Zt.firstStartCodeOffset+4,Zt.state="findFirstStartCode"}}}},getNaluDts(){let e=Zt.localDts;return Zt.localDts=Zt.localDts+40,e}},Jt={isFirstDispatch:!0,_pmtId:-1,_remainingPacketData:null,_videoPesData:[],_audioPesData:[],_gopId:0,_videoPid:-1,_audioPid:-1,_codecType:ye,_audioCodecType:Se.AAC,_vps:null,_sps:null,_pps:null,TAG_NAME:"worker TsDemuxer",videoTrack:{samples:[]},audioTrack:{samples:[]},_baseDts:-1,_audioNextPts:void 0,_videoNextDts:void 0,_audioTimestampBreak:!1,_videoTimestampBreak:!1,_lastAudioExceptionGapDot:0,_lastAudioExceptionOverlapDot:0,_lastAudioExceptionLargeGapDot:0,_isSendAACSeqHeader:!1,init(){},dispatch(e){const t=new Uint8Array(e);Jt.demuxAndFix(t,Jt.isFirstDispatch,!0,0),Jt.isFirstDispatch&&(Jt.isFirstDispatch=!1)},_probe:e=>!!e.length&&(71===e[0]&&71===e[188]&&71===e[376]),_parsePES(e){const t=e[8];if(null==t||e.length<t+9)return;if(1!==(e[0]<<16|e[1]<<8|e[2]))return;const i=(e[4]<<8)+e[5];if(i&&i>e.length-6)return;let r,s;const n=e[7];return 192&n&&(r=536870912*(14&e[9])+4194304*(255&e[10])+16384*(254&e[11])+128*(255&e[12])+(254&e[13])/2,64&n?(s=536870912*(14&e[14])+4194304*(255&e[15])+16384*(254&e[16])+128*(255&e[17])+(254&e[18])/2,r-s>54e5&&(r=s)):s=r),{data:e.subarray(9+t),pts:r,dts:s,originalPts:r,originalDts:s}},demuxAndFix(e,t,i,r){Jt._demux(e,t,i),Jt._fix(r,t,i)},_initVideoTrack:()=>({samples:[]}),_initAudioTrack:()=>({samples:[]}),_demux(e,t=!1,i=!0){t&&(Jt._pmtId=-1,Jt.videoTrack=Jt._initVideoTrack(),Jt.audioTrack=Jt._initAudioTrack()),!i||t?(Jt._remainingPacketData=null,Jt._videoPesData=[],Jt._audioPesData=[]):(Jt.videoTrack.samples=[],Jt.audioTrack.samples=[],Jt._remainingPacketData&&(e=zi(Jt._remainingPacketData,e),Jt._remainingPacketData=null));let r=e.length;const s=r%188;s&&(Jt._remainingPacketData=e.subarray(r-s),r-=s);for(let t=0;t<r;t+=188){if(71!==e[t])throw new Error("TS packet did not start with 0x47");const i=!!(64&e[t+1]),r=((31&e[t+1])<<8)+e[t+2];let s;if((48&e[t+3])>>4>1){if(s=t+5+e[t+4],s===t+188)continue}else s=t+4;switch(r){case 0:i&&(s+=e[s]+1),Jt._pmtId=(31&e[s+10])<<8|e[s+11];break;case Jt._pmtId:{i&&(s+=e[s]+1);const t=s+3+((15&e[s+1])<<8|e[s+2])-4;for(s+=12+((15&e[s+10])<<8|e[s+11]);s<t;){const t=(31&e[s+1])<<8|e[s+2];switch(e[s]){case 15:Jt._audioPid=t,Jt._audioCodecType=Se.AAC;break;case 27:Jt._videoPid=t,Jt._codecType=ye;break;case 36:Jt._videoPid=t,Jt._codecType=be;break;default:Qt.debug.warn(Jt.TAG_NAME,`Unsupported stream. type: ${e[s]}, pid: ${t}`)}s+=5+((15&e[s+3])<<8|e[s+4])}}break;case Jt._videoPid:i&&Jt._videoPesData.length&&Jt._parseVideoData(),Jt._videoPesData.push(e.subarray(s,t+188));break;case Jt._audioPid:i&&Jt._audioPesData.length&&Jt._parseAudioData(),Jt._audioPesData.push(e.subarray(s,t+188));break;case 17:case 8191:break;default:Qt.debug.warn(Jt.TAG_NAME,`Unknown pid: ${r}`)}}Jt._parseVideoData(),Jt._parseAudioData(),Jt.audioTrack.formatTimescale=Jt.videoTrack.formatTimescale=Jt.videoTrack.timescale=9e4,Jt.audioTrack.timescale=Jt.audioTrack.sampleRate||0},_parseVideoData(){if(!Jt._videoPesData.length)return void Qt.debug.log(Jt.TAG_NAME,"_parseVideoData","no video pes data");const e=Jt._parsePES(zi(...Jt._videoPesData));if(!e)return void Qt.debug.warn(Jt.TAG_NAME,"Cannot parse video pes data length",Jt._videoPesData.length);const t=function(e){const t=e.length;let i=2,r=0;for(;null!==e[i]&&void 0!==e[i]&&1!==e[i];)i++;if(i++,r=i+2,r>=t)return[];const s=[];for(;r<t;)switch(e[r]){case 0:if(0!==e[r-1]){r+=2;break}if(0!==e[r-2]){r++;break}i!==r-2&&s.push(e.subarray(i,r-2));do{r++}while(1!==e[r]&&r<t);i=r+1,r=i+2;break;case 1:if(0!==e[r-1]||0!==e[r-2]){r+=3;break}i!==r-2&&s.push(e.subarray(i,r-2)),i=r+1,r=i+2;break;default:r+=3}return i<t&&s.push(e.subarray(i)),s}(e.data);t?Jt._createVideoSample(t,e.pts,e.dts):Qt.debug.warn(Jt.TAG_NAME,"Cannot parse avc units",e),Jt._videoPesData=[]},_parseAudioData(){if(!Jt._audioPesData.length)return;const e=Jt._parsePES(zi(...Jt._audioPesData));if(e){if(Qt._opt.hasAudio){if(Jt._audioCodecType===Se.AAC){const t=function(e,t){const i=e.length;let r=0;for(;r+2<i&&(255!==e[r]||240!=(246&e[r+1]));)r++;if(r>=i)return;const s=r,n=[],a=(60&e[r+2])>>>2,o=We[a];if(!o)throw new Error(`Invalid sampling index: ${a}`);const d=1+((192&e[r+2])>>>6),l=(1&e[r+2])<<2|(192&e[r+3])>>>6;let h,f,p=0;const u=qe(o);for(;r+7<i;)if(255===e[r]&&240==(246&e[r+1])){if(f=(3&e[r+3])<<11|e[r+4]<<3|(224&e[r+5])>>5,i-r<f)break;h=2*(1&~e[r+1]),n.push({pts:t+p*u,data:e.subarray(r+7+h,r+f)}),p++,r+=f}else r++;return{skip:s,remaining:r>=i?void 0:e.subarray(r),frames:n,samplingFrequencyIndex:a,sampleRate:o,objectType:d,channelCount:l,originCodec:`mp4a.40.${d}`}}(e.data,e.originalPts);if(t){if(Jt.audioTrack.codec=t.codec,Jt.audioTrack.sampleRate=t.sampleRate,Jt.audioTrack.channelCount=t.channelCount,!Jt._isSendAACSeqHeader){const e=He({profile:t.objectType,sampleRate:t.samplingFrequencyIndex,channel:t.channelCount});Jt._isSendAACSeqHeader=!0,Qt.debug.log(Jt.TAG_NAME,"aac seq header",`profile: ${t.objectType}, sampleRate:${t.sampleRate},sampleRateIndex: ${t.samplingFrequencyIndex}, channel: ${t.channelCount}`),Qt.decodeAudio(e,0)}if(Jt._isSendAACSeqHeader){const e=[];t.frames.forEach((t=>{const i=t.pts,r=new Uint8Array(t.data.length+2);r.set([175,1],0),r.set(t.data,2);const s={type:j,pts:i,dts:i,payload:r};e.push(s)})),Jt.audioTrack.samples=Jt.audioTrack.samples.concat(e)}else Qt.debug.warn(Jt.TAG_NAME,"aac seq header not send")}else Qt.debug.warn(Jt.TAG_NAME,"aac parseADTS error")}Jt._audioPesData=[]}}else Qt.debug.warn(Jt.TAG_NAME,"Cannot parse audio pes",Jt._audioPesData)},_fix(e=0,t=!1,i=!0){e=Math.round(9e4*e);const r=Jt.videoTrack,s=Jt.audioTrack,n=r.samples,a=s.samples;if(!n.length&&!a.length)return;const o=n[0],d=a[0];let l=0;if(n.length&&a.length&&(l=o.dts-d.pts),Jt._baseDtsInited||Jt._calculateBaseDts(),t&&(Jt._calculateBaseDts(),Jt._baseDts-=e),!i){Jt._videoNextDts=l>0?e+l:e,Jt._audioNextPts=l>0?e:e-l;const t=o?o.dts-Jt._baseDts-Jt._videoNextDts:0,i=d?d.pts-Jt._baseDts-Jt._audioNextPts:0;Math.abs(t||i)>MAX_VIDEO_FRAME_DURATION&&(Jt._calculateBaseDts(Jt.audioTrack,Jt.videoTrack),Jt._baseDts-=e)}Jt._resetBaseDtsWhenStreamBreaked(),Jt._fixAudio(s),Jt._fixVideo(r);let h=r.samples.concat(s.samples);h=h.map((e=>(e.dts=Math.round(e.dts/90),e.pts=Math.round(e.pts/90),e.cts=e.pts-e.dts,e))).sort(((e,t)=>e.dts-t.dts)),h.forEach((e=>{const t=new Uint8Array(e.payload);delete e.payload,e.type===q?Jt._doDecodeVideo({...e,payload:t}):e.type===j&&Jt._doDecodeAudio({...e,payload:t})}))},_calculateBaseDts(){const e=Jt.audioTrack,t=Jt.videoTrack,i=e.samples,r=t.samples;if(!i.length&&!r.length)return!1;let s=1/0,n=1/0;i.length&&(e.baseDts=s=i[0].pts),r.length&&(t.baseDts=n=r[0].dts),Jt._baseDts=Math.min(s,n);const a=n-s;return Number.isFinite(a)&&Math.abs(a)>45e3&&Qt.debug.warn(Jt.TAG_NAME,`large av first frame gap,\n                video pts: ${n},\n                audio pts: ${s},\n                base dts: ${Jt._baseDts},\n                detect is: ${a}`),Jt._baseDtsInited=!0,!0},_resetBaseDtsWhenStreamBreaked(){if(Jt._baseDtsInited&&Jt._videoTimestampBreak&&Jt._audioTimestampBreak){if(!Jt._calculateBaseDts(Jt.audioTrack,Jt.videoTrack))return;Jt._baseDts-=Math.min(Jt._audioNextPts,Jt._videoNextDts),Jt._audioLastSample=null,Jt._videoLastSample=null,Jt._videoTimestampBreak=!1,Jt._audioTimestampBreak=!1}},_createVideoSample(e,t,i){if(!e.length)return;const r=Jt._codecType===be,s={isIFrame:!1,type:q,isHevc:r,vps:null,sps:null,pps:null,pts:t,dts:i,payload:null};e.forEach((e=>{const t=r?e[0]>>>1&63:31&e[0];switch(t){case 5:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:if(!r&&5!==t||r&&5===t)break;s.isIFrame=!0,Jt._gopId++;break;case 6:case 39:case 40:if(!r&&6!==t||r&&6===t)break;return void function(e,t){const i=e.length;let r=t?2:1,s=0,n=0,a="";for(;255===e[r];)s+=255,r++;for(s+=e[r++];255===e[r];)n+=255,r++;if(n+=e[r++],5===s&&i>r+16)for(let t=0;t<16;t++)a+=e[r].toString(16),r++;e.subarray(r)}(function(e){const t=e.byteLength,i=[];let r=1;for(;r<t-2;)0===e[r]&&0===e[r+1]&&3===e[r+2]?(i.push(r+2),r+=2):r++;if(!i.length)return e;const s=t-i.length,n=new Uint8Array(s);let a=0;for(r=0;r<s;a++,r++)a===i[0]&&(a++,i.shift()),n[r]=e[a];return n}(e),r);case 32:if(!r)break;s.vps||(s.vps=e);break;case 7:case 33:if(!r&&7!==t||r&&7===t)break;s.sps||(s.sps=e);break;case 8:case 34:if(!r&&8!==t||r&&8===t)break;s.pps||(s.pps=e)}if(r&&Pt(t)||!r&&vt(t)){const t=kt(e);if(s.payload){const e=new Uint8Array(s.payload.byteLength+t.byteLength);e.set(s.payload,0),e.set(t,s.payload.byteLength),s.payload=e}else s.payload=t}}));let n=null;r?s.sps&&s.vps&&s.pps&&(n=It({vps:s.vps,sps:s.sps,pps:s.pps})):s.sps&&s.pps&&(n=function({sps:e,pps:t}){let i=8+e.byteLength+1+2+t.byteLength,r=!1;const s=ut.parseSPS$2(e);66!==e[3]&&77!==e[3]&&88!==e[3]&&(r=!0,i+=4);let n=new Uint8Array(i);n[0]=1,n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=255,n[5]=225;let a=e.byteLength;n[6]=a>>>8,n[7]=255&a;let o=8;n.set(e,8),o+=a,n[o]=1;let d=t.byteLength;n[o+1]=d>>>8,n[o+2]=255&d,n.set(t,o+3),o+=3+d,r&&(n[o]=252|s.chroma_format_idc,n[o+1]=248|s.bit_depth_luma-8,n[o+2]=248|s.bit_depth_chroma-8,n[o+3]=0,o+=4);const l=[23,0,0,0,0],h=new Uint8Array(l.length+n.byteLength);return h.set(l,0),h.set(n,l.length),h}({sps:s.sps,pps:s.pps})),n&&(Qt.debug.log(Jt.TAG_NAME,"_createVideoSample","seqHeader"),Qt.decodeVideo(n,q,Math.round(s.pts/90),!0,0)),s.isIFrame&&Qt.calcIframeIntervalTimestamp(Math.round(s.dts/90)),Jt.videoTrack.samples=Jt.videoTrack.samples.concat(s)},_fixAudio(e){const t=e.samples;t.length&&(t.forEach((e=>{e.pts-=Jt._baseDts,e.dts=e.pts})),Jt._doFixAudioInternal(e,t,9e4))},_fixVideo(e){const t=e.samples;if(!t.length)return;if(t.forEach((e=>{e.dts-=Jt._baseDts,e.pts-=Jt._baseDts})),void 0===Jt._videoNextDts){const e=t[0];Jt._videoNextDts=e.dts}const i=t.length;let r=0;const s=t[0],n=t[1],a=Jt._videoNextDts-s.dts;let o;Math.abs(a)>45e3&&(s.dts+=a,s.pts+=a,Qt.debug.warn(Jt.TAG_NAME,`large video gap between chunk,\n             next dts is ${Jt._videoNextDts},\n             first dts is ${s.dts},\n             next dts is ${n.dts},\n             duration is ${a}`),n&&Math.abs(n.dts-s.dts)>9e4&&(Jt._videoTimestampBreak=!0,t.forEach(((e,t)=>{0!==t&&(e.dts+=a,e.pts+=a)}))));const d=e.samples[0],l=e.samples[i-1];o=1===i?9e3:Math.floor((l.dts-d.dts)/(i-1));for(let s=0;s<i;s++){const n=t[s].dts,a=t[s+1];if(r=s<i-1?a.dts-n:t[s-1]?Math.min(n-t[s-1].dts,o):o,r>9e4||r<0){Jt._videoTimestampBreak=!0,r=Jt._audioTimestampBreak?o:Math.max(r,2700);const i=Jt._audioNextPts||0;a&&a.dts>i&&(r=o),Qt.debug.warn(Jt.TAG_NAME,`large video gap between frames,\n                time is ${n/e.timescale},\n                dts is ${n},\n                origin dts is ${t[s].originalDts},\n                next dts is ${Jt._videoNextDts},\n                sample Duration is ${r} ,\n                ref Sample DurationInt is ${o}`)}t[s].duration=r,Jt._videoNextDts+=r}},_doFixAudioInternal(e,t,i){e.sampleDuration||(e.sampleDuration=qe(e.timescale,i));const r=e.sampleDuration;if(void 0===Jt._audioNextPts){const e=t[0];Jt._audioNextPts=e.pts}for(let i=0;i<t.length;i++){const s=Jt._audioNextPts,n=t[i],a=n.pts-s;if(!Jt._audioTimestampBreak&&a>=3*r&&a<=Ne&&!Je()){je(e.codec,e.channelCount)||t[0].data.subarray();const o=Math.floor(a/r);Math.abs(n.pts-Jt._lastAudioExceptionGapDot)>AUDIO_EXCETION_LOG_EMIT_DURATION&&(Jt._lastAudioExceptionGapDot=n.pts),Qt.debug.warn(Jt.TAG_NAME,`audio gap detected,\n                pts is ${t.pts},\n                originPts is ${t.originalPts},\n                count is ${o},\n                nextPts is ${s},\n                ref sample duration is ${r}`);for(let e=0;e<o;e++)Jt._audioNextPts+=r,i++;i--}else a<=-3*r&&a>=-9e4?(Math.abs(n.pts-Jt._lastAudioExceptionOverlapDot)>Oe&&(Jt._lastAudioExceptionOverlapDot=n.pts,Qt.debug.warn(Jt.TAG_NAME,`audio overlap detected,\n                    pts is ${n.pts},\n                    originPts is ${n.originalPts},\n                    nextPts is ${s},\n                    ref sample duration is ${r}`)),t.splice(i,1),i--):(Math.abs(a)>=Ne&&(Jt._audioTimestampBreak=!0,Math.abs(n.pts-Jt._lastAudioExceptionLargeGapDot)>Oe&&(Jt._lastAudioExceptionLargeGapDot=n.pts,Qt.debug.warn(Jt.TAG_NAME,`large audio gap detected,\n                        time is ${n.pts/1e3}\n                        pts is ${n.pts},\n                        originPts is ${n.originalPts},\n                        nextPts is ${s},\n                        sample duration is ${a}\n                        ref sample duration is ${r}`))),n.dts=n.pts=s,Jt._audioNextPts+=r)}},_doDecodeVideo(e){const t=new Uint8Array(e.payload);let i=null;i=e.isHevc?Ft(t,e.isIFrame):gt(t,e.isIFrame),postMessage({cmd:I,type:he,value:i.byteLength}),postMessage({cmd:I,type:fe,value:e.dts});const r=e.pts-e.dts;let s=Qt.cryptoPayload(i,e.isIFrame);Qt.decode(s,{type:q,ts:e.dts,isIFrame:e.isIFrame,cts:r})},_doDecodeAudio(){const e=new Uint8Array(sample.payload);postMessage({cmd:I,type:le,value:e.byteLength});let t=e;st(Qt._opt.m7sCryptoAudio)&&(t=Qt.cryptoPayloadAudio(e)),Qt.decode(t,{type:j,ts:sample.dts,isIFrame:!1,cts:0})},destroy(){Jt.videoTrack=null,Jt.audioTrack=null,Jt.tempSampleListInfo={},Jt._baseDts=-1,Jt._baseDtsInited=!1,Jt._basefps=50,Jt._hasCalcFps=!1,Jt._audioNextPts=void 0,Jt._videoNextDts=void 0,Jt._audioTimestampBreak=!1,Jt._videoTimestampBreak=!1,Jt._lastAudioExceptionGapDot=0,Jt._lastAudioExceptionOverlapDot=0,Jt._lastAudioExceptionLargeGapDot=0,Jt._isForHls=!0,Jt._isSendAACSeqHeader=!1,Qt.debug.log(Jt.TAG_NAME,"destroy")}},Qt={isPlayer:!0,isPlayback:!1,dropping:!1,isPushDropping:!1,isWorkerFetch:!1,isDestroyed:!1,fetchStatus:Ie,_opt:jt(),mp3Demuxer:null,delay:-1,pushLatestDelay:-1,firstTimestamp:null,startTimestamp:null,preDelayTimestamp:null,stopId:null,streamFps:null,streamAudioFps:null,streamVideoFps:null,writableStream:null,networkDelay:0,webglObj:null,startStreamRateAndStatsInterval:function(){Qt.stopStreamRateAndStatsInterval(),y=setInterval((()=>{_&&_(0);const e=JSON.stringify({demuxBufferDelay:Qt.getVideoBufferLength(),audioDemuxBufferDelay:Qt.getAudioBufferLength(),flvBufferByteLength:Qt.getFlvBufferLength(),netBuf:Qt.networkDelay||0,pushLatestDelay:Qt.pushLatestDelay||0,latestDelay:Qt.delay,isStreamTsMoreThanLocal:dt});postMessage({cmd:I,type:ue,value:e})}),1e3)},stopStreamRateAndStatsInterval:function(){y&&(clearInterval(y),y=null)},useOffscreen:function(){return Qt._opt.useOffscreen&&"undefined"!=typeof OffscreenCanvas},getDelay:function(e,t){if(!e||Qt._opt.hasVideo&&!ot)return-1;if(t===j)return Qt.delay;if(Qt.preDelayTimestamp&&Qt.preDelayTimestamp>e)return Qt.preDelayTimestamp-e>1e3&&Qt.debug.warn("worker",`getDelay() and preDelayTimestamp is ${Qt.preDelayTimestamp} > timestamp is ${e} more than ${Qt.preDelayTimestamp-e}ms and return ${Qt.delay}`),Qt.preDelayTimestamp=e,Qt.delay;if(Qt.firstTimestamp){if(e){const t=Date.now()-Qt.startTimestamp,i=e-Qt.firstTimestamp;t>=i?(dt=!1,Qt.delay=t-i):(dt=!0,Qt.delay=i-t)}}else Qt.firstTimestamp=e,Qt.startTimestamp=Date.now(),Qt.delay=-1;return Qt.preDelayTimestamp=e,Qt.delay},getDelayNotUpdateDelay:function(e,t){if(!e||Qt._opt.hasVideo&&!ot)return-1;if(t===j)return Qt.pushLatestDelay;if(Qt.preDelayTimestamp&&Qt.preDelayTimestamp-e>1e3)return Qt.debug.warn("worker",`getDelayNotUpdateDelay() and preDelayTimestamp is ${Qt.preDelayTimestamp} > timestamp is ${e} more than ${Qt.preDelayTimestamp-e}ms and return -1`),-1;if(Qt.firstTimestamp){let t=-1;if(e){const i=Date.now()-Qt.startTimestamp,r=e-Qt.firstTimestamp;i>=r?(dt=!1,t=i-r):(dt=!0,t=r-i)}return t}return-1},resetDelay:function(){Qt.firstTimestamp=null,Qt.startTimestamp=null,Qt.delay=-1,Qt.dropping=!1},resetAllDelay:function(){Qt.resetDelay(),Qt.preDelayTimestamp=null},doDecode:function(e){Qt._opt.isEmitSEI&&e.type===q&&Qt.isWorkerFetch&&Qt.findSei(e.payload,e.ts),Qt._opt.useWCS&&Qt.useOffscreen()&&e.type===q&&n.decode?n.decode(e.payload,e.ts,e.cts):e.decoder.decode(e.payload,e.ts,e.isIFrame,e.cts)},decodeNext(e){if(0===r.length)return;const t=e.ts,s=r[0],n=e.type===q&&rt(e.payload);if(nt(i))n&&(Qt.debug.log("worker",`decode data type is ${e.type} and\n                ts is ${t} next data type is ${s.type} ts is ${s.ts}\n                isVideoSqeHeader is ${n}`),r.shift(),Qt.doDecode(s));else{const i=s.ts-t,a=s.type===j&&e.type===q;(i<=20||a||n)&&(Qt.debug.log("worker",`decode data type is ${e.type} and\n                ts is ${t} next data type is ${s.type} ts is ${s.ts}\n                diff is ${i} and isVideoAndNextAudio is ${a} and isVideoSqeHeader is ${n}`),r.shift(),Qt.doDecode(s))}},init:function(){Qt.debug.log("worker","init and opt is",JSON.stringify(Qt._opt));const e=Qt._opt.playType===p,t=Qt._opt.playType===u;if(Kt.init(),Qt.isPlayer=e,Qt.isPlayback=t,Qt.isPlaybackCacheBeforeDecodeForFpsRender())Qt.debug.log("worker","playback and playbackIsCacheBeforeDecodeForFpsRender is true");else{Qt.debug.log("worker","setInterval()");const t=Qt._opt.videoBuffer+Qt._opt.videoBufferDelay,i=()=>{let i=null;if(r.length){if(Qt.isPushDropping)return void Qt.debug.warn("worker",`loop() isPushDropping is true and bufferList length is ${r.length}`);if(Qt.dropping){for(i=r.shift(),Qt.debug.warn("worker",`loop() dropBuffer is dropping and isIFrame ${i.isIFrame} and delay is ${Qt.delay} and bufferlist is ${r.length}`);!i.isIFrame&&r.length;)i=r.shift();const e=Qt.getDelayNotUpdateDelay(i.ts,i.type);i.isIFrame&&e<=Qt.getNotDroppingDelayTs()&&(Qt.debug.log("worker","loop() is dropping = false, is iFrame"),Qt.dropping=!1,Qt.doDecode(i),Qt.decodeNext(i))}else if(Qt.isPlayback||Qt.isPlayUseMSE()||0===Qt._opt.videoBuffer)for(;r.length;)i=r.shift(),Qt.doDecode(i);else if(i=r[0],-1===Qt.getDelay(i.ts,i.type))Qt.debug.log("worker","loop() common dumex delay is -1 ,data.ts is",i.ts),r.shift(),Qt.doDecode(i),Qt.decodeNext(i);else if(Qt.delay>t&&e)Qt.hasIframeInBufferList()?(Qt.debug.log("worker",`delay is ${Qt.delay} > maxDelay ${t}, set dropping is true`),Qt.resetAllDelay(),Qt.dropping=!0,postMessage({cmd:L})):(r.shift(),Qt.doDecode(i),Qt.decodeNext(i));else for(;r.length;){if(i=r[0],!(Qt.getDelay(i.ts,i.type)>Qt._opt.videoBuffer)){Qt.delay<0&&Qt.debug.warn("worker",`loop() do not decode and delay is ${Qt.delay}, bufferList is ${r.length}`);break}r.shift(),Qt.doDecode(i)}}else-1!==Qt.delay&&Qt.debug.log("worker","loop() bufferList is empty and reset delay"),Qt.resetAllDelay()};Qt.stopId=setInterval((()=>{let e=(new Date).getTime();lt||(lt=e);const t=e-lt;t>100&&Qt.debug.warn("worker",`loop demux diff time is ${t}`),i(),lt=(new Date).getTime()}),20)}nt(Qt._opt.checkFirstIFrame)&&(ot=!0)},playbackCacheLoop:function(){Qt.stopId&&(clearInterval(Qt.stopId),Qt.stopId=null);const e=()=>{let e=null;r.length&&(e=r.shift(),Qt.doDecode(e))};e();const t=Math.ceil(1e3/(Qt.streamFps*Qt._opt.playbackRate));Qt.debug.log("worker",`playbackCacheLoop fragDuration is ${t}, streamFps is ${Qt.streamFps}, streamAudioFps is ${Qt.streamAudioFps} ,streamVideoFps is ${Qt.streamVideoFps} playbackRate is ${Qt._opt.playbackRate}`),Qt.stopId=setInterval(e,t)},close:function(){if(Qt.debug.log("worker","close"),Qt.isDestroyed=!0,qt(),!m||1!==m.readyState&&2!==m.readyState?m&&Qt.debug.log("worker",`close() and socket.readyState is ${m.readyState}`):(Yt=!0,m.close(1e3,"Client disconnecting")),m=null,Qt.stopStreamRateAndStatsInterval(),Qt.stopId&&(clearInterval(Qt.stopId),Qt.stopId=null),Qt.mp3Demuxer&&(Qt.mp3Demuxer.destroy(),Qt.mp3Demuxer=null),Qt.writableStream&&nt(Qt.writableStream.locked)&&Qt.writableStream.close().catch((e=>{Qt.debug.log("worker","close() and writableStream.close() error",e)})),Qt.writableStream=null,ei)try{ei.clear&&ei.clear(),ei=null}catch(e){Qt.debug.warn("worker","close() and audioDecoder.clear error",e)}if(ti)try{ti.clear&&ti.clear(),ti=null}catch(e){Qt.debug.warn("worker","close() and videoDecoder.clear error",e)}_=null,lt=null,dt=!1,n&&(n.reset&&n.reset(),n=null),Qt.firstTimestamp=null,Qt.startTimestamp=null,Qt.networkDelay=0,Qt.streamFps=null,Qt.streamAudioFps=null,Qt.streamVideoFps=null,Qt.delay=-1,Qt.pushLatestDelay=-1,Qt.preDelayTimestamp=null,Qt.dropping=!1,Qt.isPushDropping=!1,Qt.isPlayer=!0,Qt.isPlayback=!1,Qt.isWorkerFetch=!1,Qt._opt=jt(),Qt.webglObj&&(Qt.webglObj.destroy(),Qt.offscreenCanvas.removeEventListener("webglcontextlost",Qt.onOffscreenCanvasWebglContextLost),Qt.offscreenCanvas.removeEventListener("webglcontextrestored",Qt.onOffscreenCanvasWebglContextRestored),Qt.offscreenCanvas=null,Qt.offscreenCanvasGL=null,Qt.offscreenCanvasCtx=null),r=[],s=[],ke=null,Ce=null,Ge=null,Xe=!1,at=!1,ot=!1,Ot=!1,Gt=!1,Ht=!1,Vt=null,$t=null,St=[],Et=0,At=0,pt=null,ct=null,Bt=null,Rt=null,Wt=null,Mt=0,Nt=0,Ut=null,xt=null,Qt.fetchStatus=Ie,Kt.destroy(),Xt.destroy(),Zt.destroy(),Jt.destroy(),postMessage({cmd:N})},pushBuffer:function(e,t){if(t.type===j&&Ve(e)){if(Qt.debug.log("worker",`pushBuffer audio ts is ${t.ts}, isAacCodecPacket is true`),Qt._opt.isRecordTypeFlv){const t=new Uint8Array(e);postMessage({cmd:V,buffer:t},[t.buffer])}Qt.decodeAudio(e,t.ts)}else if(t.type===q&&t.isIFrame&&rt(e)){if(Qt.debug.log("worker",`pushBuffer video ts is ${t.ts}, isVideoSequenceHeader is true`),Qt._opt.isRecordTypeFlv){const t=new Uint8Array(e);postMessage({cmd:$,buffer:t},[t.buffer])}Qt.decodeVideo(e,t.ts,t.isIFrame,t.cts)}else{if(Qt._opt.isRecording)if(Qt._opt.isRecordTypeFlv){const i=new Uint8Array(e);postMessage({cmd:W,type:t.type,buffer:i,ts:t.ts},[i.buffer])}else if(Qt._opt.recordType===c)if(t.type===q){const i=new Uint8Array(e).slice(5);postMessage({cmd:k,buffer:i,isIFrame:t.isIFrame,ts:t.ts,cts:t.cts},[i.buffer])}else if(t.type===j&&Qt._opt.isWasmMp4){const i=new Uint8Array(e),r=$e(i)?i.slice(2):i.slice(1);postMessage({cmd:E,buffer:r,ts:t.ts},[r.buffer])}if(Qt.isPlayer&&Mt>0&&Rt>0&&t.type===q){const e=t.ts-Rt,i=Mt+Mt/2;e>i&&Qt.debug.log("worker",`pushBuffer video\n                    ts is ${t.ts}, preTimestamp is ${Rt},\n                    diff is ${e} and preTimestampDuration is ${Mt} and maxDiff is ${i}\n                    maybe trigger black screen or flower screen\n                    `)}if(Qt.isPlayer&&Rt>0&&t.type===q&&t.ts<Rt&&Rt-t.ts>g&&(Qt.debug.warn("worker",`pushBuffer,\n                preTimestamp is ${Rt}, options.ts is ${t.ts},\n                diff is ${Rt-t.ts} more than 3600000,\n                and resetAllDelay`),Qt.resetAllDelay(),Rt=null,Mt=0),Qt.isPlayer&&Rt>0&&t.ts<=Rt&&t.type===q&&(Qt.debug.warn("worker",`pushBuffer,\n                options.ts is ${t.ts} less than (or equal) preTimestamp is ${Rt} and\n                payloadBufferSize is ${e.byteLength} and prevPayloadBufferSize is ${Nt}`),Qt._opt.isDropSameTimestampGop&&ot)){const e=Qt.hasIframeInBufferList(),t=nt(Qt.isPushDropping);return Qt.debug.log("worker",`pushBuffer, isDropSameTimestampGop is true and\n                    hasIframe is ${e} and isNotPushDropping is ${t} and next dropBuffer`),void(e&&t?Qt.dropBuffer$2():(Qt.clearBuffer(!0),st(Qt._opt.checkFirstIFrame)&&st(i)&&postMessage({cmd:Y})))}if(Qt.isPlayer&&ot){const e=Qt._opt.videoBuffer+Qt._opt.videoBufferDelay,i=Qt.getDelayNotUpdateDelay(t.ts,t.type);Qt.pushLatestDelay=i,i>e&&Qt.delay<e&&Qt.delay>0&&Qt.hasIframeInBufferList()&&!1===Qt.isPushDropping&&(Qt.debug.log("worker",`pushBuffer(), pushLatestDelay is ${i} more than ${e} and decoder.delay is ${Qt.delay} and has iIframe and next decoder.dropBuffer$2()`),Qt.dropBuffer$2())}if(Qt.isPlayer&&t.type===q&&(Rt>0&&(Mt=t.ts-Rt),Nt=e.byteLength,Rt=t.ts),t.type===j?r.push({ts:t.ts,payload:e,decoder:{decode:Qt.decodeAudio},type:j,isIFrame:!1}):t.type===q&&r.push({ts:t.ts,cts:t.cts,payload:e,decoder:{decode:Qt.decodeVideo},type:q,isIFrame:t.isIFrame}),Qt.isPlaybackCacheBeforeDecodeForFpsRender()&&(Qe(Qt.streamVideoFps)||Qe(Qt.streamAudioFps))){let e=Qt.streamVideoFps,t=Qt.streamAudioFps;if(Qe(Qt.streamVideoFps)&&(e=tt(r,q),e&&(Qt.streamVideoFps=e,postMessage({cmd:P,value:Qt.streamVideoFps}),Qt.streamFps=t?e+t:e,nt(Qt._opt.hasAudio)&&(Qt.debug.log("worker","playbackCacheBeforeDecodeForFpsRender, _opt.hasAudio is false and set streamAudioFps is 0"),Qt.streamAudioFps=0),Qt.playbackCacheLoop())),Qe(Qt.streamAudioFps)&&(t=tt(r,j),t&&(Qt.streamAudioFps=t,Qt.streamFps=e?e+t:t,Qt.playbackCacheLoop())),Qe(Qt.streamVideoFps)&&Qe(Qt.streamAudioFps)){const i=r.map((e=>({type:e.type,ts:e.ts})));Qt.debug.log("worker",`playbackCacheBeforeDecodeForFpsRender, calc streamAudioFps is ${t}, streamVideoFps is ${e}, bufferListLength  is ${r.length}, and ts list is ${JSON.stringify(i)}`)}const i=Qt.getAudioBufferLength()>0,s=i?60:40;r.length>=s&&(Qt.debug.warn("worker",`playbackCacheBeforeDecodeForFpsRender, bufferListLength  is ${r.length} more than ${s}, and hasAudio is ${i} an set streamFps is 25`),Qt.streamVideoFps=25,postMessage({cmd:P,value:Qt.streamVideoFps}),i?(Qt.streamAudioFps=25,Qt.streamFps=Qt.streamVideoFps+Qt.streamAudioFps):Qt.streamFps=Qt.streamVideoFps,Qt.playbackCacheLoop())}}},getVideoBufferLength(){let e=0;return r.forEach((t=>{t.type===q&&(e+=1)})),e},hasIframeInBufferList:()=>r.some((e=>e.type===q&&e.isIFrame)),isAllIframeInBufferList(){const e=Qt.getVideoBufferLength();let t=0;return r.forEach((e=>{e.type===q&&e.isIFrame&&(t+=1)})),e===t},getNotDroppingDelayTs:()=>Qt._opt.videoBuffer+Qt._opt.videoBufferDelay/2,getAudioBufferLength(){let e=0;return r.forEach((t=>{t.type===j&&(e+=1)})),e},getFlvBufferLength(){let e=0;return ke&&ke.buffer&&(e=ke.buffer.byteLength),Qt._opt.isNakedFlow&&Kt.lastBuf&&(e=Kt.lastBuf.byteLength),e},fetchStream:function(e,t){Qt.debug.log("worker","fetchStream, url is "+e,"options:",JSON.stringify(t)),Qt.isWorkerFetch=!0,t.isFlv?Qt._opt.isFlv=!0:t.isFmp4?Qt._opt.isFmp4=!0:t.isMpeg4?Qt._opt.isMpeg4=!0:t.isNakedFlow?Qt._opt.isNakedFlow=!0:t.isTs&&(Qt._opt.isTs=!0),_=Ze((e=>{postMessage({cmd:I,type:de,value:e})})),Qt.startStreamRateAndStatsInterval(),t.isFmp4&&(Xt.listenMp4Box(),Qt._opt.isFmp4Private&&Xt.initTransportDescarmber()),t.protocol===l?(ke=new ht(Qt.demuxFlv()),fetch(e,{signal:a.signal}).then((e=>{if(st(Yt))return Qt.debug.log("worker","request abort and run res.body.cancel()"),Qt.fetchStatus=Ie,void e.body.cancel();if(!function(e){return e.ok&&e.status>=200&&e.status<=299}(e))return Qt.debug.warn("worker",`fetch response status is ${e.status} and ok is ${e.ok} and emit error and next abort()`),qt(),void postMessage({cmd:I,type:_e.fetchError,value:`fetch response status is ${e.status} and ok is ${e.ok}`});if(postMessage({cmd:I,type:pe}),"undefined"!=typeof WritableStream)Qt.writableStream=new WritableStream({write:e=>a&&a.signal&&a.signal.aborted?(Qt.debug.log("worker","writableStream write() and abortController.signal.aborted is true so return"),void(Qt.fetchStatus=Le)):st(Yt)?(Qt.debug.log("worker","writableStream write() and requestAbort is true so return"),void(Qt.fetchStatus=Le)):(Qt.fetchStatus=Fe,_(e.byteLength),void(t.isFlv?ke.write(e):t.isFmp4?Qt.demuxFmp4(e):t.isMpeg4?Qt.demuxMpeg4(e):t.isTs&&Qt.demuxTs(e))),close:()=>{Qt.fetchStatus=Le,ke=null,qt(),postMessage({cmd:I,type:oe,value:h,msg:"fetch done"})},abort:e=>{if(a&&a.signal&&a.signal.aborted)return Qt.debug.log("worker","writableStream abort() and abortController.signal.aborted is true so return"),void(Qt.fetchStatus=Le);ke=null,e.name!==Te&&(qt(),postMessage({cmd:I,type:_e.fetchError,value:e.toString()}))}}),e.body.pipeTo(Qt.writableStream);else{const i=e.body.getReader(),r=()=>{i.read().then((({done:e,value:i})=>e?(Qt.fetchStatus=Le,ke=null,qt(),void postMessage({cmd:I,type:oe,value:h,msg:"fetch done"})):a&&a.signal&&a.signal.aborted?(Qt.debug.log("worker","fetchNext().then() and abortController.signal.aborted is true so return"),void(Qt.fetchStatus=Le)):st(Yt)?(Qt.debug.log("worker","fetchNext().then() and requestAbort is true so return"),void(Qt.fetchStatus=Le)):(Qt.fetchStatus=Fe,_(i.byteLength),t.isFlv?ke.write(i):t.isFmp4?Qt.demuxFmp4(i):t.isMpeg4&&Qt.demuxMpeg4(i),void r()))).catch((e=>{if(a&&a.signal&&a.signal.aborted)return Qt.debug.log("worker","fetchNext().catch() and abortController.signal.aborted is true so return"),void(Qt.fetchStatus=Le);ke=null,e.name!==Te&&(qt(),postMessage({cmd:I,type:_e.fetchError,value:e.toString()}))}))};r()}})).catch((e=>{a&&a.signal&&a.signal.aborted?Qt.debug.log("worker","fetch().catch() and abortController.signal.aborted is true so return"):e.name!==Te&&(qt(),postMessage({cmd:I,type:_e.fetchError,value:e.toString()}),ke=null)}))):t.protocol===d&&(t.isFlv&&(ke=new ht(Qt.demuxFlv())),m=new WebSocket(e),m.binaryType="arraybuffer",m.onopen=()=>{Qt.debug.log("worker","fetchStream, WebsocketStream  socket open"),postMessage({cmd:I,type:pe}),postMessage({cmd:I,type:me})},m.onclose=e=>{Qt.debug.log("worker",`fetchStream, WebsocketStream socket close and code is ${e.code}`),1006===e.code&&Qt.debug.error("worker",`fetchStream, WebsocketStream socket close abnormally and code is ${e.code}`),st(Yt)?Qt.debug.log("worker","fetchStream, WebsocketStream socket close and requestAbort is true so return"):(ke=null,postMessage({cmd:I,type:oe,value:f,msg:e.code}))},m.onerror=e=>{Qt.debug.error("worker","fetchStream, WebsocketStream socket error",e),ke=null,postMessage({cmd:I,type:_e.websocketError,value:e.isTrusted?"websocket user aborted":"websocket error"})},m.onmessage=e=>{_(e.data.byteLength),t.isFlv?ke.write(e.data):t.isFmp4?Qt.demuxFmp4(e.data):t.isMpeg4?Qt.demuxMpeg4(e.data):Qt._opt.isNakedFlow?Qt.demuxNakedFlow(e.data):Qt.demuxM7s(e.data)})},demuxFlv:function*(){yield 9;const e=new ArrayBuffer(4),t=new Uint8Array(e),i=new Uint32Array(e);for(;;){t[3]=0;const e=yield 15,r=e[4];t[0]=e[7],t[1]=e[6],t[2]=e[5];const s=i[0];t[0]=e[10],t[1]=e[9],t[2]=e[8],t[3]=e[11];let n=i[0];const a=(yield s).slice();switch(r){case K:if(a.byteLength>0){let e=a;st(Qt._opt.m7sCryptoAudio)&&(e=Qt.cryptoPayloadAudio(a)),Qt.decode(e,{type:j,ts:n})}else Qt.debug.warn("worker",`demuxFlv() type is audio and payload.byteLength is ${a.byteLength} and return`);break;case X:if(a.byteLength>=6){const e=a[0];if(Qt._isEnhancedH265Header(e))Qt._decodeEnhancedH265Video(a,n);else{a[0];const e=a[0]>>4===De;if(e&&rt(a)&&null===Vt){const e=15&a[0];Vt=e===be,$t=Ct(a,Vt),Qt.debug.log("worker",`demuxFlv() isVideoSequenceHeader is true and isHevc is ${Vt} and nalUnitSize is ${$t}`)}e&&Qt.calcIframeIntervalTimestamp(n),Qt.isPlayer&&Qt.calcNetworkDelay(n),i[0]=a[4],i[1]=a[3],i[2]=a[2],i[3]=0;let t=i[0],r=Qt.cryptoPayload(a,e);Qt.decode(r,{type:q,ts:n,isIFrame:e,cts:t})}}else Qt.debug.warn("worker",`demuxFlv() type is video and payload.byteLength is ${a.byteLength} and return`);break;case Z:postMessage({cmd:H,buffer:a},[a.buffer]);break;default:Qt.debug.log("worker",`demuxFlv() type is ${r}`)}}},decode:function(e,t){t.type===j?Qt._opt.hasAudio&&(postMessage({cmd:I,type:le,value:e.byteLength}),Qt.isPlayer?Qt.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts}):Qt.isPlayback&&(Qt.isPlaybackOnlyDecodeIFrame()||(Qt.isPlaybackCacheBeforeDecodeForFpsRender(),Qt.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts})))):t.type===q&&Qt._opt.hasVideo&&(postMessage({cmd:I,type:he,value:e.byteLength}),postMessage({cmd:I,type:fe,value:t.ts}),Qt.isPlayer?Qt.pushBuffer(e,{type:t.type,ts:t.ts,isIFrame:t.isIFrame,cts:t.cts}):Qt.isPlayback&&(Qt.isPlaybackOnlyDecodeIFrame()?t.isIFrame&&Qt.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts,isIFrame:t.isIFrame}):(Qt.isPlaybackCacheBeforeDecodeForFpsRender(),Qt.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts,isIFrame:t.isIFrame}))))},cryptoPayload:function(e,t){let i=e;return Qt._opt.isM7sCrypto?Qt._opt.cryptoIV&&Qt._opt.cryptoIV.byteLength>0&&Qt._opt.cryptoKey&&Qt._opt.cryptoKey.byteLength>0?i=function(e,t,i,r=!1){t=new Uint8Array(t),i=new Uint8Array(i);const s=e.byteLength;let n=5;for(;n<s;){let o=(a=e.slice(n,n+4))[3]|a[2]<<8|a[1]<<16|a[0]<<24;if(o>s)break;let d=e[n+4],l=!1;if(r?(d=d>>>1&63,l=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(d)):(d&=31,l=1===d||5===d),l){const r=e.slice(n+4+2,n+4+o);let s=new mi.ModeOfOperation.ctr(t,new mi.Counter(i));const a=s.decrypt(r);s=null,e.set(a,n+4+2)}n=n+4+o}var a;return e}(e,Qt._opt.cryptoKey,Qt._opt.cryptoIV,Vt):Qt.debug.error("worker",`isM7sCrypto cryptoKey.length is ${Qt._opt.cryptoKey&&Qt._opt.cryptoKey.byteLength} or cryptoIV.length is ${Qt._opt.cryptoIV&&Qt._opt.cryptoIV.byteLength} null`):Qt._opt.isSm4Crypto?Qt._opt.sm4CryptoKey&&t?i=function(e,t,i=!1){const r=e.byteLength;let s=5;for(;s<r;){let a=(n=e.slice(s,s+4))[3]|n[2]<<8|n[1]<<16|n[0]<<24;if(a>r)break;let o=e[s+4],d=!1;if(i?(o=o>>>1&63,d=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(o)):(o&=31,d=1===o||5===o),d){const i=Di(e.slice(s+4+2,s+4+a),t,0,{padding:"none",output:"array"});e.set(i,s+4+2)}s=s+4+a}var n;return e}(e,Qt._opt.sm4CryptoKey):Qt._opt.sm4CryptoKey||Qt.debug.error("worker","isSm4Crypto opt.sm4CryptoKey is null"):Qt._opt.isXorCrypto&&(Qt._opt.cryptoIV&&Qt._opt.cryptoIV.byteLength>0&&Qt._opt.cryptoKey&&Qt._opt.cryptoKey.byteLength>0?i=function(e,t,i,r=!1){const s=e.byteLength;let n=5;for(;n<s;){let o=(a=e.slice(n,n+4))[3]|a[2]<<8|a[1]<<16|a[0]<<24;if(o>s)break;let d=e[n+4],l=!1;if(r?(d=d>>>1&63,l=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(d)):(d&=31,l=1===d||5===d),l){const r=Pi(e.slice(n+4,n+4+o),t,i);e.set(r,n+4)}n=n+4+o}var a;return e}(e,Qt._opt.cryptoKey,Qt._opt.cryptoIV,Vt):Qt.debug.error("worker",`isXorCrypto cryptoKey.length is ${Qt._opt.cryptoKey&&Qt._opt.cryptoKey.byteLength} or cryptoIV.length is ${Qt._opt.cryptoIV&&Qt._opt.cryptoIV.byteLength} null`)),i},cryptoPayloadAudio:function(e){let t=e;if(Qt._opt.isM7sCrypto)if(Qt._opt.cryptoIV&&Qt._opt.cryptoIV.byteLength>0&&Qt._opt.cryptoKey&&Qt._opt.cryptoKey.byteLength>0){e[0]>>4===Se.AAC&&(t=function(e,t,i){if(e.byteLength<=30)return e;const r=e.slice(32);let s=new mi.ModeOfOperation.ctr(t,new mi.Counter(i));const n=s.decrypt(r);return s=null,e.set(n,32),e}(e,Qt._opt.cryptoKey,Qt._opt.cryptoIV))}else Qt.debug.error("worker",`isM7sCrypto cryptoKey.length is ${Qt._opt.cryptoKey&&Qt._opt.cryptoKey.byteLength} or cryptoIV.length is ${Qt._opt.cryptoIV&&Qt._opt.cryptoIV.byteLength} null`);return t},setCodecAudio:function(e,t){const i=e[0]>>4,r=e[0]>>1&1;if(Wt=i===Se.AAC?r?16:8:0===r?8:16,ei&&ei.setCodec)if(Ve(e)||i===Se.ALAW||i===Se.MULAW||i===Se.MP3){Qt.debug.log("worker",`setCodecAudio: init audio codec, codeId is ${i}`);const r=i===Se.AAC?e.slice(2):new Uint8Array(0);ei.setCodec(i,Qt._opt.sampleRate,r),i===Se.AAC&&postMessage({cmd:A,buffer:r},[r.buffer]),at=!0,i!==Se.AAC&&(i===Se.MP3?(Qt.mp3Demuxer||(Qt.mp3Demuxer=new Li(Qt),Qt.mp3Demuxer.on("data",((e,t)=>{ei.decode(e,t)}))),Qt.mp3Demuxer.dispatch(e.slice(1),t)):ei.decode(e.slice(1),t))}else Qt.debug.warn("worker","setCodecAudio: hasInitAudioCodec is false, codecId is ",i);else Qt.debug.error("worker","setCodecAudio: audioDecoder or audioDecoder.setCodec is null")},decodeAudio:function(e,t){if(Qt.isDestroyed)Qt.debug.log("worker","decodeAudio, decoder is destroyed and return");else if(st(i)&&st(Qt._opt.mseDecodeAudio))postMessage({cmd:D,payload:e,ts:t,cts:t},[e.buffer]);else{const i=e[0]>>4;at?i===Se.MP3?Qt.mp3Demuxer.dispatch(e.slice(1),t):ei.decode(i===Se.AAC?e.slice(2):e.slice(1),t):Qt.setCodecAudio(e,t)}},setCodecVideo:function(e){const t=15&e[0];if(ti&&ti.setCodec)if(rt(e))if(t===ye||t===be){Qt.debug.log("worker",`setCodecVideo: init video codec , codecId is ${t}`);const i=e.slice(5);if(t===ye&&Qt._opt.useSIMD){const e=mt(i);if(e.codecWidth>4080||e.codecHeight>4080)return postMessage({cmd:M}),void Qt.debug.warn("worker",`setCodecVideo: SIMD H264 decode video width is too large, width is ${e.codecWidth}, height is ${e.codecHeight}`)}const r=new Uint8Array(e);Xe=!0,ti.setCodec(t,i),postMessage({cmd:B,code:t}),postMessage({cmd:T,buffer:r,codecId:t},[r.buffer])}else Qt.debug.warn("worker",`setCodecVideo: hasInitVideoCodec is false, codecId is ${t} is not H264 or H265`);else Qt.debug.warn("worker",`decodeVideo: hasInitVideoCodec is false, codecId is ${t} and frameType is ${e[0]>>4} and packetType is ${e[1]}`);else Qt.debug.error("worker","setCodecVideo: videoDecoder or videoDecoder.setCodec is null")},decodeVideo:function(e,t,r,s=0){if(Qt.isDestroyed)Qt.debug.log("worker","decodeVideo, decoder is destroyed and return");else if(st(i))postMessage({cmd:C,payload:e,isIFrame:r,ts:t,cts:s,delay:Qt.delay},[e.buffer]);else if(Xe)if(!ot&&r&&(ot=!0),ot){if(r&&rt(e)){const t=15&e[0];let i={};if(t===ye){i=mt(e.slice(5))}else t===be&&(i=function(e){let t={codecWidth:0,codecHeight:0,videoType:Be.h265,width:0,height:0,profile:0,level:0};e=e.slice(5);do{let i={};if(e.length<23){console.warn("parseHEVCDecoderConfigurationRecord$2",`arrayBuffer.length ${e.length} < 23`);break}if(i.configurationVersion=e[0],1!=i.configurationVersion)break;i.general_profile_space=e[1]>>6&3,i.general_tier_flag=e[1]>>5&1,i.general_profile_idc=31&e[1],i.general_profile_compatibility_flags=e[2]<<24|e[3]<<16|e[4]<<8|e[5],i.general_constraint_indicator_flags=e[6]<<24|e[7]<<16|e[8]<<8|e[9],i.general_constraint_indicator_flags=i.general_constraint_indicator_flags<<16|e[10]<<8|e[11],i.general_level_idc=e[12],i.min_spatial_segmentation_idc=(15&e[13])<<8|e[14],i.parallelismType=3&e[15],i.chromaFormat=3&e[16],i.bitDepthLumaMinus8=7&e[17],i.bitDepthChromaMinus8=7&e[18],i.avgFrameRate=e[19]<<8|e[20],i.constantFrameRate=e[21]>>6&3,i.numTemporalLayers=e[21]>>3&7,i.temporalIdNested=e[21]>>2&1,i.lengthSizeMinusOne=3&e[21];let r=e[22],s=e.slice(23);for(let e=0;e<r&&!(s.length<3);e++){let e=63&s[0],r=s[1]<<8|s[2];s=s.slice(3);for(let n=0;n<r&&!(s.length<2);n++){let r=s[0]<<8|s[1];if(s.length<2+r)break;if(s=s.slice(2),33==e){let e=new Uint8Array(r);e.set(s.slice(0,r),0),i.psps=Dt(e),t.profile=i.general_profile_idc,t.level=i.general_level_idc/30,t.width=i.psps.pic_width_in_luma_samples-(i.psps.conf_win_left_offset+i.psps.conf_win_right_offset),t.height=i.psps.pic_height_in_luma_samples-(i.psps.conf_win_top_offset+i.psps.conf_win_bottom_offset)}s=s.slice(r)}}}while(0);return t.codecWidth=t.width||1920,t.codecHeight=t.height||1080,t.presentHeight=t.codecHeight,t.presentWidth=t.codecWidth,t.timescale=1e3,t.refSampleDuration=1e3/23976*1e3,t}(e));i.codecWidth&&i.codecHeight&&Ce&&Ge&&(i.codecWidth!==Ce||i.codecHeight!==Ge)&&(Qt.debug.warn("worker",`\n                            decodeVideo: video width or height is changed,\n                            old width is ${Ce}, old height is ${Ge},\n                            new width is ${i.codecWidth}, new height is ${i.codecHeight},\n                            and emit change event`),Gt=!0,postMessage({cmd:z}))}if(Gt)return void Qt.debug.warn("worker","decodeVideo: video width or height is changed, and return");if(Ht)return void Qt.debug.warn("worker","decodeVideo: simd decode error, and return");if(rt(e))return void Qt.debug.warn("worker","decodeVideo and payload is video sequence header so drop this frame");if(e.byteLength<12)return void Qt.debug.warn("worker",`decodeVideo and payload is too small , payload length is ${e.byteLength}`);const i=e.slice(5);ti.decode(i,r?1:0,t)}else Qt.debug.log("worker","decodeVideo first frame is not iFrame");else Qt.setCodecVideo(e)},clearBuffer:function(e=!1){Qt.debug.log("worker",`clearBuffer,bufferList length is ${r.length}, need clear is ${e}`),e&&(r=[]),Qt.isPlayer&&(Qt.resetAllDelay(),st(Qt._opt.checkFirstIFrame)&&(Qt.dropping=!0,postMessage({cmd:L}))),st(Qt._opt.checkFirstIFrame)&&nt(i)&&(ot=!1)},dropBuffer$2:function(){if(r.length>0){let e=r.findIndex((e=>st(e.isIFrame)&&e.type===q));if(Qt.isAllIframeInBufferList())for(let t=0;t<r.length;t++){const i=r[t],s=Qt.getDelayNotUpdateDelay(i.ts,i.type);if(s>=Qt.getNotDroppingDelayTs()){Qt.debug.log("worker",`dropBuffer$2() isAllIframeInBufferList() is true, and index is ${t} and tempDelay is ${s} and notDroppingDelayTs is ${Qt.getNotDroppingDelayTs()}`),e=t;break}}if(e>=0){Qt.isPushDropping=!0,postMessage({cmd:L});const t=r.length;r=r.slice(e);const i=r.shift();Qt.resetAllDelay(),Qt.getDelay(i.ts,i.type),Qt.doDecode(i),Qt.isPushDropping=!1,Qt.debug.log("worker",`dropBuffer$2() iFrameIndex is ${e},and old bufferList length is ${t} ,new bufferList is ${r.length} and new delay is ${Qt.delay} `)}else Qt.isPushDropping=!1}0===r.length&&(Qt.isPushDropping=!1)},demuxM7s:function(e){const t=new DataView(e),i=t.getUint32(1,!1),r=t.getUint8(0),s=new ArrayBuffer(4),n=new Uint32Array(s);switch(r){case j:Qt.decode(new Uint8Array(e,5),{type:j,ts:i});break;case q:if(t.byteLength>=11){const r=new Uint8Array(e,5),s=r[0];if(Qt._isEnhancedH265Header(s))Qt._decodeEnhancedH265Video(r,i);else{const e=t.getUint8(5)>>4==1;if(e&&(Qt.calcIframeIntervalTimestamp(i),rt(r)&&null===Vt)){const e=15&r[0];Vt=e===be}Qt.isPlayer&&Qt.calcNetworkDelay(i),n[0]=r[4],n[1]=r[3],n[2]=r[2],n[3]=0;let s=n[0],a=Qt.cryptoPayload(r,e);Qt.decode(a,{type:q,ts:i,isIFrame:e,cts:s})}}else Qt.debug.warn("worker",`demuxM7s() type is video and arrayBuffer length is ${e.byteLength} and return`)}},demuxNakedFlow:function(e){Kt.dispatch(e)},demuxFmp4:function(e){const t=new Uint8Array(e);Xt.dispatch(t)},demuxMpeg4:function(e){Zt.dispatch(e)},demuxTs:function(e){Jt.dispatch(e)},_decodeEnhancedH265Video:function(e,t){const i=e[0],r=48&i,s=15&i,n=e.slice(1,5),a=new ArrayBuffer(4),o=new Uint32Array(a),d="a"==String.fromCharCode(n[0]);if(Vt=nt(d),s===Pe){if(r===Me){const i=e.slice(5);if(d);else{const r=new Uint8Array(5+i.length);r.set([28,0,0,0,0],0),r.set(i,5),$t=Ct(e,Vt),Qt.debug.log("worker",`demuxFlv() isVideoSequenceHeader(enhancedH265) is true and isHevc is ${Vt} and nalUnitSize is ${$t}`),Qt.decode(r,{type:q,ts:t,isIFrame:!0,cts:0})}}}else if(s===ze){let i=e,s=0;const n=r===Me;if(n&&Qt.calcIframeIntervalTimestamp(t),d);else{o[0]=e[4],o[1]=e[3],o[2]=e[2],o[3]=0,s=o[0];i=Ft(e.slice(8),n),i=Qt.cryptoPayload(i,n),Qt.decode(i,{type:q,ts:t,isIFrame:n,cts:s})}}else if(s===Re){const i=r===Me;i&&Qt.calcIframeIntervalTimestamp(t);let s=Ft(e.slice(5),i);s=Qt.cryptoPayload(s,i),Qt.decode(s,{type:q,ts:t,isIFrame:i,cts:0})}},_isEnhancedH265Header:function(e){return!(128&~e)},findSei:function(e,t){let i=4;Qe($t)||(i=$t);Tt(e.slice(5),i).forEach((e=>{const i=Vt?e[0]>>>1&63:31&e[0];(Vt&&(i===xe.suffixSei||i===xe.prefixSei)||nt(Vt)&&i===Ue.kSliceSEI)&&postMessage({cmd:G,buffer:e,ts:t},[e.buffer])}))},flvHasUnitTypeIDR(e,t){const i=Tt(e.slice(5));let r=!1;return i.forEach((e=>{const i=t?e[0]>>>1&63:31&e[0];(t&&(i===xe.iFrame||i===xe.nLp)||nt(t)&&i===Ue.iFrame)&&(r=!0)})),r},calcNetworkDelay:function(e){if(!(ot&&e>0))return;null===pt?(pt=e,ct=Ke()):e<pt&&(Qt.debug.warn("worker",`calcNetworkDelay, dts is ${e} less than bufferStartDts is ${pt}`),pt=e,ct=Ke());const t=e-pt,i=Ke()-ct,r=i>t?i-t:0;Qt.networkDelay=r,r>Qt._opt.networkDelay&&Qt._opt.playType===p&&(Qt.debug.warn("worker",`calcNetworkDelay now dts:${e}, start dts is ${pt} vs start is ${t},local diff is ${i} ,delay is ${r}`),postMessage({cmd:I,type:ce,value:r}))},calcIframeIntervalTimestamp:function(e){null===Bt?Bt=e:Bt<e&&(xt=e-Bt,postMessage({cmd:F,value:xt}),Bt=e)},canVisibilityDecodeNotDrop:function(){return Qt._opt.visibility&&Ce*Ge<=2073600},isPlaybackCacheBeforeDecodeForFpsRender:function(){return Qt.isPlayback&&Qt._opt.playbackIsCacheBeforeDecodeForFpsRender},isPlaybackOnlyDecodeIFrame:function(){return Qt._opt.playbackRate>=Qt._opt.playbackForwardMaxRateDecodeIFrame},isPlayUseMSE:function(){return Qt.isPlayer&&Qt._opt.useMSE&&st(i)},isPlayUseMSEAndDecoderInWorker:function(){return Qt.isPlayUseMSE()&&Qt._opt.mseDecoderUseWorker},playbackUpdatePlaybackRate:function(){Qt.clearBuffer(!0)},onOffscreenCanvasWebglContextLost:function(e){Qt.debug.error("worker","handleOffscreenCanvasWebglContextLost and next try to create webgl"),e.preventDefault(),Ot=!0,Qt.webglObj.destroy(),Qt.webglObj=null,Qt.offscreenCanvasGL=null,setTimeout((()=>{Qt.offscreenCanvasGL=Qt.offscreenCanvas.getContext("webgl"),Qt.offscreenCanvasGL&&Qt.offscreenCanvasGL.getContextAttributes().stencil?(Qt.webglObj=o(Qt.offscreenCanvasGL,Qt._opt.openWebglAlignment),Ot=!1):Qt.debug.error("worker","handleOffscreenCanvasWebglContextLost, stencil is false")}),500)},onOffscreenCanvasWebglContextRestored:function(e){Qt.debug.log("worker","handleOffscreenCanvasWebglContextRestored"),e.preventDefault()},videoInfo:function(e,t,i){postMessage({cmd:B,code:e}),postMessage({cmd:v,w:t,h:i}),Ce=t,Ge=i,Qt.useOffscreen()&&(Qt.offscreenCanvas=new OffscreenCanvas(t,i),Qt.offscreenCanvasGL=Qt.offscreenCanvas.getContext("webgl"),Qt.webglObj=o(Qt.offscreenCanvasGL,Qt._opt.openWebglAlignment),Qt.offscreenCanvas.addEventListener("webglcontextlost",Qt.onOffscreenCanvasWebglContextLost,!1),Qt.offscreenCanvas.addEventListener("webglcontextrestored",Qt.onOffscreenCanvasWebglContextRestored,!1))},audioInfo:function(e,t,i){postMessage({cmd:x,code:e}),postMessage({cmd:U,sampleRate:t,channels:i,depth:Wt}),At=i},yuvData:function(t,i){if(Qt.isDestroyed)return void Qt.debug.log("worker","yuvData, decoder is destroyed and return");const r=Ce*Ge*3/2;let s=e.HEAPU8.subarray(t,t+r),n=new Uint8Array(s);if(Ut=null,Qt.useOffscreen())try{if(Ot)return;Qt.webglObj.renderYUV(Ce,Ge,n);let e=Qt.offscreenCanvas.transferToImageBitmap();postMessage({cmd:w,buffer:e,delay:Qt.delay,ts:i},[e])}catch(e){Qt.debug.error("worker","yuvData, transferToImageBitmap error is",e)}else postMessage({cmd:w,output:n,delay:Qt.delay,ts:i},[n.buffer])},pcmData:function(e,i,r){if(Qt.isDestroyed)return void Qt.debug.log("worker","pcmData, decoder is destroyed and return");let n=i,a=[],o=0,d=Qt._opt.audioBufferSize;for(let i=0;i<2;i++){let r=t.HEAPU32[(e>>2)+i]>>2;a[i]=t.HEAPF32.subarray(r,r+n)}if(Et){if(!(n>=(i=d-Et)))return Et+=n,s[0]=Float32Array.of(...s[0],...a[0]),void(2==At&&(s[1]=Float32Array.of(...s[1],...a[1])));St[0]=Float32Array.of(...s[0],...a[0].subarray(0,i)),2==At&&(St[1]=Float32Array.of(...s[1],...a[1].subarray(0,i))),postMessage({cmd:S,buffer:St,ts:r},St.map((e=>e.buffer))),o=i,n-=i}for(Et=n;Et>=d;Et-=d)St[0]=a[0].slice(o,o+=d),2==At&&(St[1]=a[1].slice(o-d,o)),postMessage({cmd:S,buffer:St,ts:r},St.map((e=>e.buffer)));Et&&(s[0]=a[0].slice(o),2==At&&(s[1]=a[1].slice(o)))},errorInfo:function(e){null===Ut&&(Ut=Ke());const t=Ke(),i=(r=xt>0?2*xt:5e3,s=1e3,n=5e3,Math.max(Math.min(r,Math.max(s,n)),Math.min(s,n)));var r,s,n;const a=t-Ut;a>i&&(Qt.debug.warn("worker",`errorInfo() emit simdDecodeError and\n                iframeIntervalTimestamp is ${xt} and diff is ${a} and maxDiff is ${i}\n                and replay`),Ht=!0,postMessage({cmd:R}))},sendWebsocketMessage:function(e){m?m.readyState===ge?m.send(e):Qt.debug.error("worker","socket is not open"):Qt.debug.error("worker","socket is null")},timeEnd:function(){},postStreamToMain(e,t){postMessage({cmd:O,type:t,buffer:e},[e.buffer])}};Qt.debug=new ft(Qt);let ei=null;t.AudioDecoder&&(ei=new t.AudioDecoder(Qt));let ti=null;e.VideoDecoder&&(ti=new e.VideoDecoder(Qt)),postMessage({cmd:b}),self.onmessage=function(e){let t=e.data;switch(t.cmd){case J:try{Qt._opt=Object.assign(Qt._opt,JSON.parse(t.opt))}catch(e){}Qt.init();break;case Q:Qt.pushBuffer(t.buffer,t.options);break;case ee:Qt.decodeAudio(t.buffer,t.ts);break;case te:Qt.decodeVideo(t.buffer,t.ts,t.isIFrame);break;case se:Qt.clearBuffer(t.needClear);break;case ne:Qt.fetchStream(t.url,JSON.parse(t.opt));break;case ie:Qt.close();break;case re:Qt.debug.log("worker","updateConfig",t.key,t.value),Qt._opt[t.key]=t.value,"playbackRate"===t.key&&(Qt.playbackUpdatePlaybackRate(),Qt.isPlaybackCacheBeforeDecodeForFpsRender()&&Qt.playbackCacheLoop());break;case ae:Qt.sendWebsocketMessage(t.message)}}}({},{},!0)}));
