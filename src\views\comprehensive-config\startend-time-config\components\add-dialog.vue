<template>
  <el-dialog v-model="isVisible" width="1160" :show-close="false" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div v-if="step === 1" class="left" style="width: 50%;margin: 0 auto;">
        <el-form label-width="112" :model="formData" ref="ruleFormRef" :rules="isLook ? {} : rules" status-icon>
          <el-form-item label="模式名称:" prop="modelName">
            <el-input v-model="formData.modelName" placeholder="请输入" clearable :disabled="isLook" />
          </el-form-item>
          <el-form-item label="模式类型:" prop="modelType">
            <el-select v-model="formData.modelType" placeholder="请选择" @change="fun_typeChange" :disabled="isLook">
              <el-option v-for="(item, index) in modeTypeOption" :key="index" :label="item.businessLabel" :value="item.businessValue" />
            </el-select>
          </el-form-item>
          <el-form-item label="自定义时间:" prop="customTime" v-if="formData.modelType == 'custom'">
            <el-date-picker
              v-model="formData.customTime"
              type="daterange"
              value-format="YYYY-MM-DD"
              :disabled="isLook"
              @change="fun_customTimeChange"
            />
          </el-form-item>
          <el-form-item label="描述:" prop="modelMsg">
            <el-input type="textarea" v-model="formData.modelMsg" placeholder="请输入" :rows="3" maxlength="500" resize="none" :show-word-limit="true" :disabled="isLook" />
          </el-form-item>
          <div>
            <el-form-item label="启用状态:" prop="status">
              <el-switch width="50" v-model="formData.status" :active-value="1" :inactive-value="0" active-text="开启" inactive-text="关闭" inline-prompt :disabled="isLook" />
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div v-if="step == 2" style="height: 700px;width: 100%;padding: 0 20px;">
        <DeviceConfig v-if="isVisible" :deviceData="formData.strategyInfoParam" :isLook="isLook" />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button key="prev" v-if="step == 2" type="primary" @click="prevBtn">
          上一步
        </el-button>
        <el-button key="next" v-if="step == 1" type="primary" @click="nextBtn(ruleFormRef)">
          下一步
        </el-button>
        <el-button key="submit" v-if="step == 2 && !isLook" type="primary" @click="submitBtn">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive, watch } from "vue";

import {
  getStartendTimeInfo,
  addStartendTimeInfo,
  updateStartendTimeInfo,
} from "@/api/comprehensive-config/startend-time-config/index";

import { getBusinessDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage, ElMessageBox } from "element-plus";
import DeviceConfig from "./device-config.vue";
import _ from "lodash";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  isLook: {
    type: Boolean,
    default: false,
  },
});

const step = ref(1)
// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  step.value = 1
}

// 表单数据-回显
const formData = computed(() => {
  console.log('props.data:',props.data);
  
  //设置初始默认状态
  if(props.data.status===null)props.data.status = 0;//启用状态
  if(!props.data.strategyInfoParam)props.data.strategyInfoParam = thisDeviceData.value;
  if(props.data.modelType === 'custom'){
    props.data.customTime = [props.data.startTime, props.data.endTime]
  }
  return props.data;
});
const thisDeviceData = ref({});
watch(()=>props.data.timeModelId, async (newVal, oldVal) => {
  console.log('props.data.timeModelId变化:',newVal);
  if(newVal){
    const {data:{strategyInfoParam}} = await getStartendTimeInfo({gid:props.data.timeModelId});
    if(strategyInfoParam)thisDeviceData.value=strategyInfoParam;
    props.data.strategyInfoParam = strategyInfoParam||{};
  }else{
    thisDeviceData.value={};
  }
})


const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});


onMounted(async () => {
  fun_getDicts();//获取字典
});

// 获取字典
const fun_getDicts = async () => {
  const {data:modeTypeOptionData} = await getBusinessDict('mode_type');//获取传感器类型
  modeTypeOption.value = modeTypeOptionData;
  console.log('模式类型modeTypeOption:',modeTypeOption.value);
}

// 模式类型
const modeTypeOption = ref([
  // { label: '平日模式', value: '1' },
  // { label: '周末模式', value: '2' },
  // { label: '节假日模式', value: '3' },
  // { label: '自定义模式', value: 'custom' },
]);
// 模式选择
const fun_typeChange = (val) => {
  console.log('类型选择:',val);
  formData.value.customTime = null;
  formData.value.startTime = ''
  formData.value.endTime = ''
}
// 自定义时间选择
const fun_customTimeChange = (val) => {
  console.log('自定义时间选择:',val);
  if(val){
    formData.value.startTime = val[0]
    formData.value.endTime = val[1]
  }else{
    formData.value.startTime = ''
    formData.value.endTime = ''
  }
}

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  modelName: [
    { required: true, message: "请输入", trigger: "blur" },
  ],
  modelType: [
    { required: true, message: "请选择", trigger: "change" },
  ],
  customTime: [
    { required: true, message: "请选择", trigger: "change" },
    { validator: (rule, value, callback) => {
      if(value){
        if(value.length < 2){
          callback(new Error('请选择'))
          return false
        }
        callback()
      } else {
        callback(new Error('请选择'))
      }
    } }
  ],
  status: [
    { required: true, message: "请选择", trigger: "change" },
  ]
});


// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 上一步
const prevBtn = () => {
  step.value = 1
}
// 下一步
const nextBtn = async (formEl) => {
  if (!formEl) return;
  if(props.isLook){
    step.value = 2
    return
  }
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      step.value = 2
    }
  });
}
// 确定
const submitBtn = () => {
  console.log('提交:',formData.value);
  if(props.title == "新增"){
    addStartendTimeInfo(formData.value).then(res => {
      ElMessage({
        message: res.message,
        type: "success",
      });
      emits("refreshList");
      emits("onChangeVisible");
    })
  }else{
    updateStartendTimeInfo(formData.value).then(res => {
      ElMessage({
        message: res.message,
        type: "success",
      });
      emits("refreshList");
      emits("onChangeVisible");
    })
  }
};

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
      ::v-deep(.el-input.is-disabled .el-input__wrapper),
      ::v-deep(.el-range-editor.is-disabled),
      ::v-deep(.el-select__wrapper.is-disabled),
      ::v-deep(.el-textarea.is-disabled .el-textarea__inner) {
        background-color: #fff;
        box-shadow: 0 0 0 1px #dcdfe6 inset;
      }
      ::v-deep(.el-range-editor.is-disabled input) {
        background-color: #fff;
      }
    }
  }
}
</style>
