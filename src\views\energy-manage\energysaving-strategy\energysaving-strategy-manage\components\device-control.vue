<template>
  <div class="pointManage font-16">
    <div class="height-all">
      <div class="posit-relat height-all over-auto" style="width: 1616px;">
        <img style="width: 2000px;height: 1200px;" src="/static/images/test1.png" alt="">
        <ul>
          <li class="posit-absol hover bg-color-ddd" 
          :style="{left:(item.left||0)+'px',top:(item.top||0)+'px',width:(item.width||30)+'px',height:(item.height||30)+'px'}" 
          v-for="(item,key) of dataList" :key="key" 
          @mouseenter="fun_handleMouseEnter(item,key)"
          @mouseleave="fun_handleMouseLeave(item,key)"
          @click="fun_handleClick(item,key)"
          ></li>
        </ul>

        <!-- 详情弹窗 -->
        <div class="info-dialog flex flex-col filter-bg-blur font-14" v-show="dataInfoVisibility">
          <div class="flex flex-middle">
            <img class="marg-0_5" style="width:18px;" src="/static/images/dialog-arrow.png" alt="">
            <span class="font-16 font-bold">{{ dataInfo.name }}</span>
          </div>
          <div class="flex flex-right">
            <el-icon class="hover font-24" @click="dataInfoVisibility=false"><Close /></el-icon>
          </div>
          <ul class="flex flex-wrap max-height-all over-auto-y">
            <li class="flex flex-middle padd-5_0" style="width: 50%;" v-for="(item,key) of dataInfo.list" :key="key">
              <span class="color-ddd padd-r-5">{{ item.name }}</span>
              <span class="font-bold">{{ item.value }}</span>
            </li>
          </ul>
        </div>
        <!-- 启停控制弹窗 -->
        <div class="control-dialog filter-bg-blur" v-show="dataControlVisibility">
          <div class="flex flex-middle flex-both padd-5_10 border-b-ddd">
            <div class="flex flex-middle">
              <el-icon class="font-18 marg-t-2 marg-r-10"><HelpFilled /></el-icon>
              <span class="font-16 font-bold">{{ dataInfo.name }}</span>
            </div>
            <el-icon class="hover font-24" @click="dataControlVisibility=false"><Close /></el-icon>
          </div>
          <div class="padd-20">
            <el-form label-width="120" :inline="false" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
              <el-form-item label="频率设定:" prop="plsd" v-if="dataInfo.type=='1'||dataInfo.type=='3'">
                <div class="width-all flex">
                  <el-input v-model.number="formData.plsd" type="text" placeholder="请输入" class="input-bg-green" />
                  <span class="flex-item-shrink-0 padd-l-5">%</span>
                </div>
              </el-form-item>
              <el-form-item label="出水温度设定:" prop="cswdsd" v-if="dataInfo.type=='2'">
                <div class="width-all flex">
                  <el-input v-model.number="formData.cswdsd" type="text" placeholder="请输入" class="input-bg-green" />
                  <span class="flex-item-shrink-0 padd-l-5">℃</span>
                </div>
              </el-form-item>
            </el-form>

            <div class="font-40 marg-b-20 marg-t-20">远程启停</div>
            <div class="flex flex-around marg-b-20">
              <el-button size="large" color="#00FF94" :dark="isDark" plain style="background-color: #00FF9433;padding: 25px 40px;" @click="handleState('start')">启动</el-button>
              <el-button size="large" color="#DDF3FF" :dark="isDark" plain style="background-color: #DDF3FF33;padding: 25px 40px;" @click="handleState('stop')">停止</el-button>
            </div>
            <div class="flex flex-center marg-b-20 padd-t-20">
              <el-button type="primary" @click="submitBtn(ruleFormRef)">确定</el-button>
              <el-button type="default" @click="dataControlVisibility=false">取消</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
const router = useRouter();

import { getDict } from "@/api/common";
// import {
//   getList,
//   del,
//   update,
// } from "@/api/comprehensive-operation/device-inspect/index";


// 获取字典
const fun_getDicts = async () => {
  // const {data:cycleTypeOptionData} = await getDict('cycle_type');//获取巡检周期
  // cycleTypeOption.value = cycleTypeOptionData.list;
  // console.log('巡检周期cycleTypeOption:',cycleTypeOption.value);
}


// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  const {name:routeName} = router.currentRoute.value;
  console.log('当前路由:',routeName);
  fun_getList(routeName);
});

const dataList = ref([
  {
    name:'新风机组',type:'1',
    left:100,top:100,width:100,height:100,
    list:[
      {name:'冷机运行状态',value:'停止'},
      {name:'压缩机状态',value:'停止'},
      {name:'故障报警',value:'停止'},
      {name:'运行模式',value:'准备启动'},
      {name:'电流百分比',value:'0%'},
    ]
  },
  {
    name:'组合式空调机组',type:'2',
    left:250,top:150,width:100,height:100,
    list:[
      {name:'冷机运行状态',value:'停止'},
      {name:'压缩机状态',value:'停止'},
      {name:'故障报警',value:'停止2'},
      {name:'运行模式',value:'准备启动'},
      {name:'电流百分比',value:'0%'},
    ]
  },
  {
    name:'送风机',type:'3',
    left:300,top:280,width:100,height:100,
    list:[
      {name:'冷机运行状态',value:'停止'},
      {name:'压缩机状态',value:'停止'},
      {name:'故障报警',value:'停止3'},
      {name:'运行模式',value:'准备启动'},
      {name:'电流百分比',value:'0%'},
    ]
  },
  {
    name:'排风机',type:'4',
    left:200,top:400,width:100,height:100,
    list:[
      {name:'冷机运行状态',value:'停止'},
      {name:'压缩机状态',value:'停止'},
      {name:'故障报警',value:'停止4'},
      {name:'运行模式',value:'准备启动'},
      {name:'电流百分比',value:'0%'},
    ]
  },
  {
    name:'热风幕',type:'5',
    left:400,top:400,width:100,height:100,
    list:[
      {name:'冷机运行状态',value:'停止'},
      {name:'压缩机状态',value:'停止'},
      {name:'故障报警',value:'停止5'},
      {name:'运行模式',value:'准备启动'},
      {name:'电流百分比',value:'0%'},
    ]
  },
]);
const fun_getList = async (routeName) => {
  if(routeName=='Cold-source'){
    
  }else if(routeName=='Hot-source'){
    
  }
  // const { data } = await getList(filter.value);
  // console.log('班次列表:',data);
  // dataList.value = data.list;
};

// 详情弹窗
const dataInfo = ref({...dataList.value[0]})
const dataInfoVisibility = ref(false)
// 鼠标移入
const fun_handleMouseEnter = (item,key) => {
  console.log('鼠标移入:',item,key);
  dataInfo.value=item;
  dataInfoVisibility.value=true;
}
// 鼠标移出
const fun_handleMouseLeave = (item,key) => {
  console.log('鼠标移出:',item,key);
}

// 启停控制弹窗
const dataControlVisibility = ref(false)
// 点击
const fun_handleClick = (item,key) => {
  console.log('点击:',item,key);
  dataInfo.value=item;
  dataControlVisibility.value=true;
}

// 启用停用
const handleState = (type) => {
  ElMessageBox.confirm(`确定${type=='start'?'启动':'停止'}吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await update({id:dataInfo.value.id,triggerStatus:type=='start'?1:0});
    ElMessage({
      type: "success",
      message: `${type=='start'?'启动':'停止'}成功`,
    });
    fun_getList();
  })
};

// 表单提交数据
const formData = ref({})

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  plsd: [
    { required: true, message: "请输入", trigger: ["blur","change"] },
    { type: 'number', message: '请输入数字类型' },
  ],
  cswdsd: [
    { required: true, message: "请输入", trigger: ["blur","change"] },
    { type: 'number', message: '请输入数字类型' },
  ],
});

// 提交
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      ElMessageBox.confirm("确定提交吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const res = await add(item);
        ElMessage({
          message: res.message,
          type: "success",
        });
        dataControlVisibility.value=false;
        formData.value={};
        fun_getList();
      })
    }
    else{
      console.log('表单验证失败:',fields);
    }
  })
};


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
//详情弹窗
.info-dialog{
  position: absolute;
  right: 0;
  top: 0;
  width: 350px;
  height: 600px;
  background: url('/static/images/dialog-bg1.png') no-repeat;
  background-size: 100% 100%;
  padding: 54px 22px;
  color: #fff;
}
//启停弹窗
.control-dialog{
  position: absolute;
  left: 50%;
  margin-left: -180px;
  top: 50%;
  margin-top: -205px;
  width: 360px;
  height: 410px;
  background-color: rgba(91, 115, 255, 0.4);
  color: #fff;
  .input-bg-green,
  .input-bg-green :deep(.el-input__wrapper){
      background-color: rgba(0, 255, 255, 0.1);
  }
  :deep(.el-form-item__label){
    color: #fff;
    font-size: 16px;
  }
  :deep(.el-input__inner){
    color: #fff;
  }
  :deep(.el-input__inner::placeholder){
    color: #fff;
  }
  :deep(.el-form-item__label:before){
    display: none;
  }
}
</style>
