import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/risk';


/* 报警监测/记录页面 */

// 报警监测/记录-设备报警-列表
export function getDeviceAlarmList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/rskAlarmDetails/byPage',
    method: 'post',
    data: {...query} //assembleIds(报警状态特定参数):[1]是报警监测,不传是报警记录,
  })
}

// 报警监测/记录-设备报警-详情
export function getDeviceAlarmInfo(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/rskAlarmDetails/getInfo',
    method: 'get',
    params: {...query}
  })
}
// 报警记录-处理流程详情
export function getDeviceAlarmHandleInfo(query: any) {
  return request({
    url: baseRouter + '/monitor/tMaintenance/getInfoByRisk',
    method: 'get',
    params: {...query}
  })
}

// 报警监测/记录-设备报警-修改
export function updateDeviceAlarmInfo(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/rskAlarmDetails/editRskAlarmDetails',
    method: 'post',
    data: data
  })
}

// 获取人员树
export function getPersonnelTree(query: any) {
  return request({
    url: baseRouter + '/system/user/deptUserTree',
    method: 'get',
    params: {...query},
  })
}

// 报警监测/记录-设备报警-报警处置(新增维修)
export function addDeviceMaint(data: any) {
  return request({
    url: baseRouter + '/monitor/tMaintenance/addTMaintenance',
    method: 'post',
    data: data
  })
}

/* 传感器监测页面 */


// 传感器报警-列表
// export function getSensorAlarmList(query: any) {
//   return request({
//     url: baseRouter + apiPrefix + '/workShift/byPage',
//     method: 'post',
//     data: {...query}
//   })
// }





