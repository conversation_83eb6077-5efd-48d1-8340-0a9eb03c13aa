<template>
  <div class="gird-box">
    <div class="chart-box">
      <div ref="refChart" class="chart"></div>
      <div class="chart-content">
        <div class="chart-progress">{{ props.datas[curIndex]?.progress }}%</div>
        <div class="chart-value" :style="{ color: `${colorLine[curIndex]}` }">
          {{ props.datas[curIndex]?.value }}
        </div>
        <div class="chart-name">{{ props.datas[curIndex]?.name }}</div>
      </div>
    </div>

    <div class="legend-box">
      <div
        v-for="(item, index) in props.datas"
        @click="handleChoose(index)"
        :key="index"
        class="legend-item"
      >
        <div
          class="legend-item-icon"
          :style="{
            background: `linear-gradient(90deg, ${setColorOpacity(
              colorLine[index],
              0
            )} 0%, ${colorLine[index]} 100%)`,
          }"
        ></div>
        <div class="legend-item-label">
          {{ item.name }}
        </div>
        <div
          class="legend-item-value"
          :style="{ color: `${colorLine[index]}` }"
        >
          {{ item.value }}
        </div>
        <div class="legend-item-progress">{{ item.progress }}%</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, watch } from "vue";
import * as echarts from "echarts";
const props = defineProps({
  datas: {
    type: Array,
    default: () => {
      return [
        {
          name: "制冷机房",
          value: 0,
          progress: 0,
        },
        {
          name: "热风幕",
          value: 0,
          progress: 0,
        },
        {
          name: "空调机组",
          value: 0,
          progress: 0,
        },
        {
          name: "照明",
          value: 0,
          progress: 0,
        },
        {
          name: "制热机组",
          value: 0,
          progress: 0,
        },
      ];
    },
  },
});
const refChart = ref();
const curIndex = ref(0);
const handleChoose = (index) => {
  curIndex.value = index;
};
watch(()=>props.datas,(newVal)=>{
    let max = Math.max(...newVal.map((item) => item.value));
    initEcharts(props.datas,max);
},{deep:true});
let colorLine = ["#81FFC4", "#FFCA81", "#ACA6FF", "#FF8190", "#81D1FF"];

const setColorOpacity = (color, opacity) => {
  opacity = Math.max(0, Math.min(1, opacity));

  // 正则匹配不同格式
  const hexMatch = color.match(/^#?([a-f\d]{6}|[a-f\d]{3})$/i);
  const rgbMatch = color.match(/^rgb$$(\d+),\s*(\d+),\s*(\d+)$$$/i);
  const rgbaMatch = color.match(
    /^rgba$$(\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)$$$/i
  );

  if (hexMatch) {
    // 处理十六进制格式
    let hex = hexMatch[1];
    if (hex.length === 3) {
      hex = hex
        .split("")
        .map((c) => c + c)
        .join("");
    }
    const alpha = Math.round(opacity * 255)
      .toString(16)
      .padStart(2, "0");
    return `#${hex}${alpha}`;
  } else if (rgbMatch) {
    // 处理RGB格式
    const [_, r, g, b] = rgbMatch;
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  } else if (rgbaMatch) {
    // 处理RGBA格式
    const [_, r, g, b] = rgbaMatch;
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  // 未知格式直接返回原始颜色
  return color;
};

let initEcharts = (chartData,max) => {
  const myChart = echarts.init(refChart.value);
  let option = {
    backgroundColor: "transparent",
    tooltip: {
      show: false,
    },
    legend: {
      show: false,
    },
    series: [
      {
        type: "pie",
        radius: ["80%", "88%"],
        center: ["50%", "50%"],
        clockwise: true,
        avoidLabelOverlap: true,
        label: {
          show: false,
        },
        roundCap: true,

        itemStyle: {
          color: function (params) {
            return {
              type: "linear",
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: setColorOpacity(colorLine[params.dataIndex], 0), // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: colorLine[params.dataIndex], // 100% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            };
          },
          borderRadius: 20,
        },
        data: chartData,
        // roseType: 'radius'//齿轮状
      },
      {
        name: "阴影圈",
        type: "pie",
        radius: ["70%", "100%"],
        center: ["50%", "50%"],
        emphasis: {
          scale: false,
        },
        tooltip: {
          show: false,
        },
        itemStyle: {
          color: "rgba(204,225,255, 0.05)",
        },
        zlevel: 4,
        labelLine: {
          show: false,
        },
        data: [max],
      },
    ],
  };
  option && myChart.setOption(option);
};
</script>
<style scoped lang="scss">
.gird-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .chart-box {
    width: 180px;
    height: 180px;
    position: relative;
    .chart-content {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      .chart-progress {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-bottom: 6px;
      }
      .chart-value {
        line-height: 32px;
        margin-bottom: 10px;
        font-family: D-DIN-PRO, D-DIN-PRO;
        font-weight: bold;
        font-size: 32px;
        color: #ff8190;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .chart-name {
        line-height: 20px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
    .chart {
      width: 180px;
      height: 180px;
    }
  }
  .legend-box {
    width: 212px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 180px;
    .legend-item {
      width: 212px;
      cursor: pointer;
      height: 28px;
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0) 100%
      );
      border-radius: 2px 2px 2px 2px;
      padding-left: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .legend-item-icon {
        width: 20px;
        height: 5px;
        background: linear-gradient(
          90deg,
          rgba(129, 255, 196, 0) 0%,
          #81ffc4 100%
        );
        border-radius: 10px 10px 10px 10px;
      }
      .legend-item-label {
        width: 56px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, 0.8);
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .legend-item-value {
        width: 36px;
        line-height: 20px;
        font-family: D-DIN-PRO, D-DIN-PRO;
        font-weight: 600;
        font-size: 16px;
        color: #ffca81;
        text-align: right;
        font-style: normal;
        text-transform: none;
      }
      .legend-item-progress {
        width: 36px;
        line-height: 20px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        text-align: right;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
</style>
