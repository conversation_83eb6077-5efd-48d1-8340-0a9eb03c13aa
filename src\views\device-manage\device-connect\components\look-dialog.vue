<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">安装时间:</el-col>
        <el-col :span="8">{{ data.azsj }}</el-col>
        <el-col :span="4" class="text-right">养护时限:</el-col>
        <el-col :span="8">{{ data.yhsx }} 天/次</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">厂家名称:</el-col>
        <el-col :span="20">{{ data.cjmc }}</el-col>
      </el-row>

      <div class="padd-10 padd-t-0">
        <el-table :data="deviceData" max-height="400px">
          <el-table-column type="index" :index="(index)=>index+1" label="序号" align="center" width="100" />
          <el-table-column prop="sbmc" label="设备名称" align="center" />
        </el-table>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive, watch } from "vue";

import { getDeviceConnectInfo } from "@/api/device-manage";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      {
      }
    },
  },
});

// 设备列表数据-回显
const deviceData = ref([]);
watch( ()=>props.data.gid, async (newVal, oldVal) => {
  console.log('props.data.gid:',newVal,oldVal);
  const {data} = await getDeviceConnectInfo({gid: newVal});
  console.log('设备关联详情:',data?.equipmentInfos);
  deviceData.value = data?.equipmentInfos||[];
});

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
