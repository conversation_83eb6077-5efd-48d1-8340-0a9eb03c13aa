<template>
  <div class="dnd-flow width-all height-all posit-relat" @drop="onDrop">
    <!-- 操作面板 -->
    <aside class="posit-absol left-0 top-0 z-index-9 flex flex-col" v-if="isEdit">
      <div class="marg-b-10 border-b-ddd flex-item-shrink-0">设备列表面板</div>
      <div class="nodes text-center height-all over-auto-y">
        <!-- <div class="vue-flow__node-input marg-auto" :draggable="true" @dragstart="onDragStart($event, 'input')">输入节点</div> -->
        <!-- <div class="vue-flow__node-output marg-auto" :draggable="true" @dragstart="onDragStart($event, 'output')">输出节点</div> -->
        <!-- <div class="vue-flow__node-default marg-auto" :draggable="true" @dragstart="onDragStart($event, 'default')">
          <img src="/static/images/emergency.png" alt="">
        </div> -->
        
        <div class="" v-for="(item,index) in panelList" :key="index">
          <div class="vue-flow__node-custom inblock" style="box-shadow: none;" :draggable="true" @dragstart="onDragStart($event, 'custom',item)">
            <img width="100" style="max-width: 100%;" :style="item.style" :src="item.imgUrl" alt="">
            <div>{{ item.label }}</div>
          </div>
        </div>
        <!-- <div class="">
          <div class="vue-flow__node-custom inblock" style="box-shadow: none;" :draggable="true" @dragstart="onDragStart($event, 'custom',{imgUrl:'/static/images/emergency.png',style:{width:'74px'},label:'节点名称1'})">
            <img src="/static/images/emergency.png" style="width:74px;" alt="">
            <div>自定义节点1</div>
          </div>
        </div> -->
      </div>
    </aside>
    <!-- 画布区域 -->
    <!-- :default-viewport="{ zoom: 0.5 }" :max-zoom="4" :min-zoom="0.1" -->
    <VueFlow v-model:nodes="nodesData" v-model:edges="edgesData" :defaultEdgeOptions="{ type: 'custom-edge' }" :edgeTypes="edgeTypes" @dragover="onDragOver" @dragleave="onDragLeave" @node-click="onNodeClick" @node-mouse-enter="onNodeMouseEnter" @node-mouse-leave="onNodeMouseLeave" @edge-click="onEdgeClick" tabindex="0" @keyup="handleKeyUp" :nodesDraggable="isEdit" @click="infoDialogVisible = false" :default-viewport="{ zoom: config?.zoom || 1 }">
      <!-- 自定义节点 -->
      <template #node-custom="customNodeProps">
        <CustomNode v-bind="customNodeProps" :isEdit="isEdit" />
      </template>
      <!-- 自定义连接线 -->
      <template #edge-custom-edge="customEdgeProps">
        <CustomEdge v-bind="customEdgeProps" :isEdit="isEdit" />
      </template>
      <!-- 自定义拖拽创建连接线样式 -->
      <!-- <template #connection-line="sss">
        <div>{{ JSON.stringify(sss) }}</div>
        <CustomConnectionLine :source-x="sss.sourceX" :source-y="sss.sourceY" :target-x="sss.targetX" :target-y="sss.targetY" />
      </template> -->

      <!-- 背景 -->
      <div v-if="isEdit" class="dropzone-background" :style="{
          backgroundColor: isDragOver ? 'rgba(139, 181, 243, 0.3)' : 'transparent',
          transition: 'background-color 0.2s ease',
        }">
        <Background :size="2" :gap="20" pattern-color="#BDBDBD" />
        <div class="overlay">
          <p v-if="isDragOver">拖放到这里</p>
        </div>
      </div>
    </VueFlow>

    <!-- 详情信息弹窗 -->
    <div class="info-dialog posit-absol right-0 top-0 flex flex-col" v-show="infoDialogVisible" v-if="isEdit">
      <h5 class="border-b-ddd padd-b-5 marg-b-10">
        {{ nowInfoType == 'node' ? '节点详情' : '连接线详情' }}
      </h5>
      <div class="font-14 height-all over-auto-y">
        <ul class="" v-if="nowInfoType == 'node'">
          <li class="flex">
            <span class="flex-item-shrink-0">节点数据ID：</span>
            <!-- <span class="over-hide-2">{{ nowNodeData.data?.id }}</span> -->
            <el-input v-model="nowNodeData.data.id" placeholder="请输入" />
          </li>
          <li class="flex">
            <span class="flex-item-shrink-0">节点ID：</span>
            <span class="over-hide-2">{{ nowNodeData.id }}</span>
          </li>
          <li class="flex">
            <span class="flex-item-shrink-0">节点名称：</span>
            <!-- <span class="over-hide-2">{{ nowNodeData.data?.label }}</span> -->
            <el-input v-model="nowNodeData.data.label" placeholder="请输入" />
          </li>
          <li class="flex">
            <span class="flex-item-shrink-0">节点类型：</span>
            <span class="over-hide-2">{{ nowNodeData.type }}</span>
          </li>
        </ul>
        <ul class="" v-if="nowInfoType == 'edge'">
          <li class="flex">
            <span class="flex-item-shrink-0">连接线ID：</span>
            <span class="over-hide-2">{{ nowEdgeData.id }}</span>
          </li>
          <!-- <li class="flex">
            <span class="flex-item-shrink-0">连接线名称：</span>
            <span class="over-hide-2">{{ nowEdgeData.data?.label }}</span>
          </li> -->
          <li class="flex">
            <span class="flex-item-shrink-0">连接线类型：</span>
            <span class="over-hide-2">{{ nowEdgeData.type }}</span>
          </li>
          <li class="flex">
            <span class="flex-item-shrink-0">连接线源节点：</span>
            <span class="over-hide-2">{{ nowEdgeData.source }}</span>
          </li>
          <li class="flex">
            <span class="flex-item-shrink-0">连接线目标节点：</span>
            <span class="over-hide-2">{{ nowEdgeData.target }}</span>
          </li>
          <li class="flex flex-middle">
            <span class="flex-item-shrink-0">连接线颜色：</span>
            <el-select v-model="nowEdgeData.data.customColor" placeholder="请选择" @change="fun_changeEdgeColor">
              <el-option label="棕色" value="#f90"></el-option>
              <el-option label="蓝色" value="#00f"></el-option>
              <el-option label="浅绿" value="#00ffea"></el-option>
              <el-option label="深绿" value="#0f0"></el-option>
              <el-option label="黄色" value="#ff0"></el-option>
              <el-option label="红色" value="#ff6556"></el-option>
            </el-select>
          </li>
        </ul>
      </div>
    </div>
    <div class="posit-absol top-0" style="left: 46%;" v-if="isEdit||isPreview">
      <el-button type="primary" @click="fun_saveDraft">暂存草稿</el-button>
      <el-button type="default" @click="fun_restoreDraft">恢复草稿</el-button>
      <div class="inblock marg-l-20">
        <el-button type="default" @click="fun_preview">{{ isPreview? '编辑':'预览' }}</el-button>
        <el-button type="primary" @click="fun_submitData">提交</el-button>
      </div>
    </div>
  </div>
</template>


<script setup>
import { ref, reactive, onMounted, onUnmounted, watch, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

import { VueFlow, useVueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import useDragAndDrop from './VueFlow-useDnD.js'

import CustomNode from './CustomNode.vue'
import CustomEdge from './CustomEdge.vue'
// import CustomConnectionLine from './CustomConnectionLine.vue'

const { onDragStart, onDragOver, onDrop, onDragLeave, isDragOver } = useDragAndDrop()
const { onConnect, addEdges, findNode, removeNodes, findEdge, removeEdges, toObject, fromObject } = useVueFlow()
onConnect(addEdges)


const props = defineProps({
  data: Object,
  editable: {
    type: Boolean,
    default: true,
  },
  panelList: {
    type: Array,
    default: () => [],
  },
  config: {
    type: Object,
    default: () => {},
  },
});
// 是否可编辑
const isEdit = ref(true);
isEdit.value = JSON.parse(JSON.stringify(props.editable));
watch(() => props.editable, (newVal) => {
  isEdit.value = newVal;
})

// 监听props.data的变化
watch(() => props.data, (newData) => {
  if (newData) {
    fun_loadData();//加载(回显)数据
  }
}, { deep: true });

// 自定义边类型
const edgeTypes = {
  'custom-edge': CustomEdge
}

const nodesData = ref([
  /* // 输入节点，使用`type:'input'指定`
  { 
    id: '1',
    type: 'input', 
    position: { x: 250, y: 30 },
    // 所有节点都可以有一个数据对象，其中包含要传递给节点的任何数据
    // 标签can属性可用于默认节点
    data: { label: 'Node 1' },
  },
  // 默认节点，您可以省略`type:'default'，因为它是回退类型
  { 
    id: '2', 
    position: { x: 100, y: 120 },
    data: { label: 'Node 2' },
  },
  // 输出节点，使用`type:'output'指定`
  { 
    id: '3', 
    type: 'output', 
    position: { x: 200, y: 300 },
    data: { label: 'Node 3' },
  },
  // 自定义节点
  {
    id: '5',
    data: { 
      label: 'Node 5-custom',
      imgUrl: '/static/images/emergency.png'
    },
    type: 'custom',
    position: { x: 540, y: 250 },
  },
  {
    id: '6',
    data: { 
      label: 'Node 6-custom',
      imgUrl: '/static/images/dialog-arrow.png'
    },
    type: 'custom',
    position: { x: 340, y: 400 },
  }, */
])

// 这些是我们的边
const edgesData = ref([
  /* // 默认贝塞尔曲线边
  // 由边ID、源节点ID和目标节点ID组成
  { 
    id: 'e1->2',
    type: 'step',
    source: '1', 
    target: '2',
  },
  // 设置 `animated: true` 来创建动画边路径
  { 
    id: 'e2->3',
    type: 'step',
    source: '2', 
    target: '3', 
    animated: true,
  },
  // 自定义边，通过使用自定义类型名称指定
  // 在这个例子中我们选择 `type: 'special'`
  {
    id: 'e3->4',
    type: 'special',
    source: '3',
    target: '4',
    // 所有边都可以有一个数据对象，包含您想要传递给边的任何数据
    data: {
      hello: 'world',
    }
  },
  // 设置 `animated: true` 来创建动画边路径
  { 
    id: 'e5->6',
    type: 'custom-edge',
    source: '5', 
    target: '6', 
    // data: { customColor: '#f00' },
  }, */
])

// 挂载
onMounted(async () => {
  // fun_loadData();//加载(回显)数据
});
// 卸载
onUnmounted(async () => {
  localStorage.removeItem('vueFlowData');//移除本地草稿数据
});

// 加载(回显)数据
const fun_loadData = () => {
  nodesData.value = props.data?.nodes || [];
  edgesData.value = props.data?.edges || [];
}


//当前节点或连线类型
const infoDialogVisible = ref(false);
const nowInfoType = ref('node');
//节点点击
const nowNodeData = ref({data:{id:'',label:''}});
const onNodeClick = (event) => {
  if(isEdit.value){
    console.log('node-click', event)
    nowInfoType.value = 'node';
    nowNodeData.value = event.node;
    setTimeout(() => {
      infoDialogVisible.value = true;//显示详情弹窗
    }, 100);
  }else if(!isPreview.value){
    //非编辑并且非预览模式下，直接触发事件
    emit('nodeClick',{id:event.node.id,label:event.node.data.label});
  }
}
//连接线点击
const nowEdgeData = ref({data:{customColor:''}});
const onEdgeClick = (event) => {
  if(!isEdit.value)return;//非编辑模式下，不显示详情弹窗
  console.log('edge-click', event)
  nowInfoType.value = 'edge';
  nowEdgeData.value = event.edge;
  if(!nowEdgeData.value.data.customColor){
    nowEdgeData.value.data.customColor = '#00f';//默认颜色(蓝色)
  }
  setTimeout(() => {
    infoDialogVisible.value = true;//显示详情弹窗
  }, 100);
}

// 修改连接线颜色
const fun_changeEdgeColor = (val) => {
  let thisEdge = findEdge(nowEdgeData.value.id);
  console.log('thisEdge', thisEdge);
  thisEdge.data.customColor = val;
}


//暂存草稿
const fun_saveDraft = () => {
  localStorage.setItem('vueFlowData', JSON.stringify(toObject()));
  ElMessage.success('暂存成功');
}
//恢复本地草稿数据
const fun_restoreDraft = () => {
  ElMessageBox.confirm('确定恢复草稿吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const draftDataStr = localStorage.getItem('vueFlowData');
    if (draftDataStr) {
      const draftData = JSON.parse(draftDataStr);
      fromObject(draftData);
    }else{
      ElMessage.warning('暂无草稿数据');
    }
  })
}
//预览/编辑
const isPreview = ref(false);
const fun_preview = () => {
  isPreview.value = !isPreview.value;
  isEdit.value = !isEdit.value;
}

// 提交数据
const fun_submitData = () => {
  ElMessageBox.confirm('确定提交吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const saveData = toObject();//转换为可序列化的JSON格式
    console.log('saveDataStr', JSON.stringify(saveData));
    emit('fun_backSaveData',saveData);
  })
}


// 键盘事件监听
const handleKeyUp = (event) => {
  if(!isEdit.value)return;//非编辑模式下，不处理键盘事件
  // 监听Delete键
  if (event.key === 'Delete') {
    // 防止在输入框中误删
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
      return;
    }
    console.log('删除键抬起:', event.key);

    event.preventDefault(); // 阻止默认行为
    if(nowInfoType.value == 'node'){
      fun_delNode(nowNodeData.value.id);
    }else if(nowInfoType.value == 'edge'){
      fun_delEdge(nowEdgeData.value.id);
    }
  }
}
const fun_delNode = (id) => {
  ElMessageBox.confirm('确定删除节点吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // const selectedNodesIds = nodesData.value.filter(node => node.selected === true).map(item => item.id);
    // console.log('删除节点:',selectedNodesIds,nodesData.value);
    // if(selectedNodesIds&&selectedNodesIds.length>0)removeNodes(selectedNodesIds);
    nodesData.value = nodesData.value.filter(node => node.selected !== true);//直接删除
    nowNodeData.value = {data:{id:'',label:''}};
    infoDialogVisible.value = false; // 关闭详情弹窗
  })
}
const fun_delEdge = (id) => {
  ElMessageBox.confirm('确定删除连接线吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // const selectedEdgesIds = edgesData.value.filter(edge => edge.selected === true).map(item => item.id);
    // console.log('删除连接线:',selectedEdgesIds,edgesData.value);
    // if(selectedEdgesIds&&selectedEdgesIds.length>0)removeEdges(selectedEdgesIds);
    edgesData.value = edgesData.value.filter(edge => edge.selected !== true);//直接删除
    nowEdgeData.value = {data:{customColor:''}};
    infoDialogVisible.value = false; // 关闭详情弹窗
  })
}


/* 回显不可编辑功能 */
//节点经过事件
const onNodeMouseEnter = (event) => {
  if(isEdit.value||isPreview.value)return;//编辑或者预览模式下，不触发事件
  // console.log('node-mouse-enter', event)
  emit('nodeMouseEnter',{id:event.node.id,label:event.node.data.label});

}
//节点离开事件
const onNodeMouseLeave = (event) => {
  if(isEdit.value||isPreview.value)return;//编辑或者预览模式下，不触发事件
  // console.log('node-mouse-leave', event)
  emit('nodeMouseLeave',{id:event.node.id,label:event.node.data.label});
}


const emit = defineEmits(["nodeClick","nodeMouseEnter","nodeMouseLeave","fun_backSaveData"]);

// defineExpose({
  
// })

</script>

<style lang="scss">
/* vue-flow必须的样式 */
@import '@vue-flow/core/dist/style.css';
/* 这包含默认主题，这些是可选样式 */
@import '@vue-flow/core/dist/theme-default.css';

// @import 'https://cdn.jsdelivr.net/npm/@vue-flow/core@1.45.0/dist/style.css';
// @import 'https://cdn.jsdelivr.net/npm/@vue-flow/core@1.45.0/dist/theme-default.css';
// @import 'https://cdn.jsdelivr.net/npm/@vue-flow/controls@latest/dist/style.css';
// @import 'https://cdn.jsdelivr.net/npm/@vue-flow/minimap@latest/dist/style.css';
// @import 'https://cdn.jsdelivr.net/npm/@vue-flow/node-resizer@latest/dist/style.css';

</style>
<style lang="scss" scoped>
.vue-flow__minimap {
  transform: scale(75%);
  transform-origin: bottom right;
}

.dnd-flow {
    flex-direction:row;
    display:flex;
    height:100%
}

.dnd-flow aside {
    width: 15%;
    height: 95%;
    color:#fff;
    font-weight:700;
    border-right:1px solid #eee;
    padding:15px 10px;
    // font-size:12px;
    background:rgba(16, 185, 129, 0.8);
    -webkit-box-shadow:0px 5px 10px 0px rgba(0,0,0,.3);
    box-shadow:0 5px 10px #0000004d
}

.dnd-flow aside .nodes [class^="vue-flow__node"] {
    margin-bottom:10px;
    cursor:grab;
    font-weight:500;
    -webkit-box-shadow:5px 5px 10px 2px rgba(0,0,0,.25);
    box-shadow:5px 5px 10px 2px #00000040
}

.dnd-flow aside .description {
    margin-bottom:10px
}

.dnd-flow .vue-flow-wrapper {
    flex-grow:1;
    height:100%
}

@media screen and (max-width: 640px) {
    .dnd-flow {
        flex-direction:column
    }

    .dnd-flow aside {
        min-width:15%
    }
}

@media screen and (max-width: 640px) {
    .dnd-flow aside .nodes {
        display:flex;
        flex-direction:row;
        gap:5px
    }
}

.dropzone-background {
    position:relative;
    height:100%;
    width:100%
}

.dropzone-background .overlay {
    position:absolute;
    top:0;
    left:0;
    height:100%;
    width:100%;
    display:flex;
    align-items:center;
    justify-content:center;
    z-index:1;
    pointer-events:none
}

/* 详情信息弹窗 */
.info-dialog {
  width: 240px;
  min-height: 300px;
  max-height: 100%;
  background: rgba(16, 185, 129, 0.8);
  color: #fff;
  padding: 10px;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}


</style>
