.el-button {
  padding: 8px 17px;
}
.el-form-item {
  margin-bottom: 0;
}

.el-form-item__content {
  width: 256px;
  // margin-bottom: 20px;
}
.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  border: 1px solid #bac3d3;
  background-color: #fff;
}
.el-table {
  --el-table-border-color: none;
}

.el-table .el-table__cell {
  height: 48px;
}
.el-table tr:nth-child(2n) {
  background: #f5f9fb;
  border: 1px solid #e8eaef;
}

.formBox {
  width: 100%;
  height: 64px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  box-sizing: border-box;
  margin-bottom: 16px;
}

.main-contanier {
  height: calc(100% - 80px);
  background: #ffffff;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.08);
  border-radius: 4px 4px 4px 4px;
  padding: 16px 16px 0;
  box-sizing: border-box;
  .main-btns {
    margin-bottom: 16px;
    height: 32px;
  }

  .tableBox {
    height: calc(100% - 112px);
    .buttons {
      display: flex;
      justify-content: space-around;
      color: #3399ff;
      cursor: pointer;
    }
  }
  .table-no-top {
    height: calc(100% - 64px);
  }

  .pageBox {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: end;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
  }
}

.el-dialog {
  --el-dialog-padding-primary: 0;
}

// 弹窗
.dialog {
  .my-header {
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fafafa;
    padding: 0 16px;
    box-sizing: border-box;
    color: #000;
    font-size: 16px;
    .closeIcon {
      cursor: pointer;
    }
  }
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
  }

  .dialog-footer {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 16px;
    box-sizing: border-box;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
  }
}

// 树结构
.el-tree-node__content {
  --el-tree-node-content-height: 32px;
}
.el-tree-node__content:hover {
  --el-fill-color-light: #ebf5ff;
  background-color: #ebf5ff;
}

.el-form--inline .el-form-item {
  vertical-align: baseline;
}
.el-date-editor.el-input {
  width: 256px;
}
