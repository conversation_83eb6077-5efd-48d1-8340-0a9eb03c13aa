import { SoldOut } from "@element-plus/icons-vue/dist/types";

export class Columns {
  get columns() {
    return [
      {
        title: "",
        align: "center",
        type: "selection",
        width: 60,
      },
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "任务名称",
        key: "inspectionPlanName",
        align: "center",
      },
      {
        title: "任务编号",
        key: "planId",
        align: "center",
      },
      {
        title: "任务时间",
        key: "startTime",
        slot:"startTime",
        align: "center",
      },
      {
        title: "巡检项",
        key: "inspectionItemName",
        align: "center",
      },
      {
        title: "巡检人员",
        key: "inspectionPersonnel",
        align: "center",
      },
      {
        title: "任务状态",
        key: "taskCompleteState",
        slot: "taskCompleteState",
        align: "center",
      },
      {
        title: "隐患数",
        key: "yhwxNum",
        slot:"yhwxNum",
        align: "center",
      },
      {
        title: "维修数",
        key: "wxNum",
        slot: "wxNum",
        align: "center",
      },
      {
        title: "养护数",
        key: "yhNum",
        slot: "yhNum",
        align: "center",
      },
      // {
      //   title: "创建时间",
      //   key: "createTime",
      //   align: "center",
      // },
      {
        title: "操作",
        slot: "operations",
        align: "center",
        width: 200,
      },
    ];
  }
}
