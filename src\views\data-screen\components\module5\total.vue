<template>
  <div class="day-box">
    <div class="day-title">
      <i class="icon-person"></i>
      <div class="title-text">今日值班人员</div>
    </div>
    <div class="day-scroll">
      <div class="day-content">
        <div class="day-item" v-for="(item, index) in props.datas" :key="index">
          <div class="item-text">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import nums from "../count-up.vue";
const props = defineProps({
  datas: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
console.log(props.datas, "datas");
</script>
<style lang="scss" scoped>
.day-box {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  .day-title {
    display: flex;
    margin-right: 8px;
    align-items: center;
    .icon-person {
      width: 32px;
      height: 32px;
      margin-right: 8px;
      background: url(@/assets/data-screen/icon-person.png) no-repeat;
      background-size: 100% 100%;
    }
    .title-text {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      color: rgba(255, 255, 255, 0.8);
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
  .day-scroll {
    flex: 1;
    overflow-y: hidden;
    overflow-x: auto;
  }
  .day-content {
    display: flex;
    align-items: baseline;
    .day-item {
      flex-shrink: 0;
      display: flex;
      align-items: baseline;
      .item-text {
        height: 28px;
        line-height: 28px;
        background: linear-gradient(
          180deg,
          rgba(255, 255, 255, 0) 16%,
          rgba(90, 133, 244, 0.16) 80%
        );
        margin-right: 4px;
        border-radius: 2px 2px 2px 2px;
        border: 1px solid;
        border-image: linear-gradient(
            180deg,
            rgba(64, 249, 255, 0.4),
            rgba(90, 133, 244, 0.4)
          )
          1 1;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #40f9ff;
        padding: 0 17px;
      }

      &::after {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #40f9ff;
        content: "、";
      }
      &:last-child::after {
        content: "";
      }
    }
  }
}
</style>
