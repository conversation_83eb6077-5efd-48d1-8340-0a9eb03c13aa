<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form label-width="112" :inline="true" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <el-form-item label="班次名称:" prop="workShiftName">
            <el-input v-model="formData.workShiftName" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="上班时间:" prop="workUpTime">
            <el-time-picker v-model="formData.workUpTime" value-format="HH:mm:ss" placeholder="请选择" />
          </el-form-item>
          <el-form-item label="下班时间:" prop="workDownTime">
            <el-time-picker v-model="formData.workDownTime" value-format="HH:mm:ss" placeholder="请选择" />
          </el-form-item>
          <el-form-item label="启用状态:" prop="validFlag">
            <el-switch width="50" v-model="formData.validFlag" :active-value="1" :inactive-value="0" active-text="启用" inactive-text="停用" inline-prompt />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

import {
  addClassConfig,
  updateClassConfig,
} from "@/api/comprehensive-operation/maint-schedule-manage/index";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage } from "element-plus";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
}

// 表单数据-回显
const formData = computed(() => {
  console.log('props.data:',props.data);
  //设置初始默认状态
  if(props.data.validFlag===undefined)props.data.validFlag = 1;

  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});



// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  workShiftName: [
    { required: true, message: "请输入", trigger: ["blur","change"] },
  ],
  workUpTime: [
    { required: true, message: "请选择", trigger: ["blur","change"] },
  ],
  workDownTime: [
    { required: true, message: "请选择", trigger: ["blur","change"] },
  ],
  validFlag: [
    { required: true, message: "请选择", trigger: ["blur","change"] },
  ],
});


// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增") {
        const res = await addClassConfig(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
      } else {
        const res = await updateClassConfig(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
      }
      emits("refreshList");
      emits("onChangeVisible");
    }
  });
};



</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
