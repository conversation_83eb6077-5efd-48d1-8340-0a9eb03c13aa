# 页面标题
VITE_APP_TITLE = 基础平台系统

# 生产环境配置
VITE_APP_ENV = 'production'

# 若依管理系统/生产环境
VITE_APP_BASE_API = '/prod-api-oper'

VITE_APP_BASE_ROUTER = "/api/platform/"

VITE_APP_CM_BASE_API = 'http://*************:8001' # 城管服务

# 若依管理系统/开发环境(文件上传)
VITE_APP_IMG_URL = '/prod-api-oper'

# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS = 'none'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false
# 视频
VITE_APP_VIDEO_URL = 'http://*************:8000'

# 视频token
VITE_APP_VIDEO_TOKEN = 'c79fc575-3a87-4ee6-9b4c-9e9b1d851f46'
#视频中台
VITE_APP_HRGVP_URL = "http://*************:15747"
VITE_APP_HRGVP_TOKEN = "4a645274069b44b4a041113a97055014"
