import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';


/* 设备保护配置页面 */

// 设备保护配置-新增
export function addDeviceProtect(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentProtect/addEquipmentProtect',
    method: 'post',
    data: data
  })
}

// 设备保护配置-修改
export function updateDeviceProtect(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentProtect/editEquipmentProtect',
    method: 'post',
    data: data
  })
}

// 设备保护配置-删除
export function delDeviceProtect(delIds: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentProtect/deleteByIds',
    method: 'delete',
    params: { ids: delIds },
  })
}

// 设备保护配置-列表
export function getDeviceProtectList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentProtect/byPage',
    method: 'post',
    data: {...query}
  })
}

// 设备保护配置-详情
export function getDeviceProtectNewInfo(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentProtect/getNewInfo',
    method: 'get',
    params: {...query}
  })
}










