<template>
  <div class="pointManage">
    <div class="formBox">
      <el-form :inline="true" :model="filter">
        <el-form-item label="传感器名称:">
          <el-input v-model="filter.jhmc" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="监测项:">
          <el-input v-model="filter.xjxmc" placeholder="请输入" clearable />
          <!-- <el-select v-model="filter.sfqy" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in sfqyOption" :key="index" :label="item.label" :value="item.value" />
          </el-select> -->
        </el-form-item>
        <!-- <el-form-item label="时间范围:">
          <el-date-picker v-model="filter.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="fun_dateChange" />
        </el-form-item> -->
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier">
      <div class="main-btns">
        <el-button type="primary" @click="addBtn" :icon="Plus">新增</el-button>
        <!-- <el-button type="danger" @click="delAllBtn" :icon="Delete">批量删除</el-button> -->
      </div>
      <div class="tableBox">
        <Table :data="tableData" :columns="columns" row-key="id" @listSelectChange="handleSelectionChange">
          <template #sfqy="{ row }">
            <div>
              <el-switch width="50" v-model="row.sfqy" :active-value="1" :inactive-value="0" active-text="开" inactive-text="关" inline-prompt :before-change="()=>{return false;}" @click="handleState(row)"  />
            </div>
          </template>
          <template #operations="{ row }">
            <div class="buttons">
              <div class="btn" @click="handleLook(row)">查看</div>
              <div class="btn" @click="handleEdit(row)">编辑</div>
              <!-- <div class="btn" @click="handleDelete(row)">删除</div> -->
              <!-- <div class="btn" @click="handleDownload(row)">下载</div> -->
            </div>
          </template>
        </Table>
        <el-pagination class="pageBox" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增-编辑 -->
    <addDialog :dialogVisible="addVisiblty" :title="addTitle" :data="addData" @onChangeVisible="addVisiblty = false"
      @refreshList="fun_getList()"></addDialog>
    <!-- 查看 -->
    <lookDialog :dialogVisible="lookVisiblty" :data="lookData" @onChangeVisible="lookVisiblty = false"></lookDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
import addDialog from "./components/add-dialog.vue";
import lookDialog from "./components/look-dialog.vue";

import { getDict } from "@/api/common";
// import {
//   listReport,
//   getReportInfo,
//   // addReport,
//   // updateReport,
//   delReport,
// } from "@/api/comprehensive-operation/device-inspect/index";

// 搜索条件
const filter = ref({
  jhmc: '',
  xjxmc: '',
  dateRange: '',
  startTime: '',
  endTime: '',
  pageSize: 10,
  pageNum: 1,
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    jhmc: "",
    xjxmc: "",
    dateRange: "",
    startTime: "",
    endTime: "",
    pageSize: 10,
    pageNum: 1,
  };
  fun_getList();
};

//监测项
// const xjxOption = ref([]);

// 获取字典
const fun_getDicts = async () => {
  const {data} = await getDict('BGLX')
  bglxOption.value = data.list;
  console.log('报告类型bglxOption:',bglxOption.value);
}

// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getList();
});

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns);
// 表格数据
const total = ref(0);
const tableData = ref([
  // {id:1,cgqmc:'桥梁名称',jhsj:'2024-09-25 10:10:10',xjsbmc:'巡检设备',xjrymc:'巡检人员',xjzq:'每3天/次',sfqy:1},
  // {id:2,cgqmc:'桥梁名称',jhsj:'2024-09-25 10:10:10',xjsbmc:'巡检设备',xjrymc:'巡检人员',xjzq:'每3天/次',sfqy:0},
]);
const fun_getList = async () => {
  // const { data } = await listReport(filter.value);
  // tableData.value = data.list;
  // total.value = data.total;
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};


// 日期范围-切换
const fun_dateChange = (val) => {
  console.log('时间范围-时间选择:',val);
  if (val) {
    filter.value.startTime = val[0] + " 00:00:00";
    filter.value.endTime = val[1] + " 23:59:59";
  } else {
    filter.value.startTime = "";
    filter.value.endTime = "";
  }
};

// 新增
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref([]);
const addBtn = () => {
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row) => {
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "编辑";
  addVisiblty.value = true;
};

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row) => {
  lookVisiblty.value = true;
  lookData.value = row;
  // lookData.value = {}
  // const res = await getReportInfo(row.id)
  // lookData.value = res.data;
};
// 启用停用
const handleState =async (row) => {
  if(row.sfqy===undefined)return false;
  ElMessageBox.confirm(`确定${row.sfqy==1?'关闭':'开启'}吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    // const res = await delReport(row.id);
    ElMessage({
      type: "success",
      message: `${row.sfqy==1?'关闭':'开启'}成功`,
    });
    row.sfqy=row.sfqy==1?0:1;//启用停用
    // fun_getList();
  })
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("删除后不可恢复,确定吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await delReport(row.id);
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    fun_getList();
  })
};
// 批量删除
const delIds = ref("");
const handleSelectionChange = (data) => {
  delIds.value = data.map((item) => item.id).join(",");
};
const delAllBtn = () => {
  if (delIds.value) {
    ElMessageBox.confirm("批量删除后不可恢复,确定吗?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        const res = await delReport(delIds.value);
        if (res.code === 0) {
          fun_getList();
          ElMessage({
            type: "success",
            message: "删除成功",
          });
        } else {
          ElMessage({
            message: res.message,
            type: "warning",
          });
        }
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "取消删除",
        });
      });
  } else {
    ElMessage({
      message: "请选择需要删除的选项",
      type: "warning",
    });
  }
};

// 附件下载
// import { preImageUrl } from '@/utils/index'
// const handleDownload = (row) => {
//   console.log('附件下载:',preImageUrl,row);
//   if (row.fileList && row.fileList.length > 0) {
//     window.open( import.meta.env.VITE_APP_IMG_URL+preImageUrl+row.fileList[0].fileGid);
//   } else {
//     ElMessage({
//       message: "没有附件",
//       type: "warning",
//     });
//   }
// }


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
