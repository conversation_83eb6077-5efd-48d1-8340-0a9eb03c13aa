import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';




/* 班次配置页面 */

// 班次配置-新增
export function addClassConfig(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/workShift/addWorkShift',
    method: 'post',
    data: data
  })
}

// 班次配置-修改
export function updateClassConfig(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/workShift/editWorkShift',
    method: 'post',
    data: data
  })
}

// 班次配置-删除
export function delClassConfig(delIds: any) {
  return request({
    url: baseRouter + apiPrefix + '/workShift/deleteByIds',
    method: 'delete',
    params: { ids: delIds },
  })
}

// 班次配置-列表
export function getClassConfigList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/workShift/byPage',
    method: 'post',
    data: {...query}
  })
}


/* 排班配置页面 */

// 排班配置-新增-班次配置下拉列表
export function getClassConfigSelectOptions(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/workShift/allList',
    method: 'get',
    data: {...query}
  })
}

// 排班配置-新增
export function addScheduleConfig(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/personnelScheduling/addPersonnelScheduling',
    method: 'post',
    data: data
  })
}

// 排班配置-修改
export function updateScheduleConfig(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/personnelScheduling/editPersonnelScheduling',
    method: 'post',
    data: data
  })
}

// 排班配置-删除
export function delScheduleConfig(delIds: any) {
  return request({
    url: baseRouter + apiPrefix + '/personnelScheduling/deleteByIds',
    method: 'delete',
    params: { ids: delIds },
  })
}

// 排班配置-列表
export function getScheduleConfigList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/personnelScheduling/byPage',
    method: 'post',
    data: {...query}
  })
}


/* 排班日历页面 */

// 排班日历-列表
export function getScheduleCalendarList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/personnelScheduling/getSchedulingCalendar',
    method: 'post',
    data: {...query}
  })
}