<template>
  <div class="pointManage">
    <div class="formBox">
      <el-form :inline="true" :model="filter">
        <el-form-item label="场景名称:">
          <el-input v-model="filter.cjmc" placeholder="请输入" clearable />
        </el-form-item>
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier">
      <div class="main-btns">
        <el-button type="primary" @click="addBtn" :icon="Plus">新增</el-button>
      </div>
      <div class="tableBox">
        <Table :data="tableData" :columns="columns" row-key="gid" @listSelectChange="handleSelectionChange">
          <template #status="{ row }">
            <div>
              <el-switch width="50" v-model="row.status" active-value="1" inactive-value="0" active-text="开启" inactive-text="关闭" inline-prompt :before-change="()=>{return false;}" @click="handleState(row)"  />
            </div>
          </template>
          <template #operations="{ row }">
            <div class="buttons">
              <div class="btn" @click="handleLook(row)">查看</div>
              <div class="btn" @click="handleEdit(row)">编辑</div>
              <!-- <div class="btn" @click="handleDelete(row)">删除</div> -->
            </div>
          </template>
        </Table>
        <el-pagination class="pageBox" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增-编辑 -->
    <addDialog :dialogVisible="addVisiblty" :title="addTitle" :data="addData" @onChangeVisible="addVisiblty = false"
      @refreshList="fun_getList()"></addDialog>
    <!-- 查看 -->
    <lookDialog :dialogVisible="lookVisiblty" :data="lookData" @onChangeVisible="lookVisiblty = false"></lookDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
import addDialog from "./components/add-dialog.vue";
import lookDialog from "./components/look-dialog.vue";

import { getDict } from "@/api/common";
import {
  getSceneList,
  updateSceneInfo,
} from "@/api/energy-manage/index";

// 搜索条件
const filter = ref({
  cjmc: '',
  pageSize: 10,
  pageNum: 1,
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    cjmc: "",
    pageSize: 10,
    pageNum: 1,
  };
  fun_getList();
};

//监测项
// const xjxOption = ref([]);

// 获取字典
const fun_getDicts = async () => {
  
}

// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getList();
});

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns);
// 表格数据
const total = ref(0);
const tableData = ref([
  // {
  //   gid: 1,
  //   cjmc: "平日模式",
  //   status: 1,
  //   createTime: "2025-01-01 12:00:00",
  //   updateTime: "2025-01-01 12:00:00",
  //   maxPeople: 100,
  //   timeMin: '08:00',
  //   timeMax: '12:00',
  //   outTempMin: 1,
  //   outTempMax: 10,
  //   windSpeedMin: 1,
  //   windSpeedMax: 10,
  //   peopleRangeMin: 1,
  //   peopleRangeMax: 10,
  //   monthMin: 1,
  //   monthMax: 10,
  // },
  // {
  //   gid: 2,
  //   cjmc: "周末模式",
  //   status: 1,
  //   createTime: "2025-01-01 12:00:00",
  //   updateTime: "2025-01-01 12:00:00",
  // },
  // {
  //   gid: 3,
  //   cjmc: "节假日模式",
  //   status: 0,
  //   createTime: "2025-01-01 12:00:00",
  //   updateTime: "2025-01-01 12:00:00",
  // }
]);
const fun_getList = async () => {
  const { data } = await getSceneList(filter.value);
  tableData.value = data.list;
  total.value = data.total;
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};

// 新增
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref([]);
const addBtn = () => {
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row) => {
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "编辑";
  addVisiblty.value = true;
};

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row) => {
  lookVisiblty.value = true;
  lookData.value = row;
  // lookData.value = {}
  // const res = await getReportInfo(row.gid)
  // lookData.value = res.data;
};
// 启用停用
const handleState =async (row) => {
  if(row.status===undefined)return false;
  ElMessageBox.confirm(`确定${row.status==1?'关闭':'开启'}吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await updateSceneInfo({gid:row.gid,status:row.status==1?0:1});
    ElMessage({
      type: "success",
      message: `${row.status==1?'关闭':'开启'}成功`,
    });
    row.status=row.status==1?0:1;//启用停用
    fun_getList();
  })
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("删除后不可恢复,确定吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    // const res = await delReport(row.gid);
    // ElMessage({
    //   type: "success",
    //   message: "删除成功",
    // });
    // fun_getList();
  })
};
// 批量删除
const delIds = ref("");
const handleSelectionChange = (data) => {
  delIds.value = data.map((item) => item.gid).join(",");
};


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
