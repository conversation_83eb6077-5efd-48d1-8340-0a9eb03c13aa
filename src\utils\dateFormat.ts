// 获取时间范围的工具函数
/**
 * Converts a Date object to a formatted string representing the date and time.
 *
 * @param {Date} date - The Date object to be formatted.
 * @returns {string} - The formatted date and time string in the format "YYYY-MM-DD HH:mm:ss".
 */
export function formatTime (date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}


// 获取今天的开始和结束时间
export function getToday () {
  const now = new Date();
  const startTime = formatTime(new Date(now.getFullYear(), now.getMonth(), now.getDate()));
  const endTime = formatTime(new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999));
  return { startTime, endTime };
}

// 获取本周的开始和结束时间（周一为起点）
export function getThisWeek () {
  const now = new Date();
  const dayOfWeek = now.getDay() === 0 ? 6 : now.getDay() - 1;
  const startTime = formatTime(new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek));
  const endTime = formatTime(new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek + 6, 23, 59, 59, 999));
  return { startTime, endTime };
}

// 获取本月的开始和结束时间
export function getThisMonth () {
  const now = new Date();
  const startTime = formatTime(new Date(now.getFullYear(), now.getMonth(), 1));
  const endTime = formatTime(new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999));
  return { startTime, endTime };
}

// 获取本年的开始和结束时间
export function getThisYear () {
  const now = new Date();
  const startTime = formatTime(new Date(now.getFullYear(), 0, 1));
  const endTime = formatTime(new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999));
  return { startTime, endTime };
}

// 获取最早的时间和结束时间
export function getAllYear () {
  const now = new Date();
  const startTime = '1970-01-01 00:00:00';
  const endTime = formatTime(now);
  return { startTime, endTime };
}