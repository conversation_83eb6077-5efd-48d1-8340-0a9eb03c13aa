export class Columns {
  private _onClick: any;

  set onClick(value: any) {
    this._onClick = value;
  }

  get onClick() {
    return this._onClick;
  }

  get columns() {
    return [
      {
        type: 'selection',
        width: 60,
      },
      {
        title: '序号',
        type: 'index',
        width: 80,
      },
      {
        title: '设备编码',
        key: 'gid',
        align: 'center',
        width: 180,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'center',
        width: 180,
      },
      {
        title: '设备类型',
        key: 'deviceTypeName',
        align: 'center',
        width: 180,
      },
      {
        title: '监测项名称',
        key: 'monitorItemName',
        align: 'center',
        width: 140,
      },
      {
        title: '监测项编码',
        key: 'monitorItemCode',
        align: 'center',
        width: 140,
      },
      {
        title: '单位',
        key: 'monitorItemMark',
        align: 'center',
        width: 90,
      },
      {
        title: '设备状态',
        key: 'status',
        align: 'center',
        width: 80,
        formatter: (row:any) => {
          if(Number(row.status) == 1) {
            return '在线'
          } else {
            return '离线'
          }
        }
      },
      {
        title: '操作时间',
        key: 'createTime',
        align: 'center',
        width: 200,
      },
      {
        title: '操作',
        type: 'operations',
        onClick: this.onClick,
        buttons: [
          {
            label: '修改',
            type: 'update',
            buttonType: 'default',
          },
          {
            label: '查看',
            type: 'view',
            buttonType: 'default',
          },
          {
            label: '删除',
            type: 'del',
            buttonType: 'default',
          },
          {
            label: '上线',
            type: 'goOnline',
            buttonType: 'default',
            visible: (row:any) => {
              return Number(row.status) != 1
            }
          },
          {
            label: '离线',
            type: 'offLine',
            buttonType: 'default',
            visible: (row:any) => {
              return Number(row.status) == 1
            }
          },
          {
            label: '告警设置',
            type: 'alarmSet',
            buttonType: 'default',
          },
        ],
      },
    ];
  }
}
