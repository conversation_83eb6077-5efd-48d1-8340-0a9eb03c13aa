import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';


//获取部门筛选树
export function getDeptTree(query: any) {
  return request({
    url: baseRouter + '/system/dept/list',
    method: 'get',
    params: {...query}
  })
}

// 获取人员树
export function getPersonnelTree(query: any) {
  return request({
    url: baseRouter + '/system/user/deptUserTree',
    method: 'get',
    params: {...query},
  })
}

// 新增
export function add(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMeInspectionteam/addTMeInspectionteam',
    method: 'post',
    data: {...data}
  })
}

// 更新
export function update(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMeInspectionteam/editTMeInspectionteam',
    method: 'post',
    data: {...data}
  })
}

// 列表-获取列表
export function getList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMeInspectionteam/byPage',
    method: 'post',
    data: {...query}
  })
}



// 查询安全评估-技术状况报告详细
// export function getReportInfo(gid: any) {
//   return request({
//     url: baseRouter + '/business/deviceInspect/getInfo',
//     method: 'get',
//     params: { gid },
//   })
// }
// 删除安全评估-突发事件报告
// export function delReport(ids: any) {
//   return request({
//     url: baseRouter + '/business/deviceInspect/deleteByIds',
//     method: 'delete',
//     params: { ids },
//   })
// }

