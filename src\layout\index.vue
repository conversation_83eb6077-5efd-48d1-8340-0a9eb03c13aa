<template>
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
    <navbar @setLayout="setLayout" />
    <div class="main">
      <template v-if="!iframeFlag">
        <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
        <sidebar v-if="!sidebar.hide" class="sidebar-container" />
        <div :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }" class="main-container">
          <div :class="{ 'fixed-header': fixedHeader }" style="background: #fff;">
             <breadcrumb v-if="needTagsView" />
          </div>
          <app-main />
          <settings ref="settingRef" />
        </div>
      </template>
      <template v-else>
        <iframe class="iframe" :src="iframeUrl" frameborder="0"></iframe>
      </template>
    </div>

  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useWindowSize } from '@vueuse/core'
import Sidebar from './components/Sidebar/index.vue'
import { AppMain, Navbar, Settings, TagsView,Breadcrumb } from './components'
import defaultSettings from '@/settings'

import {setHideMenu } from '../autologin'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

const settingsStore = useSettingsStore()
const theme = computed(() => settingsStore.theme);
const sideTheme = computed(() => settingsStore.sideTheme);
const sidebar = computed(() => useAppStore().sidebar);
const device = computed(() => useAppStore().device);
const needTagsView = computed(() => settingsStore.tagsView);
const fixedHeader = computed(() => settingsStore.fixedHeader);

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === 'mobile'
}))

const permissionStore = usePermissionStore();
const iframeFlag = computed(() => permissionStore.iframeFlag);
const iframeUrl = computed(() => permissionStore.iframeUrl);

const { width, height } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design

watchEffect(() => {
  if (device.value === 'mobile' && sidebar.value.opened) {
    useAppStore().closeSideBar({ withoutAnimation: false })
  }
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice('mobile')
    useAppStore().closeSideBar({ withoutAnimation: true })
  } else {
    useAppStore().toggleDevice('desktop')
  }
})

function handleClickOutside() {
  useAppStore().closeSideBar({ withoutAnimation: false })
}

const settingRef = ref(null);
function setLayout() {
  settingRef.value.openSetting();
}
onMounted(() => {
  setHideMenu();
  const isHide = localStorage.getItem('hideMenu')
    if (isHide == '1') {
      const navbar = document.querySelector(".navbar");
      navbar.style.display = "none";
    }
})
</script>

<style lang="scss" scoped>
@import "@/assets/styles/mixin.scss";
@import "@/assets/styles/variables.module.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }

  .navbar {
    overflow: inherit;
  }

  .main {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .sidebar-container {
      position: relative !important;
    }

    .main-container {
      margin-left: 0 !important;
      width: 100%;
      display: flex;
      flex-direction: column;
      background: #CCDCEF;
      flex: 1;
    }
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}

.iframe {
  width: 100%;
  height: 100%;
}
</style>