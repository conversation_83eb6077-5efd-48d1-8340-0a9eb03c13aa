export class Columns {
  get columns() {
    return [
      // {
      //   title: "",
      //   align: "center",
      //   type: "selection",
      //   width: 60,
      // },
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "隐患名称",
        key: "maintenanceName",
        align: "center",
      },
      {
        title: "上报时间",
        key: "createTime",
        align: "center",
      },
      {
        title: "隐患描述",
        key: "descMsg",
        align: "center",
      },
      {
        title: "隐患类型",
        key: "maintenanceType",
        align: "center",
        slot: "maintenanceType",
      },
      {
        title: "隐患等级",
        key: "level",
        align: "center",
        slot: "level",
      },
      {
        title: "所属设备",
        key: "deviceName",
        align: "center",
      },
      {
        title: "上报人",
        key: "createUserName",
        align: "center",
      },
      // {
      //   title: "隐患图片",
      //   key: "yhpic",
      //   align: "center",
      // },
      {
        title: "状态",
        key: "status",
        slot: "status",
        align: "center",
      },
      // {
      //   title: "创建时间",
      //   key: "createTime",
      //   align: "center",
      // },
      {
        title: "操作",
        slot: "operations",
        align: "center",
        width: 160,
      },
    ];
  }
}
