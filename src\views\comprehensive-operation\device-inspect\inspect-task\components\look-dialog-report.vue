<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>隐患</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="" style="height: 400px; padding-bottom: 60px">
        <Table :data="tableData" :columns="columns" row-key="id">
          <template #maintenanceType="{ row }">
            <div>
              {{
                xjlxOption.find(
                  (item) => item.businessValue == row.maintenanceType
                )?.businessLabel
              }}
            </div>
          </template>
          <template #level="{ row }">
            <div>
              {{
                xjxOption.find((item) => item.businessValue == row.level)
                  ?.businessLabel
              }}
            </div>
          </template>
        </Table>
        <div class="flex flex-right border-t-ddd marg-t-20 padd-20_0">
          <el-pagination
            class="pageBox"
            v-model:current-page="filter.pageNum"
            v-model:page-size="filter.pageSize"
            :page-sizes="[10, 50, 100]"
            layout="total, sizes, prev, pager, next,jumper"
            :total="total"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, watch } from "vue";
import Table from "@/components/Table/index.vue";
import { getMaintenance } from "@/api/comprehensive-operation/device-inspect/task";
import { Columns } from "./look-table-report";
import { getDict } from "@/api/common";
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      {
      }
    },
  },
});

const xjlxOption = ref([]); //隐患类型
const xjxOption = ref([]); //隐患等级
// 获取字典
const fun_getDicts = async () => {
  const { data: xjlxOptionData } = await getDict("YHLX"); //获取隐患类型
  xjlxOption.value = xjlxOptionData.list;
  const { data: xjxOptionData } = await getDict("YHDJ"); //获取隐患等级
  xjxOption.value = xjxOptionData.list;
};
// 挂载
onMounted(async () => {
  fun_getDicts(); //获取字典
  fun_getList();
});
// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns);
// 表格数据
const total = ref(0);
const tableData = ref([]);
// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};
const fun_getList = async () => {
  let params = {
    planId: props.data?.planId,
    deviceId: props.data?.deviceId,
    remark: "隐患",
    pageNum: filter.value.pageNum,
    pageSize:filter.value.pageSize,
  };
  const { data } = await getMaintenance(params);
  tableData.value = data.list;
  total.value = data.total;
};
// 搜索条件
const filter = ref({
  pageSize: 10,
  pageNum: 1,
});
watch(
  () => props.data,
  async (val) => {
    console.log(val);
    fun_getList();
  }
);
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
