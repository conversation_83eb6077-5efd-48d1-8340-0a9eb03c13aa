<template>
  <div class="pointManage">
    <div class="formBox">
      <el-form :inline="true" :model="filter">
        <el-form-item label="设备编号:">
          <el-input v-model="filter.sbbm" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="设备名称:">
          <el-input v-model="filter.sbmc" placeholder="请输入" clearable />
        </el-form-item>
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier">
      <div class="main-btns">
        <!-- <el-button type="primary" @click="addBtn" :icon="Plus">新增</el-button> -->
        <!-- <el-button type="danger" @click="delAllBtn" :icon="Delete">批量删除</el-button> -->
      </div>
      <div class="tableBox">
        <Table :data="tableData" :columns="columns" row-key="gid" @listSelectChange="handleSelectionChange">
          <template #status="{ row }">
            <el-tag :type="row.status!==null&&statusOption.find(item => item.value == row.status)?.type">{{ row.status!==null&&statusOption.find(item => item.value == row.status)?.label }}</el-tag>
          </template>
          <!-- <template #sblxfsbm="{ row }">
            <span>{{ row.sblxfsbm!==null&&sblxfsOption.find(item => item.value == row.sblxfsbm)?.label }}</span>
          </template> -->
          <template #operations="{ row }">
            <div class="buttons">
              <!-- <div class="btn" @click="handleState(row)">{{ row.status=='1'?'停用':'启用'}}</div> -->
              <div class="btn" @click="handleLook(row)">查看</div>
              <div class="btn" @click="handleEdit(row)">编辑</div>
              <div class="btn" @click="handleDelete(row)">删除</div>
              <!-- <div class="btn" @click="handleDownload(row)">下载</div> -->
            </div>
          </template>
        </Table>
        <el-pagination class="pageBox" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增-编辑 -->
    <addDialog :dialogVisible="addVisiblty" :title="addTitle" :data="addData" @onChangeVisible="addVisiblty = false" @refreshList="fun_getList()"></addDialog>
    <!-- <component :is="addDialogComponent" :dialogVisible="addVisiblty" :title="addTitle" :data="addData" @onChangeVisible="addVisiblty = false" @refreshList="fun_getList()"></component> -->
    <!-- 查看 -->
    <lookDialog :dialogVisible="lookVisiblty" :data="lookData" @onChangeVisible="lookVisiblty = false"></lookDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { useRoute } from "vue-router";
const route = useRoute();
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
import addDialog from "./components/add-dialog.vue";
import lookDialog from "./components/look-dialog.vue";


import { getBusinessDict } from "@/api/common";
import {
  getDeviceList,
  // getDeviceInfo,
  delDeviceInfos,
} from "@/api/device-manage/index";

// 搜索条件
const filter = ref({
  sbbm: '',
  sbmc: '',
  // status: '',
  pageSize: 10,
  pageNum: 1,
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    sbbm: "",
    sbmc: "",
    // status: "",
    pageSize: 10,
    pageNum: 1,
  };
  fun_getList();
};

//启用状态
const qyztOption = ref([
  { label: "启用", value: "1" },
  { label: "停用", value: "0" },
]);
// 设备状态
const statusOption = ref([
  { label: "在用", value: "1", type:'success' },
  { label: "备用", value: "0", type:'danger' },
]);
// 类型
const sblxfsOption = ref([
  { label: "供热", value: "1" },
  { label: "供冷", value: "0" },
]);

// 获取字典
const fun_getDicts = async () => {
  // const {data} = await getBusinessDict('equipment_type')
  // sblxfsOption.value = data;
  // console.log('类型sblxfsOption:',data);
}

// 挂载
onMounted(async () => {
  fun_getDicts();//获取字典
  fun_getList();
});


// 表格项
const headerColumns = new Columns();
const columns = ref([...headerColumns.columns_selection_index, ...headerColumns.columns, ...headerColumns[`columns_${route.name.toLowerCase().split('-').join('_')}`],...headerColumns.columns_operations]);
// 表格数据
const total = ref(0);
const tableData = ref([]);
const fun_getList = async () => {
  console.log('route.query:',route.query);
  if(!route.query.type)return;
  // const { data } = await getDeviceList({...filter.value,type:route.name.toLowerCase()});
  const { data } = await getDeviceList({...filter.value,sbdm:route.query.type});
  console.log('data:',data);
  tableData.value = data.list;
  total.value = data.total;
  /* if(route.name == 'Water-cooler-unit'){
    //水冷机组
    tableData.value = [
      {id:1,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'1',zll:100,pdgl:100,cop:100,zfqjsw:100,zfqcsw:100,zfqsll:100,zfqsyq:100,zfqgzyl:100,lnqjsw:100,lnqcsw:100,lnqsll:100,lnqsyq:100,lnqgzyl:100,
        yhxx:[
          {yhsj:'2024-09-07 10:00:00',yhry:'张三',ms:'养护描述'},
          {yhsj:'2024-09-07 10:00:00',yhry:'张三',ms:'养护描述'},
        ],
        wxxx:[
          {gzdj:'故障等级',gzms:'故障描述',wxzt:'维修状态',wxry:'维修人员',wxsj:'维修时间'},
          {gzdj:'故障等级',gzms:'故障描述',wxzt:'维修状态',wxry:'维修人员',wxsj:'维修时间'},
        ]
      },
      {id:2,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'0',zll:100,pdgl:100,cop:100,zfqjsw:100,zfqcsw:100,zfqsll:100,zfqsyq:100,zfqgzyl:100,lnqjsw:100,lnqcsw:100,lnqsll:100,lnqsyq:100,lnqgzyl:100,yhxx:[],wxxx:[]},
    ]
  }
  else if(route.name == 'Water-pump'){
    //水泵
    tableData.value = [
      {id:1,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'1',gn:'功能',ll:100,yc:100,zs:100,dy:100,djgl:100,gzyl:100,
        yhxx:[],
        wxxx:[],
      },
      {id:2,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'0',gn:'功能',ll:100,yc:100,zs:100,dy:100,djgl:100,gzyl:100,yhxx:[],wxxx:[]},
    ]
  }
  else if(route.name == 'Heat-exchanger-unit'){
    //换热机组
    tableData.value = [
      {id:1,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'1',hrl:100,ycrsjswd:100,ycrscswd:100,ycrsgzyl:100,ycrsll:100,ecrsjswd:100,ecrscswd:100,ecrsgzyl:100,ecrsll:100,ecrsyc:100,ecrsdjgl:100,ecrszdszl:100,
        yhxx:[],
        wxxx:[],
      },
      {id:2,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'0',hrl:100,ycrsjswd:100,ycrscswd:100,ycrsgzyl:100,ycrsll:100,ecrsjswd:100,ecrscswd:100,ecrsgzyl:100,ecrsll:100,ecrsyc:100,ecrsdjgl:100,ecrszdszl:100,yhxx:[],wxxx:[]},
    ]
  }
  else if(route.name == 'Fixed-pressure-water-supply-unit'){
    //定压补水机组
    tableData.value = [
      {id:1,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'1',sbll:100,sbyc:100,djgl:100,rj:100,gzyl:100,
        yhxx:[],
        wxxx:[],
      },
      {id:2,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'0',sbll:100,sbyc:100,djgl:100,rj:100,gzyl:100,yhxx:[],wxxx:[]},
    ]
  }
  else if(route.name == 'Cooling-tower'){
    //冷却塔
    tableData.value = [
      {id:1,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'1',jswd:100,cswd:100,ll:100,fj:100,zz:100,yzzl:100,
        yhxx:[],
        wxxx:[],
      },
      {id:2,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'0',jswd:100,cswd:100,ll:100,fj:100,zz:100,yzzl:100,yhxx:[],wxxx:[]},
    ]
  }
  else if(route.name == 'Room-accessories'){
    //机房附件
    tableData.value = [
      {id:1,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'1',ggxh:'规格型号',
        yhxx:[],
        wxxx:[],
      },
      {id:2,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'0',ggxh:'规格型号',yhxx:[],wxxx:[]},
    ]
  }
  else if(route.name == 'Fan'){
    //风机
    tableData.value = [
      {id:1,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'1',gdfl:100,jwjy:100,dydy:100,djgl:100,sblxfs:'供冷',sblxfsbm:'0',glll:100,jsw:100,csw:100,gzyl:100,syss:100,zs:100,
        yhxx:[],
        wxxx:[],
      },
      {id:2,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'0',gdfl:100,jwjy:100,dydy:100,djgl:100,sblxfs:'供热',sblxfsbm:'1',glll:100,jsw:100,csw:100,gzyl:100,syss:100,zs:100,yhxx:[],wxxx:[]},
    ]
  }
  else if(route.name == 'Air-conditioning-unit'){
    //空调机组
    tableData.value = [
      {id:1,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'1',zfl:100,jzzll:100,jzzrl:100,szl:100,jwyy:100,zxxfll:100,zdxfll:100,ktjzgnd:100,pdgl:100,dydy:100,dwflhgl:100,zl:100,jzjggj:100,
        yhxx:[],
        wxxx:[],
      },
      {id:2,sbbm:'123456',sbmc:'设备名称',sbazwz:'安装位置',sbazsj:'2024-09-07 10:00:00',sbcj:'厂家',yhsx:'100',status:'0',zfl:100,jzzll:100,jzzrl:100,szl:100,jwyy:100,zxxfll:100,zdxfll:100,ktjzgnd:100,pdgl:100,dydy:100,dwflhgl:100,zl:100,jzjggj:100,yhxx:[],wxxx:[]},
    ]
  } */
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};


// 新增
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref([]);
const addBtn = () => {
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row) => {
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "编辑";
  addVisiblty.value = true;
};

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row) => {
  lookVisiblty.value = true;
  lookData.value = row;
  // lookData.value = {}
  // const res = await getReportInfo(row.gid)
  // lookData.value = res.data;
};
// 启用停用
const handleState = (row) => {
  ElMessageBox.confirm(`确定${row.status==1?'停用':'启用'}吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    // const res = await delReport(row.gid);
    ElMessage({
      type: "success",
      message: `${row.status==1?'停用':'启用'}成功`,
    });
    fun_getList();
  })
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("删除后不可恢复,确定吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await delDeviceInfos(row.gid);
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    fun_getList();
  })
};
// 批量删除
const delIds = ref("");
const handleSelectionChange = (data) => {
  delIds.value = data.map((item) => item.gid).join(",");
};
const delAllBtn = () => {
  if (delIds.value) {
    ElMessageBox.confirm("批量删除后不可恢复,确定吗?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        const res = await delDeviceInfos(delIds.value);
        if (res.code === 0) {
          fun_getList();
          ElMessage({
            type: "success",
            message: "删除成功",
          });
        } else {
          ElMessage({
            message: res.message,
            type: "warning",
          });
        }
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "取消删除",
        });
      });
  } else {
    ElMessage({
      message: "请选择需要删除的选项",
      type: "warning",
    });
  }
};

// 附件下载
// import { preImageUrl } from '@/utils/index'
// const handleDownload = (row) => {
//   console.log('附件下载:',preImageUrl,row);
//   if (row.fileList && row.fileList.length > 0) {
//     window.open( import.meta.env.VITE_APP_IMG_URL+preImageUrl+row.fileList[0].fileGid);
//   } else {
//     ElMessage({
//       message: "没有附件",
//       type: "warning",
//     });
//   }
// }


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
