<template>
  <div class="total-box">
    <div class="total-item" v-for="(item, index) in props?.datas" :key="index">
      <div class="total-content">
        <div class="total-nums">{{ item.value }}</div>
        <div class="total-unit">件</div>
      </div>
      <div class="total-label">{{ item.label }}</div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  datas: {
    type: Array,
    default: () => {
      return [
        {
          label: "告警总数",
          value: 0,
        },
        {
          label: "待处理",
          value: 0,
        },
        {
          label: "处理中",
          value: 0,
        },
        {
          label: "已处理",
          value: 0,
        },
      ];
    },
  },
});
</script>
<style lang="scss" scoped>
.total-box {
  width: 424px;
  height: 44px;
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  margin-bottom: 16px;
  grid-gap: 0px;
  .total-item {
    width: 105px;
    height: 44px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    &::after {
      position: absolute;
      right: 0;
      width: 1px;
      content: "";
      height: 44px;
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.4) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      border-radius: 0px 0px 0px 0px;
    }
    .total-label {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      line-height: 20px;
      text-align: center;
    }
    .total-content {
      display: flex;
      align-items: baseline;
      justify-content: center;
      .total-unit {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: rgba(199, 214, 255, 0.8);
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .total-nums {
        line-height: 24px;
        font-family: D-DIN-PRO, D-DIN-PRO;
        font-weight: bold;
        font-size: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    &:nth-child(1) {
      .total-nums {
        background: linear-gradient(180deg, #ffffff 0%, #40f9ff 100%);
        -webkit-background-clip: text;
        color: transparent;
      }
    }
    &:nth-child(2) {
      .total-nums {
        color: #ffddaf;
      }
    }
    &:nth-child(3) {
      .total-nums {
        color: #aee1ff;
      }
    }
    &:nth-child(4) {
      .total-nums {
        color: #b3ffdb;
      }
    }
  }
}
</style>
