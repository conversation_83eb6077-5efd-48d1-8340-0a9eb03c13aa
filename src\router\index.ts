import {
  createWebHistory,
  createWebHashHistory,
  createRouter,
} from "vue-router";
/* Layout */
import Layout from "@/layout/index.vue";
import ScreenLayout from "@/layout/screenLayout.vue";
import { getToken } from "@/utils/auth";
import { autoLogin, getIsHaveToken } from "@/autologin";

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "",
    component: Layout,
    redirect: "/index",
    children: [
      {
        path: "/index",
        component: () => import("@/views/index.vue"),
        name: "/index",
        // meta: { title: "首页", icon: "dashboard", affix: true },
      },
    ],
  },

  /* {
    path: "/intelligent-control",
    component: Layout,
    redirect: "/intelligent-control/main-control-desk",
    children: [
      {
        path: "/intelligent-control/main-control-desk",
        component: () => import("@/views/intelligent-control/main-control-desk/index.vue"),
        name: "/intelligent-control/main-control-desk",
        meta: { title: "主控台", icon: "dashboard", affix: true },
      },
      {
        path: "/intelligent-control/HVAC/cold-source",
        component: () => import("@/views/intelligent-control/components/index.vue"),
        name: "/intelligent-control/HVAC/cold-source",
        meta: { title: "冷源", icon: "dashboard", affix: true },
      },
      {
        path: "/intelligent-control/HVAC/hot-source",
        component: () => import("@/views/intelligent-control/components/index.vue"),
        name: "/intelligent-control/HVAC/hot-source",
        meta: { title: "热源", icon: "dashboard", affix: true },
      },
    ],
  },
  {
    path: "/comprehensive-operation",
    component: Layout,
    redirect: "/comprehensive-operation/device-inspect/inspect-plan",
    children: [
      {
        path: "/comprehensive-operation/device-inspect/inspect-plan",
        component: () => import("@/views/comprehensive-operation/device-inspect/inspect-plan/index.vue"),
        name: "/comprehensive-operation/device-inspect/inspect-plan",
        meta: { title: "巡查计划", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-operation/device-inspect/inspect-task",
        component: () => import("@/views/comprehensive-operation/device-inspect/inspect-task/index.vue"),
        name: "/comprehensive-operation/device-inspect/inspect-task",
        meta: { title: "巡查任务", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-operation/device-maint-conserve/unmaint",
        component: () => import("@/views/comprehensive-operation/device-maint-conserve/maint-manage/index.vue"),
        name: "/comprehensive-operation/device-maint-conserve/unmaint",
        meta: { title: "维修待办", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-operation/device-maint-conserve/maint-manage",
        component: () => import("@/views/comprehensive-operation/device-maint-conserve/maint-manage/index.vue"),
        name: "/comprehensive-operation/device-maint-conserve/maint-manage",
        meta: { title: "维修管理", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-operation/device-maint-conserve/conserve-manage",
        component: () => import("@/views/comprehensive-operation/device-maint-conserve/conserve-manage/index.vue"),
        name: "/comprehensive-operation/device-maint-conserve/conserve-manage",
        meta: { title: "养护管理", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-operation/knowledge-base/knowledge-base-manage",
        component: () => import("@/views/comprehensive-operation/knowledge-base/knowledge-base-manage/index.vue"),
        name: "/comprehensive-operation/knowledge-base/knowledge-base-manage",
        meta: { title: "知识库管理", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-operation/knowledge-base/knowledge-base-query",
        component: () => import("@/views/comprehensive-operation/knowledge-base/knowledge-base-query/index.vue"),
        name: "/comprehensive-operation/knowledge-base/knowledge-base-query",
        meta: { title: "知识库查询", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-operation/maint-schedule-manage/schedule-calendar",
        component: () => import("@/views/comprehensive-operation/maint-schedule-manage/schedule-calendar/index.vue"),
        name: "/comprehensive-operation/maint-schedule-manage/schedule-calendar",
        meta: { title: "排班日历", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-operation/maint-schedule-manage/classes-config",
        component: () => import("@/views/comprehensive-operation/maint-schedule-manage/classes-config/index.vue"),
        name: "/comprehensive-operation/maint-schedule-manage/classes-config",
        meta: { title: "班次配置", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-operation/maint-schedule-manage/schedule-config",
        component: () => import("@/views/comprehensive-operation/maint-schedule-manage/schedule-config/index.vue"),
        name: "/comprehensive-operation/maint-schedule-manage/schedule-config",
        meta: { title: "排班配置", icon: "dashboard", affix: true },
      },
    ],
  },
  {
    path: "/comprehensive-config",
    component: Layout,
    redirect: "/comprehensive-config/alarm-threshold-config",
    children: [
      {
        path: "/comprehensive-config/alarm-threshold-config",
        component: () => import("@/views/comprehensive-config/alarm-threshold-config/index.vue"),
        name: "/comprehensive-config/alarm-threshold-config",
        meta: { title: "报警阈值配置", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-config/inspect-team-config",
        component: () => import("@/views/comprehensive-config/inspect-team-config/index.vue"),
        name: "/comprehensive-config/inspect-team-config",
        meta: { title: "巡检小组配置", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-config/inspect-item-config",
        component: () => import("@/views/comprehensive-config/inspect-item-config/index.vue"),
        name: "/comprehensive-config/inspect-item-config",
        meta: { title: "巡检项配置", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-config/startend-time-config",
        component: () => import("@/views/comprehensive-config/startend-time-config/index.vue"),
        name: "/comprehensive-config/startend-time-config",
        meta: { title: "启停时间配置", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-config/device-protect-config",
        component: () => import("@/views/comprehensive-config/device-protect-config/index.vue"),
        name: "/comprehensive-config/device-protect-config",
        meta: { title: "设备保护配置", icon: "dashboard", affix: true },
      },
    ],
  },
  {
    path: "/comprehensive-monitor",
    component: Layout,
    redirect: "/comprehensive-monitor/alarm-monitor",
    children: [
      {
        path: "/comprehensive-monitor/alarm-monitor",
        component: () => import("@/views/comprehensive-monitor/alarm-monitor/index.vue"),
        name: "/comprehensive-monitor/alarm-monitor",
        meta: { title: "报警监测", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-monitor/alarm-record",
        component: () => import("@/views/comprehensive-monitor/alarm-record/index.vue"),
        name: "/comprehensive-monitor/alarm-record",
        meta: { title: "报警记录", icon: "dashboard", affix: true },
      },
      {
        path: "/comprehensive-monitor/device-monitor",
        component: () => import("@/views/comprehensive-monitor/device-monitor/index.vue"),
        name: "/comprehensive-monitor/device-monitor",
        meta: { title: "设备监测", icon: "dashboard", affix: true },
      },
    ],
  },
  {
    path: "/device-manage",
    component: Layout,
    redirect: "/device-manage/device-ledger/water-cooler-unit",
    children: [
      {
        path: "/device-manage/device-ledger/water-cooler-unit",
        component: () => import("@/views/device-manage/device-ledger/water-cooler-unit/index.vue"),
        name: "/device-manage/device-ledger/water-cooler-unit",
        meta: { title: "冷水机组", icon: "dashboard", affix: true },
      },
      {
        path: "/device-manage/device-ledger/water-pump",
        component: () => import("@/views/device-manage/device-ledger/water-pump/index.vue"),
        name: "/device-manage/device-ledger/water-pump",
        meta: { title: "水泵", icon: "dashboard", affix: true },
      },
      {
        path: "/device-manage/device-ledger/heat-exchanger-unit",
        component: () => import("@/views/device-manage/device-ledger/heat-exchanger-unit/index.vue"),
        name: "/device-manage/device-ledger/heat-exchanger-unit",
        meta: { title: "换热机组", icon: "dashboard", affix: true },
      },
      {
        path: "/device-manage/device-ledger/fixed-pressure-water-supply-unit",
        component: () => import("@/views/device-manage/device-ledger/fixed-pressure-water-supply-unit/index.vue"),
        name: "/device-manage/device-ledger/fixed-pressure-water-supply-unit",
        meta: { title: "定压补水机组", icon: "dashboard", affix: true },
      },
      {
        path: "/device-manage/device-ledger/cooling-tower",
        component: () => import("@/views/device-manage/device-ledger/cooling-tower/index.vue"),
        name: "/device-manage/device-ledger/cooling-tower",
        meta: { title: "冷却塔", icon: "dashboard", affix: true },
      },
      {
        path: "/device-manage/device-ledger/room-equipment",
        component: () => import("@/views/device-manage/device-ledger/room-equipment/index.vue"),
        name: "/device-manage/device-ledger/room-equipment",
        meta: { title: "机房附件", icon: "dashboard", affix: true },
      },
      {
        path: "/device-manage/device-ledger/fan",
        component: () => import("@/views/device-manage/device-ledger/fan/index.vue"),
        name: "/device-manage/device-ledger/fan",
        meta: { title: "风机", icon: "dashboard", affix: true },
      },
      {
        path: "/device-manage/device-ledger/air-conditioning-unit",
        component: () => import("@/views/device-manage/device-ledger/air-conditioning-unit/index.vue"),
        name: "/device-manage/device-ledger/air-conditioning-unit",
        meta: { title: "空调机组", icon: "dashboard", affix: true },
      },
      {
        path: "/device-manage/device-maintenance",
        component: () => import("@/views/device-manage/device-maintenance/index.vue"),
        name: "/device-manage/device-maintenance",
        meta: { title: "设备维护", icon: "dashboard", affix: true },
      },
      {
        path: "/device-manage/device-connect",
        component: () => import("@/views/device-manage/device-connect/index.vue"),
        name: "/device-manage/device-connect",
        meta: { title: "设备关联", icon: "dashboard", affix: true },
      },
    ],
  },
  {
    path: "/energy-manage",
    component: Layout,
    redirect: "/energy-manage/energysaving-strategy/energysaving-scene-manage",
    children: [
      {
        path: "/energy-manage/energysaving-strategy/energysaving-scene-manage",
        component: () => import("@/views/energy-manage/energysaving-strategy/energysaving-scene-manage/index.vue"),
        name: "/energy-manage/energysaving-strategy/energysaving-scene-manage",
        meta: { title: "节能场景管理", icon: "dashboard", affix: true },
      },
      {
        path: "/energy-manage/energysaving-strategy/energysaving-strategy-manage",
        component: () => import("@/views/energy-manage/energysaving-strategy/energysaving-strategy-manage/index.vue"),
        name: "/energy-manage/energysaving-strategy/energysaving-strategy-manage",
        meta: { title: "节能策略管理", icon: "dashboard", affix: true },
      }
    ]
  }, */

  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login.vue"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register.vue"),
    hidden: true,
  },
  {
    path: "/data-screen",
    component: () => import("@/views/data-screen/index.vue"),
    hidden: true,
  },
  {
    path: "/screen-building-floor",
    component: () => import("@/views/screen-building-floor/index.vue"),
    hidden: true,
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/views/error/404.vue"),
    hidden: true,
  },
  {
    component: () => import("@/views/error/401.vue"),
    hidden: true,
  },
  // {
  //   path: "",
  //   component: Layout,
  //   redirect: "/task-center/task-overview",
  //   children: [
  //     {
  //       path: "/task-center/task-overview",
  //       component: () => import("@/views/task-center/task-overview/index.vue"),
  //       name: "/task-center/task-overview",
  //       // meta: { title: "首页", icon: "dashboard", affix: true },
  //     },
  //   ],
  // },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  // {
  //   path: "/bridge-monitor/operation-monitor/device",
  //   component: Layout,
  //   hidden: true,
  //   permissions: ["monitor:device:detail"],
  //   children: [
  //     {
  //       path: "detail/:deviceId(\\d+)/:qlGid/:sbGid",
  //       component: () =>
  //         import(
  //           "@/views/bridge-monitor/operation-monitor/monitor-device/detail"
  //         ),
  //       name: "monitorDeviceDetail",
  //       meta: {
  //         title: "监测设备详情",
  //         activeMenu: "/bridge-monitor/operation-monitor/monitor-device",
  //       },
  //     },
  //   ],
  // },
  // {
  //   path: "/bridge-monitor/monitor-alarm-manage/warning-info",
  //   component: Layout,
  //   hidden: true,
  //   permissions: ["warning:info:detail"],
  //   children: [
  //     {
  //       path: "detail/:warningId(\\d+)/:warningName/:deviceName",
  //       component: () =>
  //         import(
  //           "@/views/bridge-monitor/monitor-alarm-manage/warning-info-statistics/detail"
  //         ),
  //       name: "warningInfoDetail",
  //       meta: {
  //         title: "报警信息详情",
  //         activeMenu:
  //           "/bridge-monitor/monitor-alarm-manage/warning-info-statistics",
  //       },
  //     },
  //   ],
  // },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

router.beforeEach((to, from, next) => {
  const token: any = getToken();
  if (getIsHaveToken()) {
    if (!token) {
      autoLogin();
    } else {
      next();
    }
  } else {
    if (token) {
      next();
    } else {
      if (to.path == "/login") {
        next();
      } else {
        next({ path: "/login" });
      }
    }
  }
});

export default router;
