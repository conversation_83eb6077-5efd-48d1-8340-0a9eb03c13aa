<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">传感器名称:</el-col>
        <el-col :span="8">{{ data.cgqmc }}</el-col>
        <el-col :span="4" class="text-right">传感器类型:</el-col>
        <el-col :span="8">{{ data.cgqlxmc }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">监测项:</el-col>
        <el-col :span="8">{{ data.jcxmc }}</el-col>
        <el-col :span="4" class="text-right">所在设备:</el-col>
        <el-col :span="8">{{ data.cgqwzmc }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">触发条件:</el-col>
        <el-col :span="6">{{ data.cftjmc }}</el-col>
        <el-col :span="2" v-if="data.cftjbm&&data.cftjbm=='3'">{{ data.cftjNum }}次</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">状态:</el-col>
        <el-col :span="8">{{ data.sfqy=='1'?'开启':'关闭' }}</el-col>
      </el-row>

      <div class="border-ddd">
        <el-row :gutter="10" class="text-center padd-5_0 marg-0 bg-color-ddd">
          <el-col :span="6">条件</el-col>
          <el-col :span="6">值</el-col>
          <el-col :span="6">偏离值</el-col>
          <el-col :span="6">程度</el-col>
          <!-- <el-col :span="4">操作</el-col> -->
        </el-row>
        <div class="over-auto-y" style="max-height: 220px;">
          <el-row :gutter="10" class="text-center padd-5_0 marg-0 border-ddd" v-for="(item,key) of data.dataList" :key="key">
            <el-col :span="6">{{ item.gjtjmc }}</el-col>
            <el-col :span="6">{{ item.gjzNum }}</el-col>
            <el-col :span="6">{{ item.gjplzNum }}</el-col>
            <el-col :span="6">{{ item.gjdjmc }}</el-col>
            <!-- <el-col :span="4"></el-col> -->
          </el-row>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      {
      }
    },
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
