export function getTimeList(num: number): Array<string> {
  const result = [];
  const now = new Date();
  for (let i = num; i > 0; i--) {
    const pastTime = new Date(now.getTime() - i * 60 * 60 * 24 * 1000); // 计算过去的时间
    const formattedTime = `${String(pastTime.getMonth() + 1).padStart(2, '0')}-${String(pastTime.getDate()).padStart(2, '0')} ${String(pastTime.getHours()).padStart(2, '0')}:${String(pastTime.getMinutes()).padStart(2, '0')}:${String(pastTime.getSeconds()).padStart(2, '0')}`;
    result.push(formattedTime);
  }
  return result;
}