<template>
  <el-dialog v-model="dialogShow" width="600" class="dialog" title="告警设置">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ "告警设置" }}</p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form label-width="112" :inline="false" ref="ruleFormRef" :model="ruleFormData" :rules="rules" status-icon>
          <el-form-item label="阈值设置:" prop="alarmData">
            <el-select v-model="ruleFormData.symbols" placeholder="请选择阈值设置">
              <el-option key="1" label="大于" value="1" />
              <el-option key="2" label="小于" value="2" />
              <el-option key="3" label="大于等于" value="3" />
              <el-option key="4" label="小于等于" value="4" />
            </el-select>
            <el-input
              v-model="ruleFormData.alarmData"
              placeholder="请输入值"
              style="width: 200px; margin-left: 10px"
            />
          </el-form-item>
          <el-form-item label="报警规则:" prop="alarmType">
            <el-select v-model="ruleFormData.alarmType" placeholder="请选择报警规则">
              <el-option key="1" label="阈值报警" value="1" />
              <el-option key="2" label="连续报警" value="2" />
              <el-option key="3" label="间断报警" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="ruleFormData.alarmType === '2' || ruleFormData.alarmType === '3'"
            label="报警次数:"
            prop="alarmTimes"
          >
            <el-input-number v-model="ruleFormData.alarmTimes" />
            <span style="margin-right: 8px">次</span>
            <template v-if="ruleFormData.alarmType === '3'">
              <el-input-number v-model="ruleFormData.alarmWeek" />
              <span style="margin-right: 8px">秒</span>
              <!-- <el-select v-model="ruleFormData.alarmField" class="cycle">
              <el-option key="1" label="秒" value="1" />
              <el-option key="2" label="分钟" value="2" />
              <el-option key="3" label="时" value="3" />
              <el-option key="4" label="天" value="4" />
            </el-select> -->
            </template>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogShow = false">取消</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { computed, defineProps, defineEmits, nextTick, reactive, ref, markRaw, watch } from 'vue';
  import { FormInstance, ElMessage, ElLoading } from 'element-plus';
  import { ResultEnum } from '@/enums/httpEnum';
  import {
    addAlaramRule,
    editAlaramRule,
    getAlaramRuleByDeviceId,
  } from '@/api/mockDeviceManager/deviceManager';

  const props = defineProps({
    isVisible: {
      type: Boolean,
      default: false,
    },
    viewFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const emits = defineEmits(['changeDialog', 'submitFormData']);
  const formData: any = computed(() => props.viewFormData);
  const ruleFormRef = ref();
  const ruleFormData = ref<any>({
    symbols: '',
    alarmData: '',
    alarmType: '',
    alarmTimes: '',
    alarmWeek: '',
  });

  const rules = markRaw({
    alarmData: [
      { required: true, message: '请输入阈值', trigger: 'blur' },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!ruleFormData.value.symbols) {
            callback(new Error('请选择阈值设置'));
          } else {
            callback();
          }
        },
      },
    ],
    alarmType: [{ required: true, message: '请输入报警规则', trigger: 'change' }],
    alarmTimes: [
      { required: true, message: '请输入报警次数', trigger: 'blur' },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (ruleFormData.value.alarmType == '3' && !ruleFormData.value.alarmWeek) {
            callback(new Error('请输入间隔秒数'));
          } else {
            callback();
          }
        },
      },
    ],
  });

  const initAlaramRule = async () => {
    const { data } = await getAlaramRuleByDeviceId({ deviceId: formData.value.gid });
    ruleFormData.value = Object.assign(ruleFormData.value, data)
    const alarmType = Number(ruleFormData.value.alarmType)
    ruleFormData.value.alarmType = `${alarmType == 0 ? '' : alarmType}`
    const symbols = Number(ruleFormData.value.symbols)
    ruleFormData.value.symbols = `${symbols == 0 ? '' : symbols}`
    ruleFormData.value.deviceGid = formData.value.gid
  };

  const dialogShow = computed({
    get: () => props.isVisible,
    set: (newValue) => {
      emits('changeDialog', { flag: newValue, mode: 'alarmSet' });
    },
  });

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    const valid: any = await formEl.validate();
    if (!valid) return;

    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(52, 101, 255, 0)',
    });
    let result: any = null;
    if (ruleFormData.value.gid) {
      result = await editAlaramRule(ruleFormData.value);
    } else {
      result = await addAlaramRule(ruleFormData.value);
    }
    if (result.code === ResultEnum.SUCCESS) {
      ElMessage.success('设备告警设置成功');
      dialogShow.value = false;
      emits('submitFormData', true);
    } else {
      ElMessage.error(result.message);
    }
    loading.close();
  };

  watch(dialogShow, (n) => {
    if (!n) {
      ruleFormRef.value?.resetFields();
      ruleFormData.value.symbols = ''
      ruleFormData.value.alarmWeek = ''
    } else {
      initAlaramRule();
    }
  });
</script>
<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}

  :deep(.el-input .el-input__wrapper) {
    width: 200px !important;
  }

  :deep(.el-select) {
    width: 200px !important;
  }
  :deep(.cycle) {
    width: 100px !important;
  }
  :deep(.el-input-number) {
    margin-right: 8px;
  }
</style>
