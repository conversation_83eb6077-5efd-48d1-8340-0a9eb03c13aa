import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';


/* 设备台账页面 */

// 设备台账-新增
// export function addClassConfig(data: any) {
//   return request({
//     url: baseRouter + apiPrefix + '/workShift/addWorkShift',
//     method: 'post',
//     data: data
//   })
// }

// 设备台账-修改
export function updateDeviceInfo(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentInfo/editEquipmentInfo',
    method: 'post',
    data: data
  })
}

// 设备台账-删除
export function delDeviceInfos(delIds: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentInfo/deleteByIds',
    method: 'delete',
    params: { ids: delIds },
  })
}

// 设备台账-列表
export function getDeviceList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentInfo/byPage',
    method: 'post',
    data: {...query}
  })
}

// 设备台账-详情
// export function getDeviceInfo(query: any) {
//   return request({
//     url: baseRouter + apiPrefix + '/equipmentInfo/getInfo',
//     method: 'get',
//     params: {...query}
//   })
// }



/* 设备维护页面 */

// 设备维护-列表
export function getDeviceMaintenanceList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentInfo/byPage',
    method: 'post',
    data: {...query}
  })
}

//设备维护详情-巡检列表
export function getDeviceInspectList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tPlanDetail/byPage',
    method: 'post',
    data: {...query}
  })
}

//设备维护详情-养护列表
export function getDeviceConserveList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintain/byPage',
    method: 'post',
    data: {...query}
  })
}

//设备维护详情-维修列表
export function getDeviceMaintList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintenance/byPage',
    method: 'post',
    data: {...query}
  })
}




/* 设备关联页面 */

// 设备关联-列表
export function getDeviceConnectList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentAssociationMain/byPage',
    method: 'post',
    data: {...query}
  })
}

// 设备关联-详情
export function getDeviceConnectInfo(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentAssociationMain/getInfo',
    method: 'get',
    params: {...query}
  })
}

// 设备关联-获取设备列表
export function getDeviceOptions(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentInfo/getSelectList',
    method: 'get',
    params: {...query}
  })
}

// 设备关联-新增
export function addDeviceConnect(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentAssociationMain/addEquipmentAssociationMain',
    method: 'post',
    data: data
  })
}

// 设备关联-编辑
export function updateDeviceConnect(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentAssociationMain/editEquipmentAssociationMain',
    method: 'post',
    data: data
  })
}

// 设备关联-删除
export function delDeviceConnect(delIds: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentAssociationMain/deleteByIds',
    method: 'delete',
    params: { ids: delIds },
  })
}



