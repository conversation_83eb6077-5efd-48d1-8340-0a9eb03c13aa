<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <div class="marg-b-10" v-if="!formData.editIds">
          <div class="title-l-blue-box marg-b-10">
            <span>报警信息</span>
          </div>
          <el-row :gutter="10" class="marg-b-10">
            <el-col :span="4" class="text-right">报警编号:</el-col>
            <el-col :span="8">{{ formData.bjbh }}</el-col>
            <el-col :span="4" class="text-right">设备名称:</el-col>
            <el-col :span="8">{{ formData.sbmc }}</el-col>
          </el-row>
          <el-row :gutter="10" class="marg-b-10">
            <el-col :span="4" class="text-right">报警类型:</el-col>
            <el-col :span="8">{{ formData.bjlxmc }}</el-col>
            <el-col :span="4" class="text-right">报警时间:</el-col>
            <el-col :span="8">{{ formData.bjsj }}</el-col>
          </el-row>
          <el-row :gutter="10" class="marg-b-10">
            <el-col :span="4" class="text-right">位置:</el-col>
            <el-col :span="8">{{ formData.wz }}</el-col>
          </el-row>
        </div>
  
        <div class="title-l-blue-box marg-b-10">
          <span>派发维修信息</span>
        </div>
        <el-form label-width="112" :inline="false" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <el-form-item label="维修人员:" prop="xjrybm">
            <el-cascader class="width-all" ref="xjryTreeRef" v-model="formData.xjrybm" :options="xjryTree" :props="xjryTreeProps" show-all-levels collapse-tags :collapse-tags-tooltip="true" clearable @change="fun_xjryChange" placeholder="请选择" />
          </el-form-item>
          <el-form-item label="意见:" prop="remark">
            <el-input type="textarea" v-model="formData.remark" placeholder="请输入" :rows="3" maxlength="500" resize="none" :show-word-limit="true" />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

// import {
//   getQlOptions,
//   addReport,
//   updateReport,
// } from "@/api/comprehensive-operation/device-inspect/index";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage } from "element-plus";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
}

// 表单数据-回显
const formData = computed(() => {
  console.log('props.data:',props.data);
  //设置初始默认状态
  // if(props.data.xjryType===undefined)props.data.xjryType = 1;

  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});


onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getxjryTree();//获取巡检人员
});

// 获取字典
// const fun_getDicts = async () => {
//   const {data:xjlxOptionData} = await getDict('XJLX');//获取巡检类型
//   xjlxOption.value = xjlxOptionData.list;
//   console.log('巡检类型xjlxOption:',xjlxOption.value);
// }

// 获取巡检人员
const xjryTreeProps = {
  label: 'name',
  value: 'gid',
  children: 'children',
  multiple: true,//是否多选
  emitPath: false,//是否返回路径
}
const xjryTree = ref([
  {
    name: '巡检人员',
    gid: '0',
    children: [
      { name: '巡检人员1', gid: '1' },
      { name: '巡检人员2', gid: '2' },
      { name: '巡检人员3', gid: '3' },
      { name: '巡检人员4', gid: '4' },
    ]
  }
]);
const fun_getxjryTree = async () => {
  // const {data} = await getQlOptions()
  // xjryTree.value = data;
  console.log('巡检人员列表xjryTree:',xjryTree.value);
}
// 巡检人员选择
const xjryTreeRef = ref();
const fun_xjryChange = (data) => {
  const checkedNodes = xjryTreeRef.value.getCheckedNodes(true);//获取选中的叶子节点
  console.log('巡检人员-节点点击:',data,checkedNodes);
  formData.value.xjrybmStr = checkedNodes.map(item => item.gid).join(',');
  formData.value.xjrymcStr = checkedNodes.map(item => item.name).join(',');
}


// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  xjrybm: [
    { required: true, message: "请选择", trigger: "blur" },
  ],

  // fileAllList: [
  //   { required: true, message: "请上传附件", trigger: "blur" },
  // ],
});


// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增") {
        const res = await addReport(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      } else {
        const res = await updateReport(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      }
    }
  });
};


// 上传附件
/* const action = import.meta.env.VITE_APP_IMG_URL + "/api/platform/system/fileInfo/uploadFile";
const headers = { Authorization: "Bearer " + getToken() };
const beforeAvatarUpload = (file) => {
  const fileExtensions = ["doc", "docx", "pdf"];
  if (formData.value.fileAllList) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传doc,docx,pdf格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("文件大小不能超过5MB");
    return false;
  }
}; */
/**文件上传成功回调 */
// const handleAvatarSuccess = (response, file) => {
//   formData.value.fileList = formData.value.fileAllList.map((e) => {return {fileGid:e.response.data.gid ?? e.gid}});
//   // formData.value.fj = formData.value.fileAllList.map((e) => e.response.data.originalName ?? e.originalName).toString();
// };

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
