import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';


/* 数据大屏 */

//运维统计-今日值班人员
export function getSchedulingPersonnelList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/personnelScheduling/getSchedulingPersonnelList',
    method: 'post',
    data: {...query}
  })
}

// 维修工单
export function getCount1(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintenance/getCount',
    method: 'get',
    data: data
  })
}

// 巡检任务
export function getCount2(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMeInspectionplan/getCount',
    method: 'get',
    data: data
  })
}


// 右下角系统实时数据
export function getCount3(data: any) {
  return request({
    url: baseRouter + '/risk/rskAlarmDetails/getCount',
    method: 'get',
    data: data
  })
}
// 右下角系统实时数据-设备报警列表
export function getDeviceAlarmList(query: any) {
  return request({
    url: baseRouter  + '/risk/rskAlarmDetails/byPage',
    method: 'post',
    data: {...query} //assembleIds(报警状态特定参数):[1]是报警监测,不传是报警记录,
  })
}
// 左上角系统实时数据
export function getEquipmentInfoScreen(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentInfo/getEquipmentInfoScreen',
    method: 'get',
    data: data
  })
}

