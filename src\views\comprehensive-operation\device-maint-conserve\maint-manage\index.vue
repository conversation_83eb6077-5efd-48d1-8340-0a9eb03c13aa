<template>
  <div class="pointManage">
    <div class="formBox">
      <el-form :inline="true" :model="filter">
        <el-form-item label="隐患名称:">
          <el-input
            v-model="filter.maintenanceName"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model="filter.status" placeholder="请选择" clearable>
            <el-option
              v-for="(item, index) in yhztOption"
              :key="index"
              :label="item.businessLabel"
              :value="item.businessValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围:">
          <el-date-picker
            v-model="filter.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="fun_dateChange"
          />
        </el-form-item>
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier">
      <div class="main-btns">
        <el-button v-if="isManage" type="primary" @click="addBtn" :icon="Plus"
          >新增</el-button
        >
        <!-- <el-button type="danger" @click="delAllBtn" :icon="Delete">批量删除</el-button> -->
      </div>
      <div class="tableBox">
        <Table
          :data="tableData"
          :columns="columns"
          row-key="id"
          @listSelectChange="handleSelectionChange"
        >
          <template #maintenanceType="{ row }">
            <div>
              {{
                xjlxOption.find(
                  (item) => item.businessValue == row.maintenanceType
                )?.businessLabel
              }}
            </div>
          </template>
          <template #level="{ row }">
            <div>
              {{
                xjxOption.find((item) => item.businessValue == row.level)
                  ?.businessLabel
              }}
            </div>
          </template>
          <template #status="{ row }">
            <div>
              {{
                yhztOption.find((item) => item.businessValue == row.status)
                  ?.businessLabel
              }}
            </div>
          </template>
          <template #operations="{ row }">
            <div class="buttons">
              <div class="btn" @click="handleLook(row)">查看</div>
              <div
                v-if="!isManage && row.status == 0"
                class="btn"
                @click="handleProcess(row)"
              >
                派发
              </div>
              <div
                v-if="!isManage && (row.status == 1||row.status == 3)"
                class="btn"
                @click="handleProcess(row)"
              >
                处置
              </div>
              <div v-if="!isManage && row.status == 2" class="btn" @click="handleProcess(row)">
                办结
              </div>
              <!-- <div class="btn" @click="handleEdit(row)">编辑</div> -->
              <!-- <div class="btn" @click="handleDelete(row)">删除</div> -->
              <!-- <div class="btn" @click="handleDownload(row)">下载</div> -->
            </div>
          </template>
        </Table>
        <el-pagination
          class="pageBox"
          v-model:current-page="filter.pageNum"
          v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]"
          layout="total, sizes, prev, pager, next,jumper"
          :total="total"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增-编辑 -->
    <addDialog
      v-if="isManage"
      :dialogVisible="addVisiblty"
      :title="addTitle"
      :data="addData"
      :xjlxOption="xjlxOption"
      :xjxOption="xjxOption"
      @onChangeVisible="addVisiblty = false"
      @refreshList="fun_getList()"
    ></addDialog>
    <!-- 查看 -->
    <lookDialog
      :dialogVisible="lookVisiblty"
      :data="lookData"
      :yhztOption="yhztOption"
      :xjlxOption="xjlxOption"
      :xjxOption="xjxOption"
      @onChangeVisible="lookVisiblty = false"
    ></lookDialog>
    <!-- 处理-办结 -->
    <handleDialog
      :dialogVisible="handleVisiblty"
      :title="handleTitle"
      :data="handleData"
      @onChangeVisible="handleVisiblty = false"
      @refreshList="fun_getList()"
    ></handleDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeMount } from "vue";
import { useRouter, useRoute, RouterLink, RouterView } from "vue-router";
const router = useRouter();
const route = useRoute();
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
import addDialog from "./components/add-dialog.vue";
import lookDialog from "./components/look-dialog.vue";
import handleDialog from "./components/handle-dialog.vue";

import { getDict } from "@/api/common";
import {
  getList,
  getInfo,
  getMyList,
} from "@/api/comprehensive-operation/device-maint-conserve/maint-manage";

// 搜索条件
const filter = ref({
  maintenanceName: "",
  status: "",
  dateRange: "",
  startTime: "",
  endTime: "",
  pageSize: 10,
  pageNum: 1,
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    maintenanceName: "",
    status: "",
    dateRange: "",
    startTime: "",
    endTime: "",
    pageSize: 10,
    pageNum: 1,
  };
  fun_getList();
};

//隐患状态
const yhztOption = ref([
  // { label: "待处理", value: "1" },
  // { label: "待结案", value: "2" },
]);
const xjlxOption = ref([]); //隐患类型
const xjxOption = ref([]); //隐患等级
// 获取字典
const fun_getDicts = async () => {
  const { data } = await getDict("WXZT");
  yhztOption.value = data.list;
  const { data: xjlxOptionData } = await getDict("YHLX"); //获取隐患类型
  xjlxOption.value = xjlxOptionData.list;
  const { data: xjxOptionData } = await getDict("YHDJ"); //获取隐患等级
  xjxOption.value = xjxOptionData.list;
};

const isManage = ref(false); //是否是维修管理
// 挂前
onBeforeMount(() => {
  isManage.value = route.path.indexOf("/maint-manage") !== -1 ? true : false; //是否是维修管理
  // console.log('挂载前',isManage.value);
});
// 挂载
onMounted(async () => {
  fun_getDicts(); //获取字典
  fun_getList();
});

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns);
// 表格数据
const total = ref(0);
const tableData = ref([]);
const fun_getList = async () => {
  let params = JSON.parse(JSON.stringify(filter.value));
  delete params.dateRange;
  if (isManage.value) {
    const { data } = await getMyList(params);
    tableData.value = data.list;
    total.value = data.total;
  } else {
    const { data } = await getList(params);
    tableData.value = data.list;
    total.value = data.total;
  }
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};

// 日期范围-切换
const fun_dateChange = (val) => {
  if (val) {
    filter.value.startTime = val[0] + " 00:00:00";
    filter.value.endTime = val[1] + " 23:59:59";
  } else {
    filter.value.startTime = "";
    filter.value.endTime = "";
  }
};

// 新增
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref([]);
const addBtn = () => {
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row) => {
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "编辑";
  addVisiblty.value = true;
};
// 处理-办结
const handleVisiblty = ref(false);
const handleTitle = ref("处理");
const handleData = ref({});
const handleProcess = async (row) => {
  handleVisiblty.value = true;
  handleData.value = row;
};

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row) => {
  const res = await getInfo({ gid: row.maintenanceId });
  lookData.value = {};
  if (res.code == 0) {
    lookVisiblty.value = true;
    lookData.value = res.data;
  }
  //lookVisiblty.value = true;
  // lookData.value = {}
  // const res = await getReportInfo(row.id)
  // lookData.value = res.data;
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("删除后不可恢复,确定吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await delReport(row.id);
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    fun_getList();
  });
};
// 批量删除
const delIds = ref("");
const handleSelectionChange = (data) => {
  delIds.value = data.map((item) => item.id).join(",");
};
const delAllBtn = () => {
  if (delIds.value) {
    ElMessageBox.confirm("批量删除后不可恢复,确定吗?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        const res = await delReport(delIds.value);
        if (res.code === 0) {
          fun_getList();
          ElMessage({
            type: "success",
            message: "删除成功",
          });
        } else {
          ElMessage({
            message: res.message,
            type: "warning",
          });
        }
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "取消删除",
        });
      });
  } else {
    ElMessage({
      message: "请选择需要删除的选项",
      type: "warning",
    });
  }
};

// 附件下载
// import { preImageUrl } from '@/utils/index'
// const handleDownload = (row) => {
//   console.log('附件下载:',preImageUrl,row);
//   if (row.fileList && row.fileList.length > 0) {
//     window.open( import.meta.env.VITE_APP_IMG_URL+preImageUrl+row.fileList[0].fileGid);
//   } else {
//     ElMessage({
//       message: "没有附件",
//       type: "warning",
//     });
//   }
// }
</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
