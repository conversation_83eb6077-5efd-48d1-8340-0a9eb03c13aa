<template>
  <div class="select-wrapper">
    <div class="select-box" @click="handleShow">
      <div class="select-text">{{ selectText }}</div>
    </div>
    <div class="select-poup-box" v-if="isShow">
      <div class="select-poup-content">
        <div
          class="select-poup-item"
          v-for="(item, index) in selectData"
          :key="index"
          @click="handleSelect(item, index)"
          :class="{ active: index === selectIndex }"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <div class="choose-box">
      <img
        class="choose-btn"
        @click="handlePrev"
        src="@/assets/data-screen/icon-arrow-left.png"
      />

      <div class="choose-content" ref="scrollRef">
        <div
          class="choose-scroll"
          :style="{ transform: `translateX(${scroll}px)` }"
        >
          <div
            class="choose-item"
            v-for="(item, index) in chooseData"
            :key="index"
            @click="handleChoose(item, index)"
            :class="{ active: index === chooseIndex }"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
      <img
        class="choose-btn"
        @click="handleNext"
        src="@/assets/data-screen/icon-arrow-right.png"
      />
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";
const emit = defineEmits(["select", "choose"]);

//楼栋选择的值
const seletValue = ref(null);
//楼栋选择的索引
const selectIndex = ref(0);
//楼栋选择的文本
const selectText = ref("A1座");
//楼层选择的值
const chooseValue = ref(null);
//楼层选择的索引
const chooseIndex = ref(2);
const isShow = ref(false);
const handleShow = () => {
  isShow.value = !isShow.value;
};
const handleSelect = (item, index) => {
  seletValue.value = item;
  selectIndex.value = index;
  selectText.value = item.label;
  isShow.value = false;
  chooseIndex.value = null;
  chooseValue.value = null;
  emit("select", item);
};
const handleChoose = (item, index) => {
  chooseValue.value = item;
  chooseIndex.value = index;
  emit("choose", item);
};
const selectData = ref([
  {label: "A1座",value:'A1'},
  // {label: "A2座",value:'A2'},
  // {label: "A3座",value:'A3'},
  // {label: "A4座",value:'A4'},
]);
const chooseData = ref([
  {label:'B2',value:-2},
  {label:'B1',value:-1},
  {label:'1F',value:1},
  {label:'2F',value:2},
  {label:'3F',value:3},
  {label:'4F',value:4},
  {label:'5F',value:5},
  {label:'6F',value:6},
  {label:'7F',value:7},
  {label:'8F',value:8},
  {label:'9F',value:9},
  {label:'10F',value:10},
  {label:'11F',value:11},
  {label:'12F',value:12},
  {label:'13F',value:13},
  {label:'14F',value:14},
  {label:'15F',value:15},
  {label:'16F',value:16},
  {label:'17F',value:17},
  {label:'18F',value:18},
]);
const scroll = ref(0);
const scrollRef = ref(null);
const handlePrev = () => {
  if (scroll.value >= 0) return;
  scroll.value += scrollRef.value.offsetWidth / 10;
};
const handleNext = () => {
  if (
    scroll.value <=
    (-scrollRef.value.offsetWidth / 10) * (chooseData.value.length - 10)
  )
    return;
  scroll.value -= scrollRef.value.offsetWidth / 10;
};
</script>
<style lang="scss" scoped>
.select-wrapper {
  height: 48px;
  display: flex;
  align-items: center;
  position: relative;

  .select-box {
    width: 160px;
    height: 48px;
    flex-shrink: 0;
    background: rgba(2, 7, 22, 0.32) rgba(255, 255, 255, 0.04);
    box-shadow: inset 4px 0px 6px 0px rgba(67, 87, 139, 0.4);
    border-radius: 4px 0px 0px 4px;
    border: 2px solid;
    border-image: linear-gradient(
        255deg,
        rgba(64, 249, 255, 0),
        rgba(64, 249, 255, 0.64)
      )
      2 2;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    &.selectd {
      background: rgba(2, 7, 22, 0.32) rgba(255, 255, 255, 0.04);
      box-shadow: inset 4px 0px 12px 0px rgba(64, 249, 255, 0.4);
      border-radius: 4px 0px 0px 4px;
      border: 2px solid #40f9ff;
    }
    .select-text {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
    }
  }
  .select-poup-box {
    width: 160px;
    background: rgba(2, 7, 22, 0.32);
    border-radius: 4px 4px 4px 4px;
    border: 2px solid;
    border-image: linear-gradient(
        255deg,
        rgba(64, 249, 255, 0),
        rgba(64, 249, 255, 0.64)
      )
      2 2;
    position: absolute;
    padding: 4px 8px;
    top: 52px;
    left: 0;
    .select-poup-content {
      .select-poup-item {
        margin: 4px 0;
        width: 144px;
        height: 32px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: rgba(199, 214, 255, 0.64);
        &:hover,
        &.active {
          width: 144px;
          height: 32px;
          background: rgba(64, 249, 255, 0.08);
          border-radius: 4px 4px 4px 4px;
          color: #40f9ff;
        }
      }
    }
  }
  .choose-box {
    flex: 1;
    flex-shrink: 0;
    overflow: hidden;
    padding: 0 12px;
    height: 48px;
    background: rgba(2, 7, 22, 0.32);
    border-radius: 0px 4px 4px 0px;
    border: 1px solid rgba(129, 164, 255, 0.32);
    display: flex;
    align-items: center;
    justify-content: space-between;
    .choose-btn {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
    .choose-content {
      flex: 1;
      overflow: hidden;
      .choose-scroll {
        display: flex;
        align-items: center;
        transition: all 0.3s ease-in-out;
        .choose-item {
          width: 72px;
          height: 16px;
          flex-shrink: 0;
          font-family: D-DIN-PRO, D-DIN-PRO;
          font-weight: 500;
          font-size: 16px;
          line-height: 16px;
          color: rgba(199, 214, 255, 0.64);
          text-align: center;
          cursor: pointer;
          &.active {
            color: #40f9ff;
          }
        }
      }
    }
  }
}
</style>
