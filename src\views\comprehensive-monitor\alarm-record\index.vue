<template>
  <div class="pointManage">
    <div class="formBox" style="height: auto;">
      <el-form :inline="true" :model="filter">
        <el-form-item label="传感器名称:" v-if="tabs_activeName == '1'">
          <el-input v-model="filter.cgqmc" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="所属设备:" v-if="tabs_activeName == '1'">
          <el-select v-model="filter.sssbbm" placeholder="请选择" clearable @change="fun_sssbChange">
            <el-option v-for="(item, index) in sssbOption" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称:" v-if="tabs_activeName == '2'">
          <el-input v-model="filter.sbmc" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="报警状态:">
          <el-select v-model="filter.bjczjddm" placeholder="请选择" clearable @change="fun_bjztChange">
            <el-option v-for="(item, index) in bjztOption" :key="index" :label="item.businessLabel" :value="item.businessValue" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="报警类型:">
          <el-select v-model="filter.bjlxbm" placeholder="请选择" clearable @change="fun_bjlxChange">
            <el-option v-for="(item, index) in bjlxOption" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="时间范围:">
          <el-date-picker v-model="filter.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="fun_dateChange" />
        </el-form-item>
      </el-form>

      <div class="btns flex-item-shrink-0">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier flex flex-col">
      <el-tabs class="flex-item-shrink-0" v-model="tabs_activeName" @tab-change="fun_tabsChange">
        <el-tab-pane label="传感器报警" name="1"></el-tab-pane>
        <el-tab-pane label="设备报警" name="2"></el-tab-pane>
      </el-tabs>

      <!-- <div class="main-btns flex-item-shrink-0">
        <el-button type="primary" @click="addBtn" :icon="Plus">新增</el-button>
        <el-button type="primary" @click="handleEditAll" :icon="Finished">批量报警处置</el-button>
        <el-button type="danger" @click="delAllBtn" :icon="Delete">批量误报忽略</el-button>
      </div> -->
      <div class="tableBox height-all-impt flex flex-col">
        <Table :data="tableData" :columns="columns" row-key="id" @listSelectChange="handleSelectionChange">
          <template #operations="{ row }">
            <div class="buttons">
              <div class="btn" @click="handleLook(row)">查看</div>
              <!-- <div class="btn" @click="handleDelete(row)">误报忽略</div>
              <div class="btn" @click="handleEdit(row)">报警处置</div> -->
              <!-- <div class="btn" @click="handleDownload(row)">下载</div> -->
            </div>
          </template>
        </Table>
        <el-pagination class="pageBox" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增-编辑 -->
    <addDialog :dialogVisible="addVisiblty" :title="addTitle" :data="addData" @onChangeVisible="addVisiblty = false"
      @refreshList="fun_getList()"></addDialog>
    <!-- 查看 -->
    <lookDialog :dialogVisible="lookVisiblty" :data="lookData" @onChangeVisible="lookVisiblty = false"></lookDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete, Finished } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
import addDialog from "./components/add-dialog.vue";
import lookDialog from "./components/look-dialog.vue";

import { getBusinessDict } from "@/api/common";
import {
  getDeviceAlarmList
} from "@/api/comprehensive-monitor/index";

// 搜索条件
const filter = ref({
  cgqmc: '',//传感器名称
  sssbbm: '',//所属设备
  sssbmc: '',//所属设备名称
  sbmc: '',//设备名称
  bjczjddm: '',//报警状态
  bjztmc: '',//报警状态名称
  bjlxbm: '',//报警类型
  bjlxmc: '',//报警类型名称
  dateRange: '',//时间范围
  startTime: '',//开始时间
  endTime: '',//结束时间
  pageSize: 10,//每页条数
  pageNum: 1,//当前页
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    cgqmc: "",//传感器名称
    sssbbm: '',//所属设备
    sssbmc: "",//所属设备名称
    sbmc: '',//设备名称
    bjczjddm: '',//报警状态
    bjztmc: '',//报警状态名称
    bjlxbm: '',//报警类型
    bjlxmc: '',//报警类型名称
    dateRange: "",//时间范围
    startTime: "",//开始时间
    endTime: "",//结束时间
    pageSize: 10,//每页条数
    pageNum: 1,//当前页
  };
  fun_getList();
};

// 获取字典
const fun_getBusinessDicts = async () => {
  // const {data} = await getBusinessDict('BJZT')
  // bjztOption.value = data;
  // console.log('报警状态bjztOption:',bjztOption.value);
}

// 获取所属设备
const sssbOption = ref([]);
const fun_getsssbOptions = async () => {
  // const {data} = await getQlOptions()
  // sssbOption.value = data;
  console.log('设备列表sssbOption:',sssbOption.value);
}
// 所属设备选择
const fun_sssbChange = (val) => {
  console.log('所属设备选择:',val);
  filter.value.sssbmc = sssbOption.value.find(item => item.value == val).label;
}

// 获取报警状态
const bjztOption = ref([]);
const fun_getbjztOptions = async () => {
  const {data} = await getBusinessDict('BJZT')
  bjztOption.value = data;
  console.log('报警状态bjztOption:',bjztOption.value);
}
// 报警状态选择
const fun_bjztChange = (val) => {
  console.log('报警状态选择:',val);
  filter.value.bjztmc = bjztOption.value.find(item => item.businessValue == val).businessLabel;
}

// 获取报警类型
const bjlxOption = ref([]);
const fun_getbjlxOptions = async () => {
  // const {data} = await getBusinessDict('BGLX')
  // bjlxOption.value = data;
}
// 报警类型选择
const fun_bjlxChange = (val) => {
  console.log('报警类型选择:',val);
  filter.value.bjlxmc = bjlxOption.value.find(item => item.value == val).label;
}


// 挂载
onMounted(async () => {
  fun_getBusinessDicts();//获取字典
  fun_getsssbOptions();//获取设备
  fun_getbjztOptions();//获取报警状态
  fun_getbjlxOptions();//获取报警类型
  fun_getList();//获取列表
});

// 标签页
const tabs_activeName = ref("2");
const fun_tabsChange = (val) => {
  console.log('标签页切换:',val);
  if (val == '2') {
    columns.value = headerColumns.columns2;
  } else {
    columns.value = headerColumns.columns1;
  }
  filter.value.pageNum = 1;
  fun_getList(); 
}

// 表格项
const headerColumns = new Columns();
const columns = tabs_activeName.value == '1' ? ref(headerColumns.columns1) : ref(headerColumns.columns2);

// 表格数据
const total = ref(0);
const tableData = ref([
  // {id:1,cgqmc:'传感器名称',sssbbm:'所属设备',sbmc:'设备名称',bjczjddm:'报警状态',bjztmc:'报警状态名称',bjlxbm:'报警类型',bjlxmc:'报警类型名称',bjjz:'报警值',cxsbh:'传感器编号',sssbbm:'所属设备',bjsj:'报警时间',wz:'位置'},
  // {id:2,cgqmc:'传感器名称',sssbbm:'所属设备',sbmc:'设备名称',bjczjddm:'报警状态',bjztmc:'报警状态名称',bjlxbm:'报警类型',bjlxmc:'报警类型名称',bjjz:'报警值',cxsbh:'传感器编号',sssbbm:'所属设备',bjsj:'报警时间',wz:'位置'},
]);
const fun_getList = async () => {
  if(tabs_activeName.value == '1'){
    // 传感器报警
    tableData.value = [];
    total.value = 0;
  }else{
    // 设备报警
    const { data } = await getDeviceAlarmList(filter.value);
    tableData.value = data.list;
    total.value = data.total;
  }
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};


// 日期范围-切换
const fun_dateChange = (val) => {
  console.log('时间范围-时间选择:',val);
  if (val) {
    filter.value.startTime = val[0] + " 00:00:00";
    filter.value.endTime = val[1] + " 23:59:59";
  } else {
    filter.value.startTime = "";
    filter.value.endTime = "";
  }
};

// 新增
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref({});
const addBtn = () => {
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row) => {
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "报警处置";
  addVisiblty.value = true;
};

// 批量报警处置
const handleEditAll = () => {
  if (delIds.value) {
    addData.value.editIds = JSON.parse(JSON.stringify(delIds.value));
    addTitle.value = "批量报警处置";
    addVisiblty.value = true;
  } else {
    ElMessage({
      message: "请选择需要报警处置的选项",
      type: "warning",
    });
  }
}

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row) => {
  lookVisiblty.value = true;
  lookData.value = row;
  lookData.value.tabs_activeName = tabs_activeName.value;
};

// 误报忽略
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定误报忽略吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await delReport(row.id);
    ElMessage({
      type: "success",
      message: `误报忽略成功`,
    });
    fun_getList();
  })
};
// 批量误报忽略
const delIds = ref("");
const handleSelectionChange = (data) => {
  delIds.value = data.map((item) => item.id).join(",");
};
const delAllBtn = () => {
  if (delIds.value) {
    ElMessageBox.confirm("批量误报忽略后不可恢复,确定吗?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        const res = await delReport(delIds.value);
        if (res.code === 0) {
          fun_getList();
          ElMessage({
            type: "success",
            message: "误报忽略成功",
          });
        } else {
          ElMessage({
            message: res.message,
            type: "warning",
          });
        }
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "取消误报忽略",
        });
      });
  } else {
    ElMessage({
      message: "请选择需要误报忽略的选项",
      type: "warning",
    });
  }
};

// 附件下载
// import { preImageUrl } from '@/utils/index'
// const handleDownload = (row) => {
//   console.log('附件下载:',preImageUrl,row);
//   if (row.fileList && row.fileList.length > 0) {
//     window.open( import.meta.env.VITE_APP_IMG_URL+preImageUrl+row.fileList[0].fileGid);
//   } else {
//     ElMessage({
//       message: "没有附件",
//       type: "warning",
//     });
//   }
// }


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
