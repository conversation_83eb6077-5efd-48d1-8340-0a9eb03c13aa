# 页面标题
VITE_APP_TITLE = 基础平台系统

# 开发环境配置
VITE_APP_ENV = 'development'

# 若依管理系统/开发环境
VITE_APP_BASE_API = '/dev-api'

VITE_APP_BASE_ROUTER = "/api/platform/"

VITE_APP_CM_BASE_API = '/cm-api' # 城管服务

# 若依管理系统/开发环境(文件上传)
VITE_APP_IMG_URL = '/dev-api'

# 视频
VITE_APP_VIDEO_URL = 'http://223.76.234.45:8000'
# 视频token
VITE_APP_VIDEO_TOKEN = '8f168b38-3f61-4b96-9755-9954d327afd3'

#视频中台
VITE_APP_HRGVP_URL = "http://111.47.103.24:15747"
VITE_APP_HRGVP_TOKEN = "4a645274069b44b4a041113a97055014"
