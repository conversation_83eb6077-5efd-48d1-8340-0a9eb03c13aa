import request from "@/utils/request";

const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;
// 登录方法
export function login(
  username: string,
  password: string,
  code: string,
  uuid: string
) {
  return request({
    url: baseRouter + "/auth/login",
    headers: {
      isToken: false,
      repeatSubmit: false,
    },
    method: "post",
    data: { username, password, code, uuid },
  });
}

// 注册方法
export function register(data: any) {
  return request({
    url: baseRouter + "/auth/register",
    headers: {
      isToken: false,
    },
    method: "post",
    data: data,
  });
}

// 刷新方法
export function refreshToken() {
  return request({
    url: baseRouter + "/auth/refresh",
    method: "post",
  });
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: baseRouter + "/system/user/getInfo",
    method: "get",
  });
}


// 根据第三方平台token获取运行监测的token
export function getTokenByThirdPlatform(data) {
  return request({
    url: baseRouter + "/auth/thirdlogin",
    method: "post",
    headers: {
      isToken: false,
    },
    data
  });
}

// 退出方法
export function logout() {
  return request({
    url: baseRouter + "/auth/logout",
    method: "delete",
  });
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: "/code",
    headers: {
      isToken: false,
    },
    method: "get",
    timeout: 20000,
  });
}
