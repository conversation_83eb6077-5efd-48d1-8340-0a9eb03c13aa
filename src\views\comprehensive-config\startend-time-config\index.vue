<template>
  <div class="pointManage">
    <div class="formBox">
      <el-form :inline="true" :model="filter">
        <el-form-item label="模式名称:">
          <el-input v-model="filter.startEndTimeName" placeholder="请输入" clearable />
        </el-form-item>
        <!-- <el-form-item label="时间范围:">
          <el-date-picker v-model="filter.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="fun_dateChange" />
        </el-form-item> -->
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier">
      <div class="main-btns">
        <el-button type="primary" @click="addBtn" :icon="Plus">新增</el-button>
        <!-- <el-button type="danger" @click="delAllBtn" :icon="Delete">批量删除</el-button> -->
      </div>
      <div class="tableBox">
        <Table :data="tableData" :columns="columns" row-key="timeModelId" @listSelectChange="handleSelectionChange">
          <template #status="{ row }">
            <div>
              <el-switch width="50" v-model="row.status" :active-value="1" :inactive-value="0" active-text="开启" inactive-text="关闭" inline-prompt :before-change="()=>{return false;}" @click="handleState(row)"  />
            </div>
          </template>
          <template #operations="{ row }">
            <div class="buttons">
              <div class="btn" @click="handleLook(row)">查看</div>
              <div class="btn" @click="handleEdit(row)">编辑</div>
              <!-- <div class="btn" @click="handleDelete(row)">删除</div> -->
              <!-- <div class="btn" @click="handleDownload(row)">下载</div> -->
            </div>
          </template>
        </Table>
        <el-pagination class="pageBox" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增-编辑-查看 -->
    <addDialog :dialogVisible="addVisiblty" :title="addTitle" :data="addData" @onChangeVisible="addVisiblty = false"
      @refreshList="fun_getList()" :isLook="isLook"></addDialog>
    <!-- 查看 -->
    <!-- <lookDialog :dialogVisible="lookVisiblty" :data="lookData" @onChangeVisible="lookVisiblty = false"></lookDialog> -->
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
import addDialog from "./components/add-dialog.vue";

import { getDict } from "@/api/common";
import {
  getStartendTimeList,
  editStartendTimeStatus,
  // update,
} from "@/api/comprehensive-config/startend-time-config/index";

// 搜索条件
const filter = ref({
  startEndTimeName: '',
  pageSize: 10,
  pageNum: 1,
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    startEndTimeName: "",
    pageSize: 10,
    pageNum: 1,
  };
  fun_getList();
};

// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getList();
});

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns);
// 表格数据
const total = ref(0);
const tableData = ref([
  // {
  //   id:1,
  //   startEndTimeName:'平日模式',
  //   type:'1',
  //   desc:'周一-周五',
  //   status:1,
  //   createTime:'2025-05-25 10:10:10',
  //   deviceData:[
  //     {
  //       id:11,
  //       deviceType:'冷机',
  //       timeList: [{
  //         startTime: '08:00:00',
  //         endTime: '18:00:00'
  //       }],
  //       firstNum: 2,
  //       firstLoadTime: 10,
  //       minNum: 1,
  //       minUnloadTime: 10,
  //       maxNum: 3,
  //       minTemp: 15,
  //       maxTemp: [{
  //         timeRange: ['08:00:00','18:00:00'],
  //         temp: 25
  //       }]
  //     }
  //   ]
  // },
  // {id:2,startEndTimeName:'周末模式',type:'2',desc:'周末',status:1,createTime:'2024-09-25 10:10:10'},
  // {id:3,startEndTimeName:'节假日模式',desc:'法定节假日',status:0,createTime:'2024-09-25 10:10:10'},
]);
const fun_getList = async () => {
  const { data } = await getStartendTimeList(filter.value);
  console.log('启停时间配置列表:',data);
  tableData.value = data.list;
  total.value = data.total;
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};

// 新增
const isLook = ref(false);
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref([]);
const addBtn = () => {
  isLook.value = false;
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row) => {
  isLook.value = false;
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "编辑";
  addVisiblty.value = true;
};

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row) => {
  isLook.value = true;
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "详情";
  addVisiblty.value = true;
  // lookData.value = {}
  // const res = await getReportInfo(row.timeModelId)
  // lookData.value = res.data;
};
// 启用停用
const handleState =async (row) => {
  if(row.status===undefined)return false;
  ElMessageBox.confirm(`确定${row.status==1?'关闭':'开启'}吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await editStartendTimeStatus({gid:row.timeModelId,status:row.status==1?0:1});
    ElMessage({
      type: "success",
      message: `${row.status==1?'关闭':'开启'}成功`,
    });
    row.status=row.status==1?0:1;//启用停用
    fun_getList();
  })
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("删除后不可恢复,确定吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await delReport(row.timeModelId);
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    fun_getList();
  })
};
// 批量删除
const delIds = ref("");
const handleSelectionChange = (data) => {
  delIds.value = data.map((item) => item.timeModelId).join(",");
};
const delAllBtn = () => {
  if (delIds.value) {
    ElMessageBox.confirm("批量删除后不可恢复,确定吗?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        const res = await delReport(delIds.value);
        if (res.code === 0) {
          fun_getList();
          ElMessage({
            type: "success",
            message: "删除成功",
          });
        } else {
          ElMessage({
            message: res.message,
            type: "warning",
          });
        }
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "取消删除",
        });
      });
  } else {
    ElMessage({
      message: "请选择需要删除的选项",
      type: "warning",
    });
  }
};


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
