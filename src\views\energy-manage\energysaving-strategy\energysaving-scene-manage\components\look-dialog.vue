<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">场景名称:</el-col>
        <el-col :span="8">{{ data.cjmc }}</el-col>
        <el-col :span="4" class="text-right">最密人数:</el-col>
        <el-col :span="8">{{ data.zmrs || '-' }} 人</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">时间设定:</el-col>
        <el-col :span="20">{{ data.startsjsd }} 至 {{ data.endsjsd }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">室外温度:</el-col>
        <el-col :span="8">{{ data.minswwd }} ℃ 至 {{ data.maxswwd }} ℃</el-col>
        <el-col :span="4" class="text-right">风速:</el-col>
        <el-col :span="8">{{ data.minfs }} m/s 至 {{ data.maxfs }} m/s</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">人流范围:</el-col>
        <el-col :span="8">{{ data.minrlfw }} 人 至 {{ data.maxrlfw }} 人</el-col>
        <el-col :span="4" class="text-right">月份:</el-col>
        <el-col :span="8">{{ data.startyf }} 月 至 {{ data.endyf }} 月</el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      {
      }
    },
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
