import request from "@/utils/request";

// 获取字典
export const getDict = (dictCode: string,type?: string) => {
  return request({
    url: !type?`/api/platform/system/common/getDict?type=${dictCode}`:`/api/platform/system/dict/data/type/${dictCode}`,//type为空时,获取业务字典,否则获取系统字典
    method: "get",
    // params: {
    //   type:dictCode
    // }
  });
};

// 获取业务字典
export const getBusinessDict = (dictCode: string) => {
  return request({
    url: `/api/platform//system/business/data/type/${dictCode}`,
    method: "get",
  });
};

// 获取行政区划
export const getRegionTree = (params: any) => {
  return request({
    url: "/api/platform/system/region/getRegionTree",
    method: "get",
    params
  });
};

// 获取处置人员
export const getDeptUser = () => {
  return request({
    url: "/api/platform/system/user/deptUserTree",
    method: "get",
  });
};

// 文件上传
export const fileUpload = (data: any) => {
  return request({
    url: "/api/platform/system/file/upload",
    method: "post",
    data
  });
};

// 获取图片
export function getIcon(gid: any) {
  return request({
    url: `/api/platform/system/fileInfo/getFile?gid=${gid}`,
    method: "get",
    responseType: 'blob'
  });
}