<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">计划名称:</el-col>
        <el-col :span="8">{{ data.jhmc }}</el-col>
        <!-- <el-col :span="4" class="text-right">计划编号:</el-col>
        <el-col :span="8">{{ data.ssbh }}</el-col> -->
        <el-col :span="4" class="text-right">巡检周期:</el-col>
        <el-col :span="8">{{ data.xjzq }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">计划时间:</el-col>
        <el-col :span="8">{{ data.jhsj }}</el-col>
        <el-col :span="4" class="text-right">巡检人员:</el-col>
        <el-col :span="8">{{ data.xjrymc }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">巡检设备:</el-col>
        <el-col :span="8">{{ data.xjsbmc }}</el-col>
        <el-col :span="4" class="text-right">启用状态:</el-col>
        <el-col :span="8">{{ data.sfqy=='1'?'开启':'关闭' }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">跳过节假日:</el-col>
        <el-col :span="8">{{ data.tgjjr=='1'?'是':'否' }}</el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      {
      }
    },
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
