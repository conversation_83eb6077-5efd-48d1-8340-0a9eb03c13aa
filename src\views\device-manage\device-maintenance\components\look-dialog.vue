<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">设备编号:</el-col>
        <el-col :span="8">{{ data.gid }}</el-col>
        <el-col :span="4" class="text-right">设备名称:</el-col>
        <el-col :span="8">{{ data.sbmc }}</el-col>
      </el-row>

      <div class="padd-10 padd-t-0">
        <el-table v-if="data.type === 'inspect'" :data="dataList" max-height="400px">
          <el-table-column prop="updateTime" label="巡检时间" align="center" />
          <el-table-column prop="termUserName" label="巡检人" align="center" />
          <el-table-column prop="status" label="完好性" align="center" >
            <template #default="scope">
              <el-tag :type="deviceIsIntact.find(item => item.value == scope.row.status)?.type">{{ deviceIsIntact.find(item => item.value == scope.row.status)?.label }}</el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="xjzp" label="照片" align="center" width="210">
            <template #default="scope">
              <div class="posit-relat marg-auto" style="width: 180px;height: 100px;">
                <div class="posit-absol z-index-1 left-0 top-0 width-all height-all flex flex-center flex-middle hover font-18 color-fff bg-color-000s3" @click="imageRef.showPreview()">预览</div>
                <el-image ref="imageRef" :src="scope.row.xjzp[0]" :preview-teleported="true" :preview-src-list="scope.row.xjzp" show-progress style="width: 180px;height: 100px;" />
              </div>
            </template>
          </el-table-column> -->
        </el-table>

        <el-table v-else-if="data.type === 'conserve'" :data="dataList" max-height="400px">
          <el-table-column prop="yhTime" label="养护时间" align="center" />
          <el-table-column prop="createUserName" label="养护人" align="center" />
          <!-- <el-table-column prop="yhcs" label="养护次数" align="center" /> -->
        </el-table>

        <el-table v-else-if="data.type === 'maint'" :data="dataList" max-height="400px">
          <el-table-column prop="level" label="故障等级" align="center">
            <template #default="scope">
              <!-- <el-tag :type="deviceFaultLevel.find(item => item.value === scope.row.level)?.type">{{ deviceFaultLevel.find(item => item.value === scope.row.level)?.label }}</el-tag> -->
              <span>{{ deviceFaultLevel.find(item => item.businessValue == scope.row.level)?.businessLabel }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="descMsg" label="故障描述" align="center" />
          <el-table-column prop="status" label="维修状态" align="center">
            <template #default="scope">
              <!-- <el-tag :type="deviceMaintenanceStatus.find(item => item.value === scope.row.status)?.type">{{ deviceMaintenanceStatus.find(item => item.value === scope.row.status)?.label }}</el-tag> -->
              <span>{{ deviceMaintenanceStatus.find(item => item.businessValue == scope.row.status)?.businessLabel }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="maintUserName" label="维修人" align="center" />
          <el-table-column prop="createTime" label="维修时间" align="center" />
          <!-- <el-table-column prop="wxzp" label="照片" align="center" /> -->
        </el-table>
        <el-pagination class="pageBox flex flex-right border-t-ddd padd-t-10" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive, watch } from "vue";

import { getDict } from "@/api/common";

import { 
  getDeviceInspectList,
  getDeviceConserveList,
  getDeviceMaintList,
} from "@/api/device-manage/index";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {},
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};

// 获取字典
const fun_getDicts = async () => {
  const {data:deviceFaultLevelData} = await getDict('YHDJ');//获取故障等级
  deviceFaultLevel.value = deviceFaultLevelData.list;
  console.log('故障等级-deviceFaultLevel:',deviceFaultLevel.value);
  const {data:deviceMaintenanceStatusData} = await getDict('WXZT');//获取维修状态
  deviceMaintenanceStatus.value = deviceMaintenanceStatusData.list;
  console.log('维修状态-deviceMaintenanceStatus:',deviceMaintenanceStatus.value);
}

//挂载
onMounted(() => {
  fun_getDicts();//获取字典
});

// 监听
watch([() => props.data.gid, () => props.data.type], ([newGid, newType], [oldGid, oldType]) => {
  // 若新值和旧值相同，则不执行请求
  if (newGid === oldGid && newType === oldType) return; 
  fun_getList();
});


// 分页条件
const filter = ref({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const dataList = ref([]);
const total = ref(0);
const fun_getList = async () => {
  if(props.data.type==='inspect'){
    const {data} = await getDeviceInspectList({deviceId: props.data.gid, ...filter.value});
    console.log('设备巡检详情:',data);
    dataList.value = data.list;
    total.value = data.total;
  }
  else if(props.data.type==='conserve'){
    const {data} = await getDeviceConserveList({deviceId: props.data.gid, ...filter.value});
    console.log('设备养护详情:',data);
    dataList.value = data.list;
    total.value = data.total;
  }
  else if(props.data.type==='maint'){
    const {data} = await getDeviceMaintList({deviceId: props.data.gid, ...filter.value});
    console.log('设备维修详情:',data);
    dataList.value = data.list;
    total.value = data.total;
  }
};

// 分页
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};


// 设备完好性
const deviceIsIntact = ref([
  {label: '完好',value: 0, type: 'success'},
  {label: '损伤',value: 1, type: 'danger'},
]);

// 图片预览
const imageRef = ref();


// 故障等级
const deviceFaultLevel = ref([
  // {label: '轻微',value: 1, type: 'success'},
  // {label: '一般',value: 2, type: 'warning'},
  // {label: '严重',value: 3, type: 'danger'},
]);

// 维修状态
const deviceMaintenanceStatus = ref([
  // {label: '待维修',value: 1, type: 'danger'},
  // {label: '维修中',value: 2, type: 'warning'},
  // {label: '已完成',value: 3, type: 'success'},
]);




</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
