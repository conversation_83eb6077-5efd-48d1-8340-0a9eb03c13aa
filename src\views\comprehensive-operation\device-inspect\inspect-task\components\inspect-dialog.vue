<template>
  <el-dialog
    v-model="isVisible"
    width="860"
    :show-close="false"
    :close-on-click-modal="false"
    class="dialog"
    @open="fun_open"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main over-auto-y" style="max-height: 60vh">
      <div class="left">
        <div class="marg-b-10" v-if="!formData.editIds">
          <div class="title-l-blue-box marg-b-10">
            <span>基本信息</span>
          </div>
          <el-row :gutter="10" class="marg-b-10">
            <el-col :span="2" class="text-right">任务名称:</el-col>
            <el-col :span="6">{{ data.inspectionPlanName }}</el-col>
            <!-- <el-col :span="2" class="text-right">任务编号:</el-col>
        <el-col :span="4">{{ data.planId }}</el-col> -->
            <el-col :span="2" class="text-right">巡检项:</el-col>
            <el-col :span="6">{{ data.inspectionItemName }}</el-col>
            <el-col :span="2" class="text-right">巡检人员:</el-col>
            <el-col :span="6">{{ data.inspectionPersonnel }}</el-col>
            <!-- <el-col :span="2" class="text-right">巡检周期:</el-col>
        <el-col :span="4">{{ data.xjzq }}</el-col>
        <el-col :span="2" class="text-right">巡检设备:</el-col>
        <el-col :span="4">{{ data.xjsbmc }}</el-col> -->
          </el-row>
          <el-row :gutter="10" class="marg-b-10">
            <el-col :span="2" class="text-right">任务时间:</el-col>
            <el-col :span="8">{{ data.startTime + "~" + data.endTime }}</el-col>
          </el-row>
        </div>

        <div class="title-l-blue-box marg-b-10">
          <span>巡检填报</span>
        </div>
        <el-form
          label-width="60"
          :inline="false"
          :model="formData"
          ref="ruleFormRef"
          status-icon
        >
          <el-form-item label="" prop="inspect_sbbms">
            <el-checkbox-group
              v-model="formData.inspect_all_sbbms"
              @change="fun_checkboxChange"
              :max="1"
            >
              <el-checkbox
                class="block marg-10_0"
                v-for="item in planList"
                :key="item.deviceId"
                :label="item.deviceName"
                :value="item.deviceId"
                :checked="item.checked"
              >
                <span class="padd-r-10">{{ item.deviceName }}</span>
                <el-select
                  v-model="item.status"
                  placeholder="请选择"
                  @change="fun_selectChange"
                  style="width: 200px"
                >
                  <el-option
                    v-for="item2 in sbztOption"
                    :key="item2.id"
                    :label="item2.label"
                    :value="item2.id"
                  />
                </el-select>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      <conserveDialog
        :dialogVisible="conserveVisible"
        :title="conserveTitle"
        :data="conserveData"
        @onChangeVisible="conserveVisible = false"
        @refreshList="fun_refreshList"
        append-to-body
      ></conserveDialog>
      <maintDialog
        :dialogVisible="maintVisible"
        :title="maintTitle"
        :data="maintData"
        @onChangeVisible="maintVisible = false"
        @refreshList="fun_refreshList"
        append-to-body
      ></maintDialog>
    </div>
    <template #footer>
      <div class="dialog-footer color-fff">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="success" class="color-fff" @click="handleConserve()"
          >养护</el-button
        >
        <el-button
          type="warning"
          class="color-fff"
          @click="handleMaint('warning')"
          >隐患上报</el-button
        >
        <el-button type="danger" @click="handleMaint('maint')">维修</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)"
          >提交</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive, watch } from "vue";
import {
  getPlanList,
  completeTMeInspectionplan,
} from "@/api/comprehensive-operation/device-inspect/task";
import conserveDialog from "@/views/comprehensive-operation/device-maint-conserve/conserve-manage/components/add-dialog.vue";
import maintDialog from "@/views/comprehensive-operation/device-maint-conserve/maint-manage/components/add-dialog.vue";

// import {
//   getQlOptions,
//   addReport,
//   updateReport,
// } from "@/api/comprehensive-operation/device-inspect/index";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage } from "element-plus";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log("打开fun_open:", props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
};
watch(
  () => props.data,
  (newVal) => {
    if (newVal) {
      fun_getPlanList();
    }
  }
);
const planList = ref([]);
const fun_getPlanList = async () => {
  const { data } = await getPlanList({ planId: props.data.planId });
  console.log("巡检计划列表:", data);
  planList.value = data.list;
};
const fun_refreshList = () => {
  console.log(2222, "这里");
  emits("refreshList");
};
// 表单数据-回显
const formData = computed(() => {
  console.log("props.data:", props.data);
  //设置初始默认状态
  // if(props.data.xjryType===undefined)props.data.xjryType = 1;

  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});

onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getsbOption(); //获取设备
});

// 获取字典
// const fun_getDicts = async () => {
//   const {data:xjlxOptionData} = await getDict('XJLX');//获取巡检类型
//   xjlxOption.value = xjlxOptionData.list;
//   console.log('巡检类型xjlxOption:',xjlxOption.value);
// }

// 获取设备
const sbOption = ref([
  { sbbm: "sb1", sbmc: "设备1", checked: 1, sbztbm: 1 },
  { sbbm: "sb2", sbmc: "设备2", sbztbm: 1 },
  { sbbm: "sb3", sbmc: "设备3", sbztbm: 1 },
]);
const fun_getsbOption = async () => {
  // const {data} = await getDeviceList(formData.value.xjxbm);
  // sbOption.value = data.list;
  // console.log('设备sbOption:',sbOption.value);
  sbOption.value.forEach((item) => {
    if (!item.sbztbm) item.sbztbm = 1; //默认设备状态完好
  });
};
// 设备选择
const fun_checkboxChange = (values) => {
  console.log("设备选择:", values);
  console.log("formData.inspect_all_sbbms:", formData.value.inspect_all_sbbms);
  // 排除回显时已选中的设备(即:排除已完成填报的设备)
  formData.value.inspect_sbbms = sbOption.value
    .filter((v) => values.includes(v.sbbm) && v.checked != true)
    .map((v) => v.sbbm);
  console.log("formData.inspect_sbbms:", formData.value.inspect_sbbms);
  ruleFormRef.value.validateField("inspect_sbbms"); //手动触发表单验证
};

// 获取设备状态
const sbztOption = ref([
  { id: 0, label: "完好" },
  { id: 1, label: "损坏" },
]);
// 设备状态选择
const fun_selectChange = (item) => {
  console.log("设备状态选择:", item);
  console.log("sbOption:", sbOption.value);
};

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  inspect_sbbms: [
    { required: true, message: "请至少勾选一项", trigger: ["change", "blur"] },
  ],

  // fileAllList: [
  //   { required: true, message: "请上传附件", trigger: "blur" },
  // ],
});

// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      console.log(planList.value);
      const res = await completeTMeInspectionplan({ list: planList.value });
      ElMessage({
        message: res.message,
        type: "success",
      });
      emits("refreshList");
      emits("onChangeVisible");
    }
  });
};

// 养护-弹窗
const conserveVisible = ref(false);
const conserveTitle = ref("养护上报");
const conserveData = ref([]);
const handleConserve = () => {
  //手动触发表单验证
  ruleFormRef.value
    .validateField("inspect_sbbms")
    .then(() => {
      if (formData.value.inspect_all_sbbms.length == 0) {
        ElMessage.warning("请选择设备");
        return;
      }
      conserveVisible.value = true;
      console.log(formData.value);
      conserveData.value = {
        inspect_sbbms: formData.value.inspect_all_sbbms[0],
        inspectTaskDialogType: "conserve",
        planId: formData.value.planId,
      };
      console.log("conserveData:", conserveData.value);
    })
    .catch(() => {
      ElMessage.warning("请选择需要填报的设备");
    });
};

// 隐患,维修-弹窗
const maintVisible = ref(false);
const maintTitle = ref("");
const maintData = ref([]);
const handleMaint = (type) => {
  //手动触发表单验证
  ruleFormRef.value
    .validateField("inspect_sbbms")
    .then(() => {
      if (formData.value.inspect_all_sbbms.length == 0) {
        ElMessage.warning("请选择设备");
        return;
      }
      if (type === "warning") {
        maintTitle.value = "隐患上报";
      } else if (type === "maint") {
        maintTitle.value = "维修上报";
      }
      maintVisible.value = true;
      maintData.value = {
        inspect_sbbms: formData.value.inspect_all_sbbms[0],
        planId: formData.value.planId,
        inspectTaskDialogType: type,
      };
      console.log("maintData:", maintData.value);
    })
    .catch(() => {
      ElMessage.warning("请选择需要填报的设备");
    });
};

// 上传附件
/* const action = import.meta.env.VITE_APP_IMG_URL + "/api/platform/system/fileInfo/uploadFile";
const headers = { Authorization: "Bearer " + getToken() };
const beforeAvatarUpload = (file) => {
  const fileExtensions = ["doc", "docx", "pdf"];
  if (formData.value.fileAllList) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传doc,docx,pdf格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("文件大小不能超过5MB");
    return false;
  }
}; */
/**文件上传成功回调 */
// const handleAvatarSuccess = (response, file) => {
//   formData.value.fileList = formData.value.fileAllList.map((e) => {return {fileGid:e.response.data.gid ?? e.gid}});
//   // formData.value.fj = formData.value.fileAllList.map((e) => e.response.data.originalName ?? e.originalName).toString();
// };
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
