<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">巡检项:</el-col>
        <el-col :span="8">{{ data.itemName }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">描述:</el-col>
        <el-col :span="20">{{ data.remark }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">设备:</el-col>
        <el-col :span="20">{{ data.deviceNames }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">状态:</el-col>
        <el-col :span="8">{{ data.status=='1'?'开启':'关闭' }}</el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      {
      }
    },
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
