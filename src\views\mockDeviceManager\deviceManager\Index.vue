<template>
  <div class="pointManage">
    <div class="formBox">
      <el-form :inline="true" :model="listQuery">
        <el-form-item label="设备名称:">
          <el-input v-model="listQuery.deviceName" placeholder="请输入" clearable @keyup.enter="onSearch" />
        </el-form-item>
        <el-form-item label="设备类型:">
          <el-cascader v-model="listQuery.deviceType" size="default" :props="{ label: 'name', value: 'gid' }" :options="typeList" placeholder="请选择" @change="onTypeChange"/>
        </el-form-item>
        <el-form-item label="监测项名称:">
          <el-input v-model="listQuery.monitorItemName" placeholder="请输入监测项名称" @keyup.enter="onSearch"/>
        </el-form-item>
        <el-form-item label="设备状态:">
          <el-select v-model="listQuery.status" :options="deviceStatusList" placeholder="请选择">
              <el-option v-for="item in deviceStatusList" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
        </el-form-item>
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="clear">重置</el-button>
      </div>
    </div>

    <div class="main-contanier">
      <div class="main-btns">
        <el-button type="primary" @click="onAdd" :icon="plusIcon">新增</el-button>
        <el-button type="danger" :disabled="multiDelEnable" @click="onMultiDelete(ids)" :icon="deleteIcon">批量删除</el-button>
      </div>
      <div class="tableBox">
        <Table :data="list" :columns="columns" @listSelectChange="onListSelectChange" />
        <el-pagination
          class="pageBox"
          background
          layout="total, sizes, prev, pager, next"
          :total="total"
          @size-change="onSizeChange"
          @current-change="onCurrentChange"
        />
      </div>
    </div>
    <Add
      ref="addModel"
      :isVisible="addModelVisible"
      @submitFormData="submitFormData"
      @changeDialog="changeDialog"
    />
    <Edit
      ref="editModel"
      :viewFormData="viewFormData"
      :isVisible="editModelVisible"
      @submitFormData="submitFormData"
      @changeDialog="changeDialog"
    />
    <Rule
      ref="ruleModel"
      :viewFormData="viewFormData"
      :isVisible="ruleVisible"
      @submitFormData="submitFormData"
      @changeDialog="changeDialog"
    />
    <Status
      ref="statusModel"
      :isVisible="statusVisible"
      :viewFormData="viewFormData"
      @submitFormData="submitFormData"
      @changeDialog="changeDialog"
    />
    <View
      ref="viewModel"
      :isVisible="viewModelVisible"
      :viewFormData="viewFormData"
      @changeDialog="changeDialog"
    />
  </div>
</template>

<script lang="ts" setup>
  import Table from '@/components/table2/Index.vue';
  import { Delete, Plus } from '@element-plus/icons-vue';
  import { markRaw, onMounted, reactive, ref } from 'vue';
  import { ResultEnum } from '@/enums/httpEnum';
  import { Columns } from './columns';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import {
    byPage,
    getMonitorItemByType,
    deleteDeviceItem,
    offlineDeviceItem,
  } from '@/api/mockDeviceManager/deviceManager';

  import Add from './Add.vue';
  import Edit from './Edit.vue';
  import View from './View.vue';
  import Rule from './Rule.vue';
  import Status from './Status.vue';

  const deleteIcon = markRaw(Delete);
  const plusIcon = markRaw(Plus);

  const addModelVisible = ref(false);
  const editModelVisible = ref(false);
  const viewModelVisible = ref(false);
  const ruleVisible = ref(false);
  const statusVisible = ref(false);

  const multiDelEnable = ref(true);

  const addModel = ref();
  const editModel = ref();
  const statusModel = ref();
  const ruleModel = ref();
  const viewModel = ref();

  const total = ref(0);
  const typeList = ref<any>([]);
  const viewFormData = ref();
  const headerColumns = new Columns();
  headerColumns.onClick = async (event: any, row: any) => {
    viewFormData.value = Object.assign({}, row);
    if (event.type === 'update') {
      editModelVisible.value = true;
    } else if (event.type === 'view') {
      viewModelVisible.value = true;
    } else if (event.type === 'goOnline') {
      statusVisible.value = true;
    } else if (event.type === 'alarmSet') {
      ruleVisible.value = true;
    } else if (event.type === 'offLine') {
      onOffLine(row.gid);
    } else if (event.type === 'del') {
      onMultiDelete(row.gid);
    }
  };
  const columns = ref(headerColumns.columns);

  const deviceStatusList = ref([
    {
      label: '在线',
      value: 1,
    },
    {
      label: '离线',
      value: 0,
    },
  ]);

  const onOffLine = (gid: any) => {
    ElMessageBox.confirm('是否确认设备离线?', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      const { code, message }: any = await offlineDeviceItem({ deviceId: gid });
      if (code === ResultEnum.SUCCESS) {
        ElMessage.success('离线设置成功');
        getListData();
      } else {
        ElMessage.error(message);
      }
    });
  };

  const onTypeChange = async (row: any) => {
    listQuery.deviceTypeCode = row[row.length - 1];
  };

  const listQuery = reactive({
    deviceName: '',
    deviceType: '',
    deviceTypeCode: '',
    monitorItemName: '',
    status: null,
    pageNum: 1,
    pageSize: 10,
  });

  const onAdd = () => {
    addModelVisible.value = true;
  };

  const list = ref([]);
  const getListData = async () => {
    const result = await byPage(listQuery);
    list.value = result.data.list;
    total.value = Number(result.data.total);
  };

  const onSearch = () => {
    getListData();
  };

  const onSizeChange = (evt: any) => {
    listQuery.pageSize = evt;
    getListData();
  };

  const onCurrentChange = (evt: any) => {
    listQuery.pageNum = evt;
    getListData();
  };

  let ids: any = null;
  const onListSelectChange = (evt: any) => {
    const data = evt.map((v: any) => v.gid);
    if (data.length > 0) {
      multiDelEnable.value = false;
    } else {
      multiDelEnable.value = true;
    }
    ids = data.join(',');
  };

  const changeDialog = (params: any) => {
    const { flag, mode } = params;
    if (mode === 'update') {
      editModelVisible.value = flag;
    } else if (mode === 'add') {
      addModelVisible.value = flag;
    } else if (mode === 'view') {
      viewModelVisible.value = flag;
    } else if (mode === 'status') {
      statusVisible.value = flag;
    } else if (mode === 'alarmSet') {
      ruleVisible.value = flag;
    }
  };

  const submitFormData = () => {
    getListData();
  };

  const onMultiDelete = (ids: any) => {
    ElMessageBox.confirm('是否确定删除? 删除后将无法恢复', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        const { code, message }: any = await deleteDeviceItem({ ids });
        if (code === ResultEnum.SUCCESS) {
          ElMessage.success('删除成功');
          getListData();
        } else {
          ElMessage.error(message);
        }
      })
      .catch(() => {
        ElMessage.info('取消删除');
      });
  };

  const clear = () => {
    listQuery.deviceName = '';
    listQuery.deviceType = '';
    listQuery.deviceTypeCode = '';
    listQuery.monitorItemName = '';
    listQuery.status = null;
    listQuery.pageNum = 1;
    listQuery.pageSize = 10;
    getListData();
  };

  onMounted(async () => {
    const { data } = await getMonitorItemByType({ type: 'SSLX,JCSBLX' });
    typeList.value = data.list;
    getListData();
  });
</script>


<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}

.el-tree {
  .el-tree-node {
    margin: 2px 0;
  }
}

</style>