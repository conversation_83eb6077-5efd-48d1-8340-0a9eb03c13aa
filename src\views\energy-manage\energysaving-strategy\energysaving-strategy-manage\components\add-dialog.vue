<template>
  <el-dialog v-model="isVisible" :show-close="false" width="85vw" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left over-auto-y" style="width: 100%;height:70vh;">
        <el-form label-width="112" :inline="true" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <div>
            <el-form-item label="选择场景:" prop="cjId">
              <el-select v-model="formData.cjId" placeholder="请选择" clearable @change="fun_sceneChange" :disabled="isLook">
                <el-option v-for="(item, index) in sceneList" :key="index" :label="item.cjmc" :value="item.gid" />
              </el-select>
            </el-form-item>
          </div>
          <div v-if="!!formData.cjId" style="padding-left: 30px;">
            <el-row style="margin-left:0;margin-right:0;margin-bottom: 20px;" :gutter="10">
              <el-col :span="3">
                室外温度：{{ currentScene.minswwd }}℃ ~ {{ currentScene.maxswwd }}℃
              </el-col>
              <el-col :span="3">
                风速：{{ currentScene.minfs }}m/s ~ {{ currentScene.maxfs }}m/s
              </el-col>
              <el-col :span="3">
                最密人数：{{ currentScene.zmrs }}人
              </el-col>
              <el-col :span="3">
                人流范围：{{ currentScene.minrlfw }} ~ {{ currentScene.maxrlfw }}
              </el-col>
              <el-col :span="3">
                月份：{{ currentScene.startyf }} ~ {{ currentScene.endyf }}
              </el-col>
              <el-col :span="9">
                时间：{{ currentScene.startsjsd }} ~ {{ currentScene.endsjsd }}
              </el-col>
            </el-row>
          </div>
          <div v-if="!!formData.cjId">
            <el-form-item label="策略名称:" prop="clmc">
              <el-input v-model="formData.clmc" :disabled="isLook"></el-input>
            </el-form-item>
          </div>
          <template v-if="!!formData.cjId">
            <el-form-item label="送风温度设定:" prop="sfwdsd">
              <el-input-number v-model="formData.sfwdsd" :controls="false" :disabled="isLook"></el-input-number>
            </el-form-item>
            <el-form-item label="回风温度设定:" prop="hfwdsd">
              <el-input-number v-model="formData.hfwdsd" :controls="false" :disabled="isLook"></el-input-number>
            </el-form-item>
            <el-form-item label="CO2浓度设定:" prop="cndsd">
              <template #label>
                <span>CO<sub>2</sub>浓度设定:</span>
              </template>
              <el-input-number v-model="formData.cndsd" :controls="false" :disabled="isLook"></el-input-number>
            </el-form-item>
            <div style="min-height: 500px;padding: 20px;width: 70%;">
              <DeviceConfig v-if="isVisible" :deviceData="formData" :isLook="isLook" />
            </div>
            <div style="height: 700px;">
              <!-- <DeviceControl v-if="isVisible" /> -->
              <VueFlowLook v-if="isVisible" :isFrom_energysavingStrategyManage="true" :from_energysavingStrategyManageData="formData.strategyEquipmentInfoList" />
            </div>
          </template>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)" v-if="!isLook">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

import {
  // getSceneList,
  getStrategyInfo,
  addStrategyInfo,
  updateStrategyInfo,
} from "@/api/energy-manage/index";

import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";
import DeviceConfig from "./device-config.vue";
import DeviceControl from "./device-control.vue";
import VueFlowLook from "@/views/intelligent-control/components/vue-flow-look.vue";

import { ElMessage, ElMessageBox } from "element-plus";
import _ from "lodash";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  isLook: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  sceneList: {
    type: Array,
    default: () => {
      return [];
    },
  },
});

// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
}

// 表单数据-回显
const formData = computed(() => {
  console.log('props.data:',props.data);
  //设置初始默认状态
  if(props.data.status===undefined)props.data.status = 1;//启用状态
  if(sceneList.value.length>0 && props.data.cjId) {
    currentScene.value = sceneList.value.find(item => item.gid === props.data.cjId)
  } else {
    currentScene.value = {}
  }
  //启停时间数组
  if(!props.data.strategyStartCloseInfoList)props.data.strategyStartCloseInfoList=strategyStartCloseInfoList.value;
  //温度上限数组
  if(!props.data.strategyCoolerTemperatureList)props.data.strategyCoolerTemperatureList=strategyCoolerTemperatureList.value;
  //工艺图设备数组
  if(!props.data.strategyEquipmentInfoList)props.data.strategyEquipmentInfoList=strategyEquipmentInfoList.value;

  return props.data;
});
const strategyStartCloseInfoList = ref([]);
const strategyCoolerTemperatureList = ref([]);
const strategyEquipmentInfoList = ref([]);
watch(()=>props.data.gid, async (newVal, oldVal) => {
  console.log('props.data.gid变化:',newVal);
  if(newVal){
    const {data} = await getStrategyInfo({gid:props.data.gid});
    console.log('getStrategyInfo:',data);
    strategyStartCloseInfoList.value=data.strategyStartCloseInfoList;//启停时间数组
    strategyCoolerTemperatureList.value=data.strategyCoolerTemperatureList;//温度上限数组
    strategyEquipmentInfoList.value=data.strategyEquipmentInfoList;//工艺图设备数组
    props.data.strategyStartCloseInfoList=data.strategyStartCloseInfoList;//启停时间数组
    props.data.strategyCoolerTemperatureList=data.strategyCoolerTemperatureList;//温度上限数组
    props.data.strategyEquipmentInfoList=data.strategyEquipmentInfoList;//工艺图设备数组
  }else{
    strategyStartCloseInfoList.value=[];
    strategyCoolerTemperatureList.value=[];
    strategyEquipmentInfoList.value=[];
  }
})

const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});

const sceneList = computed(() => {
  return props.sceneList;
})
// const sceneList = ref([
//   {
//     id: 1,
//     sceneName: "平日模式",
//     status: 1,
//     createTime: "2025-01-01 12:00:00",
//     updateTime: "2025-01-01 12:00:00",
//     maxPeople: 100,
//     timeMin: '08:00',
//     timeMax: '12:00',
//     outTempMin: 1,
//     outTempMax: 10,
//     windSpeedMin: 1,
//     windSpeedMax: 10,
//     peopleRangeMin: 1,
//     peopleRangeMax: 10,
//     monthMin: 1,
//     monthMax: 10,
//   },
//   {
//     id: 2,
//     sceneName: "周末模式",
//     status: 1,
//     createTime: "2025-01-01 12:00:00",
//     updateTime: "2025-01-01 12:00:00",
//   },
//   {
//     id: 3,
//     sceneName: "节假日模式",
//     status: 0,
//     createTime: "2025-01-01 12:00:00",
//     updateTime: "2025-01-01 12:00:00",
//   }
// ])

const currentScene = ref({})
const fun_sceneChange = (val) => {
  if (val) {
    currentScene.value = sceneList.value.find(item => item.gid === val)
  } else {
    currentScene.value = {}
  }
}

onMounted(async () => {
  // fun_getDicts();//获取字典
  // fun_getSceneList();//获取场景下拉列表
});

// 获取字典
// const fun_getDicts = async () => {
//   const {data:cgqlxOptionData} = await getDict('cgqLX');//获取传感器类型
//   cgqlxOption.value = cgqlxOptionData.list;
//   console.log('传感器类型cgqlxOption:',cgqlxOption.value);
// }

// 获取场景下拉列表
// const fun_getSceneList = async () => {
//   if(sceneList.value.length!=0)return;//如果场景列表有数据，直接返回
//   console.log('获取场景下拉列表');
//   const { data } = await getSceneList();
//   sceneList.value = data.list;
// }


// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  cjId: [{ required: true, message: '请选择场景', trigger: 'change' }],
  clmc: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
});


// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增") {
        const res = await addStrategyInfo(formData.value);
        console.log('新增:',res);
        ElMessage({
          message: res.message,
          type: "success",
        });
      } else {
        const res = await updateStrategyInfo(formData.value);
        console.log('更新:',res);
        ElMessage({
          message: res.message,
          type: "success",
        });
      }
      emits("refreshList");
      emits("onChangeVisible");
    }
  });
};

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    min-height: 700px;
    // max-height: 780px;
    overflow-y: auto;

    .left {
      width: 1620px;
      margin: 0 auto;
      .el-form--inline .el-form-item {
        margin-right: 10px;
      }
      ::v-deep(.el-form-item__label) {
        sub {
          font-size: 10px;
        }
      }
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
      ::v-deep(.el-input.is-disabled .el-input__wrapper),
      ::v-deep(.el-range-editor.is-disabled),
      ::v-deep(.el-select__wrapper.is-disabled),
      ::v-deep(.el-textarea.is-disabled .el-textarea__inner) {
        background-color: #fff;
        box-shadow: 0 0 0 1px #dcdfe6 inset;
      }
      ::v-deep(.el-range-editor.is-disabled input) {
        background-color: #fff;
      }
      .device-config {
        min-height: 500px;
        ::v-deep(.left), ::v-deep(.right) {
          min-height: 100%;
          height: unset;
        }
      }
    }
  }
}
</style>
