<template>
  <div class="list-box">
    <div class="list-item">
      <img class="item-icon" src="@/assets/data-screen/icon-set.png" />
      <div class="item-box">
        <div class="item-content">
          <div class="item-label">今日维修工单</div>
          <div class="item-count">
            <div class="item-nums">
              {{ props.datas?.orderData?.orderCount }}
            </div>
            <div class="item-unit">件</div>
          </div>
        </div>
        <div class="item-progress">
          <div class="progress-label">完成率</div>
          <div class="progress-bar">
            <div
              class="progress-inner"
              :style="{ width: `${props.datas?.orderData?.orderRate}%` }"
            ></div>
          </div>
          <div class="progress-value">
            <div class="nums">{{ props.datas?.orderData?.orderRate }}</div>
            <div class="unit">%</div>
          </div>
        </div>
      </div>
    </div>
    <div class="list-item">
      <img class="item-icon" src="@/assets/data-screen/icon-search.png" />
      <div class="item-box">
        <div class="item-content">
          <div class="item-label">今日巡检任务</div>
          <div class="item-count">
            <div class="item-nums">
              {{ props.datas?.patrolData?.patrolCount }}
            </div>
            <div class="item-unit">件</div>
          </div>
        </div>
        <div class="item-grid">
          <div class="gird-item">
            <div class="gird-label">已完成</div>
            <div class="gird-content">
              <div class="gird-nums">
                {{ props.datas?.patrolData?.patrolDoneCount }}
              </div>
              <div class="gird-unit">件</div>
            </div>
          </div>
          <div class="gird-item">
            <div class="gird-label">待巡检</div>
            <div class="gird-content">
              <div class="gird-nums">
                {{ props.datas?.patrolData?.patrolUnDoneCount }}
              </div>
              <div class="gird-unit">件</div>
            </div>
          </div>
        </div>
        <div class="item-progress">
          <div class="progress-label">完成率</div>
          <div class="progress-bar">
            <div
              class="progress-inner"
              :style="{ width: `${props.datas?.patrolData?.patrolRate}%` }"
            ></div>
          </div>
          <div class="progress-value">
            <div class="nums">{{ props.datas?.patrolData?.patrolRate }}</div>
            <div class="unit">%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  datas: {
    type: Object,
    default: () => {
      return {
        orderData: {
          orderCount: 0,
          orderRate: 0,
        },
        patrolData: {
          patrolCount: 0,
          patrolDoneCount: 0,
          patrolUnDoneCount: 0,
          patrolRate: 0,
        },
      };
    },
  },
});
</script>
<style lang="scss" scoped>
.list-box {
  .list-item {
    display: flex;
    justify-content: space-between;
    align-items: self-start;
    .item-icon {
      width: 32px;
      height: 32px;
      margin-right: 8px;
    }
    .item-box {
      flex: 1;
      .item-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        .item-label {
          line-height: 23px;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 16px;
          color: rgba(255, 255, 255, 0.8);
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .item-count {
          display: flex;
          align-items: baseline;
          .item-nums {
            line-height: 28px;
            font-family: D-DIN-PRO, D-DIN-PRO;
            font-weight: bold;
            font-size: 28px;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(180deg, #ffffff 16%, #ac7dd4 80%);
            -webkit-background-clip: text;
            color: transparent;
          }
          .item-unit {
            line-height: 20px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: rgba(199, 214, 255, 0.64);
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
      }
      .item-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        margin-bottom: 4px;
        .gird-item {
          display: flex;
          align-items: center;
          .gird-label {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-right: 16px;
          }
          .gird-content {
            display: flex;
            align-items: baseline;
            .gird-nums {
              font-family: D-DIN-PRO, D-DIN-PRO;
              font-weight: bold;
              font-size: 20px;
              text-align: left;
              font-style: normal;
              text-transform: none;
              background: linear-gradient(180deg, #ffffff 0%, #40f9ff 100%);
              -webkit-background-clip: text;
              color: transparent;
            }
            .gird-unit {
              font-family: Source Han Sans CN, Source Han Sans CN;
              font-weight: 400;
              font-size: 14px;
              color: rgba(199, 214, 255, 0.64);
              text-align: left;
              font-style: normal;
              text-transform: none;
            }
          }
          &:last-child {
            .gird-nums {
              background: linear-gradient(180deg, #ffffff 0%, #ffca81 100%);
              -webkit-background-clip: text;
              color: transparent;
            }
          }
        }
      }
      .item-progress {
        display: flex;
        align-items: center;
        justify-content: space-around;
        .progress-label {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-right: 8px;
        }
        .progress-bar {
          flex: 1;
          height: 6px;
          background: rgba(9, 20, 53, 0.24);
          box-shadow: inset 0px 0px 4px 0px rgba(255, 255, 255, 0.32);
          border-radius: 5px;
          position: relative;
          .progress-inner {
            position: absolute;
            top: 1px;
            left: 0;
            height: 3px;
            background: linear-gradient(
              90deg,
              rgba(255, 255, 255, 0) 0%,
              #40f9ff 100%
            );
            border-radius: 7px;
          }
        }
        .progress-value {
          display: flex;
          align-items: baseline;
          margin-left: 8px;
          .nums {
            font-family: D-DIN-PRO, D-DIN-PRO;
            font-weight: 600;
            font-size: 16px;
            color: #40f9ff;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .unit {
            margin-left: 2px;
            font-family: D-DIN-PRO, D-DIN-PRO;
            font-weight: 400;
            font-size: 14px;
            color: rgba(199, 214, 255, 0.64);
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
      }
    }
    &:first-child {
      margin-bottom: 12px;
      .item-box .item-content .item-nums {
        background: linear-gradient(180deg, #ffffff 16%, #5a85f4 80%);
        -webkit-background-clip: text;
        color: transparent;
      }
    }
  }
}
</style>
