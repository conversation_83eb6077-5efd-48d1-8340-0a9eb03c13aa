<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form label-width="112" :inline="true" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <el-form-item label="选择班次:" prop="relationShiftId">
            <el-select v-model="formData.relationShiftId" placeholder="请选择" clearable @change="fun_classesChange">
              <el-option v-for="(item, index) in classesOption" :key="index" :label="item.workShiftName" :value="item.workShiftId" />
            </el-select>
          </el-form-item>
          <el-form-item label="选择人员:" prop="personnelCode">
            <el-cascader class="width-all" ref="personnelTreeRef" v-model="formData.personnelCode" :options="personnelTree" :props="personnelTreeProps" show-all-levels collapse-tags :collapse-tags-tooltip="true" clearable @change="fun_personnelChange" placeholder="请选择" />
          </el-form-item>
          <el-form-item label="日期范围:" prop="dateRange">
            <el-date-picker v-model="formData.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="fun_dateChange" />
          </el-form-item>
          <el-form-item label="倒班规则:" prop="isHolidayStatus">
            <el-select v-model="formData.isHolidayStatus" placeholder="请选择" clearable @change="fun_shiftRulesChange">
              <el-option v-for="(item, index) in shiftRulesOption" :key="index" :label="item.businessLabel" :value="item.businessValue" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";
import { ElMessage } from "element-plus";

import { getDict } from "@/api/common";
import {
  getClassConfigSelectOptions,
  addScheduleConfig,
  updateScheduleConfig,
} from "@/api/comprehensive-operation/maint-schedule-manage/index";
import {
  getPersonnelTree,
} from "@/api/comprehensive-operation/device-inspect/index";


import { getToken } from "@/utils/auth";


const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
}

// 表单数据-回显
const formData = computed(() => {
  console.log('props.data:',props.data);
  //设置初始默认状态
  if(props.data.personnels&&props.data.personnels.length>0)props.data.personnelCode = props.data.personnels.map(item => item.personGid);
  if(props.data.workStartTime)props.data.dateRange = [props.data.workStartTime,props.data.workEndTime];
  // if(props.data.triggerStatus===undefined)props.data.triggerStatus = 1;

  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});


onMounted(async () => {
  fun_getDicts();//获取字典
  fun_getClassesOptions();//获取班次
  fun_getpersonnelTree();//获取巡检人员
});

// 获取字典
const fun_getDicts = async () => {
  const {data:shiftRulesOptionData} = await getDict('DBGZ');//倒班规则
  shiftRulesOption.value = shiftRulesOptionData.list;
  console.log('倒班规则shiftRulesOption:',shiftRulesOption.value);
}

// 倒班规则
const shiftRulesOption = ref([
  // { businessLabel: "一天一倒", businessValue: 1 },
  // { businessLabel: "三天一倒", businessValue: 2 },
  // { businessLabel: "跳过节假日", businessValue: 3 },
]);
// 倒班规则选择
const fun_shiftRulesChange = (val) => {
  console.log('倒班规则选择:',val);
  // formData.value.shiftRulesName = shiftRulesOption.value.find(item => item.businessValue == val).businessLabel;
}

// 日期范围
const fun_dateChange = (val) => {
  console.log('日期范围选择:',val);
  if(val){
    formData.value.workStartTime = val[0] + ' 00:00:00';
    formData.value.workEndTime = val[1] + ' 23:59:59';
  }else{
    formData.value.workStartTime = '';
    formData.value.workEndTime = '';
  }
}

// 获取班次
const classesOption = ref([]);
const fun_getClassesOptions = async () => {
  const {data} = await getClassConfigSelectOptions()
  classesOption.value = data.list;
  console.log('班次列表classesOption:',classesOption.value);
}
// 班次选择
const fun_classesChange = (val) => {
  console.log('班次选择:',val);
  formData.value.relationShiftName = classesOption.value.find(item => item.workShiftId == val).workShiftName;
}

// 获取巡检人员
const personnelTreeProps = {
  label: 'label',
  value: 'id',
  children: 'childrenList',
  multiple: true,//是否多选
  emitPath: false,//是否返回路径
}
const personnelTree = ref([
  // {
  //   label: '巡检人员',
  //   id: '0',
  //   childrenList: [
  //     { label: '巡检人员1', id: '1' },
  //     { label: '巡检人员2', id: '2' },
  //     { label: '巡检人员3', id: '3' },
  //     { label: '巡检人员4', id: '4' },
  //   ]
  // }
]);
const fun_getpersonnelTree = async () => {
  const {data} = await getPersonnelTree()
  personnelTree.value = data;
  // console.log('巡检人员列表personnelTree:',personnelTree.value);
}
// 巡检人员选择
const personnelTreeRef = ref();
const fun_personnelChange = (data) => {
  const checkedNodes = personnelTreeRef.value.getCheckedNodes(true);//获取选中的叶子节点
  // console.log('巡检人员-节点点击:',data,checkedNodes);
  // formData.value.userRelationId = checkedNodes.map(item => item.value).join(',');
  // formData.value.userRelationName = checkedNodes.map(item => item.label).join(',');
  formData.value.personnels = checkedNodes.map(item => ({personGid:item.value, personName:item.label}));
}


// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  relationShiftId: [
    { required: true, message: "请选择", trigger: ["blur","change"] },
  ],
  personnelCode: [
    { required: true, message: "请选择", trigger: ["blur","change"] },
  ],
  dateRange: [
    { required: true, message: "请选择", trigger: ["blur","change"] },
  ],
  isHolidayStatus: [
    { required: true, message: "请选择", trigger: ["blur","change"] },
  ],
});


// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增") {
        const res = await addScheduleConfig(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
      } else {
        const res = await updateScheduleConfig(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
      }
      emits("refreshList");
      emits("onChangeVisible");
    }
  });
};



</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
