<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form label-width="112" :inline="true" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <el-form-item label="传感器名称:" prop="cgqmc">
            <el-input v-model="formData.cgqmc" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="传感器类型:" prop="cgqlxbm">
            <el-select v-model="formData.cgqlxbm" placeholder="请选择" clearable @change="fun_cgqlxChange">
              <el-option v-for="(item, index) in cgqlxOption" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="监测项:" prop="jcxbm">
            <el-select v-model="formData.jcxbm" placeholder="请选择" clearable @change="fun_jcxChange">
              <el-option v-for="(item, index) in jcxOption" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="所在设备:" prop="cgqwzbm">
            <el-select v-model="formData.cgqwzbm" placeholder="请选择" clearable multiple collapse-tags collapse-tags-tooltip @change="fun_cgqwzChange">
              <el-option v-for="(item, index) in cgqwzOption" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="触发条件:" prop="cftjbm">
            <div class="flex">
              <el-select class="flex-item-shrink-0 marg-r-10" style="width:255px;" v-model="formData.cftjbm" placeholder="请选择" clearable @change="fun_cftjChange">
                <el-option v-for="(item, index) in cftjOption" :key="index" :label="item.label" :value="item.value" />
              </el-select>
              <el-input-number v-if="formData.cftjbm&&formData.cftjbm=='3'" style="width:150px;" v-model="formData.cftjNum" :min="1" :max="99" :precision="1" :step-strictly="true" />
            </div>
          </el-form-item>
          <div>
            <el-form-item label="启用状态:" prop="sfqy">
              <el-switch width="50" v-model="formData.sfqy" :active-value="1" :inactive-value="0" active-text="启用" inactive-text="停用" inline-prompt />
            </el-form-item>
          </div>

          <div class="border-ddd">
            <el-row :gutter="10" class="text-center padd-5_0 marg-0 bg-color-ddd">
              <el-col :span="5">条件</el-col>
              <el-col :span="5">值</el-col>
              <el-col :span="5">偏离值</el-col>
              <el-col :span="5">程度</el-col>
              <el-col :span="4">操作</el-col>
            </el-row>
            <div class="over-auto-y" style="max-height: 220px;">
              <el-row :gutter="10" class="text-center padd-5_0 marg-0 border-ddd" v-for="(item,key) of formData.dataList" :key="key">
                <el-col :span="5">
                  <el-select v-model="item.gjtjbm" placeholder="请选择" @change="fun_gjtjChange">
                    <el-option v-for="(item, index) in gjtjOption" :key="index" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-input-number v-model="item.gjzNum" :min="-999" :max="999" :precision="1" :step-strictly="true" />
                </el-col>
                <el-col :span="5">
                  <el-input-number v-model="item.gjplzNum" :min="-999" :max="999" :precision="1" :step-strictly="true" />
                </el-col>
                <el-col :span="5">
                  <el-select v-model="item.gjdjbm" placeholder="请选择" @change="fun_gjdjChange">
                    <el-option v-for="(item, index) in gjdjOption" :key="index" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <div class="height-all flex flex-even flex-middle">
                    <el-icon class="font-28 hover-blue" @click="fun_dataListAdd(key)"><CirclePlusFilled /></el-icon>
                    <el-icon class="font-28 hover-blue" @click="fun_dataListDel(key)" v-if="formData.dataList.length>1"><RemoveFilled /></el-icon>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

// import {
//   getQlOptions,
//   addReport,
//   updateReport,
// } from "@/api/comprehensive-operation/device-inspect/index";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage, ElMessageBox } from "element-plus";
import _ from "lodash";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
}

// 表单数据-回显
const formData = computed(() => {
  console.log('props.data:',props.data);
  //设置初始默认状态
  if(props.data.cftjNum===undefined)props.data.cftjNum = 1;//触发条件数
  if(props.data.sfqy===undefined)props.data.sfqy = 1;//启用状态
  if(props.data.dataList===undefined)props.data.dataList = [_.clone(dataListObj)];

  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});


onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getcgqwzOptions();//获取位置
});

// 获取字典
const fun_getDicts = async () => {
  const {data:cgqlxOptionData} = await getDict('cgqLX');//获取传感器类型
  cgqlxOption.value = cgqlxOptionData.list;
  console.log('传感器类型cgqlxOption:',cgqlxOption.value);
  const {data:jcxOptionData} = await getDict('jcX');//获取监测项
  jcxOption.value = jcxOptionData.list;
  console.log('监测项jcxOption:',jcxOption.value);
  const {data:cftjOptionData} = await getDict('cftj');//获取触发条件
  cftjOption.value = cftjOptionData.list;
  console.log('触发条件cftjOption:',cftjOption.value);
  const {data:gjtjOptionData} = await getDict('gjtj');//获取告警条件
  gjtjOption.value = gjtjOptionData.list;
  console.log('告警条件gjtjOption:',gjtjOption.value);
  const {data:gjdjOptionData} = await getDict('gjdj');//获取告警等级
  gjdjOption.value = gjdjOptionData.list;
  console.log('告警等级gjdjOption:',gjdjOption.value);
}

// 传感器类型
const cgqlxOption = ref([]);
// 传感器类型选择
const fun_cgqlxChange = (val) => {
  console.log('传感器类型选择:',val);
  // formData.value.cgqlxmc = cgqlxOption.value.find(item => item.businessValue == val).businessLabel;
}

// 监测项
const jcxOption = ref([]);
// 监测项选择
const fun_jcxChange = (val) => {
  console.log('监测项选择:',val);
  // formData.value.jcxmc = jcxOption.value.find(item => item.businessValue == val).businessLabel;
}

// 获取位置
const cgqwzOption = ref([
  { label: '位置1', value: '1' },
  { label: '位置2', value: '2' },
  { label: '位置3', value: '3' },
  { label: '位置4', value: '4' },
  { label: '位置5', value: '5' },
]);
const fun_getcgqwzOptions = async () => {
  // const {data} = await getQlOptions()
  // cgqwzOption.value = data;
  console.log('位置列表cgqwzOption:',cgqwzOption.value);
}
// 位置选择
const fun_cgqwzChange = (val) => {
  console.log('位置选择:',val);
  formData.value.cgqwzbmStr = val.join(',');
  // formData.value.cgqwzmcStr = val.map(item => cgqwzOption.value.find(item => item.value === item).label).join(',');
}

// 触发条件
const cftjOption = ref([
  { label: '触发即报警', value: '1' },
  { label: '触发3次后报警', value: '2' },
  { label: '自定义触发次数', value: '3' },
  { label: '偏离值范围内一天只报警一次', value: '4' },
]);
// 触发条件选择
const fun_cftjChange = (val) => {
  console.log('触发条件选择:',val);
  // formData.value.cftjmc = cftjOption.value.find(item => item.businessValue == val).businessLabel;
}

// 列表动态增减数据操作
//告警条件
const gjtjOption = ref([
  { label: '大于', value: '1' },
  { label: '小于', value: '2' },
  { label: '等于', value: '3' },
  { label: '大于等于', value: '4' },
  { label: '小于等于', value: '5' },
])
// 告警条件选择
const fun_gjtjChange = (val) => {
  console.log('告警条件选择:',val);
  // formData.value.gjtjmc = gjtjOption.value.find(item => item.businessValue == val).businessLabel;
}

//告警等级
const gjdjOption = ref([
  { label: '1级告警', value: '1' },
  { label: '2级告警', value: '2' },
  { label: '3级告警', value: '3' },
  { label: '4级告警', value: '4' },
  { label: '5级告警', value: '5' },
])
// 告警等级选择
const fun_gjdjChange = (val) => {
  console.log('告警等级选择:',val);
  // formData.value.gjdjmc = gjdjOption.value.find(item => item.businessValue == val).businessLabel;
}

// 列表数据单项初始值
const dataListObj = {
  gjtjbm: '1',
  gjzNum: 1,
  gjplzNum: 1,
  gjdjbm: '1',
};

// 列表数据增加一条
const fun_dataListAdd = (key)=>{
  formData.value.dataList.splice(key+1,0,_.clone(dataListObj));
}
// 列表数据删除一条
const fun_dataListDel = (key)=>{
  if(formData.value.dataList.length>1){
    ElMessageBox.confirm('确定删除该条数据吗？').then(()=>{
      formData.value.dataList.splice(key,1);
    })
  }else{
    ElMessage({
      message: '至少保留一条数据',
      type: 'warning',
    })
  }
}


// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  cgqmc: [
    { required: true, message: "请输入", trigger: "blur" },
  ],
  cgqlxbm: [
    { required: true, message: "请选择", trigger: "blur" },
  ],
  jcxbm: [
    { required: true, message: "请选择", trigger: "blur" },
  ],
  cgqwzbm: [
    { required: true, message: "请选择", trigger: "blur" },
  ],
  cftjbm: [
    { required: true, message: "请选择", trigger: "blur" },
  ],
});


// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增") {
        const res = await addReport(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      } else {
        const res = await updateReport(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      }
    }
  });
};

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
