<template>
  <el-dialog v-model="isVisible" width="1400" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="title-l-blue-box marg-b-10">
        <span>基本信息</span>
      </div>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="2" class="text-right">任务名称:</el-col>
        <el-col :span="2">{{ data.inspectionPlanName }}</el-col>
        <!-- <el-col :span="2" class="text-right">任务编号:</el-col>
        <el-col :span="4">{{ data.planId }}</el-col> -->
        <el-col :span="2" class="text-right">巡检项:</el-col>
        <el-col :span="3">{{ data.inspectionItemName}}</el-col>
        <el-col :span="2" class="text-right">巡检人员:</el-col>
        <el-col :span="2">{{ data.inspectionPersonnel }}</el-col>
        <el-col :span="2" class="text-right">任务时间:</el-col>
        <el-col :span="5">{{ data.startTime+'~' + data.endTime  }}</el-col>
        <!-- <el-col :span="2" class="text-right">巡检周期:</el-col>
        <el-col :span="4">{{ data.xjzq }}</el-col>
        <el-col :span="2" class="text-right">巡检设备:</el-col>
        <el-col :span="4">{{ data.xjsbmc }}</el-col> -->
        <el-col :span="2" class="text-right">任务状态:</el-col>
        <el-col :span="2">{{ props.rwztOption.find((item) => Number(item.businessValue) === Number(data.taskCompleteState))?.businessLabel }}</el-col>
      </el-row>
      <!-- <el-row :gutter="10" class="marg-b-10">
        <el-col :span="6"
          ><div class="grid-content ep-bg-purple" />
          <p class="label">跳过节假日:</p>
          {{ data.tgjjr=='1'?'是':'否' }}
        </el-col>
      </el-row> -->

      <div class="title-l-blue-box marg-b-10 marg-t-20">
        <span>巡检项</span>
      </div>
      <div class="" style="height: 400px;padding-bottom: 60px;">
        <Table :data="tableData" :columns="columns" row-key="id">
          <template #status="{ row }">
            <div>
              <el-tag v-if="row.status == 0" type="success">完好</el-tag>
              <el-tag v-else type="danger">损坏</el-tag>
            </div>
          </template>
          <template #maintainStatus="{ row }">
            <div>
              <el-tag v-if="row.maintainStatus==1" type="success" class="color-blue hover" @click="fun_conserveLook(row)">是</el-tag>
              <el-tag v-else type="danger">否</el-tag>
            </div>
          </template>
          <template #dealStatus="{ row }">
            <div>
              <el-tag v-if="row.dealStatus==1" type="success" class="color-blue hover" @click="fun_maintLook(row)">是</el-tag>
              <el-tag v-else type="danger">否</el-tag>
            </div>
          </template>
          <template #dangerStatus="{ row }">
            <div v-if="row.dangerStatus">
              <span class="color-blue hover" @click="fun_reportLook(row)">{{ row.dangerStatus }}</span>
            </div>
            <div v-else>
              <span>0</span>
            </div>
          </template>
        </Table>
        <div class="flex flex-right border-t-ddd marg-t-20 padd-20_0">
          <el-pagination class="pageBox" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
            :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
            @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </div>
    </div>

    <!-- 查看弹窗-是否养护 -->
    <lookDialogConserve :dialogVisible="lookVisiblty_conserve" :data="lookData_conserve" @onChangeVisible="lookVisiblty_conserve = false"></lookDialogConserve>
    <!-- 查看弹窗-是否维修 -->
    <lookDialogMaint :dialogVisible="lookVisiblty_maint" :data="lookData_maint" @onChangeVisible="lookVisiblty_maint = false"></lookDialogMaint>
    <!-- 查看弹窗-隐患上报 -->
    <lookDialogReport :dialogVisible="lookVisiblty_report" :data="lookData_report" @onChangeVisible="lookVisiblty_report = false"></lookDialogReport>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, watch } from "vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./look-table";
import {
  getPlanList
} from "@/api/comprehensive-operation/device-inspect/task";
import lookDialogConserve from "./look-dialog-conserve.vue";
import lookDialogMaint from "./look-dialog-maint.vue";
import lookDialogReport from "./look-dialog-report.vue";


const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    // default: () => {{}},
  },
  rwztOption: {
    type: Array,
    default: () => [],
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};

watch(() => props.data, (newVal) => {
  if (newVal.planId) {
    fun_getList();
  }
},{
  deep:true,
  immediate:true,
});


// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getList();
});

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns);
// 表格数据
const total = ref(0);
const tableData = ref([
]);
const fun_getList = async () => {
 const { data } = await getPlanList({planId:props.data.planId});
  console.log("巡检计划列表:",data);
  tableData.value = data.list;
  total.value = data.total;
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};

// 搜索条件
const filter = ref({
  pageSize: 10,
  pageNum: 1,
});

//任务状态
const rwztOption = ref([
  { label: "未开始", value: "1" },
  { label: "进行中", value: "2" },
  { label: "已超时", value: "3" },
  { label: "已完成", value: "4" },
]);

// 查看弹窗-是否养护
const lookVisiblty_conserve = ref(false);
const lookData_conserve = ref({});
function fun_conserveLook(row){
  lookVisiblty_conserve.value = true;
  lookData_conserve.value = row;
}
// 查看弹窗-是否维修
const lookVisiblty_maint = ref(false);
const lookData_maint = ref({});
function fun_maintLook(row){
  lookVisiblty_maint.value = true;
  lookData_maint.value = row;
}
//  查看弹窗-隐患上报
const lookVisiblty_report = ref(false);
const lookData_report = ref({});
function fun_reportLook(row){
  lookVisiblty_report.value = true;
  lookData_report.value = row;
}







</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
