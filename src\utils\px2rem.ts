let isTransfer = true

// // 设置是否转换
// export function setTransferStatus(flag) {
//     isTransfer = flag
// }

export function px2rem(px:any) {
    if (!isTransfer) return px
    if (/%/gi.test(px) || /rem/gi.test(px)) {
        // 有百分号%，特殊处理，表述pc是一个有百分号的数，比如：90%
        return px
    } else {
        return parseFloat(px) / 192 + 'rem' // 这里的37.5，和rootValue值对应
    }
}

export function getTransferPx(px:any) {
    if (!isTransfer) return px
    if (/%/gi.test(px) || /rem/gi.test(px)) {
        // 有百分号%，特殊处理，表述pc是一个有百分号的数，比如：90%
        return px
    } else {
        return (parseFloat(px) / 1920) * document.body.clientWidth
    }
}