<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>监测数据</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main over-auto-y" style="height: 60vh;">
      <div class="height-all">
        <div class="height-all tableBox flex flex-col">
          <Table :data="tableData" :columns="columns" row-key="id">
            <template #operations="{ row }">
              <div class="buttons">
                <div class="color-blue hover" @click="handleLook(row)">查看</div>
              </div>
            </template>
          </Table>
          <el-pagination class="pageBox padd-t-10 flex-right border-t-ddd" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
            :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
            @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </div>

      <!-- 监测历史数据 -->
      <monitorHistoryDialog :dialogVisible="monitorHistoryVisiblty" :data="monitorHistoryData" @onChangeVisible="monitorHistoryVisiblty = false" append-to-body></monitorHistoryDialog>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

import Table from "@/components/Table/index.vue";
import { Columns } from "../table";
import monitorHistoryDialog from "./look-monitor-history-dialog.vue";

// import {
//   listReport,
// } from "@/api/comprehensive-operation/device-inspect/index";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {return {}},
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};

// 监听
watch(isVisible, (val) => {
  if(val){
    fun_getList();//获取列表
  }
});

// 挂载
// onMounted(async () => {
//   fun_getList();//获取列表
// });

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns2);

// 表格数据
const total = ref(0);
const tableData = ref([
  {id:1,jcxbm:'监测项代码',jcxmc:'监测项',zxjcxz:'最新监测项值',jcsbsj:'监测上报时间'},
  {id:2,jcxbm:'监测项代码',jcxmc:'监测项',zxjcxz:'最新监测项值',jcsbsj:'监测上报时间'},
]);
const fun_getList = async () => {
  // const { data } = await listReport({...filter.value,gid:props.data.gid});
  // tableData.value = data.list;
  // total.value = data.total;
};

// 分页
const filter = ref({
  pageNum: 1,
  pageSize: 10,
});
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};

// 监测历史数据-弹窗
const monitorHistoryVisiblty = ref(false);
const monitorHistoryData = ref({});
const handleLook = async (row) => {
  monitorHistoryVisiblty.value = true;
  monitorHistoryData.value = row;
};


</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
