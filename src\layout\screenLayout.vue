<template>
  <Header
    title="决策建议系统"
    :type="type"
    :leftMenus="
      headerMenu.filter((d) => d.pos === 'left').sort((a, b) => a.type - b.type)
    "
    :rightMenus="headerMenu.filter((d) => d.pos === 'right')"
    @menuSelected="onMenuSelected"
    @showMore="onShowMore"
  />

  <transition name="fade" mode="out-in">
    <router-view></router-view>
  </transition>

  <div v-if="showMoreFlag" class="more-container">
    <div
      v-for="item in headerMenu.filter((d) => d.pos === 'more')"
      class="menu"
      :class="`${type === item.type ? 'selected' : ''}`"
      @click="onMenuSelected(item)"
    >
      {{ item.name }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onUnmounted, ref } from "vue";
import Header from "./screenComponents/header.vue";
import { ElMessage } from "element-plus";
import router from "@/router";
import { headerMenu } from "./menu.ts";
import { useRoute } from "vue-router";

const type = ref(0);
const showMoreFlag = ref(false);
const route = useRoute();

headerMenu.forEach((d) => {
  if (d.route === route.name) {
    type.value = d.type;
  }
});

const onMenuSelected = (item: any) => {
  showMoreFlag.value = !showMoreFlag;
  if (!item.route || item.type === 13 || item.type === 14) {
    ElMessage({
      message: "开发中,尽请期待~^~",
      type: "warning",
    });
    return;
  }
  type.value = item.type;
  router.push("/screen/" + item.route);
};

const onShowMore = () => {
  showMoreFlag.value = !showMoreFlag.value;
};
</script>

<style lang="scss" scoped>
.more-container {
  background: url(../common/static/images/header/btn-selector.png);
  height: 220px;
  width: 133px;
  position: absolute;
  z-index: 9999999;
  right: 173px;
  top: 55px;

  .menu {
    height: 37px;
    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
    font-weight: 400;
    font-size: 20px;
    color: rgba(235, 248, 255, 0.8);
    line-height: 37px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    cursor: pointer;
  }

  .selected {
    background: rgba(3, 200, 255, 0.25);
    color: #4dfff9;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
