
// 导入threejs
import * as THREE from 'three';
// 导入轨道控制器
// import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
// import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
// 导入gltf加载器(加载gltf模型)
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
// 导入draco解压缩gltf模型加载器
import { DRACOLoader } from 'three/addons/loaders/DRACOLoader.js';
// 导入hdr加载器(加载hdr贴图)
import { RGBELoader } from 'three/addons/loaders/RGBELoader.js';
// 导入tween补间动画库
import * as TWEEN from 'three/addons/libs/tween.module.js';
// 导入字体加载器(加载字体)
import { FontLoader } from 'three/addons/loaders/FontLoader.js';
// 导入文本几何体加载器(加载文本)
import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';
// 导入lil.gui(调试开发工具)
import { GUI } from 'three/addons/libs/lil-gui.module.min.js';


// 添加几何体模型测试1
function fun_addGeometryModelTest1({scene,renderer,camera}:any){
    //创建几何体-立方体
    const geometryCube = new THREE.BoxGeometry(1,1,1);//立方体-参数:width,height,depth,...
    //创建材质(基础材质(不需要光源):MeshBasicMaterial,标准材质(需要有光源):MeshStandardMaterial)-立方体
    const materialCube = new THREE.MeshBasicMaterial({ wireframe:false });//基础材质-参数:color,wireframe,...
    // const materialCube = new THREE.MeshStandardMaterial();//标准材质-参数:color,wireframe,...
    // materialCube.color = new THREE.Color(0xff0000);//设置立方体颜色
    // materialCube.side = THREE.DoubleSide;//设置材质两面可见(适用于平面,默认背面不可见)
    //添加纹理
    const texture = new THREE.TextureLoader().load('/static/threejsData/logo.svg');
    texture.colorSpace = THREE.SRGBColorSpace;//设置纹理颜色空间-SRGBColorSpace(人类感知色彩光谱范围值空间)
    //设置贴图(map:颜色贴图,alphaMap:透明度贴图,envMap:环境贴图,lightMap:光照贴图,aoMap:环境光遮蔽贴图,gradientMap:渐变贴图,bumpmap:凹凸贴图,normalMap:法线贴图,specularMap:镜面反射贴图,displacementMap:位移贴图)
    materialCube.map = texture;//设置颜色贴图
    // materialCube.alphaMap = texture;//设置透明度贴图
    // materialCube.transparent = true;//设置材质透明
    // materialCube.opacity = 0.3;//设置材质透明度
    //创建网格-立方体
    const cube = new THREE.Mesh(geometryCube, materialCube);
    //设置立方体在网格(父元素网格或场景网格)的位置
    cube.position.x=3;//x轴
    // cube.position.y=2;//y轴
    // cube.position.z=2;//z轴
    // cube.position.set(3,2,2);//设置立方体在网格的位置:x,y,z轴
    //设置立方体的网格的缩放
    // cube.scale.set(2,2,2);//设置立方体在网格的缩放:x,y,z轴
    //添加立方体-到场景网格中
    // scene.add(cube);

    //创建几何体-圆柱体
    const geometryCylinder = new THREE.CylinderGeometry(1,1,1);//圆柱体-参数:radiusTop,radiusBottom,height,...
    //创建材质(基础材质(不需要光源):MeshBasicMaterial,标准材质(需要有光源):MeshStandardMaterial)-圆柱体
    // const materialCylinder = new THREE.MeshBasicMaterial({ color: 0x0000ff,wireframe:true });//基础材质-参数:color,wireframe,...
    const materialCylinder = new THREE.MeshStandardMaterial({ color: 0x0000ff,wireframe:false });//标准材质-参数:color,wireframe,...
    //创建网格-圆柱体
    const cylinder = new THREE.Mesh(geometryCylinder, materialCylinder);
    //设置圆柱体在网格(父元素网格或场景网格)的位置
    cylinder.position.x=-2;//x轴
    //设置圆柱体的网格的缩放
    // cylinder.scale.set(2,2,2);//设置圆柱体在网格的缩放:x,y,z轴
    //添加圆柱体-到场景网格中
    // scene.add(cylinder);

    // 添加绑定1
    scene.add(cylinder);//添加圆柱体-到场景网格中
    cylinder.add(cube);//添加立方体-到圆柱体网格中
    // 添加绑定2
    // scene.add(cube);//添加立方体-到场景网格中
    // cube.add(cylinder);//添加圆柱体-到立方体网格中
    
    renderer.render(scene, camera);//渲染到标签中
    return {cube,cylinder};
}

// 添加动画测试1
function fun_animateTest1({scene,renderer,camera}:any,{cube,cylinder}:any){
  // 旋转
  cube.rotation.x += 0.01;//立方体绕x轴旋转
  cube.rotation.y += 0.01;//立方体绕y轴旋转
  // cube.rotation.z += 0.01;//立方体绕z轴旋转

  renderer.render(scene, camera);//渲染到标签中
  requestAnimationFrame(()=>fun_animateTest1({scene,renderer,camera},{cube,cylinder}));//重复调用当前函数渲染
}

/* tween补间动画(即:动画的中间过渡效果) */
// 添加几何体模型补间动画测试1
function fun_addModelTweenTest1({renderer,scene,camera}:any){
  //创建几何体-立方体
  const geometryCube = new THREE.BoxGeometry(1,1,1);//立方体-参数:width,height,depth,...
  //创建材质-立方体
  const materialCube = new THREE.MeshBasicMaterial({color:0xffff00, wireframe:false });//立方体-参数:color,wireframe,...
  // materialCube.color = new THREE.Color(0xff0000);//设置立方体颜色
  // materialCube.side = THREE.DoubleSide;//设置材质两面可见
  //创建网格-立方体
  const cubeTween1 = new THREE.Mesh(geometryCube, materialCube);
  //设置立方体在网格(父元素网格或场景网格)的位置
  cubeTween1.position.z=4;//z轴
  // cubeTween1.position.set(3,2,2);//设置立方体在网格的位置:x,y,z轴
  //设置立方体的网格的缩放
  // cubeTween1.scale.set(2,2,2);//设置立方体在网格的缩放:x,y,z轴
  //添加立方体-到场景网格中
  scene.add(cubeTween1);

  renderer.render(scene, camera);//渲染到标签中

  const tween = new TWEEN.Tween(cubeTween1.position);
  tween.to({x:0,y:0,z:-4},1000);//设置立方体在网格的位置:x,y,z轴
  // tween.repeat(Infinity);//设置动画重复次数
  // tween.yoyo(true);//设置动画反向播放
  tween.easing(TWEEN.Easing.Quadratic.InOut);//设置动画缓动效果(Quadratic:慢快慢缓动):https://tweenjs.github.io/tween.js/examples/03_graphs.html
  // tween.delay(1000);//设置动画延迟时间
  tween.start();//开始动画
  //注意:***然后持续调用(在上面fun_animateTest1)TWEEN.update();//更新tween补间动画帧数*** //
  fun_animateTween();//持续更新tween补间动画函数

  // 创建动画链
  const tween2 = new TWEEN.Tween(cubeTween1.position).to({x:4,y:0,z:4},1000);
  tween2.easing(TWEEN.Easing.Elastic.InOut);//设置动画缓动效果(Elastic:弹性缓动):https://tweenjs.github.io/tween.js/examples/03_graphs.html
  tween.chain(tween2);// 连接下一个动画
  const tween3 = new TWEEN.Tween(cubeTween1.position).to({x:0,y:0,z:4},1000);
  tween2.chain(tween3);// 连接下一个动画
  tween3.chain(tween);// 连接下一个动画(连接回到第一个动画则会执行第一个动画,即会循环播放)

  // 回调函数
  // tween.onStart(function(){//设置动画开始回调
  //   console.log('动画开始');
  // });
  // tween.onComplete(function(){//设置动画完成回调
  //   console.log('动画完成');
  // });
  // tween.onUpdate(function(){//设置动画更新回调
  //   console.log('动画更新');
  // });
  // tween.onStop(function(){//设置动画停止回调
  //   console.log('动画停止');
  // });
}
// 持续更新tween补间动画函数
function fun_animateTween(){
  TWEEN.update();//更新tween补间动画帧数
  requestAnimationFrame(fun_animateTween);//重复调用当前函数渲染 
}

// 添加图片模型(平面模型)
function fun_addImageModel({scene,renderer,camera}:any,config:{path:string,width?:number,height?:number,position?:{x:number,y:number,z:number},scale?:number,lookAtCamera?:boolean}){
  //创建几何体-平面
  const geometryPlane = new THREE.PlaneGeometry(config.width||20,config.height||20);//平面-参数:width,height,depth,...
  //创建材质(基础材质(不需要光源):MeshBasicMaterial,标准材质(需要有光源):MeshStandardMaterial)-平面
  const materialPlane = new THREE.MeshBasicMaterial({ wireframe:false });//基础材质-参数:color,wireframe,...
  // const materialPlane = new THREE.MeshStandardMaterial();//标准材质-参数:color,wireframe,...
  // materialPlane.color = new THREE.Color(0xff0000);//设置平面颜色
  materialPlane.side = THREE.DoubleSide;//设置材质两面可见(适用于平面,默认背面不可见)
  //添加纹理
  const texture = new THREE.TextureLoader().load(config.path);
  texture.colorSpace = THREE.SRGBColorSpace;//设置纹理颜色空间-SRGBColorSpace(人类感知色彩光谱范围值空间)
  //设置贴图(map:颜色贴图,alphaMap:透明度贴图,envMap:环境贴图,lightMap:光照贴图,aoMap:环境光遮蔽贴图,gradientMap:渐变贴图,bumpmap:凹凸贴图,normalMap:法线贴图,specularMap:镜面反射贴图,displacementMap:位移贴图)
  materialPlane.map = texture;//设置颜色贴图
  // materialPlane.alphaMap = texture;//设置透明度贴图
  materialPlane.transparent = true;//设置材质透明
  // materialPlane.opacity = 0.3;//设置材质透明度
  //创建网格-平面
  const plane = new THREE.Mesh(geometryPlane, materialPlane);
  //设置平面在网格(父元素网格或场景网格)的位置(x,y,z轴)
  plane.position.set(config.position?.x||0,config.position?.y||0,config.position?.z||0);//x,y,z轴
  // plane.position.x=config.position?.x||0;//x轴
  // plane.position.y=config.position?.x||0;//y轴
  // plane.position.z=config.position?.x||0;//z轴
  //设置平面的网格的缩放(x,y,z轴)
  const modelScale = config.scale||1;
  plane.scale.set(modelScale,modelScale,modelScale);//x,y,z轴
  //设置平面的网格的旋转(x,y,z轴)
  // plane.rotation.x = -Math.PI/2;//x轴
  // plane.rotation.y = -Math.PI/2;//y轴
  // plane.rotation.z = -Math.PI/2;//z轴
  //设置平面的网格的旋转:始终正对相机
  if(config.lookAtCamera)fun_imageAnimate({scene,renderer,camera},{plane});//持续更新平面模型动画函数(保证始终正对相机)
  //添加平面-到场景网格中
  scene.add(plane);

  renderer.render(scene, camera);//渲染到标签中
  return {plane};
}
// 持续更新平面模型动画函数(保证始终正对相机)
function fun_imageAnimate({scene,renderer,camera}:any,{plane}:any){
  //设置平面的网格的旋转:正对相机
  plane.lookAt(camera.position);
  renderer.clear();
  renderer.render(scene, camera);//渲染到标签中
  requestAnimationFrame(()=>fun_imageAnimate({scene,renderer,camera},{plane}));//重复调用当前函数渲染
}
// 添加文字模型
const fontLoader = new FontLoader();//创建字体加载器
function fun_addTextModel({scene,renderer,camera}:any,config:{text:string,fontPath?:string,position?:{x:number,y:number,z:number},scale?:number,color?:string|number,size?:number,depth?:number,weight?:number,lookAtCamera?:boolean}){
  // '/static/threejsData/fonts/helvetiker_regular.typeface.json'
  fontLoader.load(config.fontPath||'/static/threejsData/fonts/OPPOSansL_Regular.typeface.json', function (font) {//加载字体
    // console.log('font:',font);
    // 创建文本几何体
    const textGeometry = new TextGeometry(config.text, {
      font: font,//字体
      size: config.size||2,//字号
      depth: config.depth||0.1,//厚度
      curveSegments: 12,//曲线上的分段数
      bevelEnabled: true,//是否开启斜角
      bevelThickness: 0.03,//斜角厚度
      bevelSize: config.weight||0.05,//斜角大小(加粗)
      bevelOffset: 0,//斜角偏移
      bevelSegments: 5//斜角分段数
    })
    // 优化几何体，合并缓冲区
    textGeometry.computeBoundingBox();
    // textGeometry.center();
    // 创建材质
    const textMaterial = new THREE.MeshBasicMaterial({ color: config.color||'#ffffff', wireframe: false })//基础材质-参数:color,wireframe,...
    // 创建文本
    const text = new THREE.Mesh(textGeometry, textMaterial)
    // 设置文本位置
    text.position.set(config.position?.x||0,config.position?.y||0,config.position?.z||0)
    // 设置文本缩放
    const modelScale = config.scale || 1;
    text.scale.set(modelScale, modelScale, modelScale);
    // 设置文本正对相机
    if(config.lookAtCamera)fun_textAnimate({scene,renderer,camera},{text});//持续更新文本模型动画函数(保证始终正对相机)

    // 添加文本
    scene.add(text);
    // 渲染
    renderer.render(scene, camera);
    return {text};
  })
}
// 持续更新文本模型动画函数(保证始终正对相机)
function fun_textAnimate({scene,renderer,camera}:any,{text}:any){
  // 设置文本正对相机
  text.lookAt(camera.position);
  renderer.clear();
  renderer.render(scene, camera);//渲染到标签中
  requestAnimationFrame(()=>fun_textAnimate({scene,renderer,camera},{text}));//重复调用当前函数渲染
}

/* 添加gltf模型(物理模型需要场景中有光照才能显示自带的贴图或者添加场景环境(scene.environment)hdr贴图,否则只显示黑色) */
const dracoLoader = new DRACOLoader();//创建draco解压缩gltf模型加载器
dracoLoader.setDecoderPath('three/addons/libs/draco/');//设置draco解压缩gltf模型加载器路径
// dracoLoader.setDecoderPath('/static/threejsData/draco/');//设置draco解压缩gltf模型加载器路径
// 创建gltf模型加载器
const gltfLoader = new GLTFLoader();
gltfLoader.setDRACOLoader(dracoLoader);// 设置解压gltf压缩模型
// 添加gltf模型函数
function fun_addGltfModel({scene}:any,config:{path:string,position?:{x:number,y:number,z:number},scale?:number}){
  console.log('fun_addGltfModel:',config);
  // 加载gltf模型1(gltf格式是可打开json的格式,glb格式是不可打开二进制的格式)
  gltfLoader.load(config.path, (gltf:any) => { //path:'/static/threejsData/empire_state_building.glb'
    console.log('gltf模型1:',gltf);
    // 设置模型位置
    gltf.scene.position.set(config.position?.x||0,config.position?.y||0,config.position?.z||0);
    // 设置模型大小
    const modelScale = config.scale||1;
    gltf.scene.scale.set(modelScale,modelScale,modelScale);

    // 循环模型所有子几何体
    gltf.scene.traverse((child:any) => {
      if (child.isMesh) {
        // 设置模型所有子几何体材质
        // child.material = new THREE.MeshBasicMaterial({ color: 0x999999,wireframe:false });//设置模型材质:颜色,线框(三角形线框面)
        //添加纹理
        // const texture = new THREE.TextureLoader().load('/static/threejsData/water.jpg');
        // texture.colorSpace = THREE.SRGBColorSpace;//设置纹理颜色空间-SRGBColorSpace(人类感知色彩光谱范围值空间)
        // texture.flipY = false;//设置纹理翻转
        // child.material.map = texture;

        // 添加模型边缘线框
        // const geometry = child.geometry;
        // // 获取边缘线框几何体-最少线框面
        // const edgesGeometry = new THREE.EdgesGeometry(geometry);
        // // 获取边缘线框几何体-三角形线框面
        // // const edgesGeometry = new THREE.WireframeGeometry(geometry);
        // // 创建边缘线框材质
        // const lineMaterial = new THREE.LineBasicMaterial({ color: 0xffffff });
        // // 创建边缘线框
        // const line = new THREE.LineSegments(edgesGeometry, lineMaterial);
        // // 复制模型的信息到边缘线框上-更新模型世界矩阵
        // child.updateWorldMatrix(true,true);
        // // 复制模型的信息到边缘线框上-复制模型世界矩阵到边缘线框上
        // line.matrix.copy(child.matrixWorld);
        // // 复制模型的信息到边缘线框上-解构到边缘线(位置,旋转,缩放)
        // line.matrix.decompose(line.position, line.quaternion, line.scale);
        // // 直接设置边缘线框-位置
        // // line.position.set(0,0,0);
        // // 直接设置边缘线框-缩放
        // // const lineScale = 0.001;
        // // line.scale.set(lineScale,lineScale,lineScale);
        // // 添加边缘线框到场景
        // scene.add(line);
      }
    })
    
    // 添加模型到场景
    scene.add(gltf.scene);
  });
}
/* 添加gltf模型 */

/* 添加环境贴图 */
// 创建hdr贴图加载器
const hdrLoader = new RGBELoader();
function fun_addHdr({scene}:any,config:{path:string}){
  console.log('fun_addHdr:scene',scene);
  // 加载hdr贴图
  hdrLoader.load(config.path, (hdr:any) => { //path:'/static/threejsData/city1.hdr'
    console.log('hdr贴图:',hdr);
    // 设置hdr贴图
    hdr.mapping = THREE.EquirectangularReflectionMapping;//设置全景贴图
    //设置场景背景贴图
    scene.background = hdr;
    // 设置场景环境hdr贴图
    scene.environment = hdr;
  });
}
/* 添加环境贴图 */



// 全屏方法(需要点击按钮触发此事件)
function fun_fullScreen({renderer}:any){
  renderer.domElement.requestFullscreen();//整个画布全屏
  // document.documentElement.requestFullscreen();//整个页面全屏
}


/* gui(调试开发工具) */
// gui自定义事件对象(添加到gui中的方法要放到对象中)
const guiEventObj:any = {}

// 添加gui调试
function fun_addGui({renderer}:any,{cube}:any){
  // 创建gui调试
  const gui = new GUI();
  // 创建全屏按钮
  guiEventObj.fun_fullScreen = ()=>fun_fullScreen({renderer});//添加全屏方法到gui事件对象中
  gui.add(guiEventObj,'fun_fullScreen').name('全屏');
  // 创建立方体x轴位置移动
  // gui.add(cube.position,'x',-10,10,1).name('立方体x轴');
  gui.add(cube.position,'x').name('立方体x轴').min(-100).max(100).step(1);
  // 创建立方体y轴位置移动
  gui.add(cube.position,'y').name('立方体y轴').min(-100).max(100).step(1);
  // 创建立方体z轴位置移动
  gui.add(cube.position,'z').name('立方体z轴').min(-100).max(100).step(1);
}
/* gui(调试开发工具) */



export {
  fun_addGeometryModelTest1,//添加几何体模型测试1
  fun_animateTest1,//添加动画测试1
  fun_addModelTweenTest1,//添加几何体模型补间动画测试1

  fun_addImageModel,//添加图片模型(平面模型)
  fun_addTextModel,//添加文字模型

  fun_addGltfModel,//添加gltf模型
  fun_addHdr,//添加环境贴图

  fun_fullScreen,//全屏方法

  fun_addGui,//添加gui调试
}

