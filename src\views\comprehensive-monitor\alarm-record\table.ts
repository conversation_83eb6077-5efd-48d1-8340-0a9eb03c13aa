export class Columns {
  get columns1() {
    return [
      {
        title: "",
        align: "center",
        type: "selection",
        width: 60,
      },
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "报警编号",
        key: "bjbh",
        align: "center",
      },
      {
        title: "监测项",
        key: "jcxmc",
        align: "center",
      },
      {
        title: "报警状态",
        key: "bjczjdmc",
        align: "center",
      },
      // {
      //   title: "报警类型",
      //   key: "bjlxmc",
      //   align: "center",
      // },
      {
        title: "报警值",
        key: "dqbjz",
        align: "center",
      },
      {
        title: "传感器编号",
        key: "cxsbh",
        align: "center",
      },
      {
        title: "所属设备",
        key: "sssbmc",
        align: "center",
      },
      {
        title: "报警时间",
        key: "createTime",
        align: "center",
      },
      {
        title: "位置",
        key: "bjwz",
        align: "center",
      },
      {
        title: "操作",
        slot: "operations",
        align: "center",
        width: 160,
      },
    ];
  }
  get columns2() {
    return [
      {
        title: "",
        align: "center",
        type: "selection",
        width: 60,
      },
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "报警编号",
        key: "bjbh",
        align: "center",
      },
      {
        title: "监测项",
        key: "jcxmc",
        align: "center",
      },
      {
        title: "报警状态",
        key: "bjczjdmc",
        align: "center",
      },
      // {
      //   title: "报警类型",
      //   key: "bjlxmc",
      //   align: "center",
      // },
      {
        title: "报警值",
        key: "dqbjz",
        align: "center",
      },
      {
        title: "设备名称",
        key: "sbmc",
        align: "center",
      },
      {
        title: "报警时间",
        key: "createTime",
        align: "center",
      },
      {
        title: "位置",
        key: "bjwz",
        align: "center",
      },
      {
        title: "操作",
        slot: "operations",
        align: "center",
        width: 160,
      },
    ];
  }
}
