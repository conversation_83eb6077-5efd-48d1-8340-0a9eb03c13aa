<template>
  <div class="gird-box">
    <div class="gird-item">
      <div class="gird-content">
        <div class="gird-nums">
          <nums :value="props.datas?.realTimePower" />
        </div>
        <div class="gird-unit">kw</div>
      </div>
      <div class="gird-label">实时功率</div>
    </div>
    <div class="gird-item">
      <div class="gird-content">
        <div class="gird-nums">
          <nums :value="props.datas?.realTimeLoad" />
        </div>
        <div class="gird-unit">kw</div>
      </div>
      <div class="gird-label">实时负荷</div>
    </div>
  </div>
</template>
<script setup>
import nums from "../count-up.vue";
const props = defineProps({
  datas: Object,
});
</script>
<style lang="scss" scoped>
.gird-box {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 16px;
  overflow: hidden;
  width: 100%;
  margin-bottom: 16px;
  .gird-item {
    width: 204px;
    height: 90px;
    padding: 15px 16px 15px 106px;
    position: relative;
    .gird-content {
      display: flex;
      align-items: baseline;
      margin-bottom: 8px;
      .gird-nums {
        font-family: D-DIN-PRO, D-DIN-PRO;
        font-weight: bold;
        font-size: 24px;
      }
      .gird-unit {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: rgba(199, 214, 255, 0.8);
        margin-left: 4px;
      }
    }
    .gird-label {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    &::before {
      content: "";
      position: absolute;
      width: 80px;
      height: 60px;
      top: 15px;
      left: 20px;
    }
    &:nth-child(1) {
      background: url(@/assets/data-screen/module-1-gird-bg-1.png) no-repeat;
      background-size: 100% 100%;
      &::before {
        background: url(@/assets/data-screen/module-1-grid-img-1.png) no-repeat;
        background-size: 100% 100%;
      }
      .gird-nums {
        background: linear-gradient(180deg, #ffffff 16%, #5a85f4 80%);

        -webkit-background-clip: text;
        color: transparent;
      }
    }
    &:nth-child(2) {
      background: url(@/assets/data-screen/module-1-gird-bg-2.png) no-repeat;
      background-size: 100% 100%;
      &::before {
        background: url(@/assets/data-screen/module-1-gird-img-2.png) no-repeat;
        background-size: 100% 100%;
      }
      .gird-nums {
        background: linear-gradient(180deg, #ffffff 16%, #ac7dd4 80%);
        -webkit-background-clip: text;
        color: transparent;
      }
    }
  }
}
</style>
