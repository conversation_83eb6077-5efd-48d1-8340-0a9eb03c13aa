<template>
  <div class="pointManage">
    <div class="main-contanier height-all">
      <el-calendar v-model="filter.calendar">
        <template #date-cell="{ data }">
          <div class="height-all flex flex-col">
            <p v-if="data.day.split('-')[1]==moment(filter.calendar).format('MM')" :class="data.isSelected ? 'is-selected' : ''">
              {{ data.day.split('-').slice(1).join('-') }}
            </p>
            <div class="height-all over-auto-y" v-if="data.day.split('-')[1]==moment(filter.calendar).format('MM')">{{ dataList.filter(v=>v.date == data.day).map(item => item.person).join(',') }}</div>
          </div>
        </template>
      </el-calendar>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import moment from "moment"
import { 
  getScheduleCalendarList,//排班日历-列表
} from "@/api/comprehensive-operation/maint-schedule-manage/index";

// 搜索条件
const filter = ref({
  calendar: new Date(),
  startTime:'',
  endTime:'',
  pageSize: 1000,
  pageNum: 1,
});


// 挂载
onMounted(async () => {
  console.log('qqqq',moment('2025-04-31').format('YYYY-MM-DD'));
  
  fun_getList();
});

const dataList = ref([
  // {id:1,date:'2025-06-29',person:'张想,李想,周想'},
  // {id:2,date:'2025-06-21',person:'张强,李强,周强'},
  // {id:3,date:'2025-06-22',person:'张志,李志,周志'},
  // {id:4,date:'2025-06-22',person:'张志2,李志2,周志2'},
  // {id:5,date:'2025-06-22',person:'张志3,李志3,周志3'},
]);
const fun_getList = async () => {
  // 获取当月起止时间
  filter.value.startTime = moment(filter.value.calendar).format('YYYY-MM')+'-01 00:00:00';
  filter.value.endTime = moment(filter.value.calendar).format('YYYY-MM')+'-'+moment(filter.value.calendar).daysInMonth()+' 23:59:59';
  // 获取上个月和下个月(如果当月日历显示上个月和下个月的几天)
  // filter.value.startTime = moment(filter.value.calendar).subtract(1, 'month').format('YYYY-MM')+'-15';//上一个月
  // filter.value.endTime = moment(filter.value.calendar).add(1, 'month').format('YYYY-MM')+'-15';//下一个月

  const { data } = await getScheduleCalendarList(filter.value);
  console.log('数据列表:',data);
  dataList.value = data.list;
};

// 监听-日历改变
watch(()=>filter.value.calendar, (val,oldVal) => {
  console.log('日历改变:',moment(val).format('YYYY-MM'));
  const valStr=moment(val).format('YYYY-MM');
  const oldValStr=moment(oldVal).format('YYYY-MM');
  if(valStr!==oldValStr){
    fun_getList();
  }
})



</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
