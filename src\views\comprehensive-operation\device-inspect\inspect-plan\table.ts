export class Columns {
  get columns() {
    return [
      {
        title: "",
        align: "center",
        type: "selection",
        width: 60,
      },
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "计划名称",
        key: "inspectionPlanName",
        align: "center",
      },
      {
        title: "计划时间",
        key: "dateRange",
        slot: "dateRange",
        align: "center",
      },
      {
        title: "巡检项",
        key: "inspectionItemName",
        align: "center",
      },
      {
        title: "巡检人员",
        key: "inspectionPersonnel",
        align: "center",
      },
      {
        title: "巡检周期",
        key: "cycle",
        slot: "cycle",
        align: "center",
      },
      {
        title: "是否启用",
        key: "triggerStatus",
        slot: "triggerStatus",
        align: "center",
      },
      // {
      //   title: "创建时间",
      //   key: "createTime",
      //   align: "center",
      // },
      {
        title: "操作",
        slot: "operations",
        align: "center",
        width: 200,
      },
    ];
  }
}
