import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'
const AccessTokenKey = 'accessToken'

const ExpiresInKey = 'Admin-Expires-In'

export function getToken() {
  return Cookies.get(AccessTokenKey)
}

export function setToken(token) {
  return Cookies.set(AccessTokenKey, token)
}

export function removeToken() {
  return Cookies.remove(AccessTokenKey)
}

export function getExpiresIn() {
  return Cookies.get(ExpiresInKey) || -1
}

export function setExpiresIn(time) {
  return Cookies.set(ExpiresInKey, time)
}

export function removeExpiresIn() {
  return Cookies.remove(ExpiresInKey)
}


export function getAccessToken() {
  return Cookies.get(AccessTokenKey) ? Cookies.get(AccessTokenKey) : import.meta.env.VITE_APP_VIDEO_TOKEN
}

export function getHRGVPToken() {
  return  import.meta.env.VITE_APP_HRGVP_TOKEN
}
