<template>
  <el-row :gutter="40" class="device-config">
    <el-col :span="6" class="left">
      <div class="tree-header">设备</div>
      <div class="tree-container">
        <el-tree :data="treeData" :props="defaultProps" @node-click="nodeClick" highlight-current />
      </div>
    </el-col>
    <el-col :span="18" class="right">
      <div class="time-list" v-if="currentDevice.id">
        <el-row v-for="(item,index) in currentTimeList" :key="index" style="margin-bottom: 10px;">
          <el-col :span="20">
            <el-row>
              <el-col :span="12" class="flex flex-middle">
                <span>启动时间：</span>
                <el-time-picker v-model="item.openTime" value-format="HH:mm:ss" :disabled="isLook" />
              </el-col>
              <el-col :span="12" class="flex flex-middle">
                <span>关闭时间：</span>
                <el-time-picker v-model="item.closeTime" value-format="HH:mm:ss" :disabled="isLook" />
              </el-col>
            </el-row>
            <el-row v-if="currentDevice.deviceType == '冷机'" class="marg-t-5">
              <el-col :span="12" class="flex flex-middle">
                <span>冷机启动个数：</span>
                <el-input-number v-model="item.coolerNum" :controls="false" step-strictly :disabled="isLook" style="width: 1.18rem;" />
              </el-col>
              <el-col :span="12" class="flex flex-middle">
                <span>冷却塔启动个数：</span>
                <el-input-number v-model="item.coolTowerNum" :controls="false" step-strictly :disabled="isLook" style="width: 1.11rem;" />
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="4">
            <div class="height-all flex flex-even flex-middle">
              <el-icon class="font-28 hover-blue" @click="fun_dataListAdd(index)" v-if="!isLook"><CirclePlusFilled /></el-icon>
              <el-icon class="font-28 hover-blue" @click="fun_dataListDel(index)" v-if="currentTimeList.length>1 && !isLook"><RemoveFilled /></el-icon>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-form label-width="112" :inline="true" v-if="currentDevice.id" :model="currentFormData" ref="ruleFormRef" status-icon>
        <!-- <template v-if="currentDevice.deviceType == '冷机'">
          <div class="form-title">冷机第一次启动设定</div>
          <el-form-item label="冷机个数:" prop="dycljgs">
            <el-input-number v-model="currentFormData.dycljgs" :controls="false" step-strictly :disabled="isLook" />
          </el-form-item>
          <el-form-item label="冷机加载时间:" prop="dycljjzsj">
            <el-input-number v-model="currentFormData.dycljjzsj" :controls="false" step-strictly :disabled="isLook">
              <template #suffix>
                <span>分钟</span>
              </template>
            </el-input-number>
          </el-form-item>
          <div class="form-title">冷机最小运行阶段设定</div>
          <el-form-item label="冷机个数:" prop="minljgs">
            <el-input-number v-model="currentFormData.minljgs" :controls="false" step-strictly :disabled="isLook" />
          </el-form-item>
          <el-form-item label="冷机卸载时间:" prop="minljxzsj">
            <el-input-number v-model="currentFormData.minljxzsj" :controls="false" step-strictly :disabled="isLook">
              <template #suffix>
                <span>分钟</span>
              </template>
            </el-input-number>
          </el-form-item>
          <div class="form-title">冷机最大运行阶段设定</div>
          <el-form-item label="冷机个数:" prop="maxljgs">
            <el-input-number v-model="currentFormData.maxljgs" :controls="false" step-strictly :disabled="isLook" />
          </el-form-item>
          <div class="form-title">主机温度设定</div>
          <el-form-item label="温度下限:" prop="wdxx">
            <el-input-number v-model="currentFormData.wdxx" :controls="false" step-strictly :disabled="isLook">
              <template #suffix>
                <span>℃</span>
              </template>
            </el-input-number>
          </el-form-item>
          <el-form-item label="温度上限:" prop="strategyCoolerTemperatureList" style="display: flex;">
            <div style="width: 80%;">
              <el-row :gutter="10" v-for="(temp, tempKey) in currentFormData.strategyCoolerTemperatureList" :key="tempKey" style="margin-bottom: 10px;">
                <el-col :span="12">
                  <div class="flex flex-both">
                    <el-time-picker style="width: 100%;" v-model="temp.wdsxkssj" format="HH:mm" value-format="HH:mm" placeholder="开始时间" :disabled="isLook"/>
                    <span class="flex-item-shrink-0"> - </span>
                    <el-time-picker style="width: 100%;" v-model="temp.wdsxjssj" format="HH:mm" value-format="HH:mm" placeholder="结束时间" :disabled="isLook"/>
                  </div>
                </el-col>
                <el-col :span="6">
                  <el-input-number v-model="temp.wdsx" :controls="false" style="width: 100%;" step-strictly :disabled="isLook">
                    <template #suffix>
                      <span>℃</span>
                    </template>
                  </el-input-number>
                </el-col>
                <el-col :span="6">
                  <div class="height-all flex flex-even flex-middle">
                    <el-icon class="font-28 hover-blue" @click="fun_tempListAdd(tempKey)" v-if="!isLook"><CirclePlusFilled /></el-icon>
                    <el-icon class="font-28 hover-blue" @click="fun_tempListDel(tempKey)" v-if="currentFormData.strategyCoolerTemperatureList.length>1 && !isLook"><RemoveFilled /></el-icon>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-form-item>
        </template> -->
        <!-- <template v-if="currentDevice.deviceType == '供回水'">
          <el-row>
            <el-col :span="11">
              <div class="inline-set" style="display: flex; align-items: center;">
                <span class="inline-set-title">冷冻水总管温差设定</span>
                <el-input-number style="width: 80px;" v-model="currentFormData.minldszgwc" :controls="false" step-strictly :disabled="isLook">
                  <template #suffix>
                    <span>℃</span>
                  </template>
                </el-input-number>
                <span>至</span>
                <el-input-number style="width: 80px;" v-model="currentFormData.maxldszgwc" :controls="false" step-strictly :disabled="isLook">
                  <template #suffix>
                    <span>℃</span>
                  </template>
                </el-input-number>
              </div>
            </el-col>
            <el-col :span="11">
              <div class="inline-set" style="display: flex; align-items: center;">
                <span class="inline-set-title">冷冻水供回水温差设定</span>
                <el-input-number style="width: 80px;" v-model="currentFormData.minldsghswc" :controls="false" step-strictly :disabled="isLook">
                  <template #suffix>
                    <span>℃</span>
                  </template>
                </el-input-number>
                <span>至</span>
                <el-input-number style="width: 80px;" v-model="currentFormData.maxldsghswc" :controls="false" step-strictly :disabled="isLook">
                  <template #suffix>
                    <span>℃</span>
                  </template>
                </el-input-number>
              </div>
            </el-col>
            <el-col :span="11">
              <div class="inline-set" style="display: flex; align-items: center;">
                <span class="inline-set-title">冷却水总管温差设定</span>
                <el-input-number style="width: 80px;" v-model="currentFormData.minlqszgwc" :controls="false" step-strictly :disabled="isLook">
                  <template #suffix>
                    <span>℃</span>
                  </template>
                </el-input-number>
                <span>至</span>
                <el-input-number style="width: 80px;" v-model="currentFormData.maxlqszgwc" :controls="false" step-strictly :disabled="isLook">
                  <template #suffix>
                    <span>℃</span>
                  </template>
                </el-input-number>
              </div>
            </el-col>
            <el-col :span="11">
              <div class="inline-set" style="display: flex; align-items: center;">
                <span class="inline-set-title">冷却水供回水温差设定</span>
                <el-input-number style="width: 80px;" v-model="currentFormData.minlqsghswc" :controls="false" step-strictly :disabled="isLook">
                  <template #suffix>
                    <span>℃</span>
                  </template>
                </el-input-number>
                <span>至</span>
                <el-input-number style="width: 80px;" v-model="currentFormData.maxlqsghswc" :controls="false" step-strictly :disabled="isLook">
                  <template #suffix>
                    <span>℃</span>
                  </template>
                </el-input-number>
              </div>
            </el-col>
            <el-col :span="11">
              <div class="inline-set" style="display: flex; align-items: center;">
                <span class="inline-set-title">分区误差温度设定</span>
                <el-input-number style="width: 80px;" v-model="currentFormData.minfqwcwd" :controls="false" step-strictly :disabled="isLook">
                  <template #suffix>
                    <span>℃</span>
                  </template>
                </el-input-number>
                <span>至</span>
                <el-input-number style="width: 80px;" v-model="currentFormData.maxfqwcwd" :controls="false" step-strictly :disabled="isLook">
                  <template #suffix>
                    <span>℃</span>
                  </template>
                </el-input-number>
              </div>
            </el-col>
          </el-row>
        </template> -->
        <!-- <template v-if="currentDevice.deviceType == '冷却塔'">
          <el-row>
            <el-form-item label="冷却塔首次启动台数" label-width="150" prop="lqtscqdts">
              <el-input-number v-model="currentFormData.lqtscqdts" :controls="false" step-strictly :disabled="isLook">
                <template #suffix>
                  <span>台</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="冷却塔最大运行台数" label-width="150" prop="lqtzdyxts">
              <el-input-number v-model="currentFormData.lqtzdyxts" :controls="false" step-strictly :disabled="isLook">
                <template #suffix>
                  <span>台</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="冷却塔最小运行台数" label-width="150" prop="lqtzxyxts">
              <el-input-number v-model="currentFormData.lqtzxyxts" :controls="false" step-strictly :disabled="isLook">
                <template #suffix>
                  <span>台</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="冷却塔加载时间" label-width="150" prop="lqtjzsj">
              <el-input-number v-model="currentFormData.lqtjzsj" :controls="false" step-strictly :disabled="isLook">
                <template #suffix>
                  <span>分钟</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="冷却塔卸载时间" label-width="150" prop="lqtxzsj">
              <el-input-number v-model="currentFormData.lqtxzsj" :controls="false" step-strictly :disabled="isLook">
                <template #suffix>
                  <span>分钟</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-row>
        </template> -->
      </el-form>
    </el-col>
  </el-row>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

// import { getStartendTimeInfo } from '@/api/comprehensive-config/startend-time-config';

const props = defineProps({
  deviceData: {
    type: Object,
    default: () => {}
  },
  isLook: {
    type: Boolean,
    default: false
  }
});

const treeData = ref([
  {
    id: 1,
    name: '冷机机组',
    children: [
      {
        id: 11,
        name: '冷机',
        deviceType: '冷机'
      },
      // {
      //   id: 12,
      //   name: '供回水',
      //   deviceType: '供回水'
      // },
      // {
      //   id: 13,
      //   name: '冷却塔',
      //   deviceType: '冷却塔'
      // },
      // {
      //   id: 14,
      //   name: '泵',
      //   deviceType: null
      // },
    ]
  },
  // {
  //   id: 2,
  //   name: '热机'
  // },
  // {
  //   id: 3,
  //   name: '照明',
  //   children: [
  //     {
  //       id: 31,
  //       name: '1F--A类'
  //     },
  //     {
  //       id: 32,
  //       name: '1F--B类'
  //     },
  //   ]
  // }
]);
const defaultProps = ref({
  children: 'children',
  label: 'name'
});

//挂载
onMounted(async () => {
  //编辑/查看
  // if(props.deviceData.timeModelId){
  //   //获取详情
  //   const {data:{strategyInfoParam}} = await getStartendTimeInfo({gid:props.deviceData.timeModelId});
  //   props.deviceData.strategyInfoParam = strategyInfoParam;
  //   props.deviceData.strategyStartCloseInfoList = strategyInfoParam?.strategyStartCloseInfoList&&strategyInfoParam.strategyStartCloseInfoList.length!=0?strategyInfoParam.strategyStartCloseInfoList:[{openTime: null,closeTime: null}];//设备启停时间
  //   props.deviceData.strategyCoolerTemperatureList = strategyInfoParam?.strategyCoolerTemperatureList&&strategyInfoParam.strategyCoolerTemperatureList.length!=0?strategyInfoParam.strategyCoolerTemperatureList:[{wdsxkssj: null,wdsxjssj: null,wdsx: null}];//冷水机组温度上限
  // }
})


const currentDevice = ref({})
const currentTimeList = computed(() => {
  // const itemData = props.deviceData.find(item => item.id === currentDevice.value.id)
  // if(itemData) {
  //   return itemData.timeList
  // }
  // return []
  return props.deviceData.strategyStartCloseInfoList&&props.deviceData.strategyStartCloseInfoList.length!=0?props.deviceData.strategyStartCloseInfoList:[{openTime: null,closeTime: null}]
})
const currentFormData = computed(() => {
  // const itemData = props.deviceData.find(item => item.id === currentDevice.value.id)
  // if(itemData) {
  //   return itemData
  // }
  // return {}
  return props.deviceData||{}
})
const nodeClick = (node) => {
  console.log(node)
  if (node.deviceType) {
    // if (!props.deviceData.find(item => item.id === node.id)) {
    //   const deviceData = {
    //     id: node.id,
    //     timeList: [{
    //       startTime: null,
    //       endTime: null
    //     }]
    //   }
    //   if (node.deviceType == '冷机') {
    //     deviceData.maxTemp = [{
    //       timeRange: null,
    //       temp: null
    //     }]
    //   }
    //   props.deviceData.push(deviceData)
    // }
    //设备启停时间
    if(!props.deviceData.strategyStartCloseInfoList||props.deviceData.strategyStartCloseInfoList.length==0){
      props.deviceData.strategyStartCloseInfoList = [{
        openTime: null,
        closeTime: null
      }]
    }
    //冷水机组温度上限
    if(node.deviceType == '冷机' && (!props.deviceData.strategyCoolerTemperatureList||props.deviceData.strategyCoolerTemperatureList.length==0)){
      props.deviceData.strategyCoolerTemperatureList = [{
        wdsxkssj: null,
        wdsxjssj: null,
        wdsx: null
      }]
    }
    currentDevice.value = node
  } else {
    currentDevice.value = {}
  }
}
const fun_dataListAdd = (index) => {
  currentTimeList.value.splice(index + 1, 0, {
    openTime: null,
    closeTime: null
  })
}
const fun_dataListDel = (index) => {
  currentTimeList.value.splice(index, 1)
}
const fun_tempListAdd = (tempKey) => {
  currentFormData.value.strategyCoolerTemperatureList.splice(tempKey + 1, 0, {
    wdsxkssj: null,
    wdsxjssj: null,
    wdsx: null
  })
}
const fun_tempListDel = (tempKey) => {
  currentFormData.value.strategyCoolerTemperatureList.splice(tempKey, 1)
}
</script>

<style lang="scss" scoped>
.device-config {
  height: 100%;
  .left {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: rgba(242, 242, 242, 1);
    padding: 10px;
    .tree-header {
      font-size: 18px;
      font-weight: bold;
      text-align: center;
    }
    .tree-container {
      height: 100%;
      flex: 1;
      overflow-y: auto;
      margin-top: 10px;
      .el-tree {
        background: none;
      }
    }
  }
  .right {
    height: 100%;
    overflow-y: auto;
    .form-title {
      font-weight: bold;
      margin-bottom: 20px;
    }
    ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
    }
    ::v-deep(.el-input.is-disabled .el-input__wrapper),
    ::v-deep(.el-range-editor.is-disabled),
    ::v-deep(.el-select__wrapper.is-disabled),
    ::v-deep(.el-textarea.is-disabled .el-textarea__inner) {
      background-color: #fff;
      box-shadow: 0 0 0 1px #dcdfe6 inset;
    }
    ::v-deep(.el-range-editor.is-disabled input) {
      background-color: #fff;
    }
    .time-list {
      margin-bottom: 20px;
    }
    .inline-set {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 20px;
      .inline-set-title {
        width: 150px;
      }
    }
  }
}
</style>
