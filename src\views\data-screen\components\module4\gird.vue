<template>
  <div class="gird-box">
    <div class="gird-item" v-for="(item, index) in props.datas" :key="index">
      <div class="gird-content">
        <div class="gird-nums">{{ item.value }}</div>
        <div class="gird-unit">{{ item.unit }}</div>
      </div>
      <div class="gird-label">{{ item.label }}</div>
    </div>
  </div>
</template>
<script setup>
import nums from "../count-up.vue";
const props = defineProps({
  datas: {
    type: Array,
    default: () => {
      return [
        {
          label: "室外温度",
          value: 0,
          unit: "℃",
        },
        {
          label: "室内平均温度",
          value: 0,
          unit: "℃",
        },
        {
          label: "室外湿度",
          value: 0,
          unit: "RH%",
        },
        {
          label: "室内平均湿度",
          value: 0,
          unit: "RH%",
        },
      ];
    },
  },
});
</script>
<style lang="scss" scoped>
.gird-box {
  display: grid;
  width: 100%;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 4px;
  overflow: hidden;
  .gird-item {
    width: 103px;
    height: 92px;
    background: url(@/assets/data-screen/module-4-grid-img.png) no-repeat center
      bottom;
    background-size: 85.44% auto;
    .gird-content {
      display: flex;
      align-items: baseline;
      justify-content: center;
      margin-bottom: 12px;
      .gird-nums {
        line-height: 28px;
        font-family: D-DIN-PRO, D-DIN-PRO;
        font-weight: bold;
        font-size: 28px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(180deg, #ffffff 16%, #81a4ff 80%);
        -webkit-background-clip: text;
        color: transparent;
      }
      .gird-unit {
        margin-left: 2px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: rgba(199, 214, 255, 0.64);
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .gird-label {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      line-height: 18px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
