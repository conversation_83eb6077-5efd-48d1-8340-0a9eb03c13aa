<template>
  <div ref="refChart" class="chart-container"></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, watch, markRaw } from "vue";
import * as echarts from "echarts";
import { getTransferPx } from "@/utils/px2rem";

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

const refChart = ref<any>();
const chartInstance = ref();
const chartsData: any = ref({});

onMounted(() => {
  watch(
    () => props.data,
    (val) => {
      if (val) {
        chartsData.value = val;
        initChart();
      }
    },
    {
      deep: true,
      immediate: true,
    }
  );
});

onBeforeUnmount(() => {
  // 销毁图表
  chartInstance.value.dispose();
  chartInstance.value = null;
});

const initChart = () => {
  chartInstance.value = markRaw(echarts.init(refChart.value));

  const initOption = {
    title: {
      text: chartsData.value.value[0] + "%",
      textStyle: {
        color: "#041627",
        fontSize: getTransferPx(26),
      },
      subtext: "",
      subtextStyle: {
        color: "#333D46",
        fontSize: getTransferPx(14),
      },
      itemGap: 10,
      left: "center",
      top: "center",
    },
    angleAxis: {
      max: 100,
      clockwise: false, // 逆时针
      // 隐藏刻度线
      show: false,
      startAngle: 180,
    },
    radiusAxis: {
      type: "category",
      show: true,
      axisLabel: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    polar: [
      {
        center: ["50%", "50%"], //中心点位置
        radius: "173%", //图形大小
      },
    ],
    series: [
      {
        stack: "round",
        type: "bar",
        z: 10,
        data: chartsData.value.value,
        showBackground: false,
        backgroundStyle: {
          color: "blue",
          borderWidth: getTransferPx(5),
          width: getTransferPx(5),
        },
        coordinateSystem: "polar",
        roundCap: true,
        barWidth: getTransferPx(5), //大的占比环
        itemStyle: {
          normal: {
            opacity: 1,
            color: new echarts.graphic.LinearGradient(1, 0, 0, 1, [
              {
                offset: 0,
                color: chartsData.value.colorList[0],
              },
              {
                offset: 1,
                color: chartsData.value.colorList[1],
              },
            ]),
            shadowBlur: 5,
            shadowColor: "#ebf7ff",
          },
        },
      },
      {
        stack: "round",
        z: 11,
        type: "bar",
        data: [0.1],
        showBackground: true,
        coordinateSystem: "polar",
        roundCap: true,
        barWidth: getTransferPx(1),
        itemStyle: {
          color: "#fff",
          borderColor: chartsData.value.borderColor,
        },
      },
    ],
  };

  // 图表初始化配置
  chartInstance.value.setOption(initOption);

  setTimeout(function () {
    window.addEventListener("resize", () => {
      chartInstance.value?.resize();
    });
  }, 200);
};
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
