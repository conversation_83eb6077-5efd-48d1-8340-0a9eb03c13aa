<template>
  <div class="energy-analysis">
    <div class="sider-menu">
      <div class="sider-title">设备筛选</div>
      <div class="sider-content">
        <el-scrollbar class="scrollbar">
          <el-checkbox-group v-model="checked" @change="handleCheck" :max="1">
            <el-checkbox
              class="check-block"
              v-for="(item, index) in checkList"
              :key="index"
              :label="item.id"
              >{{ item.equipmentName }}</el-checkbox
            >
          </el-checkbox-group>
        </el-scrollbar>
      </div>
    </div>
    <div class="main-content">
      <charts :data="chartData"></charts>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted } from "vue";
import { getSelectList } from "@/api/energy-manage/analysis";
import charts from "./components/charts.vue";
const checkList = ref([]);
const getData = async () => {
  const res = await getSelectList();
  if (res.code == 0) {
    checkList.value = res.data;
    checked.value = [res.data[0].id];
  }
  console.log(res);
};
const checked = ref([]);
const handleCheck = (val) => {
  console.log(val);
  if (val.length != 0) {
    chartData.value.value = mockData();
  }
};
const mockData = () => {
  let list = [];
  for (let i = 0; i < 12; i++) {
    list.push(Math.floor(Math.random() * 250) + 50);
  }
  return list;
};
const chartData = ref({
  category: [
    "8:00",
    "9:00",
    "10:00",
    "11:00",
    "12:00",
    "13:00",
    "14:00",
    "15:00",
    "16:00",
    "17:00",
    "18:00",
    "19:00",
    "20:00",
  ],
  value: [120, 200, 150, 80, 70, 110, 130, 190, 230, 210, 120, 130],
});
onMounted(() => {
  getData();
});
</script>
<style lang="scss" scoped>
.energy-analysis {
  background: #fff;
  width: 100%;
  height: 100%;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  .sider-menu {
    width: 200px;
    margin-right: 12px;
    height: 100%;
    .sider-title {
      font-size: 16px;
      color: #333;
      margin-bottom: 10px;
    }
    .sider-content {
      height: calc(100% - 60px);
      .check-block {
        display: block;
        height: 24px;
      }
    }
  }
  .main-content {
    flex: 1;
    height: 100%;
    padding: 24px;
  }
}
</style>
