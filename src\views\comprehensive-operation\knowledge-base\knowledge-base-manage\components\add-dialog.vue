<template>
  <el-dialog v-model="isVisible" width="960" :show-close="false" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form label-width="112" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <el-row>
            <el-col :span="4">
              <div class="title">基本信息</div>
            </el-col>
            <el-col :span="16">
              <el-form-item label="标题:" prop="title">
                <el-input v-model="formData.title" placeholder="请输入" clearable />
                <!-- <el-select v-model="formData.qlid" placeholder="请选择" clearable @change="fun_qlbmChange">
                  <el-option v-for="(item, index) in qlOption" :key="index" :label="item.qlmc" :value="item.id" />
                </el-select> -->
              </el-form-item>
              <el-form-item label="类型:" prop="type">
                <el-select v-model="formData.type" placeholder="请选择" clearable @change="fun_taskTypeChange">
                  <el-option v-for="(item, index) in typeOption" :key="index" :label="item.businessLabel" :value="item.businessValue" />
                </el-select>
              </el-form-item>
              <el-form-item label="描述:" prop="description">
                <el-input v-model="formData.description" type="textarea" :rows="3" maxlength="500" show-word-limit placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <div class="title">正文</div>
            </el-col>
            <el-col :span="18">
              <Editor v-model="formData.content" :height="400" type="base64" />
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";
import Editor from "@/components/Editor";

import {
  add,
  update,
} from "@/api/comprehensive-operation/knowledge-base/index";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage } from "element-plus";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  typeOption: {
    type: Array,
    default: () => {
      return [];
    },
  },
});

// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
}

// 表单数据-回显
const formData = computed(() => {
  console.log('props.data:',props.data);
  
  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});


onMounted(async () => {
  fun_getDicts();//获取字典
});

// 获取字典
const fun_getDicts = async () => {
  // const {data:typeOptionData} = await getDict('type');//获取知识库类型
  // typeOption.value = typeOptionData.list;
  // console.log('知识库类型typeOption:',typeOption.value);
}

// 类型
// const typeOption = ref([]);
// 类型选择
const fun_taskTypeChange = (val) => {
  console.log('知识库类型选择:',val);
  // formData.value.bglxmc = taskTypeOption.value.find(item => item.businessValue == val).businessLabel;
}



// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  title: [
    { required: true, message: "请输入", trigger: "blur" },
  ],
  type: [
    { required: true, message: "请选择", trigger: "change" },
  ],
});


// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增") {
        const res = await add(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      } else {
        const res = await update(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      }
    }
  });
};


// 上传附件
/* const action = import.meta.env.VITE_APP_IMG_URL + "/api/platform/system/fileInfo/uploadFile";
const headers = { Authorization: "Bearer " + getToken() };
const beforeAvatarUpload = (file) => {
  const fileExtensions = ["doc", "docx", "pdf"];
  if (formData.value.fileAllList) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传doc,docx,pdf格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("文件大小不能超过5MB");
    return false;
  }
}; */
/**文件上传成功回调 */
// const handleAvatarSuccess = (response, file) => {
//   formData.value.fileList = formData.value.fileAllList.map((e) => {return {fileid:e.response.data.id ?? e.id}});
//   // formData.value.fj = formData.value.fileAllList.map((e) => e.response.data.originalName ?? e.originalName).toString();
// };

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
      .title {
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
}
</style>
