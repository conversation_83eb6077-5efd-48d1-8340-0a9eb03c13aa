<template>
  <div ref="threejsRef" id="threejs"></div>
</template>


<script setup lang="ts">
import {ref, onMounted, onUnmounted, nextTick} from 'vue'

// 导入threejs
import * as THREE from 'three';
// 导入轨道控制器
// import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
// 导入gltf加载器(加载gltf模型)
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
// 导入draco解压缩gltf模型加载器
import { DRACOLoader } from 'three/addons/loaders/DRACOLoader.js';
// 导入hdr加载器(加载hdr贴图)
import { RGBELoader } from 'three/addons/loaders/RGBELoader.js';
// 导入tween补间动画库
import * as TWEEN from 'three/addons/libs/tween.module.js';
// 导入lil.gui(调试开发工具)
import { GUI } from 'three/addons/libs/lil-gui.module.min.js';


// 定义props的 config 的类型
interface ConfigType {
  scaleWidth?: number;//画布(渲染器)和相机宽高比(默认window.innerWidth)
  scaleHeight?: number;//画布(渲染器)和相机宽高比(默认window.innerHeight)
  backgroundColor?: string;//场景背景颜色(默认#333)
  // 相机配置
  camera?: {
    fov?: number;// fov视角(默认50)
    near?: number;// near近平面(默认0.1)
    far?: number;// far远平面(默认2000)
    x?: number;//相机位置x(默认8)
    y?: number;//相机位置y(默认8)
    z?: number;//相机位置z(默认10)
    lookAt?: [number,number,number];//相机指向位置(默认[0,0,0])
  };
  axesHelperShow?: boolean;//是否添加坐标线辅助器(默认false)
  // 轨道控制器配置
  controls?: {
    enableRotate?: boolean;//是否启用旋转(默认true)
    enablePan?: boolean;//是否启用平移(默认true)
    enableZoom?: boolean;//是否启用缩放(默认true)
    autoRotate?: boolean;//是否自动围绕目标旋转(默认false)
    autoRotateSpeed?: number;//自动围绕目标旋转速度(默认2.0)
    enableDamping?: boolean;//是否启用阻尼惯性(默认true)
    dampingFactor?: number;//阻尼惯性值(默认0.1)
  };
  light?:{
    type?: string;//光源类型(默认'ambient'):ambient(环境光),directional(平行光即太阳光),point(点光源),spot(聚光灯)

  };
  
}

const props = defineProps({
  config: {
    type: Object as () => ConfigType, // 定义 config 的类型为 ConfigType,
    // required: true,
  },
})

// 创建渲染器
const renderer = new THREE.WebGLRenderer();
// 设置渲染器输出颜色空间(glTF模型使用)
renderer.outputColorSpace = THREE.SRGBColorSpace;//设置渲染器输出颜色空间-SRGBColorSpace(人类感知色彩光谱范围值空间)
// 设置渲染器宽高
renderer.setSize(props.config?.scaleWidth||window.innerWidth, props.config?.scaleHeight||window.innerHeight);
// 设置渲染器背景透明度为 0
renderer.setClearAlpha(0);//默认黑色0x000000

//创建场景
const scene = new THREE.Scene();
if(props.config?.backgroundColor)scene.background = new THREE.Color(props.config?.backgroundColor);//设置场景背景颜色(默认不设置,可以使用常规颜色仅不支持透明度)
// scene.background = new THREE.TextureLoader().load('/static/threejsData/test1.png');//设置场景背景图片

/* 创建相机 */
let camera:any = null;
function fun_createCamera(){
  camera = new THREE.PerspectiveCamera(
    props.config?.camera?.fov||props.config?.camera?.fov===0?props.config?.camera?.fov:50,// fov垂直视野角度(默认50)
    (props.config?.scaleWidth||window.innerWidth) / (props.config?.scaleHeight||window.innerHeight),//aspect宽高比(默认1)
    props.config?.camera?.near||props.config?.camera?.near===0?props.config?.camera?.near:0.1,// near近平面(默认0.1)
    props.config?.camera?.far||props.config?.camera?.far===0?props.config?.camera?.far:2000,// far远平面(默认2000)
  );
  //设置相机位置
  camera.position.x=props.config?.camera?.x||props.config?.camera?.x===0||8;
  camera.position.y=props.config?.camera?.y||props.config?.camera?.y===0||8;
  camera.position.z=props.config?.camera?.z||props.config?.camera?.z===0||10;
  // camera.lookAt(props.config?.camera?.lookAt?.[0]||0,props.config?.camera?.lookAt?.[1]||0,props.config?.camera?.lookAt?.[2]||0);//有变化不大,在下边轨道控制器上设置效果好
}
fun_createCamera();

// 添加坐标线辅助器
if(props.config?.axesHelperShow){
  const axesHelper = new THREE.AxesHelper( 1000 );
  scene.add( axesHelper );
}

// 创建轨道控制器
const controls = new OrbitControls(camera, renderer.domElement);//在threejs的canvas画布上控制
// const controls = new OrbitControls(camera, document.body);//也可以设置在其他标签上控制threejs
controls.enableRotate = props.config?.controls?.enableRotate||true;//启用旋转(默认true)
controls.enablePan = props.config?.controls?.enablePan||true;//启用平移(默认true)
controls.enableZoom = props.config?.controls?.enableZoom||true;//启用缩放(默认true)
controls.autoRotate = props.config?.controls?.autoRotate||false;//自动围绕目标旋转(默认false)
controls.autoRotateSpeed = props.config?.controls?.autoRotateSpeed||2.0;//自动围绕目标旋转速度(默认2.0)
controls.enableDamping = props.config?.controls?.enableDamping||true; // 设置带阻尼的惯性(默认true)
controls.dampingFactor = props.config?.controls?.dampingFactor||0.1; // 阻尼惯性值(默认0.1)
//设置相机目标视角位置(默认[0,0,0])
controls.target.set(props.config?.camera?.lookAt?.[0]||0,props.config?.camera?.lookAt?.[1]||0,props.config?.camera?.lookAt?.[2]||0);//设置相机目标视角位置(默认[0,0,0])
//轨道控制器事件结束监听
controls.addEventListener('end',()=>{
  console.log('end:',camera.position);
})
/* 添加轨道控制器 */
function fun_addOrbitControls(){
  controls.update();
  renderer.render(scene, camera);//渲染到标签中
  requestAnimationFrame(fun_addOrbitControls);//重复调用当前函数渲染
}
fun_addOrbitControls();
/* 添加轨道控制器 */


// 定义threejs的canvas画布父亲盒子
const threejsRef = ref();
onMounted(()=>{
  threejsRef.value.appendChild(renderer.domElement)
  emit('fun_loaded',renderer,scene,camera);
})

//监听窗口变化-重置演染器和相机宽高比
window.addEventListener("resize",()=>{
  renderer.setSize(props.config?.scaleWidth||window.innerWidth, props.config?.scaleHeight||window.innerHeight);//重置演染器宽高比
  camera.aspect = (props.config?.scaleWidth||window.innerWidth) / (props.config?.scaleHeight||window.innerHeight);//重置相机宽高比
  camera.updateProjectionMatrix();//更新相机投影矩阵
})


/* 添加光源 */
function fun_addLight(type:string='ambient'){
  if(!type||type==='ambient'){
    // 环境光，均匀照亮整个场景
    const ambientLight = new THREE.AmbientLight(0xffffff, 3); // 颜色，强度
    scene.add(ambientLight); 
  }
  else if(type==='directional'){
    // 方向光，模拟太阳光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 3);//颜色，强度
    directionalLight.position.set(0, 1, 1);// 设置光源位置
    // directionalLight.target.position.set(0, 0, 0);// 设置光源目标位置
    scene.add(directionalLight);
  }
  else if(type==='point'){
    // 点光源，模拟灯泡
    const pointLight = new THREE.PointLight(0xffffff, 160);//颜色，强度
    pointLight.position.set(0, 1, 1);// 设置光源位置
    // pointLight.castShadow = true;// 设置光源是否产生阴影
    // pointLight.shadow.mapSize.width = 1024;// 设置阴影贴图的宽度
    // pointLight.shadow.mapSize.height = 1024;// 设置阴影贴图的高度
    scene.add(pointLight);
  }
  else if(type==='spot'){
    // 聚光灯，模拟手电筒
    const spotLight = new THREE.SpotLight(0xffffff, 100, 1000, Math.PI / 6, 0.2, 1);//颜色，强度，距离，角度，半影衰减百分比，衰减半径
    spotLight.position.set(0, 15, 5);// 设置光源位置
    // spotLight.target.position.set(3, 0, 0);// 设置光源目标位置
    scene.add(spotLight);
  }
}
fun_addLight(props.config?.light?.type);//添加光源(默认ambient环境光)
/* 添加光源 */



/* 模型点击交互 */
// 创建射线(光线投射器)
const raycaster = new THREE.Raycaster();
// 创建鼠标向量
const mouse = new THREE.Vector2();
// 监听模型点击
onMounted(()=>{
  threejsRef.value.addEventListener('click',fun_clickModel)
})
// 点击模型方法
function fun_clickModel(event:any){
  // console.log('鼠标位置:',event.clientX,event.clientY);
  const boundClient = threejsRef.value.getBoundingClientRect();
  // console.log('threejsRef.value:',threejsRef.value.offsetWidth,threejsRef.value.offsetHeight,boundClient);
  // 获取鼠标位置
  mouse.x = (event.clientX-boundClient.left) / threejsRef.value.offsetWidth * 2 - 1;
  mouse.y = -( (event.clientY-boundClient.top) / threejsRef.value.offsetHeight * 2 - 1 );
  // console.log('转换后的鼠标位置:',mouse.x,mouse.y);
  // 更新射线
  raycaster.setFromCamera(mouse, camera);
  // 获取射线与场景的交点
  const intersects = raycaster.intersectObjects(scene.children);
  console.log('交点的模型:',intersects);
  // 如果射线与场景的交点存在
  if (intersects.length > 0) {
    const intersect = intersects[0];// 当前交点
    // 递归查找所在组
    let currentParent = intersect.object.parent;
    while (currentParent && !(currentParent instanceof THREE.Group)) {
      currentParent = currentParent.parent;
    }
    if (currentParent) {
      console.log('当前交点对象所在的组:', currentParent);
      if (!currentParent._isSelect) { //自定义选中属性
        console.log('当前交点的对象:',intersect);
        currentParent._isSelect = true;//自定义选中属性
        // currentParent._originColor = intersect.object.material.color.getHex();//自定义源颜色属性
        // intersect.object.material.color.set(0xffff00);//设置选中对象颜色
      }else{
        currentParent._isSelect = false;//自定义选中属性
        // intersect.object.material.color.set(currentParent._originColor);//设置选中对象颜色
      }
      emit('fun_clickModelBack',{modelThis:currentParent,_isSelect:currentParent._isSelect});
    } else {
      console.log('当前交点对象不在任何组中');
      if (!intersect.object._isSelect) { //自定义选中属性
        // console.log('当前交点的对象:',intersect.object);
        intersect.object._isSelect = true;//自定义选中属性
        // intersect.object._originColor = intersect.object.material.color.getHex();//自定义源颜色属性
        // intersect.object.material.color.set(0xffff00);//设置选中对象颜色
      }else{
        intersect.object._isSelect = false;//自定义选中属性
        // intersect.object.material.color.set(intersect.object._originColor);//设置选中对象颜色
      }
      emit('fun_clickModelBack',{modelThis:intersect.object,_isSelect:intersect.object._isSelect});
    }
  }
}
/* 模型点击交互 */


// 自定义函数
function fun_custom(var_fun:Function){
  var_fun({renderer,scene,camera});
}

//销毁
onUnmounted(()=>{
  TWEEN.removeAll();//移除所有tween补间动画
  renderer.dispose();//销毁渲染器
  renderer.domElement.remove();//销毁标签
})

// 返回事件
const emit = defineEmits(['fun_loaded','fun_clickModelBack']);
// 暴露方法
defineExpose({
  fun_custom,//自定义函数
})
</script>


<style scoped>
#threejs{
  width: 100%;
  height: 100%;
}
#threejs>:deep(canvas){
  width: 100%!important;
  height: 100%!important;
}

</style>
