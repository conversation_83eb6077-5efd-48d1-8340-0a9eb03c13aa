<template>
  <div class="pointManage">
    <div class="formBox">
      <el-form :inline="true" :model="filter">
        <el-form-item label="班次名称:">
          <el-input v-model="filter.relationShiftName" placeholder="请输入" clearable />
        </el-form-item>
        <!-- <el-form-item label="时间范围:">
          <el-date-picker v-model="filter.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="fun_dateChange" />
        </el-form-item> -->
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier">
      <div class="main-btns">
        <el-button type="primary" @click="addBtn" :icon="Plus">新增</el-button>
        <el-button type="danger" @click="delAllBtn" :icon="Delete">批量删除</el-button>
      </div>
      <div class="tableBox">
        <Table :data="tableData" :columns="columns" row-key="id" @listSelectChange="handleSelectionChange">
          <template #personnels="{ row }">
            <span>{{ row.personnels.map(item => item.personName).join(',') }}</span>
          </template>
          <template #workStartTime="{ row }">
            <span>{{ row.workStartTime?.split(' ')[0] }}</span>
          </template>
          <template #workEndTime="{ row }">
            <span>{{ row.workEndTime?.split(' ')[0] }}</span>
          </template>
          <template #shiftRules="{ row }">
            <span>{{ shiftRulesOption.find(v=>v.businessValue == row.shiftRules)?.businessLabel }}</span>
          </template>
          <template #operations="{ row }">
            <div class="buttons">
              <div class="btn" @click="handleLook(row)">查看</div>
              <div class="btn" @click="handleEdit(row)">编辑</div>
              <div class="btn" @click="handleDelete(row)">删除</div>
              <!-- <div class="btn" @click="handleDownload(row)">下载</div> -->
            </div>
          </template>
        </Table>
        <el-pagination class="pageBox" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增-编辑 -->
    <addDialog :dialogVisible="addVisiblty" :title="addTitle" :data="addData" @onChangeVisible="addVisiblty = false"
      @refreshList="fun_getList()"></addDialog>
    <!-- 查看 -->
    <lookDialog :dialogVisible="lookVisiblty" :data="lookData" @onChangeVisible="lookVisiblty = false"></lookDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
import addDialog from "./components/add-dialog.vue";
import lookDialog from "./components/look-dialog.vue";

import { getDict } from "@/api/common";
import {
  getScheduleConfigList,
  delScheduleConfig,
} from "@/api/comprehensive-operation/maint-schedule-manage/index";

// 搜索条件
const filter = ref({
  relationShiftName: '',
  dateRange: '',
  workStartTime: '',
  workEndTime: '',
  pageSize: 10,
  pageNum: 1,
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    relationShiftName: "",
    dateRange: "",
    workStartTime: "",
    workEndTime: "",
    pageSize: 10,
    pageNum: 1,
  };
  fun_getList();
};


// 倒班规则
const shiftRulesOption = ref([
  // { businessLabel: "一天一倒", businessValue: 1 },
  // { businessLabel: "三天一倒", businessValue: 2 },
  // { businessLabel: "工作日上班", businessValue: 3 },
]);
// 获取字典
const fun_getDicts = async () => {
  // const {data:shiftRulesOptionData} = await getDict('shift_rules');//倒班规则
  // shiftRulesOption.value = shiftRulesOptionData.list;
  // console.log('倒班规则shiftRulesOption:',shiftRulesOption.value);
}


// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getList();
});

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns);
// 表格数据
const total = ref(0);
const tableData = ref([
  // {id:1,relationShiftName:'桥梁名称',userRelationName:'人员名称',workStartTime:'2024-03-02 10:00:00',workEndTime:'2024-03-02 14:00:00',shiftRules:1},
  // {id:2,relationShiftName:'桥梁名称',userRelationName:'人员名称',workStartTime:'2024-03-02 10:00:00',workEndTime:'2024-03-02 14:00:00',shiftRules:2},
]);
const fun_getList = async () => {
  const { data } = await getScheduleConfigList(filter.value);
  console.log('倒班列表:',data);
  tableData.value = data.list;
  total.value = data.total;
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};


// 日期范围-切换
const fun_dateChange = (val) => {
  console.log('时间范围-时间选择:',val);
  if (val) {
    filter.value.workStartTime = val[0] + " 00:00:00";
    filter.value.workEndTime = val[1] + " 23:59:59";
  } else {
    filter.value.workStartTime = "";
    filter.value.workEndTime = "";
  }
};

// 新增
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref([]);
const addBtn = () => {
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row) => {
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "编辑";
  addVisiblty.value = true;
};

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row) => {
  lookVisiblty.value = true;
  lookData.value = row;
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("删除后不可恢复,确定吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await delScheduleConfig(row.id);
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    fun_getList();
  })
};
// 批量删除
const delIds = ref("");
const handleSelectionChange = (data) => {
  delIds.value = data.map((item) => item.id).join(",");
};
const delAllBtn = () => {
  if (delIds.value) {
    ElMessageBox.confirm("批量删除后不可恢复,确定吗?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        const res = await delScheduleConfig(delIds.value);
        if (res.code === 0) {
          fun_getList();
          ElMessage({
            type: "success",
            message: "批量删除成功",
          });
        } else {
          ElMessage({
            message: res.message,
            type: "warning",
          });
        }
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "取消删除",
        });
      });
  } else {
    ElMessage({
      message: "请选择需要删除的选项",
      type: "warning",
    });
  }
};

// 附件下载
// import { preImageUrl } from '@/utils/index'
// const handleDownload = (row) => {
//   console.log('附件下载:',preImageUrl,row);
//   if (row.fileList && row.fileList.length > 0) {
//     window.open( import.meta.env.VITE_APP_IMG_URL+preImageUrl+row.fileList[0].fileGid);
//   } else {
//     ElMessage({
//       message: "没有附件",
//       type: "warning",
//     });
//   }
// }


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
