export class Columns {
  private typeFormatter
  constructor(init: any) {
    this.typeFormatter = init.typeFormatter
  }
  get columns() {
    return [
      // {
      //   title: "",
      //   align: "center",
      //   type: "selection",
      //   width: 60,
      // },
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "标题",
        key: "title",
        align: "center",
      },
      {
        title: "类型",
        key: "type",
        align: "center",
        // slot: "type",
        formatter: this.typeFormatter
      },
      {
        title: "描述",
        key: "description",
        align: "center",
      },
      {
        title: "发布时间",
        key: "releaseTime",
        align: "center",
      },
      {
        title: "操作",
        slot: "operations",
        align: "center",
        width: 200,
      },
    ];
  }
}
