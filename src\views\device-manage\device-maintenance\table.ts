export class Columns {
  get columns() {
    return [
      // {
      //   title: "",
      //   align: "center",
      //   type: "selection",
      //   width: 60,
      // },
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "设备编号",
        key: "gid",
        align: "center",
      },
      {
        title: "设备名称",
        key: "sbmc",
        align: "center",
      },
      {
        title: "设备类型",
        key: "sblx",
        align: "center",
      },
      {
        title: "安装位置",
        key: "sbwz",
        align: "center",
      },
      // {
      //   title: "生产厂家",
      //   key: "scjc",
      //   align: "center",
      // },
      {
        title: "养护时限",
        key: "yhsx",
        slot: "yhsx",
        align: "center",
      },
      {
        title: "巡检次数",
        key: "xcNum",
        slot: "xcNum",
        align: "center",
      },
      {
        title: "养护次数",
        key: "yhNum",
        slot: "yhNum",
        align: "center",
      },
      {
        title: "维修次数",
        key: "wxNum",
        slot: "wxNum",
        align: "center",
      },
      // {
      //   title: "操作",
      //   slot: "operations",
      //   align: "center",
      //   width: 160,
      // },
    ];
  }
}
