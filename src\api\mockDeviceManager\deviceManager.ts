import request from '@/utils/request';
// import { ContentType, Method, RequestParams } from 'axios-mapper';

const apiPrefix = '/api/iot';


// 分页查询
export function byPage(params: any) {
  return request({
    url: apiPrefix + '/center/analogDeviceInfo/byPage',
    method: 'post',
    data: {...params},
  })
}

// 添加物联设备
export function addDeviceItem(params: any) {
  return request({
    url: apiPrefix + '/center/analogDeviceInfo/addAnalogDeviceInfo',
    method: 'post',
    data: {...params},
  })
}

// 编辑物联设备
export function editDeviceItem(params: any) {
  return request({
    url: apiPrefix + '/center/analogDeviceInfo/editAnalogDeviceInfo',
    method: 'post',
    data: {...params},
  })
}

// 删除物联设备
export function deleteDeviceItem(params: any) {
  return request({
    url: apiPrefix + '/center/analogDeviceInfo/deleteByIds?ids=' + params.ids,
    method: 'delete',
  })
}

// 物联设备上线
export function onlineDeviceItem(params: any) {
  return request({
    url: apiPrefix + '/center/analogDeviceInfo/deviceOnline',
    method: 'post',
    data: {...params},
  })
}

// 物联设备离线
export function offlineDeviceItem(params: any) {
  return request({
    url: apiPrefix + '/center/analogDeviceInfo/deviceOutline',
    method: 'get',
    params,
  })
}


// 根据类型查询设施设备
export function getMonitorItemByType(params: any) {
  return request({
    url: apiPrefix + '/center/deviceMonitorItem/getTreeByType',
    method: 'get',
    params,
  })
}

// 查找监测项
export function getMonitorItem(params: any) {
  return request({
    url: apiPrefix + '/center/deviceMonitorItem/getByParentGid',
    method: 'get',
    params,
  })
}

// 设备告警查询
export function getAlaramRuleByDeviceId(params: any) {
  return request({
    url: apiPrefix + '/center/deviceAlarmRule/getByDeviceId',
    method: 'get',
    params,
  })
}

// 新增设备告警规则配置
export function addAlaramRule(params: any) {
  return request({
    url: apiPrefix + '/center/deviceAlarmRule/addDeviceAlarmRule',
    method: 'post',
    data: {...params},
  })
}

// 修改设备告警规则配置
export function editAlaramRule(params: any) {
  return request({
    url: apiPrefix + '/center/deviceAlarmRule/editDeviceAlarmRule',
    method: 'post',
    data: {...params},
  })
}

// 查看物联数据
export function getMockData(params: any) {
  return request({
    url: apiPrefix + '/datasearch/deviceInfo/byPage',
    method: 'post',
    data: {...params},
  })
}


