export class Columns {
  get columns() {
    return [
      {
        title: "",
        align: "center",
        type: "selection",
        width: 60,
      },
      {
        title: "序号",
        align: "center",
        type: "index",
        width: 80,
      },
      {
        title: "班次名称",
        key: "relationShiftName",
        align: "center",
      },
      {
        title: "人员名称",
        key: "personnels",
        slot: "personnels",
        align: "center",
      },
      {
        title: "开始日期",
        key: "workStartTime",
        slot: "workStartTime",
        align: "center",
      },
      {
        title: "结束日期",
        key: "workEndTime",
        slot: "workEndTime",
        align: "center",
      },
      {
        title: "倒班规则",
        key: "shiftRules",
        slot: "shiftRules",
        align: "center",
      },
      // {
      //   title: "创建时间",
      //   key: "createTime",
      //   align: "center",
      // },
      {
        title: "操作",
        slot: "operations",
        align: "center",
        width: 200,
      },
    ];
  }
}
