import { useVueFlow } from '@vue-flow/core'
import { ref, watch } from 'vue'


// let id = 0
/**
 * @returns {string} - A unique id.
 */
// function getId() {
//   return `dndnode_${id++}`
// }

/**
 * In a real world scenario you'd want to avoid creating refs in a global scope like this as they might not be cleaned up properly.
 * @type {{draggedType: Ref<string|null>, isDragOver: Ref<boolean>, isDragging: Ref<boolean>}}
 */
const state = {
  /**
   * The type of the node being dragged.
   */
  draggedType: ref(null),
  isDragOver: ref(false),
  isDragging: ref(false),
  customOptions: ref(null),
}

export default function useDragAndDrop() {
  const { draggedType, isDragOver, isDragging, customOptions } = state


  const { addNodes, screenToFlowCoordinate, onNodesInitialized, updateNode } = useVueFlow()

  watch(isDragging, (dragging) => {
    document.body.style.userSelect = dragging ? 'none' : ''
  })

  function onDragStart(event, type, options) {
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/vueflow', type)
      event.dataTransfer.effectAllowed = 'move'
    }

    draggedType.value = type
    isDragging.value = true
    customOptions.value = options

    document.addEventListener('drop', onDragEnd)
  }

  /**
   * Handles the drag over event.
   *
   * @param {DragEvent} event
   */
  function onDragOver(event) {
    event.preventDefault()

    if (draggedType.value) {
      isDragOver.value = true

      if (event.dataTransfer) {
        event.dataTransfer.dropEffect = 'move'
      }
    }
  }

  function onDragLeave() {
    isDragOver.value = false
  }

  function onDragEnd() {
    isDragging.value = false
    isDragOver.value = false
    draggedType.value = null
    customOptions.value = null
    document.removeEventListener('drop', onDragEnd)
  }

  /**
   * Handles the drop event.
   *
   * @param {DragEvent} event
   */
  function onDrop(event) {
    const position = screenToFlowCoordinate({
      x: event.clientX,
      y: event.clientY,
    })

    // const nodeId = `dndnode_${crypto.randomUUID()}`;
    const nodeId = generateRandomString(20);
    const newNode = {
      id: customOptions.value.id||nodeId, //传入ID则面板每个内容只可拖拽到画布一次,不传ID则可多次拖拽(使用传入的唯一ID,不传则生成随机节点ID)
      // id: nodeId, //面板每个内容可多次拖拽到画布中(生成随机节点ID)
      type: draggedType.value,
      position,
      data: {
        id: customOptions.value.id||nodeId,
        label: customOptions.value.label||'',
      },
    }

    // 为自定义节点类型添加额外数据
    if (draggedType.value === 'custom') {
      newNode.data = {
        ...newNode.data,
        ...customOptions.value,
        description: `这是一个自定义节点`,
        type: 'custom',
      }
    }

    /**
     * Align node position after drop, so it's centered to the mouse
     *
     * We can hook into events even in a callback, and we can remove the event listener after it's been called.
     */
    const { off } = onNodesInitialized(() => {
      updateNode(nodeId, (node) => ({
        position: { x: node.position.x - node.dimensions.width / 2, y: node.position.y - node.dimensions.height / 2 },
      }))

      off()
    })

    addNodes(newNode)
  }

  return {
    draggedType,
    isDragOver,
    isDragging,
    onDragStart,
    onDragLeave,
    onDragOver,
    onDrop,
  }
}


function generateRandomString(length) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  
  return result;
}