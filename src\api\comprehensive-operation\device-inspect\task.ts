import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';

// 获取设备
export function getDeviceTree(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/deviceInfo/getDeviceTree',
    method: 'get',
    params: {...query},
  })
}


// 获取巡检项
export function getInspectionItem(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tInspectionItems/byPage',
    method: 'post',
    data: {...query}
  })
}
// 获取人员
export function getPersonnelTree(query: any) {
  return request({
    url: baseRouter + '/system/user/deptUserTree',
    method: 'get',
    params: {...query},
  })
}

// 获取小组
export function getGroupOptions(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMeInspectionteam/byPage',
    method: 'post',
    data: {...query}
  })
}

// 巡检任务-新增
export function add(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMeInspectionplan/addTMeInspectionplan',
    method: 'post',
    data: data
  })
}

// 巡检任务-修改
export function update(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMeInspectionplan/editTMeInspectionplan',
    method: 'post',
    data: data
  })
}

// 巡检任务-删除
export function del(delIds: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMeInspectionplan/deleteByIds',
    method: 'delete',
    params: { ids: delIds },
  })
}

// 巡检任务-列表
export function getList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMeInspectionplan/byPage',
    method: 'post',
    data: {...query}
  })
}

// 巡检任务- 详情中巡检项
export function getPlanList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tPlanDetail/byPage',
    method: 'post',
    data: {...query}
  })
}
// 巡检任务- 巡检提交
export function completeTMeInspectionplan(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMeInspectionplan/completeTMeInspectionplan',
    method: 'post',
    data: {...query}
  })
}
// 巡检任务- 详情中隐患和养护 remark字段区分弹窗
export function getMaintenance(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintenance/byPage',
    method: 'post',
    data: {...query}
  })
}

// 巡检任务- 详情中养护弹窗
export function getMaintain(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/tMaintain/byPage',
    method: 'post',
    data: {...query}
  })
}



// 查询安全评估-技术状况报告详细
// export function getReportInfo(gid: any) {
//   return request({
//     url: baseRouter + '/business/deviceInspect/getInfo',
//     method: 'get',
//     params: { gid },
//   })
// }


