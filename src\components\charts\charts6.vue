<template>
  <div ref="refChart" class="chart-container"></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, watch, markRaw } from "vue";
import * as echarts from "echarts";
import { getTransferPx } from "@/utils/px2rem";

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

const refChart = ref<any>();
const chartInstance = ref();
const chartsData: any = ref({});

const OptityBarBgColor = "#daeaff";

onMounted(() => {
  watch(
    () => props.data,
    (val) => {
      if (val) {
        chartsData.value = val;
        initChart();
      }
    },
    {
      deep: true,
      immediate: true,
    }
  );
});

onBeforeUnmount(() => {
  chartInstance.value.dispose();
  chartInstance.value = null;
});

const initChart = () => {
  chartInstance.value = markRaw(echarts.init(refChart.value));

  const initOption = {
    backgroundColor: "#fff",
    title: {
      text: "桥梁完好情况",
      top: getTransferPx(10),
      left: getTransferPx(18),
      textStyle: {
        color: "#858B99",
        fontSize: getTransferPx(15),
      },
    },
    grid: {
      left: getTransferPx(25),
      right: getTransferPx(20),
      bottom: "10%",
      top: getTransferPx(55),
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
    },
    xAxis: {
      type: "value",
      min: 0,
      max: 200,
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "#eaeaea",
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: getTransferPx(15),
      },
    },
    yAxis: {
      type: "category",
      data: chartsData.value.title, // 如 ["合格", "A", "B"]
      axisLine: {
        show: true,
        lineStyle: {
          color: "#e5e5e5",
        },
      },
      offset: 20,
      axisTick: {
        show: false,
        length: 9,
        alignWithLabel: true,
        lineStyle: {
          color: "#858B99",
        },
      },
      axisLabel: {
        fontSize: getTransferPx(15),
        color: "#858B99",
      },
    },
    series: [
      {
        type: "bar",
        data: chartsData.value.value,
        // tooltip: {
        //   show: false,
        // },
        label: {
          normal: {
            show: false,
          },
        },
        itemStyle: {
          color: "#daeaff",
        },
      },
    ],
  };

  chartInstance.value.setOption(initOption);

  setTimeout(function () {
    window.addEventListener("resize", () => {
      chartInstance.value?.resize();
    });
  }, 200);
};
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>