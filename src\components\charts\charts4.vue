<template>
  <div ref="refChart" class="chart-container"></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, watch, markRaw } from "vue";
import * as echarts from "echarts";
import { getTransferPx } from "@/utils/px2rem";

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

const refChart = ref<any>();
const chartInstance = ref();
const chartsData: any = ref({});
// 柱体左面
const CubeLeft = echarts.graphic.extendShape({
  shape: {
    x: 0,
    y: 0,
  },
  buildPath: function (ctx: any, shape: any) {
    const xAxisPoint = shape.xAxisPoint;
    const c0 = [shape.x, shape.y]; // 右上点
    const c1 = [shape.x - 12, shape.y - 5]; // 左上点
    const c2 = [xAxisPoint[0] - 12, xAxisPoint[1] - 6]; // 左下点
    const c3 = [xAxisPoint[0], xAxisPoint[1]]; // 右下点
    ctx
      .moveTo(c0[0], c0[1])
      .lineTo(c1[0], c1[1])
      .lineTo(c2[0], c2[1])
      .lineTo(c3[0], c3[1])
      .closePath();
  },
});
// 柱体右面
const CubeRight = echarts.graphic.extendShape({
  shape: {
    x: 0,
    y: 0,
  },
  buildPath: function (ctx: any, shape: any) {
    const xAxisPoint = shape.xAxisPoint;
    const c1 = [shape.x, shape.y]; // 左上点
    const c2 = [xAxisPoint[0], xAxisPoint[1]]; // 左下点
    const c3 = [xAxisPoint[0] + 20, xAxisPoint[1] - 6]; // 右下点
    const c4 = [shape.x + 20, shape.y - 3.5]; // 右上点
    ctx
      .moveTo(c1[0], c1[1])
      .lineTo(c2[0], c2[1])
      .lineTo(c3[0], c3[1])
      .lineTo(c4[0], c4[1])
      .closePath();
  },
});
// 柱体顶面
const CubeTop = echarts.graphic.extendShape({
  shape: {
    x: 0,
    y: 0,
  },
  buildPath: function (ctx: any, shape: any) {
    const c1 = [shape.x, shape.y]; // 下点
    const c2 = [shape.x + 20, shape.y - 4]; // 右点
    const c3 = [shape.x + 5, shape.y - 10]; // 上点
    const c4 = [shape.x - 12, shape.y - 5]; // 左点
    ctx
      .moveTo(c1[0], c1[1])
      .lineTo(c2[0], c2[1])
      .lineTo(c3[0], c3[1])
      .lineTo(c4[0], c4[1])
      .closePath();
  },
});
echarts.graphic.registerShape("CubeLeft", CubeLeft);
echarts.graphic.registerShape("CubeRight", CubeRight);
echarts.graphic.registerShape("CubeTop", CubeTop);

const OptityBarBgColor = "#daeaff";

onMounted(() => {
  watch(
    () => props.data,
    (val) => {
      if (val) {
        chartsData.value = val;
        initChart();
      }
    },
    {
      deep: true,
      immediate: true,
    }
  );
});

onBeforeUnmount(() => {
  // 销毁图表
  chartInstance.value.dispose();
  chartInstance.value = null;
});

const initChart = () => {
  chartInstance.value = markRaw(echarts.init(refChart.value));

  const initOption = {
    // 图表背景大图
    backgroundColor: "#fff",
    // 标题
    title: {
      text: "单位:个",
      top: getTransferPx(10),
      left: getTransferPx(18),
      textStyle: {
        color: "#858B99",
        fontSize: getTransferPx(15),
      },
    },
    // 控制图片位置偏移
    grid: {
      left: getTransferPx(25),
      right: getTransferPx(20),
      bottom: "10%",
      top: getTransferPx(55),
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
    },
    xAxis: {
      type: "category",
      data: chartsData.value.title,
      axisLine: {
        show: true,
        lineStyle: {
          color: "#e5e5e5",
        },
      },
      offset: 20,
      axisTick: {
        show: false,
        length: 9,
        alignWithLabel: true,
        lineStyle: {
          color: "#858B99",
        },
      },
      axisLabel: {
        fontSize: getTransferPx(15),
        color: "#858B99",
        formatter: function (params: any) {
          var newParamsName = ""; // 最终拼接成的字符串
          var paramsNameNumber = params.length; // 实际标签的个数
          var provideNumber = 4; // 每行能显示的字的个数
          var rowNumber = Math.ceil(paramsNameNumber / provideNumber); // 换行的话，需要显示几行，向上取整
          /**
           * 判断标签的个数是否大于规定的个数， 如果大于，则进行换行处理 如果不大于，即等于或小于，就返回原标签
           */
          // 条件等同于rowNumber>1
          if (paramsNameNumber > provideNumber) {
            /** 循环每一行,p表示行 */
            for (var p = 0; p < rowNumber; p++) {
              var tempStr = ""; // 表示每一次截取的字符串
              var start = p * provideNumber; // 开始截取的位置
              var end = start + provideNumber; // 结束截取的位置
              // 此处特殊处理最后一行的索引值
              if (p == rowNumber - 1) {
                // 最后一次不换行
                tempStr = params.substring(start, paramsNameNumber);
              } else {
                // 每一次拼接字符串并换行
                tempStr = params.substring(start, end) + "\n";
              }
              newParamsName += tempStr; // 最终拼成的字符串
            }
          } else {
            // 将旧标签的值赋给新标签
            newParamsName = params;
          }
          //将最终的字符串返回
          return newParamsName;
        },
      },
    },
    yAxis: {
      type: "value",
      min: 0,
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "#eaeaea",
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: getTransferPx(15),
      },
      boundaryGap: ["20%", "20%"],
    },
    series: [
      // 透明的柱体，和背景色是同色系的
      {
        type: "custom",
        data: chartsData.value.max,
        tooltip: {
          show: false,
        },
        renderItem: function (params: any, api: any) {
          const location = api.coord([api.value(0), api.value(1)]);
          return {
            type: "group",
            children: [
              {
                type: "CubeLeft",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: OptityBarBgColor,
                },
              },
              {
                type: "CubeRight",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: "#daeaff",
                },
              },
              {
                type: "CubeTop",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: "#daeaff",
                },
              },
            ],
          };
        },
      },
      // 显示柱体文本
      {
        type: "bar",
        data: chartsData.value.max,
        tooltip: {
          show: false,
        },
        label: {
          normal: {
            show: false,
          },
        },
        itemStyle: {
          color: "transparent",
        },
      },
      // 实际显示的柱体
      {
        type: "custom",
        data: chartsData.value.value,
        tooltip: {},
        renderItem: (params: any, api: any) => {
          const location = api.coord([api.value(0), api.value(1)]);
          return {
            type: "group",
            children: [
              {
                type: "CubeLeft",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: "#529aff",
                },
              },
              {
                type: "CubeRight",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: "#76afff",
                },
              },
              {
                type: "CubeTop",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: "#3388FF",
                },
              },
            ],
          };
        },
      },
    ],
  };

  // 图表初始化配置
  chartInstance.value.setOption(initOption);

  setTimeout(function () {
    window.addEventListener("resize", () => {
      chartInstance.value?.resize();
    });
  }, 200);
};
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
