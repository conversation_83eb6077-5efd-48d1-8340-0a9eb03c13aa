<template>
  <div class="header-container">
    <div class="left">
      <weather class="weather"></weather>
    </div>
    <div class="left-menu-container">
      <div
        v-for="(item, index) in leftMenus"
        :class="`menu${index + 1} ${type === item.type ? 'menu-sel' : ''}`"
        @click="onMenuClick(item)"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="right-menu-container">
      <div
        v-for="(item, index) in rightMenus"
        :class="`menu${index + 1} ${type === item.type ? 'menu-sel' : ''}`"
        @click="onMenuClick(item)"
      >
        {{ item.name }}
      </div>
      <div class="menu5" @click="showMore">更多专项</div>
    </div>
    <div class="title">{{ title }}</div>
    <div class="right">
      <div class="user-info">
        <div class="user-icon">
          <User />
        </div>
        <div class="user-name" :title="getUserRoleName">
          {{ getUserRoleName() }}
        </div>
        <div class="user-tail-icon">
          <CaretBottom />
        </div>

        <!-- <div class="close">
          <i class="iconfont icontuichu" @click="logout"></i>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import useUserStore from "@/store/modules/user";
import Weather from "@/views/special/common/module/weather.vue";
import { defineEmits } from "vue";

const emits = defineEmits(["menuSelected", "showMore"]);

const props = defineProps({
  title: {
    type: String,
    required: true,
    default: "燃气安全运行监测",
  },
  type: {
    type: Number,
    required: true,
    default: 1,
  },
  leftMenus: {
    type: Object,
    default: [],
  },
  rightMenus: {
    type: Object,
    default: [],
  },
});

// 获取用户名和角色
const getUserRoleName: any = () => {
  return useUserStore().name;
};

const onMenuClick = (item: any) => {
  emits("menuSelected", item);
};

const showMore = () => {
  emits("showMore");
};
</script>

<style lang="scss" scoped>
.header-container {
  position: absolute;
  width: 100%;
  height: 53px;
  background: url(../../assets/images/bg.png) no-repeat;
  background-size: 100% 100%;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  background-size: cover;
  background-repeat: no-repeat;

  .title {
    display: flex;
    justify-content: center;
    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
    font-weight: 400;
    font-size: 43px;
    color: #ffffff;
    line-height: 26px;
    letter-spacing: 9px;
    padding-top: 5px;
    margin-top: 10px;
  }

  .left-menu-container {
    position: absolute;
    height: 43px;
    left: 168px;
    top: 10px;

    .menu5,
    .menu1,
    .menu2,
    .menu3,
    .menu4 {
      width: 133px;
      height: 43px;
      background: url(../../assets/images/l-btn.png);
      background-size: cover;
      background-repeat: no-repeat;
      position: absolute;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 20px;
      color: #ffffff;
      padding: 7px 0 0 28px;
      cursor: pointer;
    }

    .menu-sel {
      width: 133px;
      height: 43px;
      background: url(../../assets/images/l-btn-sel.png);
      background-size: cover;
      background-repeat: no-repeat;
    }

    .menu1 {
      left: 0;
      top: 0px;
    }

    .menu2 {
      left: 105px;
      top: 0px;
    }

    .menu3 {
      left: 210px;
      top: 0px;
    }
    .menu4 {
      left: 315px;
      top: 0px;
    }

    .menu5 {
      left: 420px;
      top: 0px;
    }
  }

  .right-menu-container {
    position: absolute;
    right: 168px;
    top: 10px;
    height: 43px;

    .menu1,
    .menu2,
    .menu3,
    .menu4,
    .menu5 {
      width: 133px;
      height: 43px;
      background: url(../../assets/images/r-btn.png);
      background-size: cover;
      background-repeat: no-repeat;
      position: absolute;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 20px;
      color: #ffffff;
      padding: 7px 0 0 32px;
      cursor: pointer;
    }

    .menu-sel {
      background: url(../../assets/images/r-btn-sel.png);
    }

    .menu5 {
      right: 0px;
      top: 0px;
    }

    .menu4 {
      right: 105px;
      top: 0px;
    }

    .menu3 {
      right: 210px;
      top: 0px;
    }

    .menu2 {
      right: 315px;
      top: 0px;
    }

    .menu1 {
      right: 420px;
      top: 0px;
    }
  }

  .left {
    padding-left: 16px;
    padding-top: 3px;
    width: 300px;
  }

  .right {
    display: flex;
    justify-content: flex-end;
    padding-right: 16px;
    width: 300px;
    padding-top: 3px;

    .user-info {
      display: flex;
      height: 24px;
      font-family: SourceHanSansCN-Regular;
      font-weight: 400;
      font-size: 13px;
      color: #ffffff;
      margin-top: 10px;
      cursor: pointer;
      .user-icon {
        width: 30px;
        height: 30px;
        background: rgba(51, 255, 248, 0.1505);
        // opacity: 0.1505;
        border-radius: 50%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-right: 5px;

        svg {
          color: #33fff8ff;
          width: 20px;
          height: 20px;
        }
      }

      .user-name {
        font-size: 20px;
        margin: 0 3px;
      }

      .user-tail-icon {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-bottom: 4px;

        svg {
          color: #33fff8ff;
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
</style>
