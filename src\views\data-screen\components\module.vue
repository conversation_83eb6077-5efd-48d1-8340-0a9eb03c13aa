<template>
  <div class="module-wrapper" :style="{ height: height + 'px' }">
    <div class="module-header">
      <div class="module-title">{{ title }}</div>
      <slot name="header"></slot>
    </div>
    <div class="module-body">
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
import { defineProps, toRefs } from "vue";
const props = defineProps({
  title: String,
  height: Number,
});
const { title, height } = toRefs(props);
</script>
<style lang="scss" scoped>
.module-wrapper {
  width: 456px;
  padding: 8px 16px 0;
  background: rgba(2, 7, 22, 0.32);
  box-shadow: inset 0px 4px 8px 0px rgba(255, 255, 255, 0.16),
    0px 4px 12px 0px rgba(129, 164, 255, 0.24);
  border-radius: 8px 8px 8px 8px;
  border: 2px solid;
  border-image: linear-gradient(
      255deg,
      rgba(64, 249, 255, 0),
      rgba(64, 249, 255, 0.64)
    )
    2 2;
  .module-header {
    width: 100%;
    height: 37px;
    background: url(@/assets/data-screen/module-header-bg.png) no-repeat center
      center;
    background-size: auto 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .module-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: bold;
      font-size: 20px;
      color: #ffffff;
      text-transform: none;
    }
  }
  .module-body {
    height: calc(100% - 37px);
    padding: 16px 0;
    overflow: hidden;
  }
}
</style>
