<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form label-width="112" :inline="true" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <el-form-item label="安装时间:">
            <el-date-picker v-model="formData.azsj" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" clearable placeholder="请选择" :disabled="formData.type=='onlyEditDevice'" />
          </el-form-item>
          <el-form-item label="养护时限:" prop="yhsx">
            <div class="width-all flex">
              <el-input v-model="formData.yhsx" type="number" clearable placeholder="请输入" :disabled="formData.type=='onlyEditDevice'" />
              <span class="flex-item-shrink-0 padd-l-5">天/次</span>
            </div>
          </el-form-item>
          <el-form-item label="厂家名称:" prop="cjmc">
            <el-input v-model="formData.cjmc" clearable placeholder="请输入" :disabled="formData.type=='onlyEditDevice'" />
          </el-form-item>

          <el-form-item label="选择设备:" prop="equipmentIds" style="display: flex;">
            <el-transfer v-model="formData.equipmentIds" :data="devicesTransfer" :props="{label:'equipmentName',key:'id'}" :titles="['设备列表', '已选设备']" @change="fun_devicesChange">
              <template #right-empty>
                <span>未选择</span>
              </template>
            </el-transfer>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive, watch } from "vue";

import {
  getDeviceConnectInfo,
  getDeviceOptions,
  addDeviceConnect,
  updateDeviceConnect,
} from "@/api/device-manage/index";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage, ElMessageBox } from "element-plus";
import _ from "lodash";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
}

// 表单数据-回显
const formData = computed( () => {
  console.log('props.data:',props.data);
  //设置初始默认状态
  if(props.data.equipmentIds===undefined)props.data.equipmentIds = [];//设备

  return props.data;
});
// 获取详情中回显的已选设备列表-回显
watch( ()=>props.data.gid, async (newVal, oldVal) => {
  console.log('props.data.gid:',newVal,oldVal);
  const {data} = await getDeviceConnectInfo({gid: newVal});
  console.log('设备关联详情:',data?.equipmentInfos);
  props.data.equipmentIds = data?.equipmentInfos.map(item => item.gid)||[];
});

const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});


onMounted(() => {
  // fun_getDicts();//获取字典
  if(props.data.gid)fun_getInfo();//获取详情中回显的已选设备列表
  fun_getdevicesTransfer();//获取设备
});

// 获取字典
// const fun_getDicts = async () => {
//   const {data:cgqlxOptionData} = await getDict('cgqLX');//获取传感器类型
//   cgqlxOption.value = cgqlxOptionData.list;
//   console.log('传感器类型cgqlxOption:',cgqlxOption.value);
// }

// 获取设备
const devicesTransfer = ref([
  // { equipmentName: '设备1', id: '1'},
  // { equipmentName: '设备2', id: '2'},
  // { equipmentName: '设备3', id: '3'},
  // { equipmentName: '设备4', id: '4'},
  // { equipmentName: '设备5', id: '5'},
])
const fun_getdevicesTransfer = async () => {
  const {data} = await getDeviceOptions()
  console.log('获取设备列表devicesTransfer:',data);
  devicesTransfer.value = data;
}
// 设备选择
const fun_devicesChange = (val) => {
  console.log('设备选择:',val);
  // formData.value.devicesbmStr = val.join(',');
  // formData.value.devicesmcStr = val.map(item => devicesTransfer.value.find(item => item.value === item).label).join(',')
}

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  azsj: [
    { required: true, message: "请选择", trigger: "blur" },
  ],
  yhsx: [
    { required: false, message: "请输入", trigger: "blur" },
    { validator: fun_isPositiveInteger},//正整数
  ],
  cjmc: [
    { required: false, message: "请输入", trigger: "blur" },
  ],
  equipmentIds: [
    { required: false, message: "请选择", trigger: "blur" },
    { validator: (rule, value, callback)=>{
        if(value.length==0){
          callback(new Error('请选择设备'));
        }else{
          callback();
        }
      }, 
      trigger: 'change' 
    },
    // { validator: (rule, value, callback) => {
    //   if (value.length > 10) {
    //     callback(new Error('最多选择10个'));
    //   } else {
    //     callback();
    //   }
    // }, trigger: 'change' },
  ]
});
// 正则正整数
function fun_isPositiveInteger(rule, value, callback) {
  if (value!=''&&!/^[1-9]\d*$/.test(value)) {
    callback(new Error('请输入正整数'));
  } else {
    callback();
  }
}

// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增") {
        const res = await addDeviceConnect(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
      } else {
        let res = {};
        if(formData.value.type=='onlyEditDevice'){
          res = await updateDeviceConnect(formData.value);//关联设备编辑
        }else{
          res = await updateDeviceConnect(formData.value);//编辑
        }
        ElMessage({
          message: res.message,
          type: "success",
        });
      }
      emits("refreshList");
      emits("onChangeVisible");
    }
  });
};

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
