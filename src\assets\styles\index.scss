@import "./variables.module.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./btn.scss";
@import "./ruoyi.scss";
@import "./common.scss";

body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: SourceHanSansCN-Normal;
  overflow: hidden;
}

@media screen and (min-width: 960px) and (max-width: 1299px) {
  html {
      font-size: 546.875%;
  }

  /*14px */
}

@media screen and (min-width: 1300px) and (max-width: 1499px) {
  html {
      font-size: 625%;
  }

  /*16px */
}

@media screen and (min-width: 1500px) and (max-width:1650) {
  html {
      font-size: 703.125%;
  }

  /*18px */
}

@media screen and (min-width: 1651px) and (max-width: 1800px) {
  html {
      font-size: 780%;
  }

  /*20px */
}

@media screen and (min-width: 1801px) and (max-width: 1919px) {
  html {
      font-size: 858%;
  }
  /* 22px */
}

@media screen and (min-width: 1920px) and (max-width: 2879px) {
  html {
      font-size: 937.5%;
  }
  /* 24px */
}

@media screen and (min-width: 2880px) {
  html {
      font-size: 1125%;
  }
  /* 28px */
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: inline-block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}
a,
button,
h1,
h2,
h3,
h4,
h5,
h6,
input,
ol,
p,
textarea,
th,
ul {
  background: transparent;
  border: 0;
  border-radius: 0;
  font: inherit;
  list-style: none;
  margin: 0;
  outline: 0;
  overflow: visible;
  padding: 0;
  vertical-align: baseline;
}

a {
  color: #2675eb;
  background: transparent;
  text-decoration: none;
  outline: none;
  cursor: pointer;
  transition: color 0.2s ease;
}

a:active,
a:hover {
  outline-width: 0;
}

a:hover {
  color: #57a3f3;
}

a:active {
  color: #2b85e4;
}

a:active,
a:hover {
  outline: 0;
  text-decoration: none;
}



/* 2025-02-27 - jswn */
img{user-select: none;}
.hover{cursor: pointer;user-select: none;}
.no-hover{cursor: no-drop;user-select: none;}
.pointer-passe{pointer-events: none;} /*鼠标点击穿透*/
.pointer-none{pointer-events: auto;} /*鼠标点击不穿透*/
.user-none{user-select: none;}
.user-text{user-select: text;}

.fl{float: left;}
.fr{float: right;}
.float-left{float: left;}
.float-right{float: right;}
.clear{clear:both;}
.clearfix{zoom:1;}
.clearfix:after{visibility:hidden;display:block;content:" ";clear:both;}

.none{display: none;}
.hide{visibility: hidden;}
.visib{visibility: visible;}
.block{display: block;}
.inblock{display: inline-block;vertical-align: top;}
.inblock-middle{display: inline-block;vertical-align: middle;}
.inblock-bottom{display: inline-block;vertical-align: bottom;}

.text-l{text-align: left;}
.text-r{text-align: right;}
.text-left{text-align: left;}
.text-center{text-align: center;}
.text-right{text-align: right;}
.text-both{text-align: justify;}

.text-indent{text-indent: 2em;}

.flex{display: flex;}
.inflex{display: inline-flex;}
.flex-row{flex-direction: row;} /* 行从左到右排序(默认) */
.flex-row-r{flex-direction: row-reverse;} /* 行从右到左排序 */
.flex-col{flex-direction: column;} /* 列从上到下排序 */
.flex-col-b{flex-direction: column-reverse;} /* 列从下到上排序 */
.flex-wrap{flex-wrap: wrap;}
.flex-left{justify-content:flex-start;}
.flex-center{justify-content:center;}
.flex-right{justify-content:flex-end;}
.flex-even{justify-content:space-evenly;}
.flex-around{justify-content:space-around;}
.flex-both{justify-content:space-between;}
.flex-top{align-items: flex-start;}
.flex-middle{align-items: center;}
.flex-bottom{align-items: flex-end;}

.flex-item-top{align-self: flex-start;}
.flex-item-middle{align-self: center;}
.flex-item-bottom{align-self: flex-end;}
.flex-item-grow-1{flex-grow: 1;} /* 存在剩余空间将放大 */
.flex-item-shrink-0{flex-shrink: 0;} /* 空间不足将不缩小 */
.flex-item-order-0{order: 0;} /* 单独排序(默认:0) */
.flex-item-order-1{order: 1;} /* 单独排序 */
.flex-item-order-2{order: 2;} /* 单独排序 */

.grid{display: grid;}
.ingrid{display: inline-grid;}
.grid-row{grid-auto-flow: row;} /* 先行后列排序(默认) */
.grid-col{grid-auto-flow: column;} /* 先列后行排序 */
.grid-temp-col-12{grid-template-columns: repeat(12,1fr);}
.grid-temp-row-12{grid-template-rows: repeat(12,1fr);}
/* (项目上)网格单个项目-容纳几个网格 */
.grid-span-1{grid-column-start: span 1;}
.grid-span-2{grid-column-start: span 2;}
.grid-span-3{grid-column-start: span 3;}
.grid-span-4{grid-column-start: span 4;}
.grid-span-5{grid-column-start: span 5;}
.grid-span-6{grid-column-start: span 6;}
.grid-span-7{grid-column-start: span 7;}
.grid-span-8{grid-column-start: span 8;}
.grid-span-9{grid-column-start: span 9;}
.grid-span-10{grid-column-start: span 10;}
.grid-span-11{grid-column-start: span 11;}
.grid-span-12{grid-column-start: span 12;}
.grid-span-1-h{grid-row-start: span 1;}
.grid-span-2-h{grid-row-start: span 2;}
.grid-span-3-h{grid-row-start: span 3;}
.grid-span-4-h{grid-row-start: span 4;}
.grid-span-5-h{grid-row-start: span 5;}
.grid-span-6-h{grid-row-start: span 6;}
.grid-span-7-h{grid-row-start: span 7;}
.grid-span-8-h{grid-row-start: span 8;}
.grid-span-9-h{grid-row-start: span 9;}
.grid-span-10-h{grid-row-start: span 10;}
.grid-span-11-h{grid-row-start: span 11;}
.grid-span-12-h{grid-row-start: span 12;}
/* (容器上)网格项目-行列间距 */
.grid-gap-5{grid-gap: 05px;}
.grid-gap-10{grid-gap: 10px;}
.grid-gap-0_5{grid-gap: 0 05px;}
.grid-gap-0_10{grid-gap: 0 10px;}
.grid-gap-5_0{grid-gap: 05px 0;}
.grid-gap-10_0{grid-gap: 10px 0;}
.grid-gap-row-5{grid-row-gap: 05px;}
.grid-gap-row-10{grid-row-gap: 10px;}
.grid-gap-col-5{grid-column-gap: 05px;}
.grid-gap-col-10{grid-column-gap: 10px;}
/* (容器上)网格项目在整个的网格中-水平垂直对齐方式 */
.grid-center-center{place-content:center center;} /* 垂直和水平的简写(垂直+水平) */
.grid-left{justify-content:start;}
.grid-center{justify-content:center;}
.grid-right{justify-content:end;}
.grid-even{justify-content:space-evenly;}
.grid-around{justify-content:space-around;}
.grid-both{justify-content:space-between;}
.grid-just{justify-content:stretch;}
.grid-top{align-content:start;}
.grid-middle{align-content:center;}
.grid-bottom{align-content:end;}
.grid-even-h{align-content:space-evenly;}
.grid-around-h{align-content:space-around;}
.grid-both-h{align-content:space-between;}
.grid-full{align-content:stretch;}
/* (容器上)网格项目在各自的网格中-水平垂直对齐方式 */
.grid-items-center-center{place-items:center center;} /* 垂直和水平的简写(垂直+水平) */
.grid-items-left{justify-items:start;}
.grid-items-center{justify-items:center;}
.grid-items-right{justify-items:end;}
.grid-items-both{justify-items:stretch;}
.grid-items-top{align-items:start;}
.grid-items-middle{align-items:center;}
.grid-items-bottom{align-items:end;}
.grid-items-full{align-items:stretch;}
/* (项目上)网格单个项目在网格中-水平垂直对齐方式 */
.grid-item-right{justify-self:end;}
.grid-item-bottom{align-self:end;}

/* 网格边框 */
.grid-table{border: 1px solid #ddd;}
.grid-table>*{border: 1px solid #ddd;display: flex;align-items: center;padding: 10px;}

.over-auto{overflow: auto;}
.over-auto-x{overflow-x: auto;}
.over-auto-y{overflow-y: auto;}
.over-hide{overflow: hidden;}
.over-hide-0{overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
.over-hide-1{overflow: hidden;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;}
.over-hide-2{overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;}
.over-hide-3{overflow: hidden;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;}
.over-hide-4{overflow: hidden;display: -webkit-box;-webkit-line-clamp: 4;-webkit-box-orient: vertical;}
.over-hide-5{overflow: hidden;display: -webkit-box;-webkit-line-clamp: 5;-webkit-box-orient: vertical;}

.posit-absol-all{position: absolute;left: 0;top: 0;right: 0;bottom: 0;}
.posit-fixed-all{position: fixed;left: 0;top: 0;right: 0;bottom: 0;}
.posit-relat{position: relative;}
.posit-absol{position: absolute;}
.posit-fixed{position: fixed;}
.left-0{left: 0;}
.right-0{right: 0;}
.top-0{top: 0;}
.bottom-0{bottom: 0;}
.z-index--1{z-index: -1;}
.z-index-1{z-index: 1;}
.z-index-9{z-index: 9;}
.z-index-10{z-index: 10;}
.z-index-99{z-index: 99;}
.z-index-100{z-index: 100;}
.z-index-999{z-index: 999;}
.z-index-1000{z-index: 1000;}
.z-index-9999{z-index: 9999;}

.width-auto{width: auto;}
.width-all{width: 100%;}
.width-all-impt{width: 100%!important;}
.max-width-all{max-width: 100%;}
.height-auto{height: auto;}
.height-all{height: 100%;}
.height-all-impt{height: 100%!important;}
.max-height-all{max-height: 100%;}

.color-parent{color: inherit;}
.color-000{color: #000;}
.color-333{color: #333;}
.color-666{color: #666;}
.color-999{color: #999;}
.color-bbb{color: #bbb;}
.color-ddd{color: #ddd;}
.color-fff{color: #fff;}
.color-fffs8{color: rgba(255, 255, 255, 0.8);}
.color-blue{color: #409eff;}
.color-red{color: #f56c6c;}
.color-green{color: #67c23a;}
.color-green1{color: #00FF94;}
.color-green2{color: #00E7FF;}
.color-yellow{color: #FF9900;}

.hover-blue:hover{cursor: pointer;user-select: none;color: #409eff!important;}

.bg-none{background: none!important;}
.bg-color-fff{background-color: #fff;}
.bg-color-ddd{background-color: #ddd;}
.bg-color-000s3{background-color: rgba(0, 0, 0, 0.3);}
.bg-color-000s5{background-color: rgba(0, 0, 0, 0.5);}

.border-none{border:none!important;}
.border-parent{border: 1px solid;}
.border-bbb{border: 1px solid #bbb;}
.border-ddd{border: 1px solid #ddd;}
.border-t-ddd{border-top: 1px solid #ddd;}
.border-b-ddd{border-bottom: 1px solid #ddd;}
.border-b-ddd-dashed{border-bottom: 1px dashed #ddd;}

.border-radius-0{border-radius: 0!important;}
.border-radius-3{border-radius: 3px;}
.border-radius-5{border-radius: 5px;}
.border-radius-10{border-radius: 10px;}
.border-radius-all{border-radius: 1000px;}

.outline-ddd{outline: 1px solid #ddd;}

.line-none{line-height: normal!important;}
.line-1{line-height: 1;}
.line-1s2{line-height: 1.2;}
.line-1s4{line-height: 1.4;}
.line-1s6{line-height: 1.6;}
.line-1s8{line-height: 1.8;}
.line-2{line-height: 2;}

.font-none{font-weight: normal!important;}
.font-bold{font-weight: bold;}
.font-0{font-size: 0!important;}
.font-8{font-size: 08px;}
.font-9{font-size: 09px;}
.font-10{font-size: 10px;}
.font-11{font-size: 11px;}
.font-12{font-size: 12px;}
.font-13{font-size: 13px;}
.font-14{font-size: 14px;}
.font-15{font-size: 15px;}
.font-16{font-size: 16px;}
.font-17{font-size: 17px;}
.font-18{font-size: 18px;}
.font-19{font-size: 19px;}
.font-20{font-size: 20px;}
.font-22{font-size: 22px;}
.font-24{font-size: 24px;}
.font-26{font-size: 26px;}
.font-28{font-size: 28px;}
.font-30{font-size: 30px;}
.font-40{font-size: 40px;}

.padd-0{padding: 0!important;}
.padd-2{padding: 02px;}
.padd-5{padding: 05px;}
.padd-8{padding: 08px;}
.padd-10{padding: 10px;}
.padd-15{padding: 15px;}
.padd-20{padding: 20px;}

.padd-0_2{padding: 0 02px;}
.padd-0_5{padding: 0 05px;}
.padd-0_8{padding: 0 08px;}
.padd-0_10{padding: 0 10px;}
.padd-0_15{padding: 0 15px;}
.padd-0_20{padding: 0 20px;}

.padd-2_0{padding: 02px 0;}
.padd-5_0{padding: 05px 0;}
.padd-8_0{padding: 08px 0;}
.padd-10_0{padding: 10px 0;}
.padd-15_0{padding: 15px 0;}
.padd-20_0{padding: 20px 0;}

.padd-2_5{padding: 02px 05px;}
.padd-5_10{padding: 05px 10px;}
.padd-8_10{padding: 08px 10px;}
.padd-10_5{padding: 10px 05px;}
.padd-10_8{padding: 10px 08px;}
.padd-10_15{padding: 10px 15px;}
.padd-10_20{padding: 10px 20px;}

.padd-t-0{padding-top: 0!important;}
.padd-t-2{padding-top: 02px;}
.padd-t-5{padding-top: 05px;}
.padd-t-8{padding-top: 08px;}
.padd-t-10{padding-top: 10px;}
.padd-t-15{padding-top: 15px;}
.padd-t-20{padding-top: 20px;}

.padd-b-0{padding-bottom: 0!important;}
.padd-b-2{padding-bottom: 02px;}
.padd-b-5{padding-bottom: 05px;}
.padd-b-8{padding-bottom: 08px;}
.padd-b-10{padding-bottom: 10px;}
.padd-b-15{padding-bottom: 15px;}
.padd-b-20{padding-bottom: 20px;}

.padd-l-0{padding-left: 0!important;}
.padd-l-2{padding-left: 02px;}
.padd-l-5{padding-left: 05px;}
.padd-l-8{padding-left: 08px;}
.padd-l-10{padding-left: 10px;}
.padd-l-15{padding-left: 15px;}
.padd-l-20{padding-left: 20px;}

.padd-r-0{padding-right: 0!important;}
.padd-r-2{padding-right: 02px;}
.padd-r-5{padding-right: 05px;}
.padd-r-8{padding-right: 08px;}
.padd-r-10{padding-right: 10px;}
.padd-r-15{padding-right: 15px;}
.padd-r-20{padding-right: 20px;}

.marg-auto{margin: 0 auto;}
.marg-0{margin: 0!important;}
.marg-2{margin: 02px;}
.marg-5{margin: 05px;}
.marg-8{margin: 08px;}
.marg-10{margin: 10px;}
.marg-15{margin: 15px;}
.marg-20{margin: 20px;}

.marg-0_2{margin: 0 02px;}
.marg-0_5{margin: 0 05px;}
.marg-0_8{margin: 0 08px;}
.marg-0_10{margin: 0 10px;}
.marg-0_15{margin: 0 15px;}
.marg-0_20{margin: 0 20px;}

.marg-2_0{margin: 02px 0;}
.marg-5_0{margin: 05px 0;}
.marg-8_0{margin: 08px 0;}
.marg-10_0{margin: 10px 0;}
.marg-15_0{margin: 15px 0;}
.marg-20_0{margin: 20px 0;}

.marg-2_5{margin: 02px 05px;}
.marg-5_10{margin: 05px 10px;}
.marg-8_10{margin: 08px 10px;}
.marg-10_5{margin: 10px 05px;}
.marg-10_8{margin: 10px 08px;}

.marg-t-0{margin-top: 0!important;}
.marg-t-2{margin-top: 02px;}
.marg-t-5{margin-top: 05px;}
.marg-t-8{margin-top: 08px;}
.marg-t-10{margin-top: 10px;}
.marg-t-15{margin-top: 15px;}
.marg-t-20{margin-top: 20px;}

.marg-b-0{margin-bottom: 0!important;}
.marg-b-2{margin-bottom: 02px;}
.marg-b-5{margin-bottom: 05px;}
.marg-b-8{margin-bottom: 08px;}
.marg-b-10{margin-bottom: 10px;}
.marg-b-15{margin-bottom: 15px;}
.marg-b-20{margin-bottom: 20px;}

.marg-l-0{margin-left: 0!important;}
.marg-l-2{margin-left: 02px;}
.marg-l-5{margin-left: 05px;}
.marg-l-8{margin-left: 08px;}
.marg-l-10{margin-left: 10px;}
.marg-l-15{margin-left: 15px;}
.marg-l-20{margin-left: 20px;}

.marg-r-0{margin-right: 0!important;}
.marg-r-2{margin-right: 02px;}
.marg-r-5{margin-right: 05px;}
.marg-r-8{margin-right: 08px;}
.marg-r-10{margin-right: 10px;}
.marg-r-15{margin-right: 15px;}
.marg-r-20{margin-right: 20px;}

/* 滤镜-毛玻璃效果 */
.filter-blur{filter: blur(5px);}
.filter-bg-blur{backdrop-filter: blur(5px);}
/* 2025-02-27 - jswn */

/* 暂无数据 */
.not-data{position: absolute;left: 0;right: 0;text-align: center;top: 50%;font-size: 18px;}


// elementUI-table表格复选框居中对齐
.el-table__body-wrapper .el-table-column--selection>.cell, .el-table__header-wrapper .el-table-column--selection>.cell{
  justify-content: center;
}

// 标题栏-带左侧蓝色竖条
.title-l-blue-box {
  background: #F7F5F5;
  &>span {
    display: inline-block;
    padding: 0 10px;
    margin: 8px 0;
    border-left: 5px solid #57a3f3;
  }
}








