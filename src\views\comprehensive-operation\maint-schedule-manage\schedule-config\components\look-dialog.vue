<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">班次名称:</el-col>
        <el-col :span="8">{{ data.relationShiftName }}</el-col>
        <el-col :span="4" class="text-right">人员名称:</el-col>
        <el-col :span="8">{{ data.userRelationName }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">开始日期:</el-col>
        <el-col :span="8">{{ data.workStartTime?.split(' ')[0] }}</el-col>
        <el-col :span="4" class="text-right">结束日期:</el-col>
        <el-col :span="8">{{ data.workEndTime?.split(' ')[0] }}</el-col>
        <el-col :span="4" class="text-right">倒班规则:</el-col>
        <el-col :span="8">{{ shiftRulesOption.find(item => item.businessValue === data.shiftRules)?.businessLabel }}</el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";
import { getDict } from "@/api/common";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {},
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};

// 挂载
onMounted(async () => {
  fun_getDicts();//获取字典
})

// 获取字典
const fun_getDicts = async () => {
  // const {data:shiftRulesOptionData} = await getDict('shift_rules');//倒班规则
  // shiftRulesOption.value = shiftRulesOptionData.list;
  // console.log('倒班规则shiftRulesOption:',shiftRulesOption.value);
}

// 倒班规则
const shiftRulesOption = ref([
  // { businessLabel: "一天一倒", businessValue: 1 },
  // { businessLabel: "三天一倒", businessValue: 2 },
  // { businessLabel: "工作日上班", businessValue: 3 },
]);

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
