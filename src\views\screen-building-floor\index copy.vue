<template>
  <div class="data-screen-wrapper">
    <div class="header-wrapper posit-relat z-index-9">
      <div class="header-left">
        <div class="weatcher-wrapper">
          <div class="weather-icon"></div>
          <div class="weather-info">
            <div class="weather-temp">5-10℃</div>
            <div class="weather-detail">多云转晴</div>
          </div>
        </div>
        <div class="line"></div>
        <div class="date">17:26:24 2022/03/15</div>
      </div>
      <div class="header-center">楼宇能源管控平台</div>
      <div class="header-right" @click="handleGoIndex">
        <img class="icon-out" src="@/assets/data-screen/icon-out.png" />
        <div class="right-text">进入系统后台</div>
      </div>
    </div>

    <!-- 楼层平面图 -->
    <!-- <div class="center-wrapper">
      <img class="width-all" :src="`/static/images/building-floor/${buildingFloorActiveImgName}.png`" alt="">
    </div> -->
    <!-- 楼层3D立体图 -->
    <div class="posit-fixed left-0 top-0 right-0 bottom-0">
      <threejs ref="threejsRef" :config="{scaleHeight: 1080,backgroundColor:'#061b3a',axesHelperShow: true,camera: { x: 0, y: 1400, z: 1000, lookAt: [0, 100, 0], far: 100000 }}" @fun_loaded="fun_threejsOnload" @fun_clickModelBack="fun_clickModelBack"></threejs>
    </div>

    <!-- 设备列表定位 -->
    <ul>
      <li class="posit-absol hover" 
      :style="{left:(item.coordx||0)+'px',top:(item.coordy||0)+'px'}" 
      v-for="(item,key) of dataList" :key="key" 
      @mouseenter="fun_handleMouseEnter(item,key)"
      @mouseleave="fun_handleMouseLeave(item,key)"
      @click="fun_handleClick(item,key)"
      >
        <!-- <img width="50" :src="`/static/images/building-floor/icon/${item.imgName}.png`" alt=""> -->
        <img :src="item.filePath" alt="">
      </li>
    </ul>
    <!-- 楼层选择 -->
    <div class="top-wrapper">
      <select-box @select="handleSelect" @choose="handleChoose"></select-box>
    </div>
    <!-- 右上角弹层 -->
    <div class="rightTop-dialog color-fff posit-absol padd-5" style="right:20px;top:95px;">
      <div class="header flex flex-both flex-middle padd-5 font-18">
        <span class="padd-0_5">图层</span>
        <!-- <el-icon class="font-18" @click="layerListVisibility=!layerListVisibility">
          <MoreFilled />
        </el-icon> -->
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange"></el-checkbox>
      </div>
      <ul class="layerUl">
        <li class="layerList hover flex flex-both flex-middle font-16 padd-2_5 marg-5_0" :class="{'layerListActive':item.checked}" v-for="(item,key) of layerList" :key="key" @click="fun_handleClickLayer(item,key)">
          <div class="flex flex-both flex-middle">
            <div class="icon"></div>
            <span class="marg-l-5">{{ item.label }}</span>
          </div>
          <el-checkbox v-model="item.checked" @click.stop="handleChangeLayer()"></el-checkbox>
        </li>
      </ul>
    </div>
    <!-- 详情弹窗 -->
    <div class="info-dialog flex flex-col filter-bg-blur font-14" v-show="dataInfoVisibility">
      <div class="flex flex-middle">
        <img class="marg-0_5" style="width:18px;" src="/static/images/dialog-arrow.png" alt="">
        <span class="font-16 font-bold">{{ dataInfo.name }}</span>
      </div>
      <div class="flex flex-right">
        <el-icon class="hover font-24" @click="dataInfoVisibility=false"><Close /></el-icon>
      </div>
      <div class="padd-10_20">
        <span class="color-ddd">位置：</span>
        <span class="">{{ dataInfo.position||'暂无' }}</span>
      </div>
      <ul class="flex flex-wrap max-height-all over-auto-y">
        <li class="flex flex-right flex-middle padd-5_0" style="width: 48%;" v-for="(item,key) of dataInfo.list" :key="key">
          <span class="color-ddd padd-r-5 text-right">{{ item.name }}:</span>
          <span class="flex-item-shrink-0 font-bold">{{ item.value||'暂无' }}</span>
          <span class="padd-l-5" v-if="item.unit">({{ item.unit }})</span>
      </li>
      </ul>
    </div>
    <!-- 启停控制弹窗 -->
    <div class="control-dialog filter-bg-blur flex flex-col" v-show="dataControlVisibility">
      <div class="flex flex-middle flex-both padd-5_10 border-b-ddd">
        <div class="flex flex-middle">
          <el-icon class="font-18 marg-t-2 marg-r-10"><HelpFilled /></el-icon>
          <span class="font-16 font-bold">{{ formData.name }}</span>
        </div>
        <el-icon class="hover font-24" @click="dataControlVisibility=false"><Close /></el-icon>
      </div>
      <div class="padd-20 height-all over-auto-y">
        <div class="height-all over-auto-y">
          <div>
            <div class="font-18 font-bold marg-b-20">远程启停</div>
            <div class="flex flex-around marg-b-20">
              <el-button size="large" :color="formData.sdqd=='1'?'#DDF3FF':'#00FF94'" :dark="true" plain style="padding: 25px 40px;" :style="{backgroundColor: formData.sdqd=='1'?'#DDF3FF33':'#00FF9433'}" @click="handleState('start')">{{formData.sdqd=='1'?'已启动':'启动'}}</el-button>
              <el-button size="large" :color="formData.sdqd=='1'?'#00FF94':'#DDF3FF'" :dark="true" plain style="padding: 25px 40px;" :style="{backgroundColor: formData.sdqd=='1'?'#00FF9433':'#DDF3FF33'}" @click="handleState('stop')">{{formData.sdqd=='1'?'停止':'已停止'}}</el-button>
            </div>
          </div>

          <el-form label-width="160" :inline="false" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
            <!-- <el-form-item label="出水温度设定:" prop="cswdsd" v-if="dataInfo.type=='2'">
              <div class="width-all flex">
                <el-input v-model.number="formData.cswdsd" type="text" placeholder="请输入" />
                <span class="flex-item-shrink-0 padd-l-5">℃</span>
              </div>
            </el-form-item> -->
            <el-form-item :label="item.attributeDesc" v-for="(item,key) of dataInfoFormData" :key="key" style="margin-bottom: 5px;">
              <div class="width-all flex" v-if="item.attributeMark.substring(0,1)=='B'">
                <el-select v-model="formData[item.attributeType]" placeholder="请选择">
                  <el-option v-for="(status, index2) in triggerStatusOption" :key="index2" :label="status.label" :value="status.value" />
                </el-select>
              </div>
              <div class="width-all flex" v-else>
                <el-input v-model.number="formData[item.attributeType]" type="text" placeholder="请输入" />
                <span class="flex-item-shrink-0 padd-l-5">{{item.unit||''}}</span>
              </div>
            </el-form-item>
          </el-form>

          <div class="flex flex-center marg-b-20 padd-t-20">
            <el-button type="primary" @click="submitBtn(ruleFormRef)">确定</el-button>
            <el-button type="default" @click="dataControlVisibility=false">取消</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted,watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter,useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

import selectBox from "./components/select.vue";

import {
  getDevicePositionList,
  getDeviceAttributeList,
  updateDeviceAttribute,
} from "@/api/screen-building-floor/index";

import threejs from "@/components/threejsComm/threejsComm.vue";
import {
  // fun_addImageModel,
  // fun_addTextModel,
  fun_addGltfModel,
  fun_addHdr,
  fun_fullScreen,
  fun_addGui,
} from "@/components/threejsComm/threejsComm";

import {
  fun_addImageModel,
  fun_addTextModel,
  fun_addBuildingFloorModel_A1_1,
} from "@/components/threejsOthers/threejsOthers";


//threejs
const threejsRef = ref();

// 监听threejs组件加载完成
let rendererThis = "",
  sceneThis = "",
  cameraThis = "";
function fun_threejsOnload(renderer, scene, camera) {
  // console.log('fun_threejsOnload:renderer,scene,camera',renderer,scene,camera);
  rendererThis = renderer;
  sceneThis = scene;
  cameraThis = camera;

  //添加图片模型(平面模型)
  // fun_addImageModel({renderer,scene,camera},{path:'/static/threejsData/test1.png',position:{x:-25,y:0,z:0},lookAtCamera:true});
  //添加文字模型
  // fun_addTextModel({renderer,scene,camera},{text:'你好',position:{x:10,y:0,z:0},color:'#ff00ff',size:2,depth:0.1,weight:0.05,lookAtCamera:true});
  fun_addBuildingFloorModel_A1_1({renderer,scene,camera},{position:{x:0,y:0,z:0},scale:1});

  //添加Gltf模型-楼群-哈尔滨灰模
  // fun_addGltfModel({scene},{path:'/static/threejsData/harbin.glb',position:{x:0,y:0,z:0},scale:1});
  //添加Gltf模型-楼群-哈尔滨灰模带地面
  // fun_addGltfModel({ scene },{path: "/static/threejsData/harbinMap.glb",position: { x: -500, y: 0, z: 2500 },scale: 1});

  //添加Hdr环境贴图
  // fun_addHdr({ renderer, scene, camera },{ path: "/static/threejsData/sky.hdr" });

  //添加Gui自定义调试工具
  // fun_addGui({renderer,scene,camera},{cube,cylinder});
}

// 模型点击回调
function fun_clickModelBack({ modelThis, _isSelect }) {
  console.log("fun_clickModelBack:", modelThis, _isSelect);
  // router.push({path:'/screen-building-floor'});
}


//栋楼选择事件处理函数
const handleSelect = (val) => {
  console.log('栋楼选择事件处理函数:',val);
  buildingBtnValue.value=val.value;
};
//楼层选择事件处理函数
const handleChoose = (val) => {
  console.log('楼层选择事件处理函数:',val);
  floorBtnValue.value=val.value;
};

const buildingBtnValue = ref('A1');//当前楼栋值
const floorBtnValue = ref(1);//当前楼层值
//楼层平面图
const buildingFloorActiveImgName = ref('screenBuildingFloor-A1-1');
watch([buildingBtnValue,floorBtnValue],() => {
  //4-18楼层一样
  if(floorBtnValue.value>=4&&floorBtnValue.value<=18){
    buildingFloorActiveImgName.value = `screenBuildingFloor-${buildingBtnValue.value}-${4}`;
  }else{
    buildingFloorActiveImgName.value = `screenBuildingFloor-${buildingBtnValue.value}-${floorBtnValue.value}`;
  }
  fun_getList();
})

onMounted(() => {
  //获取数据
  // fun_getList();
  handleCheckedLayer();
});

//启停状态
const triggerStatusOption = ref([
  { label: "启动", value: 1 },
  { label: "关闭", value: 0 },
]);

const dataList = ref([]);//当前楼层设备列表数据
const fun_getList = async () => {
  const { data } = await getDevicePositionList({sbdmList:layerListValues.value,szld:buildingBtnValue.value,szlc:floorBtnValue.value,pageSize:1000});
  console.log('设备扎点列表:',data);
  dataList.value = data.list||[];
};

//右上角弹窗
const layerList = ref([
  {label:'摄像头',checked:true,value:17},
  {label:'温度',checked:false,value:18},
  {label:'人员热力',checked:false,value:16},
  {label:'管线排布',checked:false,value:14},
  {label:'环境传感器',checked:false,value:15},
])
const layerListValues = ref(layerList.value.filter(item => item.checked).map(item => item.value));
const fun_handleClickLayer = (item,key) => {
  console.log('右上角弹窗点击:',item,key);
  layerList.value[key].checked=!layerList.value[key].checked;
  handleCheckedLayer();
}
const handleChangeLayer = (val) => {
  // console.log('右上角弹窗checkbox:',val);
  setTimeout(() => {
    handleCheckedLayer();
  }, 100);
}
const handleCheckedLayer = () => {
  layerListValues.value = layerList.value.filter(item => item.checked).map(item => item.value);
  if(layerListValues.value.length>0){
    fun_getList();
  }else{
    dataList.value=[];
  }
  //全选按钮状态同步
  checkAll.value = layerList.value.every(item => item.checked)
  isIndeterminate.value = layerList.value.some(item => item.checked)
  if (checkAll.value)isIndeterminate.value = false;
  // console.log(checkAll.value,isIndeterminate.value);
}
// 全选
const checkAll = ref(false);//是否有对号(优先级低)
const isIndeterminate = ref(false);//是否有横杠(优先级高)
const handleCheckAllChange = (val) => {
  console.log('全选按钮:',val);
  isIndeterminate.value = false;
  if(val){
    layerList.value.forEach(item => {
      item.checked = true;
    })
    handleCheckedLayer();
  }else{
    layerList.value.forEach(item => {
      item.checked = false;
    })
    handleCheckedLayer();
  }
}


// 详情弹窗
const dataInfo = ref({list:[]})
const dataInfoVisibility = ref(false)
// 鼠标单击-显示详情弹窗
// const fun_handleClick = async (node,key) => {
//   console.log('鼠标单击:',node,key);
//   const { data } = await getDeviceAttributeList({equipmentId:node.gid,pageSize: 100});
//   console.log('设备属性详情:',data);
//   dataInfo.value.name=node.name;
//   dataInfo.value.list=data?.list.map(item => {return{name:item.attributeDesc,value:item.attributeValue,unit:item.attributeUnit}})||[];
//   dataInfoVisibility.value=true;
// }
// 鼠标移入-显示详情弹窗
const mouseEnterTimer = ref(null)
const fun_handleMouseEnter = async (node,key) => {
  if(mouseEnterTimer.value){
    clearTimeout(mouseEnterTimer.value);
  }
  mouseEnterTimer.value = setTimeout(async () => {
    console.log('鼠标移入:',node,key);
    const { data } = await getDeviceAttributeList({equipmentId:node.gid,pageSize: 100});
    console.log('设备属性详情:',data);
    dataInfo.value.name=node.sbmc;
    dataInfo.value.list=data?.list.map(item => {return{name:item.attributeDesc,value:item.attributeValue,unit:item.attributeUnit}})||[];
    dataInfoVisibility.value=true;
  }, 250) // 250ms内没有移动视为移入
}
// 鼠标移出-隐藏详情弹窗
const fun_handleMouseLeave = (node,key) => {
  console.log('鼠标移出:',node,key);
  if(mouseEnterTimer.value){
    clearTimeout(mouseEnterTimer.value);
  }
  // dataInfoVisibility.value=false;
}

// 启停控制弹窗
const dataControlVisibility = ref(false)
const dataInfoFormData = ref([])
const dataInfoSubmitData = ref([])
// 鼠标单击-显示启停控制弹窗
const fun_handleClick = async (node,key) => {
  console.log('鼠标单击:',node,key);
  const { data } = await getDeviceAttributeList({equipmentId:node.gid,status:1,pageSize: 100});
  console.log('设备属性详情:',data);
  formData.value.name=node.sbmc;
  // formData.value.id=node.gid;
  dataInfoSubmitData.value=data?.list||[];
  dataInfoFormData.value=data?.list.filter(item => item.attributeType!='sdqd')||[];//.map(item => {return{attributeDesc:item.attributeDesc,attributeType:item.attributeType,attributeValue:item.attributeValue,unit:item.attributeUnit,gid:item.gid}})
  // 控制属性值回显
  dataInfoFormData.value.forEach(item => {
    formData.value[item.attributeType]=item.attributeValue;
  })
  // 启停控制按钮值
  formData.value.sdqd = data?.list.find(item => item.attributeType=='sdqd')?.attributeValue||'';
  dataControlVisibility.value=true;
}

// 启用停用
const handleState = (type) => {
  ElMessageBox.confirm(`确定${type=='start'?'启动':'停止'}吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    formData.value.sdqd=type=='start'?'1':'0';
    for(let item of dataInfoSubmitData.value){
      if(item.attributeType=='sdqd'){
        item.attributeValue=formData.value.sdqd;
        break;
      }
    }
    const res = await updateDeviceAttribute({list:dataInfoSubmitData.value});
    ElMessage({
      type: "success",
      message: `${type=='start'?'启动':'停止'}成功`,
    });
    // fun_getList();
  })
};

// 表单提交数据
const formData = ref({})

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  // plsd: [
  //   { required: true, message: "请输入", trigger: ["blur","change"] },
  //   { type: 'number', message: '请输入数字类型' },
  // ],
  // cswdsd: [
  //   { required: true, message: "请输入", trigger: ["blur","change"] },
  //   { type: 'number', message: '请输入数字类型' },
  // ],
});

// 提交
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      ElMessageBox.confirm("确定提交吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        dataInfoSubmitData.value.forEach(item => {
          item.attributeValue=formData.value[item.attributeType];
        })
        const res = await updateDeviceAttribute({list:dataInfoSubmitData.value});
        ElMessage({
          message: res.message,
          type: "success",
        });
        dataControlVisibility.value=false;
        formData.value={};
        // fun_getList();
      })
    }
    else{
      console.log('表单验证失败:',fields);
    }
  })
};


const handleGoIndex = () => {
  //返回首页
  router.push({path:'/index'});
};
const handleBack = () => {
  //返回按钮事件处理函数
  router.go(-1);
};

</script>


<style lang="scss" scoped>
.data-screen-wrapper {
  width: 1920px;
  height: 100%;
  background: #061b3a;
  position: relative;
  .header-wrapper {
    width: 100%;
    height: 104px;
    background: url(@/assets/data-screen/header-bg.png) no-repeat center;
    background-size: auto 100%;
    display: flex;
    padding: 16px 0 16px 24px;
    align-items: flex-start;
    justify-content: space-between;
    .header-left {
      display: flex;
      height: 40px;
      align-items: center;
      .weatcher-wrapper {
        width: 120px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .icon-weather {
          height: 40px;
          width: 40px;
        }
        .weather-info {
          width: 56px;
          .weather-temp {
            height: 20px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: bold;
            font-size: 16px;
            color: #ebf8ff;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }
          .weather-detail {
            height: 18px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #ebf8ff;
            text-align: ce;
            font-style: normal;
            text-transform: none;
          }
        }
      }
      .line {
        width: 0px;
        height: 32px;
        border-radius: 0px 0px 0px 0px;
        border: 2px solid;
        border-image: linear-gradient(
            150deg,
            rgba(255, 255, 255, 0),
            rgba(255, 255, 255, 0.5),
            rgba(255, 255, 255, 0)
          )
          2 2;
          margin:0 16px;
      }
      .date {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 18px;
        color: #ebf8ff;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .header-center {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);

      font-size: 48px;
      color: #ffffff;
      letter-spacing: 7px;
      text-shadow: 2px 4px 0px rgba(14, 26, 66, 0.56);
    }
    .header-right {
      display: flex;
      align-items: center;
      justify-content: center;
      background: url(@/assets/data-screen/header-right-bg.png) no-repeat center;
      background-size: 100% 100%;
      width: 208px;
      height: 46px;
      cursor: pointer;
      .icon-out {
        width: 24px;
        margin-right: 8px;
        height: 24px;
      }
      .right-text {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 18px;
        color: rgba(199, 214, 255, 0.8);
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .top-wrapper {
    position: absolute;
    left: 492px;
    right: 492px;
    top: 130px;
    height: 48px;
  }
}

//又上角弹窗
.rightTop-dialog{
  width: 200px;
  // height: 272px;
  background: rgba(2,7,22,0.32);
  box-shadow: 0px 4px 12px 0px rgba(129,164,255,0.24);
  border-radius: 4px 4px 4px 4px;
  border: 2px solid;
  border-image: linear-gradient(79deg, rgba(64, 249, 255, 0), rgba(64, 249, 255, 0.64)) 2 2;
  .header{
    background: linear-gradient( 90deg, rgba(64,249,255,0.16) 0%, rgba(64,249,255,0) 100%), rgba(199,214,255,0.16);
    border-radius: 2px 2px 2px 2px;
  }
  :deep(.el-checkbox__inner){
    background: none;
    border-color: rgba(199, 214, 255, 0.64);
  }
  :deep(.el-checkbox__input.is-checked .el-checkbox__inner){
    border-color: rgba(64, 249, 255, 1);
  }
  :deep(.el-checkbox__input.is-checked .el-checkbox__inner:after){
    border-color: rgba(64, 249, 255, 1);
  }
  .layerUl{
    .layerList{
      color: rgba(199, 214, 255, 0.64);
      .icon{
        width: 20px;
        height: 20px;
        background: url('/static/images/building-floor/screenBuildingFloor_icon0.png');
        background-size: 100% 100%;
      }
      &:nth-child(1) .icon{
        background-image: url('/static/images/building-floor/screenBuildingFloor_icon0.png');
      }
      &:nth-child(2) .icon{
        background-image: url('/static/images/building-floor/screenBuildingFloor_icon1.png');
      }
      &:nth-child(3) .icon{
        background-image: url('/static/images/building-floor/screenBuildingFloor_icon2.png');
      }
      &:nth-child(4) .icon{
        background-image: url('/static/images/building-floor/screenBuildingFloor_icon3.png');
      }
      &:nth-child(5) .icon{
        background-image: url('/static/images/building-floor/screenBuildingFloor_icon4.png');
      }
      &:hover{
        background: rgba(64,249,255,0.08);
        border-radius: 4px 4px 4px 4px;
        color: rgba(64, 249, 255, 1);
        &:nth-child(1) .icon{
          background-image: url('/static/images/building-floor/screenBuildingFloor_iconActive0.png');
        }
        &:nth-child(2) .icon{
          background-image: url('/static/images/building-floor/screenBuildingFloor_iconActive1.png');
        }
        &:nth-child(3) .icon{
          background-image: url('/static/images/building-floor/screenBuildingFloor_iconActive2.png');
        }
        &:nth-child(4) .icon{
          background-image: url('/static/images/building-floor/screenBuildingFloor_iconActive3.png');
        }
        &:nth-child(5) .icon{
          background-image: url('/static/images/building-floor/screenBuildingFloor_iconActive4.png');
        }
      }
    }
    .layerListActive{
      background: rgba(64,249,255,0.08);
      border-radius: 4px 4px 4px 4px;
      color: rgba(64, 249, 255, 1);
      &:nth-child(1) .icon{
        background-image: url('/static/images/building-floor/screenBuildingFloor_iconActive0.png');
      }
      &:nth-child(2) .icon{
        background-image: url('/static/images/building-floor/screenBuildingFloor_iconActive1.png');
      }
      &:nth-child(3) .icon{
        background-image: url('/static/images/building-floor/screenBuildingFloor_iconActive2.png');
      }
      &:nth-child(4) .icon{
        background-image: url('/static/images/building-floor/screenBuildingFloor_iconActive3.png');
      }
      &:nth-child(5) .icon{
        background-image: url('/static/images/building-floor/screenBuildingFloor_iconActive4.png');
      }
    }
  }
}

//详情弹窗
.info-dialog{
  position: absolute;
  right: 20px;
  bottom: 20px;
  width: 350px;
  height: 520px;
  background: url('/static/images/dialog-bg1.png') no-repeat;
  background-size: 100% 100%;
  padding: 72px 22px 54px;
  color: #fff;
}
//启停弹窗
.control-dialog{
  position: absolute;
  left: 50%;
  margin-left: -180px;
  top: 50%;
  margin-top: -205px;
  width: 360px;
  height: 410px;
  background-color: rgba(91, 115, 255, 0.4);
  color: #fff;
  .input-bg-green,
  .input-bg-green :deep(.el-input__wrapper){
      background-color: rgba(0, 255, 255, 0.1);
  }
  :deep(.el-form-item__label){
    color: #fff;
    font-size: 14px;
    line-height: 1;
    align-items: center;
  }
  :deep(.el-form-item){
    max-width: calc(100% - 0.167rem);
  }
  :deep(.el-input__inner){
    color: #fff;
  }
  :deep(.el-input__inner::placeholder){
    color: #fff;
  }
  :deep(.el-form-item__label:before){
    display: none;
  }
}


</style>
