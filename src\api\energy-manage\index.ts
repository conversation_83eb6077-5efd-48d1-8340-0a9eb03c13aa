import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';


/* 场景管理页面 */

// 场景管理-新增
export function addSceneInfo(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/sceneInfo/addSceneInfo',
    method: 'post',
    data: data
  })
}

// 场景管理-修改
export function updateSceneInfo(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/sceneInfo/editSceneInfo',
    method: 'post',
    data: data
  })
}

// 场景管理-删除
export function delSceneInfos(delIds: any) {
  return request({
    url: baseRouter + apiPrefix + '/sceneInfo/deleteByIds',
    method: 'delete',
    params: { ids: delIds },
  })
}

// 场景管理-列表
export function getSceneList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/sceneInfo/byPage',
    method: 'post',
    data: {...query}
  })
}

// 场景管理-详情
export function getSceneInfo(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/sceneInfo/getInfo',
    method: 'get',
    params: {...query}
  })
}



/* 节能策略管理页面 */

// 节能策略管理-新增
export function addStrategyInfo(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/strategyInfo/addStrategyInfo',
    method: 'post',
    data: data
  })
}

// 节能策略管理-修改
export function updateStrategyInfo(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/strategyInfo/editStrategyInfo',
    method: 'post',
    data: data
  })
}

// 节能策略管理-删除
export function delStrategyInfos(delIds: any) {
  return request({
    url: baseRouter + apiPrefix + '/strategyInfo/deleteByIds',
    method: 'delete',
    params: { ids: delIds },
  })
}

// 节能策略管理-列表
export function getStrategyList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/strategyInfo/byPage',
    method: 'post',
    data: {...query}
  })
}

// 节能策略管理-详情
export function getStrategyInfo(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/strategyInfo/getInfo',
    method: 'get',
    params: {...query}
  })
}

// 节能策略管理-启用停用
export function editStrategyStatus(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/strategyInfo/editStrategyStatus',
    method: 'get',
    params: {...query}
  })
}




