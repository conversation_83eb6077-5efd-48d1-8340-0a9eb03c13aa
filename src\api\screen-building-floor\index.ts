import request from "@/utils/request";
const baseRouter = import.meta.env.VITE_APP_BASE_ROUTER;

const apiPrefix = '/monitor';


/* 楼层图大屏页面 */

// 设备属性-详情
export function getDeviceAttributeList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentAttribute/getListByParam',
    method: 'post',
    data: {...query}
  })
}

// 设备属性-启停控制
export function updateDeviceAttribute(data: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentAttribute/remoteControl',
    method: 'post',
    data: data
  })
}

// 设备扎点-列表
export function getDevicePositionList(query: any) {
  return request({
    url: baseRouter + apiPrefix + '/equipmentInfo/getEquipmentInfoList',
    method: 'post',
    data: {...query}
  })
}


