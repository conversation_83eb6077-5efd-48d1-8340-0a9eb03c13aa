<template>
  <div class="pointManage">
    <div class="formBox">
      <el-form :inline="true" :model="filter">
        <el-form-item label="任务名称:">
          <el-input
            v-model="filter.inspectionPlanName"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="任务状态:">
          <el-select
            v-model="filter.taskCompleteState"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="(item, index) in rwztOption"
              :key="index"
              :label="item.businessLabel"
              :value="item.businessValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围:">
          <el-date-picker
            v-model="filter.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="fun_dateChange"
          />
        </el-form-item>
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier">
      <div class="main-btns">
        <el-button type="primary" @click="addBtn" :icon="Plus">新增</el-button>
        <el-button type="danger" @click="delAllBtn" :icon="Delete"
          >批量删除</el-button
        >
      </div>
      <div class="tableBox">
        <Table
          :data="tableData"
          :columns="columns"
          row-key="planId"
          @listSelectChange="handleSelectionChange"
        >
          <template #startTime="{ row }">
            <div>
              {{ row.startTime ? row.startTime.slice(0, 10) : "" }}
            </div>
          </template>
          <template #taskCompleteState="{ row }">
            <div>
              {{
                rwztOption.find(
                  (item) =>
                    Number(item.businessValue) === Number(row.taskCompleteState)
                )?.businessLabel
              }}
            </div>
          </template>
          <template #yhwxNum="{ row }">
            <div v-if="row.yhwxNum">
              <span class="color-blue hover" @click="handleReort(row)">{{
                row.yhwxNum
              }}</span>
            </div>
            <div v-else>
              <span>0</span>
            </div>
          </template>
          <template #wxNum="{ row }">
            <div v-if="row.wxNum">
              <span class="color-blue hover" @click="handleMaint(row)">{{
                row.wxNum
              }}</span>
            </div>
            <div v-else>
              <span>0</span>
            </div>
          </template>
          <template #yhNum="{ row }">
            <div v-if="row.yhNum">
              <span class="color-blue hover" @click="handleConserve(row)">{{
                row.yhNum
              }}</span>
            </div>
            <div v-else>
              <span>0</span>
            </div>
          </template>
          <template #operations="{ row }">
            <div class="buttons">
              <!-- <div class="btn" @click="handleState(row)">{{ row.sfqy==1?'停用':'启用'}}</div> -->
              <div class="btn" @click="handleLook(row)">查看</div>
              <div
                class="btn"
                @click="handleEdit(row)"
                v-if="Number(row.taskCompleteState) != 3"
              >
                编辑
              </div>
              <div
                class="btn"
                @click="handleDelete(row)"
                v-if="Number(row.taskCompleteState) != 3"
              >
                删除
              </div>
              <div
                v-if="Number(row.taskCompleteState) != 3"
                class="btn"
                @click="handleInspect(row)"
              >
                巡检
              </div>
              <!-- <div class="btn" @click="handleDownload(row)">下载</div> -->
            </div>
          </template>
        </Table>
        <el-pagination
          class="pageBox"
          v-model:current-page="filter.pageNum"
          v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]"
          layout="total, sizes, prev, pager, next,jumper"
          :total="total"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增-编辑 -->
    <addDialog
      :dialogVisible="addVisiblty"
      :title="addTitle"
      :data="addData"
      @onChangeVisible="addVisiblty = false"
      @refreshList="fun_getList()"
    ></addDialog>
    <!-- 查看 -->
    <lookDialog
      :dialogVisible="lookVisiblty"
      :data="lookData"
      :rwztOption="rwztOption"
      @onChangeVisible="lookVisiblty = false"
    ></lookDialog>
    <!-- 巡检 -->
    <inspectDialog
      :dialogVisible="inspectVisiblty"
      :title="inspectTitle"
      :data="inspectData"
      @onChangeVisible="inspectVisiblty = false"
      @refreshList="fun_getList()"
    ></inspectDialog>
    <!-- 隐患弹窗 -->
    <reportDialog
      :dialogVisible="reportVisiblty"
      :data="reportData"
      @onChangeVisible="reportVisiblty = false"
    ></reportDialog>

    <!--  维修弹窗 -->
    <maintDialog
      :dialogVisible="maintVisiblty"
      :data="maintData"
      @onChangeVisible="maintVisiblty = false"
    ></maintDialog>
    <!--  养护弹窗 -->
    <conserveDialog
      :dialogVisible="conserveVisiblty"
      :data="conserveData"
      @onChangeVisible="conserveVisiblty = false"
    ></conserveDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
import addDialog from "./components/add-dialog.vue";
import lookDialog from "./components/look-dialog.vue";
import inspectDialog from "./components/inspect-dialog.vue";
import reportDialog from "./components/look-dialog-report.vue";
import maintDialog from "./components/look-dialog-maint.vue";
import conserveDialog from "./components/look-dialog-conserve.vue";
import { getDict } from "@/api/common";

import {
  getList,
  del,
  update,
  getPlanList,
} from "@/api/comprehensive-operation/device-inspect/task";

// 搜索条件
const filter = ref({
  inspectionPlanName: "",
  taskCompleteState: "",
  dateRange: "",
  startSeleTime: "",
  endSeleTime: "",
  pageSize: 10,
  pageNum: 1,
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    inspectionPlanName: "",
    taskCompleteState: "",
    dateRange: "",
    startSeleTime: "",
    endSeleTime: "",
    pageSize: 10,
    pageNum: 1,
  };
  fun_getList();
};

//任务状态
const rwztOption = ref([
  // { label: "未开始", value: "1" },
  // { label: "进行中", value: "2" },
  // { label: "已超时", value: "3" },
  // { label: "已完成", value: "4" },
]);

// 获取字典
const fun_getDicts = async () => {
  const { data } = await getDict("JHRWZT");
  rwztOption.value = data.list;
  console.log("报告类型bglxOption:", rwztOption.value);
};

// 挂载
onMounted(async () => {
  fun_getDicts(); //获取字典
  fun_getList();
});

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns);
// 表格数据
const total = ref(0);
const tableData = ref([]);
const fun_getList = async () => {
  let params = JSON.parse(JSON.stringify(filter.value));
  delete params.dateRange;
  const { data } = await getList(params);
  tableData.value = data.list;
  total.value = data.total;
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};

// 日期范围-切换
const fun_dateChange = (val) => {
  console.log("时间范围-时间选择:", val);
  if (val) {
    filter.value.startSeleTime = val[0] + " 00:00:00";
    filter.value.endSeleTime = val[1] + " 23:59:59";
  } else {
    filter.value.startSeleTime = "";
    filter.value.endSeleTime = "";
  }
};

// 新增
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref([]);
const addBtn = () => {
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row) => {
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "编辑";
  addVisiblty.value = true;
};
// 巡检
const inspectVisiblty = ref(false);
const inspectTitle = ref("巡检");
const inspectData = ref([]);
const handleInspect = (row) => {
  inspectData.value = JSON.parse(JSON.stringify(row));
  inspectTitle.value = "巡检";
  inspectVisiblty.value = true;
};

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row) => {
  lookVisiblty.value = true;
  lookData.value = row;
  // lookData.value = {}
  // const res = await getReportInfo(row.id)
  // lookData.value = res.data;
};
//隐患弹窗
const reportVisiblty = ref(false);
const reportData = ref({});
const handleReort = (row) => {
  reportData.value = JSON.parse(JSON.stringify(row));
  reportVisiblty.value = true;
};
//维修弹窗
const maintVisiblty = ref(false);
const maintData = ref({});
const handleMaint = (row) => {
  maintData.value = JSON.parse(JSON.stringify(row));
  maintVisiblty.value = true;
};
//养护弹窗
const conserveVisiblty = ref(false);
const conserveData = ref({});
const handleConserve = (row) => {
  conserveData.value = JSON.parse(JSON.stringify(row));
  conserveVisiblty.value = true;
};
// 启用停用
const handleState = (row) => {
  ElMessageBox.confirm(`确定${row.sfqy == 1 ? "停用" : "启用"}吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    // const res = await del(row.id);
    ElMessage({
      type: "success",
      message: `${row.sfqy == 1 ? "停用" : "启用"}成功`,
    });
    fun_getList();
  });
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("删除后不可恢复,确定吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await del(row.planId);
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    fun_getList();
  });
};
// 批量删除
const delIds = ref("");
const handleSelectionChange = (data) => {
  delIds.value = data.map((item) => item.planId).join(",");
};
const delAllBtn = () => {
  if (delIds.value) {
    ElMessageBox.confirm("批量删除后不可恢复,确定吗?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        const res = await del(delIds.value);
        if (res.code === 0) {
          fun_getList();
          ElMessage({
            type: "success",
            message: "删除成功",
          });
        } else {
          ElMessage({
            message: res.message,
            type: "warning",
          });
        }
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "取消删除",
        });
      });
  } else {
    ElMessage({
      message: "请选择需要删除的选项",
      type: "warning",
    });
  }
};

// 附件下载
// import { preImageUrl } from '@/utils/index'
// const handleDownload = (row) => {
//   console.log('附件下载:',preImageUrl,row);
//   if (row.fileList && row.fileList.length > 0) {
//     window.open( import.meta.env.VITE_APP_IMG_URL+preImageUrl+row.fileList[0].fileGid);
//   } else {
//     ElMessage({
//       message: "没有附件",
//       type: "warning",
//     });
//   }
// }
</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
