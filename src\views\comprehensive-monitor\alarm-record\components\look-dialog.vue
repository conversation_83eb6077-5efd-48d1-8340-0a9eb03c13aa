<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main over-auto-y" style="height: 60vh;">
      <div class="title-l-blue-box marg-b-10">
        <span>报警信息</span>
      </div>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">报警编号:</el-col>
        <el-col :span="8">{{ data.bjbh }}</el-col>
        <el-col :span="4" class="text-right">监测项:</el-col>
        <el-col :span="8">{{ data.jcxmc }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">当前报警值:</el-col>
        <el-col :span="8">{{ data.dqbjz }}</el-col>
        <el-col :span="4" class="text-right">当前报警时间:</el-col>
        <el-col :span="8">{{ data.dqbjsj }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right" v-if="data.tabs_activeName == '1'">传感器名称:</el-col>
        <el-col :span="8" v-if="data.tabs_activeName == '1'">{{ data.cgqmc }}</el-col>
        <el-col :span="4" class="text-right" v-if="data.tabs_activeName == '1'">所属设备:</el-col>
        <el-col :span="8" v-if="data.tabs_activeName == '1'">{{ data.sssb }}</el-col>
        <el-col :span="4" class="text-right" v-if="data.tabs_activeName == '2'">设备名称:</el-col>
        <el-col :span="8" v-if="data.tabs_activeName == '2'">{{ data.sbmc }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">位置:</el-col>
        <el-col :span="8">{{ data.bjdd }}</el-col>
        <el-col :span="4" class="text-right">处置状态:</el-col>
        <el-col :span="8">{{ data.bjczjdmc }}</el-col>
      </el-row>

      <div class="title-l-blue-box marg-b-10 marg-t-20">
        <span>处理记录</span>
      </div>
      <div class="posit-relat">
        <el-timeline style="max-width: 600px">
          <el-timeline-item v-for="(item, index) in handleProcessList" :key="index" :timestamp="item.createTime" color="#0bbd87" :hide-timestamp="true">
            <div class="line-2">
              <div class="">
                <span class="inblock">{{ item.maintenanceStepType }}</span>
                <span class="inblock marg-0_20">{{ item.createTime }}</span>
                <span class="inblock">处理人：{{ item.maintUserName }}</span>
              </div>
              <!-- <div class="" v-if="item.handleResult">研判结果：{{ item.handleResult }}</div> -->
              <div class="bg-color-ddd padd-5">
                <span>意见：</span>
                <span>{{ item.maintenanceMsg }}</span>
              </div>
              <div class="img-list" v-if="item.imagesList&&item.imagesList.length > 0">
                <el-image class="marg-r-10 marg-t-10" v-for="(img, index) in item.imagesList" :key="index" :src="img" :preview-src-list="item.imagesList" :initial-index="index" show-progress :preview-teleported="true" style="width: 100px; height: 100px" />
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
        <div class="not-data" v-if="handleProcessList.length == 0">暂无处理记录</div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { create } from "lodash";
import { computed, ref, onMounted, reactive, watch } from "vue";

import {
  // getDeviceAlarmInfo,
  getDeviceAlarmHandleInfo
} from "@/api/comprehensive-monitor/index";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      {
      }
    },
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};

watch(()=>props.data.gid, (val) => {
  if(val){
    fun_getInfo();//获取详情
  }
});

// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  // fun_getInfo();//获取详情
});

//处理记录
const handleProcessList = ref([
  // {
  //   createTime: '2018-04-15 10:10:10',
  //   typeValue: '1',
  //   typeName: '报警研判',
  //   handleUser: '处理人',
  //   remark: '设备故障，需要处理',
  //   handleResult: '报警处置',
  //   imagesList:['/static/images/construction/zh.png','/static/images/router/router.png','/static/images/emergency.png'],
  // },
  // {
  //   createTime: '2018-04-13',
  //   typeValue: '2',
  //   typeName: '故障维修',
  //   handleUser: '处理人',
  //   remark: '备注',
  // },
  // {
  //   createTime: '2018-04-11',
  //   typeValue: '3',
  //   typeName: '处置办结',
  //   handleUser: '处理人',
  //   remark: '备注',
  // },
]);

const fun_getInfo = async ()=>{
  const { data } = await getDeviceAlarmHandleInfo({gid:props.data.gid});
  if(data?.stepList&&data?.stepList.length>0){
    data.stepList.forEach(item=>{
      item.imagesList = item.fileList.map(item=>item.fileInfo.path);
    });
  }
  handleProcessList.value=data?.stepList||[];
  // //报警研判
  // if(data.judgment){
  //   handleProcessList.value.push({
  //     createTime: data.judgment.bjypsj,
  //     typeValue: '1',
  //     typeName: '报警研判',
  //     handleUser: data.judgment.yprmc,
  //     remark: data.judgment.ypryj,
  //     handleResult: data.judgment.bjypjgStr||'',
  //     imagesList: data.judgment.imagesList||[],//(目前没发现这个字段)
  //   });
  // }
  // //报警派发
  // if(data.dispatch){
  //   handleProcessList.value.push({
  //     createTime: data.dispatch.pfsj,
  //     typeValue: '2',
  //     typeName: '报警派发',
  //     handleUser: data.dispatch.pfrymc,
  //     remark: data.dispatch.pfyj,
  //     handleResult: data.dispatch.pfjgStr||'',//(目前没发现这个字段)
  //     imagesList: data.dispatch.imagesList||[],//(目前没发现这个字段)
  //   });
  // }
  // //报警核查
  // if(data.verifyList&&data.verifyList.length>0){
  //   handleProcessList.value.push(...data.verifyList.map(item=>{
  //     return {
  //       createTime: item.hcsj,
  //       typeValue: '3',
  //       typeName: '报警核查',
  //       handleUser: item.hcrymc,
  //       remark: item.hcyj,
  //       handleResult: item.bjhcjgStr||'',//(目前没发现这个字段)
  //       imagesList: item.imagesList||[],//(目前没发现这个字段)
  //     }
  //   }));
  // }
  // //报警处置
  // if(data.handlerList&&data.handlerList.length>0){
  //   handleProcessList.value.push(...data.handlerList.map(item=>{
  //     return {
  //       createTime: item.bjczsj,
  //       typeValue: '4',
  //       typeName: '报警处置',
  //       handleUser: item.bjczrymc,
  //       remark: item.bjczyj,
  //       handleResult: item.bjczztStr||'',//(目前没发现这个字段)
  //       imagesList: item.imagesList||[],//(目前没发现这个字段)
  //     }
  //   }));
  // }
  // //报警办结
  // if(data.concludeList&&data.concludeList.length>0){
  //   handleProcessList.value.push(...data.concludeList.map(item=>{
  //     return {
  //       createTime: item.bjbjsj,
  //       typeValue: '5',
  //       typeName: '报警办结',
  //       handleUser: item.bjbjrymc,
  //       remark: item.bjbjyj,
  //       handleResult: item.bjbjztStr||'',//(目前没发现这个字段)
  //       imagesList: item.imagesList||[],//(目前没发现这个字段)
  //     }
  //   }));
  // }
}


</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
