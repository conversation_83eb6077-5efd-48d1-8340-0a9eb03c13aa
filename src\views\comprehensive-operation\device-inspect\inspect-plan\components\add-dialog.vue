<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" :close-on-click-modal="false" class="dialog" @open="fun_open">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form label-width="112" :inline="true" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
          <el-form-item label="计划名称:" prop="inspectionPlanName">
            <el-input v-model="formData.inspectionPlanName" placeholder="请输入" clearable />
            <!-- <el-select v-model="formData.qlid" placeholder="请选择" clearable @change="fun_qlbmChange">
              <el-option v-for="(item, index) in qlOption" :key="index" :label="item.qlmc" :value="item.id" />
            </el-select> -->
          </el-form-item>
          <el-form-item label="巡检类型:" prop="taskType">
            <el-select v-model="formData.taskType" placeholder="请选择" clearable @change="fun_taskTypeChange">
              <el-option v-for="(item, index) in taskTypeOption" :key="index" :label="item.businessLabel" :value="item.businessValue" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="巡检设备:" prop="deviceCode">
            <el-select v-model="formData.deviceCode" placeholder="请选择" clearable @change="fun_deviceChange">
              <el-option v-for="(item, index) in deviceOption" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item> -->
          <el-form-item label="巡检项:" prop="inspectionItemIdArr">
            <el-select v-model="formData.inspectionItemIdArr" placeholder="请选择" multiple :collapse-tags="true" collapse-tags-tooltip @change="fun_inspectionItemChange">
              <el-option v-for="(item, index) in inspectionItemOption" :key="index" :label="item.itemName" :value="item.gid" />
            </el-select>
          </el-form-item>
          <el-form-item label="计划时间:" prop="dateRange">
            <el-date-picker v-model="formData.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="fun_dateChange" />
          </el-form-item>
          <el-form-item label="巡检周期:" prop="cycleType">
            <div class="width-all flex flex-both">
              <el-input-number v-model="formData.cycle" :min="1" :max="99" :precision="1" :step-strictly="true" />
              <el-select style="width: 80px;" v-model="formData.cycleType" placeholder="请选择" @change="fun_cycleTypeChange">
                <el-option v-for="(item, index) in cycleTypeOption" :key="index" :label="item.businessLabel" :value="item.businessValue" />
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="巡检时限:" prop="limitType">
            <el-select v-model="formData.limitType" placeholder="请选择" @change="fun_limitTypeChange">
              <el-option v-for="(item, index) in limitTypeOption" :key="index" :label="item.businessLabel" :value="item.businessValue" />
            </el-select>
            <div class="width-all flex padd-t-5" v-if="formData.limitType=='3'">
              <el-input-number v-model="formData.limitTime" :min="1" :max="99" :precision="1" :step-strictly="true" />
              <span class="padd-l-5">天</span>
              <!-- <el-select style="width: 80px;" v-model="formData.xjsxUnit" placeholder="请选择" @change="fun_xjsxUnitChange">
                <el-option v-for="(item, index) in xjsxUnitOption" :key="index" :label="item.businessLabel" :value="item.businessValue" />
              </el-select> -->
            </div>
          </el-form-item>
          <el-form-item label="巡检人员:" :prop="formData.memberType==1?'inspectionPersonnelCode':'groupId'">
            <el-radio-group v-model="formData.memberType" @change="fun_memberTypeChange">
              <el-radio :value="1">选择人员</el-radio>
              <el-radio :value="2">选择小组</el-radio>
            </el-radio-group>
            <el-cascader v-if="formData.memberType==1" class="width-all" ref="inspectionPersonnelTreeRef" v-model="formData.inspectionPersonnelCode" :options="inspectionPersonnelTree" :props="inspectionPersonnelTreeProps" show-all-levels collapse-tags :collapse-tags-tooltip="true" clearable @change="fun_inspectionPersonnelChange" placeholder="请选择" />
            <el-select v-else class="width-all" v-model="formData.groupId" clearable placeholder="请选择" @change="fun_groupChange">
              <el-option v-for="(item, index) in groupOption" :key="index" :label="item.inspectionTeamName" :value="item.inspectorId" />
            </el-select>
          </el-form-item>
          <div>
            <el-form-item label="跳过节假日:" prop="holidayStatus">
              <el-switch width="50" v-model="formData.holidayStatus" :active-value="1" :inactive-value="0" active-text="是" inactive-text="否" inline-prompt />
            </el-form-item>
          </div>
          <div>
            <el-form-item label="启用状态:" prop="triggerStatus">
              <el-switch width="50" v-model="formData.triggerStatus" :active-value="1" :inactive-value="0" active-text="启用" inactive-text="停用" inline-prompt />
            </el-form-item>
          </div>
          <!-- <el-form-item label="上传附件:" prop="fileAllList">
            <el-upload
              v-model:file-list="formData.fileAllList"
              class="upload-demo"
              :action="action"
              :headers="headers"
              :limit="1"
              :before-upload="beforeAvatarUpload"
              :on-success="handleAvatarSuccess"
            >
              <el-button type="primary" :disabled="formData.fileAllList?.length >= 1">点击上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  文件大小不超过5MB,支持扩展名:doc,docx,pdf
                </div>
              </template>
            </el-upload>
          </el-form-item> -->
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";

import {
  getInspectItemList,
  // getDeviceTree,
  getPersonnelTree,
  getGroupOptions,
  add,
  update,
} from "@/api/comprehensive-operation/device-inspect/index";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";

import { ElMessage } from "element-plus";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开
const fun_open = () => {
  console.log('打开fun_open:',props.data);
  // 回显附件
  // if (props.data.fileList) {
  //   props.data.fileAllList = props.data.fileList.map((e) => {
  //     return {
  //       name: e.fileName,
  //       url: import.meta.env.VITE_APP_IMG_URL+e.filePath,
  //     };
  //   });
  // }
}

// 表单数据-回显
const formData = computed(() => {
  console.log('props.data:',props.data);
  //设置初始默认状态
  if(props.data.startTime)props.data.dateRange=[props.data.startTime,props.data.endTime];//计划时间
  if(props.data.cycle===undefined)props.data.cycle = 1;//巡检周期
  // if(props.data.cycleType===undefined)props.data.cycleType = '1';
  // if(props.data.limitType===undefined)props.data.limitType = '1';
  if(props.data.limitTime===undefined)props.data.limitTime = 1;//巡检时限
  // if(props.data.xjsxUnit===undefined)props.data.xjsxUnit = '1';//巡检时限单位
  //巡检项
  if(props.data.inspectionItemId==undefined||props.data.inspectionItemId==''){
    props.data.inspectionItemIdArr = [];
  }else{
    props.data.inspectionItemIdArr = props.data.inspectionItemId.split(',');
  }
  //巡检人员类型
  if(!props.data.groupId){
    props.data.memberType = 1;
    props.data.groupId = '';
    props.data.groupName = '';
    if(props.data.inspectionPersonnelId)props.data.inspectionPersonnelCode = props.data.inspectionPersonnelId.split(',');
  }else{
    props.data.memberType = 2;
    props.data.inspectionPersonnelId = '';
    props.data.inspectionPersonnel = '';
  }

  if(props.data.holidayStatus===undefined)props.data.holidayStatus = 1;
  if(props.data.triggerStatus===undefined)props.data.triggerStatus = 1;


  return props.data;
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});


onMounted(async () => {
  fun_getDicts();//获取字典
  fun_getInspectItemList();//获取巡检项筛选列表
  // fun_getDeviceOptions();//获取巡检设备
  fun_getInspectionPersonnelTree();//获取巡检人员
  fun_getGroupOptions();//获取巡检小组
});

// 获取字典
const fun_getDicts = async () => {
  const {data:taskTypeOptionData} = await getDict('task_type');//获取巡检类型
  taskTypeOption.value = taskTypeOptionData.list;
  console.log('巡检类型taskTypeOption:',taskTypeOption.value);
  const {data:cycleTypeOptionData} = await getDict('cycle_type');//获取巡检周期
  cycleTypeOption.value = cycleTypeOptionData.list;
  console.log('巡检周期cycleTypeOption:',cycleTypeOption.value);
  const {data:limitTypeOptionData} = await getDict('limit_type');//获取巡检时限
  limitTypeOption.value = limitTypeOptionData.list;
  console.log('巡检时限limitTypeOption:',limitTypeOption.value);
}

// 巡检类型
const taskTypeOption = ref([]);
// 巡检类型选择
const fun_taskTypeChange = (val) => {
  console.log('巡检类型选择:',val);
  // formData.value.bglxmc = taskTypeOption.value.find(item => item.businessValue == val).businessLabel;
}
//巡检周期单位
const cycleTypeOption = ref([
  // { label: '天', value: '1' },
  // { label: '周', value: '2' },
  // { label: '月', value: '3' },
  // { label: '季', value: '4' },
  // { label: '年', value: '5' },
])
// 巡检周期单位选择
const fun_cycleTypeChange = (val) => {
  console.log('巡检周期单位选择:',val);
  // formData.value.bglxmc = cycleTypeOption.value.find(item => item.businessValue == val).businessLabel;
}

// 巡检时限类型
const limitTypeOption = ref([
  // { label: '当天', value: '1' },
  // { label: '下次周期开始前', value: '2' },
  // { label: '自定义', value: '3' },
])
// 巡检时限类型选择
const fun_limitTypeChange = (val) => {
  console.log('巡检时限类型选择:',val);
  // formData.value.bglxmc = limitTypeOption.value.find(item => item.businessValue == val).businessLabel;
}

//巡检时限单位
// const xjsxUnitOption = ref([
//   { label: '天', value: '1' },
//   { label: '周', value: '2' },
//   { label: '月', value: '3' },
//   { label: '季', value: '4' },
//   { label: '年', value: '5' },
// ])
// 巡检时限单位选择
// const fun_xjsxUnitChange = (val) => {
//   console.log('巡检时限单位选择:',val);
//   formData.value.bglxmc = xjsxUnitOption.value.find(item => item.businessValue == val).businessLabel;
// }

// 巡检项
const inspectionItemOption = ref([
  // { itemName: '巡检项1', gid: '1' },
  // { itemName: '巡检项2', gid: '2' },
  // { itemName: '巡检项3', gid: '3' },
  // { itemName: '巡检项4', gid: '4' },
  // { itemName: '巡检项5', gid: '5' },
]);
const fun_getInspectItemList = async () => {
  const {data} = await getInspectItemList({pageSize:1000,pageNum:1})
  console.log('巡检项列表inspectionItemOption:',data.list);
  inspectionItemOption.value = data.list;
}
// 巡检项选择
const fun_inspectionItemChange = (val) => {
  console.log('巡检项选择:',val);
  formData.value.inspectionItemId = val.join(',');
  formData.value.inspectionItemName = val.map(item => inspectionItemOption.value.find(v => v.gid == item).itemName).join(',');
}

// 计划时间
const fun_dateChange = (val) => {
  console.log('计划时间选择:',val);
  if(val){
    formData.value.startTime = val[0] + ' 00:00:00';
    formData.value.endTime = val[1] + ' 23:59:59';
  }else{
    formData.value.startTime = '';
    formData.value.endTime = '';
  }
}

// 获取巡检设备
// const deviceOption = ref([]);
// const fun_getDeviceOptions = async () => {
//   const {data} = await getDeviceTree()
//   deviceOption.value = data.list;
//   console.log('巡检设备列表deviceOption:',deviceOption.value);
// }
// 巡检设备选择
// const fun_deviceChange = (val) => {
//   console.log('巡检设备选择:',val);
//   formData.value.deviceName = deviceOption.value.find(item => item.id == val).name;
// }

// 获取巡检人员
const inspectionPersonnelTreeProps = {
  label: 'label',
  value: 'id',
  children: 'childrenList',
  multiple: true,//是否多选
  emitPath: false,//是否返回路径
}
const inspectionPersonnelTree = ref([
  // {
  //   label: '巡检人员',
  //   id: '0',
  //   childrenList: [
  //     { label: '巡检人员1', id: '1' },
  //     { label: '巡检人员2', id: '2' },
  //     { label: '巡检人员3', id: '3' },
  //     { label: '巡检人员4', id: '4' },
  //   ]
  // }
]);
const fun_getInspectionPersonnelTree = async () => {
  const {data} = await getPersonnelTree()
  inspectionPersonnelTree.value = data;
  // console.log('巡检人员列表inspectionPersonnelTree:',inspectionPersonnelTree.value);
}
// 巡检人员选择
const inspectionPersonnelTreeRef = ref();
const fun_inspectionPersonnelChange = (data) => {
  const checkedNodes = inspectionPersonnelTreeRef.value.getCheckedNodes(true);//获取选中的叶子节点
  // console.log('巡检人员-节点点击:',data,checkedNodes);
  formData.value.inspectionPersonnelId = checkedNodes.map(item => item.value).join(',');
  formData.value.inspectionPersonnel = checkedNodes.map(item => item.label).join(',');
}

// 获取巡检小组
const groupOption = ref([]);
const fun_getGroupOptions = async () => {
  const {data} = await getGroupOptions({pageNum:1,pageSize:999999})
  groupOption.value = data.list;
  console.log('巡检小组列表groupOption:',groupOption.value);
}
// 巡检小组选择
const fun_groupChange = (data) => {
  console.log('巡检小组-节点点击:',data);
  // formData.value.groupId = data;
  formData.value.groupName = groupOption.value.find(item => item.inspectorId == data).inspectionTeamName;
}

// 巡检人员类型切换
const fun_memberTypeChange = (val) => {
  console.log('巡检人员类型切换:',val);
  if(val==1){
    formData.value.groupId = '';
    formData.value.groupName = '';
  }else{
    formData.value.inspectionPersonnelCode = '';
    formData.value.inspectionPersonnelId = '';
    formData.value.inspectionPersonnel = '';
  }
}



// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  inspectionPlanName: [
    { required: true, message: "请输入", trigger: "blur" },
  ],
  taskType: [
    { required: true, message: "请选择", trigger: "blur" },
  ],
  // deviceCode: [
  //   { required: true, message: "请选择", trigger: "blur" },
  // ],
  inspectionPersonnelCode: [
    { required: true, message: "请选择", trigger: "blur" },
  ],
  groupId: [
    { required: true, message: "请选择", trigger: "blur" },
  ],
  dateRange: [
    { required: true, message: "请选择", trigger: "blur" },
  ],
  cycleType: [
    { required: true, message: "请选择", trigger: "blur" },
  ],
  limitType: [
    { required: true, message: "请选择", trigger: "blur" },
  ],
  xjsxUnit: [
    { required: true, message: "请选择", trigger: "blur" },
  ],
  inspectionItemIdArr: [
    { required: true, message: "请选择", trigger: "blur" },
    { validator: (rule, value, callback) => {
        if (value.length > 10) {
          callback(new Error('最多选择10个'));
        } else {
          callback();
        }
      }, 
      trigger: 'change' 
    },
  ],

  // fileAllList: [
  //   { required: true, message: "请上传附件", trigger: "blur" },
  // ],
});


// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (props.title == "新增") {
        const res = await add(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      } else {
        const res = await update(formData.value);
        ElMessage({
          message: res.message,
          type: "success",
        });
        emits("refreshList");
        emits("onChangeVisible");
      }
    }
  });
};


// 上传附件
/* const action = import.meta.env.VITE_APP_IMG_URL + "/api/platform/system/fileInfo/uploadFile";
const headers = { Authorization: "Bearer " + getToken() };
const beforeAvatarUpload = (file) => {
  const fileExtensions = ["doc", "docx", "pdf"];
  if (formData.value.fileAllList) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传doc,docx,pdf格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("文件大小不能超过5MB");
    return false;
  }
}; */
/**文件上传成功回调 */
// const handleAvatarSuccess = (response, file) => {
//   formData.value.fileList = formData.value.fileAllList.map((e) => {return {fileid:e.response.data.id ?? e.id}});
//   // formData.value.fj = formData.value.fileAllList.map((e) => e.response.data.originalName ?? e.originalName).toString();
// };

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
