import { defineConfig, loadEnv } from "vite";
import path from "path";
import createVitePlugins from "./vite/plugins";
import eslint from "vite-plugin-eslint";

import vueJsx from "@vitejs/plugin-vue-jsx";

import postCssPxToRem from "postcss-pxtorem";

const pxtoremSystems = ["special"];

const excludeFiles = [];

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: "./",
    plugins: [createVitePlugins(env, command === "build"), vueJsx()],
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        "~": path.resolve(__dirname, "./"),
        // 设置别名
        "@": path.resolve(__dirname, "./src"),
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
    },
    // vite 相关配置
    server: {
      port: 8100,
      host: true,
      open: true,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        "/dev-api": {
          target: "http://************:8080",
          // target: "http://*************:8000/prod-api-oper",
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, ""),
        },
        "/cm-api": {
          // target: "http://*************:8000/cm-server",
          // target: "http://*************:8001",
          target: "http://************:8000",
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/cm-api/, ""),
        },
         "/my-api": {
          // target: "http://*************:8000/cm-server",
          // target: "http://*************:8001",
          target: "http://************:8091/",
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/my-api/, "/my-api"),
        },
      },
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: "internal:charset-removal",
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === "charset") {
                  atRule.remove();
                }
              },
            },
          },
          postCssPxToRem({
            rootValue: 192, // UI设计稿的宽度/10
            unitPrecision: 3, // 转rem精确到小数点多少位
            propList: ["*"], // 需要转换的属性 *表示所有
            selectorBlackList: ["norem"], // 不进行px转换的选择器
            replace: true, // 是否直接更换属性值，而不添加备用属性
            mediaQuery: false, // 是否在媒体查询的css代码中也进行转换
            minPixelValue: 0, // 设置要替换的最小像素值
            //   exclude: file => {
            //     const isExclude = excludeFiles.some(
            //         url => file.indexOf(url) >= 0
            //     )
            //     if (isExclude) return true
            //     return pxtoremSystems.every(sys => file.indexOf(sys) === -1)
            // } // 排除node_modules文件夹下的文件
          }),
        ],
      },
    },
  };
});
