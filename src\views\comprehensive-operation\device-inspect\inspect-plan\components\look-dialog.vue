<template>
  <el-dialog v-model="isVisible" width="860" :show-close="false" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>详情</p>
        <p class="closeIcon" @click="returnBtn">
          <el-icon><CloseBold /></el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">计划名称:</el-col>
        <el-col :span="8">{{ data.inspectionPlanName }}</el-col>
        <el-col :span="4" class="text-right">巡检类型:</el-col>
        <el-col :span="8">{{ taskTypeOption.find(item => item.businessValue === data.taskType)?.businessLabel }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">巡检项:</el-col>
        <!-- <el-col :span="8">{{ inspectionItemOption.find(item => item.businessValue === data.inspectionItemId)?.businessLabel }}</el-col> -->
        <el-col :span="8">{{ data.inspectionItemName }}</el-col>
        <el-col :span="4" class="text-right">计划时间:</el-col>
        <el-col :span="8">{{ data.startTime?.split(' ')[0] }} - {{ data.endTime?.split(' ')[0] }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">巡检周期:</el-col>
        <el-col :span="8">{{ data.cycle }} {{ cycleTypeOption.find(item => item.businessValue === data.cycleType)?.businessLabel }}</el-col>
        <el-col :span="4" class="text-right">巡检时限:</el-col>
        <el-col :span="8">
          <span v-if="data.limitType!=3">{{ limitTypeOption.find(item => item.businessValue === data.limitType)?.businessLabel }}</span>
          <span class="padd-r-5" v-else>{{ data.limitTime }} 天</span>
        </el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">巡检人员:</el-col>
        <el-col :span="8">{{ data.inspectionPersonnel }}</el-col>
      </el-row>
      <el-row :gutter="10" class="marg-b-10">
        <el-col :span="4" class="text-right">跳过节假日:</el-col>
        <el-col :span="8">{{ holidayStatusOption.find(item => item.value === data.holidayStatus)?.label }}</el-col>
        <el-col :span="4" class="text-right">启用状态:</el-col>
        <el-col :span="8">{{ triggerStatusOption.find(item => item.value === data.triggerStatus)?.label }}</el-col>
      </el-row>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="returnBtn">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";
import { getDict } from "@/api/common";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {
      {
      }
    },
  },
});
const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    emits("onChangeVisible");
  },
});
const emits = defineEmits(["onChangeVisible"]);

const returnBtn = () => {
  emits("onChangeVisible");
};

// 挂载
onMounted(async () => {
  fun_getDicts();//获取字典
})

// 获取字典
const fun_getDicts = async () => {
  const {data:taskTypeOptionData} = await getDict('task_type');//获取巡检类型
  taskTypeOption.value = taskTypeOptionData.list;
  console.log('巡检类型taskTypeOption:',taskTypeOption.value);
  const {data:cycleTypeOptionData} = await getDict('cycle_type');//获取巡检周期
  cycleTypeOption.value = cycleTypeOptionData.list;
  console.log('巡检周期cycleTypeOption:',cycleTypeOption.value);
  const {data:limitTypeOptionData} = await getDict('limit_type');//获取巡检时限
  limitTypeOption.value = limitTypeOptionData.list;
  console.log('巡检时限limitTypeOption:',limitTypeOption.value);
}

// 巡检类型
const taskTypeOption = ref([]);
// 巡检周期
const cycleTypeOption = ref([]);
// 巡检时限
const limitTypeOption = ref([]);

// 巡检项
const inspectionItemOption = ref([]);

//跳过节假日
const holidayStatusOption = ref([
  { label: "是", value: 1, type: "success" },
  { label: "否", value: 0, type: "danger" },
]);
//启用状态
const triggerStatusOption = ref([
  { label: "启用", value: 1, type: "success" },
  { label: "停用", value: 0, type: "danger" },
]);

</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    padding: 24px 50px;
    box-sizing: border-box;
  }
}
</style>
