<template>
  <div class="gird-box">
    <div class="gird-item" v-for="(item, index) in props.datas" :key="index">
      <div class="gird-content">
        <div class="gird-nums">{{ item.value }}</div>
        <div class="gird-unit">{{ item.unit }}</div>
      </div>
      <div class="gird-label">{{ item.label }}</div>
    </div>
  </div>
</template>
<script setup>
import nums from "../count-up.vue";
const props = defineProps({
  datas: {
    type: Array,
    default: () => {
      return [
        {
          label: "耗电量",
          value: "0",
          unit: "度",
        },
        {
          label: "耗水量",
          value: "0",
          unit: "吨",
        },
        {
          label: "总能耗",
          value: "0",
          unit: "",
        },
      ];
    },
    required: true,
  },
});
</script>
<style lang="scss" scoped>
.gird-box {
  display: grid;
  width: 100%;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  .gird-item {
    width: 136px;
    height: 64px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .gird-content {
      margin-bottom: 4px;
      display: flex;
      justify-content: center;
      align-items: baseline;
      .gird-nums {
        font-family: D-DIN-PRO, D-DIN-PRO;
        font-weight: bold;
        font-size: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(180deg, #ffffff 16%, #81a4ff 80%);
        -webkit-background-clip: text;
        color: transparent;
      }
      .gird-unit {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        margin-left: 4px;
        line-height: 24px;
        color: rgba(199, 214, 255, 0.64);
      }
    }
    .gird-label {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
    }
    &:nth-child(1) {
      background: url(@/assets/data-screen/module-3-gird-bg-1.png) no-repeat;
      background-size: 100% 100%;
    }
    &:nth-child(2) {
      background: url(@/assets/data-screen/module-3-gird-bg-2.png) no-repeat;
      background-size: 100% 100%;
    }
    &:nth-child(3) {
      background: url(@/assets/data-screen/module-3-gird-bg-1.png) no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
