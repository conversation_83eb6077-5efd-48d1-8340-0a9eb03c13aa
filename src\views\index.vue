<template>
  <div class="app-container home">
    首页
  </div>
</template>

<script setup name="Index">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
const router = useRouter();

/* 跳转到顶部导航栏第一个叶子路由 */
/* // 直接获取所有路由
// console.log('router', router);
const routes = router.getRoutes();
// console.log('routes', routes);
const routes_path0 = routes.find(route => route.path).path;
// console.log('routes_path0', routes_path0);
// 跳转到第一个有路径的路由-由于此路由排序是从最深层级开始的正序排列,所以不太理想
if(routes_path0)router.replace(routes_path0); */

// 从store中获取顶部导航栏所有路由
import usePermissionStore from "@/store/modules/permission";
const permissionStore = usePermissionStore();
const store_routers = computed(() => permissionStore.topbarRouters);
// console.log('store_routers', store_routers.value);
// 调用递归函数获取第一个叶子路由路径
const routes_path0 = fun_findFirstLeafRoute(store_routers.value);
// console.log('routes_path0', routes_path0);
// 跳转到顶部导航栏第一个叶子路由
if(routes_path0)router.replace(routes_path0);


// 递归函数，用于查找第一个叶子路由路径
function fun_findFirstLeafRoute(routes) {
  for (const route of routes) {
    if (route.children && route.children.length > 0) {
      return fun_findFirstLeafRoute(route.children);
    } else {
      return route.path;
    }
  }
}


</script>

<style scoped lang="scss">
// .home {}
</style>

