import { createApp } from "vue";

import Cookies from "js-cookie";

import ElementPlus, { ElMessage } from "element-plus";
import "element-plus/dist/index.css";
import locale from "element-plus/es/locale/lang/zh-cn";
import zhCn from "element-plus/es/locale/lang/zh-cn";

import "@/assets/styles/index.scss"; // global css
import './assets/fonts/font.css';
import './assets/styles/animate.css';
import App from "./App.vue";
import store from "./store";
import router from "./router";
import directive from "./directive"; // directive
import 'amfe-flexible'
// 注册指令
import plugins from "./plugins"; // plugins
import { download } from "@/utils/request";

// svg图标
import "virtual:svg-icons-register";
import SvgIcon from "@/components/SvgIcon/index.vue";
import elementIcons from "@/components/SvgIcon/svgicon";
import "@hgzh/hgzh2d-sdk/dist/HGZH2D.min.css";

import "./permission"; // permission
import "@/styles/main.scss";
import "@/styles/theme-default.css";
import useUserStore from "@/store/modules/user";
import usePermissionStore from "@/store/modules/permission";
import { isHttp } from "@/utils/validate";

const env = (import.meta as any).env;
const mode = env.MODE;
console.log(mode, "modemodemode===========");

if (mode === "development") {
  const script = document.createElement("script");
  script.src = "./config_dev.js";
  document.body.appendChild(script);
} else if (mode === "production") {
  const script = document.createElement("script");
  script.src = "./config_pro.js";
  document.body.appendChild(script);
}
const app = createApp(App);

app.use(store);
app.use(router);
app.use(plugins);
app.use(elementIcons);
app.component("svg-icon", SvgIcon as any);

HGZH2D.ready({});

DC.ready({}).then(()=> {
  (window as any).HGZH3D = DC.getLib("Cesium");
});
directive(app);



// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale: zhCn,
});

app.mount("#app");

// 全局禁用error报错提示
ElMessage.error = (): any => {}


