<template>
  <el-dialog
    v-model="isVisible"
    width="860"
    :show-close="false"
    :close-on-click-modal="false"
    class="dialog"
    @open="fun_open"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ title }}</p>
        <p class="closeIcon" @click="cancelBtn">
          <el-icon>
            <CloseBold />
          </el-icon>
        </p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form
          label-width="112"
          :inline="false"
          :model="formData"
          ref="ruleFormRef"
          :rules="rules"
          status-icon
        >
          <el-form-item
            label="维修人:"
            prop="person"
            v-if="props.data.status == 0"
          >
            <el-select
              v-model="formData.person"
              placeholder="请选择维修人员"
              clearable
              value-key="personGid"
            >
              <el-option
                v-for="(item, index) in personList"
                :key="index"
                :label="item.personName"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="维修状态:"
            prop="status"
            v-if="props.data.status == 1 || props.data.status == 3"
          >
            <el-select
              class="width-all"
              ref="xjryTreeRef"
              v-model="formData.status"
              placeholder="请选择"
            >
              <el-option label="维修完成" :value="1"></el-option>
              <el-option label="无需维修" :value="2"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="办结:" prop="type" v-if="props.data.status == 2">
            <el-radio-group v-model="formData.type">
              <el-radio :value="1">办结</el-radio>
              <el-radio :value="2">退回</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="说明:" prop="maintenanceMsg">
            <el-input
              type="textarea"
              v-model="formData.maintenanceMsg"
              placeholder="请输入"
              :rows="3"
              maxlength="500"
              resize="none"
              :show-word-limit="true"
            />
          </el-form-item>
          <el-form-item
            label="图片:"
            prop="fileListEdit"
            v-if="props.data.status == 1 || props.data.status == 3"
            style="display: flex"
          >
            <el-upload
              v-model:file-list="formData.fileListEdit"
              :action="action"
              :headers="headers"
              list-type="picture-card"
              :limit="9"
              :on-success="handlePictureCardSuccess"
              :before-upload="handlePictureCardBeforeUpload"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" @click="submitBtn(ruleFormRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, reactive } from "vue";
import { getDict } from "@/api/common";
import { getToken } from "@/utils/auth";
import {
  getPersonList,
  addTMaintenanceStep,
} from "@/api/comprehensive-operation/device-maint-conserve/maint-manage";
import { ElMessage } from "element-plus";

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
// 打开
const fun_open = () => {
  console.log("打开fun_open:", props.data);
};
const action =
  import.meta.env.VITE_APP_IMG_URL + "/api/platform/system/fileInfo/uploadFile";
const headers = { Authorization: "Bearer " + getToken() };
// 上传图片前
const handlePictureCardBeforeUpload = (file) => {
  console.log("上传图片前:", file);
  const fileExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
  if (formData.value.fileListEdit) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传jpg,jpeg,png,gif,bmp,webp格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("图片大小不能超过5MB");
    return false;
  }
};
// 上传图片成功
const handlePictureCardSuccess = (file) => {
  console.log("上传图片成功:", file);
  formData.value.fileList = formData.value.fileListEdit.map((e) => ({
    fileGid: e.response.data.gid ?? e.gid,
    // originalName: e.response.data.originalName ?? e.originalName,
  }));
};
// 表单数据-回显
const formData = ref({
  maintenanceStep: "",
  maintenanceStepType: 0,
  maintenanceMsg: "",
  maintUserId: "",
  status: 1,
  type: 1,
  maintUserName: "",
  person: "",
  fileListEdit: [],
  fileList: [],
});
const emits = defineEmits(["onChangeVisible", "refreshList"]);

const isVisible = computed({
  get: () => props.dialogVisible,
  set: () => {
    formData.value = {
      maintenanceStep: "",
      maintenanceStepType: 0,
      maintenanceMsg: "",
      maintUserId: "",
      status: 1,
      type: 1,
      maintUserName: "",
      person: "",
      fileListEdit: [],
      fileList: [],
    };
    emits("onChangeVisible");
  },
});

onMounted(async () => {
  // fun_getDicts();//获取字典
  getPerson(); //获取人员
});

const personList = ref([]);
const getPerson = async () => {
  let currentDate = new Date().toISOString().split("T")[0];
  let params = {
    startTime: currentDate + " 00:00:00",
    endTime: currentDate + " 23:59:59",
  };
  let res = await getPersonList(params);
  if (res.code == 0) {
    personList.value = res?.data?.list;
  }
  console.log("巡检人员列表res:", res);
  // const {data} = await getQlOptions()
};

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  person: [{ required: true, message: "请选择", trigger: "blur" }],
  type: [{ required: true, message: "请选择", trigger: "change" }],
  // xjryType: [{ required: true, message: "请选择", trigger: "blur" }],
  // remark: [
  //   { required: true, message: "请填写说明", trigger: "blur" },
  // ],

  fileListEdit: [{ required: true, message: "请上传附件", trigger: "blur" }],
});

// 取消
const cancelBtn = () => {
  emits("onChangeVisible");
};
// 确定
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      console.log("表单数据:", formData.value, props.data);
      if (props.data.status == 0) {
        let params = {
          maintenanceId: props.data.maintenanceId,
          maintenanceStep: "派发",
          maintenanceStepType: "1",
          maintenanceMsg: formData.value.maintenanceMsg,
          maintUserId: formData.value.person.planGid,
          maintUserName: formData.value.personName,
        };
        let res = await addTMaintenanceStep(params);
        if (res.code == 0) {
          ElMessage({
            message: "操作成功",
            type: "success",
          });
          emits("refreshList");
          emits("onChangeVisible");
        }
      }
      if (props.data.status == 1 || props.data.status == 3) {
        let params = {
          maintenanceId: props.data.maintenanceId,
          maintenanceStep: "处置",
          maintenanceStepType: "2",
          maintenanceMsg: formData.value.maintenanceMsg,
          fileList: formData.value.fileList,
          remark:(formData.value.status==1)?'维修完成':'无需维修'
        };
        console.log("params:", params);
        let res = await addTMaintenanceStep(params);
        if (res.code == 0) {
          ElMessage({
            message: "操作成功",
            type: "success",
          });
          emits("refreshList");
          emits("onChangeVisible");
        }
      }
      if (props.data.status == 2) {
        //办结
        if (formData.value.type == 1) {
          let params = {
            maintenanceId: props.data.maintenanceId,
            maintenanceStep: "办结",
            maintenanceStepType: "4",
            maintenanceMsg: formData.value.maintenanceMsg,
          };
          let res = await addTMaintenanceStep(params);
          if (res.code == 0) {
            ElMessage({
              message: "操作成功",
              type: "success",
            });
            emits("refreshList");
            emits("onChangeVisible");
          }
        } else if (formData.value.type == 2) {
          //回退
          let params = {
            maintenanceId: props.data.maintenanceId,
            maintenanceStep: "回退",
            maintenanceStepType: "3",
            maintenanceMsg: formData.value.maintenanceMsg,
           
          };
          let res = await addTMaintenanceStep(params);
          if (res.code == 0) {
            ElMessage({
              message: "操作成功",
              type: "success",
            });
            emits("refreshList");
            emits("onChangeVisible");
          }
        }
      }
      //  const res = await addReport(formData.value);
      //     ElMessage({
      //       message: res.message,
      //       type: "success",
      //     });
      //     emits("refreshList");
      //     emits("onChangeVisible");
    }
  });
};

// 上传附件
/* const action = import.meta.env.VITE_APP_IMG_URL + "/api/platform/system/fileInfo/uploadFile";
const headers = { Authorization: "Bearer " + getToken() };
const beforeAvatarUpload = (file) => {
  const fileExtensions = ["doc", "docx", "pdf"];
  if (formData.value.fileAllList) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    console.log(fileExtensions.indexOf(fileExtension));
    if (fileExtensions.indexOf(fileExtension) < 0) {
      ElMessage.warning("请上传doc,docx,pdf格式的文件!");
      return false;
    }
  }
  const isLt = file.size / 1024 / 1024 > 5;
  if (isLt) {
    ElMessage.warning("文件大小不能超过5MB");
    return false;
  }
}; */
/**文件上传成功回调 */
// const handleAvatarSuccess = (response, file) => {
//   formData.value.fileList = formData.value.fileAllList.map((e) => {return {fileGid:e.response.data.gid ?? e.gid}});
//   // formData.value.fj = formData.value.fileAllList.map((e) => e.response.data.originalName ?? e.originalName).toString();
// };
</script>

<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
