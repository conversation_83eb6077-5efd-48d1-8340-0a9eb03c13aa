<template>
  <div class="pointManage font-16">
    <div class="height-all border-radius-5 padd-20" style="background-color: #CCDCEF;">
      <div class="posit-relat flex flex-center flex-middle width-all height-all over-hide">
        <!-- style="width: 2000px;height: 1200px;" -->
        <img class="width-all" :src="`/static/images/building-floor/${buildingFloorActiveImgName}.png`" alt="">
        <!-- 楼栋和楼层选择 -->
        <div class="posit-absol top-0 left-0 flex flex-middle bg-color-fff border-radius-3" style="height:40px;">
          <div class="height-all border-radius-3" style="border: 1px solid #fff;">
            <el-select class="height-all buildingBtnList" style="width:100px;" v-model="buildingBtnValue" placeholder="选择楼栋">
              <el-option v-for="(item, index) in buildingBtnList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <ul class="flex flex-middle padd-0_10 floorBtn">
            <li class="" v-for="(item,key) of floorBtnList" :key="key">
              <span class="inblock marg-0_5 padd-0_15 border-radius-3 hover" :class="{floorBtnActive:floorBtnValue==item.value}" @click="floorBtnValue=item.value">{{ item.label }}</span>
            </li>
          </ul>
        </div>
        <!-- 设备列表定位 -->
        <ul>
          <li class="posit-absol hover" 
          :style="{left:(item.coordx||0)+'px',top:(item.coordy||0)+'px'}" 
          v-for="(item,key) of dataList" :key="key" 
          @mouseenter="fun_handleMouseEnter(item,key)"
          @mouseleave="fun_handleMouseLeave(item,key)"
          @click="fun_handleClick(item,key)"
          >
            <!-- <img width="50" :src="`/static/images/building-floor/icon/${item.imgName}.png`" alt=""> -->
            <img :src="item.filePath" alt="">
          </li>
        </ul>

        <!-- 详情弹窗 -->
        <div class="info-dialog flex flex-col filter-bg-blur font-14" v-show="dataInfoVisibility">
          <div class="flex flex-both padd-10_20 border-b-ddd">
            <div class="flex flex-middle">
              <!-- <img class="marg-0_5" style="width:18px;" src="/static/images/dialog-arrow.png" alt=""> -->
              <span class="font-16 font-bold">{{ dataInfo.name }}</span>
            </div>
            <div class="flex flex-right">
              <el-icon class="hover font-24" @click="dataInfoVisibility=false"><Close /></el-icon>
            </div>
          </div>
          <div class="padd-10_20">
            <span class="color-333">位置：</span>
            <span class="">{{ dataInfo.position||'暂无' }}</span>
          </div>
          <div class="height-all padd-20 padd-t-0 over-auto-y">
            <ul class="flex flex-wrap max-height-all over-auto-y">
              <li class="flex padd-5_0" style="width: 50%;" v-for="(item,key) of dataInfo.list" :key="key">
                <span class="color-333 padd-r-5" style="width:70%;">{{ item.name }}:</span>
                <span class="font-bold">{{ item.value||'暂无' }}</span>
                <span class="color-333 padd-l-5" v-if="item.unit">({{ item.unit }})</span>
            </li>
            </ul>
          </div>
        </div>
        <!-- 启停控制弹窗 -->
        <div class="control-dialog filter-bg-blur flex flex-col" v-show="dataControlVisibility">
          <div class="flex flex-middle flex-both padd-10_20 border-b-ddd">
            <div class="flex flex-middle">
              <!-- <el-icon class="font-18 marg-t-2 marg-r-10"><HelpFilled /></el-icon> -->
              <span class="font-16 font-bold">{{ formData.name }}</span>
            </div>
            <el-icon class="hover font-24" @click="dataControlVisibility=false"><Close /></el-icon>
          </div>
          <div class="padd-20 height-all over-auto-y">
            <div class="height-all over-auto-y">
              <div class="font-18 font-bold marg-b-20">远程启停</div>
              <div class="flex flex-around marg-b-20">
                <el-button :type="formData.sdqd=='1'?'default':'primary'" size="large" :dark="true" style="padding: 20px 50px;" @click="handleState('start')">{{formData.sdqd=='1'?'已启动':'启动'}}</el-button>
                <el-button :type="formData.sdqd=='1'?'primary':'default'" size="large" :dark="true" style="padding: 20px 50px;" @click="handleState('stop')">{{formData.sdqd=='1'?'停止':'已停止'}}</el-button>
              </div>
  
              <el-form label-width="160" :inline="false" :model="formData" ref="ruleFormRef" :rules="rules" status-icon>
                <!-- <el-form-item label="出水温度设定:" prop="cswdsd" v-if="dataInfo.type=='2'">
                  <div class="width-all flex">
                    <el-input v-model.number="formData.cswdsd" type="text" placeholder="请输入" />
                    <span class="flex-item-shrink-0 padd-l-5">℃</span>
                  </div>
                </el-form-item> -->
                <el-form-item :label="item.attributeDesc" v-for="(item,key) of dataInfoFormData" :key="key" style="margin-bottom: 5px;">
                  <div class="width-all flex" v-if="item.attributeMark.substring(0,1)=='B'">
                    <el-select v-model="formData[item.attributeType]" placeholder="请选择">
                      <el-option v-for="(status, index2) in triggerStatusOption" :key="index2" :label="status.label" :value="status.value" />
                    </el-select>
                  </div>
                  <div class="width-all flex" v-else>
                    <el-input v-model.number="formData[item.attributeType]" type="text" placeholder="请输入" />
                    <span class="flex-item-shrink-0 padd-l-5">{{item.unit||''}}</span>
                  </div>
                </el-form-item>
              </el-form>
  
              <div class="flex flex-center marg-b-20 padd-t-20">
                <el-button type="primary" @click="submitBtn(ruleFormRef)">确定</el-button>
                <el-button type="default" @click="dataControlVisibility=false">取消</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter,useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

import { getDict } from "@/api/common";
import {
  // getFlowChart,
  getDevicePositionList,
  getDeviceAttributeList,
  updateDeviceAttribute,
} from "@/api/intelligent-control/index";


// 获取字典
const fun_getDicts = async () => {
  // const {data:cycleTypeOptionData} = await getDict('cycle_type');//获取巡检周期
  // cycleTypeOption.value = cycleTypeOptionData.list;
  // console.log('巡检周期cycleTypeOption:',cycleTypeOption.value);
}

// 楼栋按钮列表
const buildingBtnList = ref([
  {label:'A1',value:'A1'},
  // {label:'A2',value:'A2'},
  // {label:'A3',value:'A3'},
]);
const buildingBtnValue = ref('A1');
// 楼层按钮列表
const floorBtnList = ref([
  {label:'B2',value:-2},
  {label:'B1',value:-1},
  {label:'1F',value:1},
  {label:'2F',value:2},
  {label:'3F',value:3},
  {label:'4F',value:4},
  {label:'5F',value:5},
  {label:'6F',value:6},
  {label:'7F',value:7},
  {label:'8F',value:8},
  {label:'9F',value:9},
  {label:'10F',value:10},
  {label:'11F',value:11},
  {label:'12F',value:12},
  {label:'13F',value:13},
  {label:'14F',value:14},
  {label:'15F',value:15},
  {label:'16F',value:16},
  {label:'17F',value:17},
  {label:'18F',value:18},
]);
const floorBtnValue = ref(1);
//楼层平面图
const buildingFloorActiveImgName = ref('buildingFloor-A1-1');
watch([buildingBtnValue,floorBtnValue],() => {
  //4-18楼层一样
  if(floorBtnValue.value>=4&&floorBtnValue.value<=18){
    buildingFloorActiveImgName.value = `buildingFloor-${buildingBtnValue.value}-${4}`;
  }else{
    buildingFloorActiveImgName.value = `buildingFloor-${buildingBtnValue.value}-${floorBtnValue.value}`;
  }
  fun_getList();
})

// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  // const {name:routeName} = router.currentRoute.value;
  // console.log('当前路由:',routeName);
  fun_getList();
});

//启停状态
const triggerStatusOption = ref([
  { label: "启动", value: 1 },
  { label: "关闭", value: 0 },
]);

const dataList = ref([
  // {
  //   name:'新风机组',id:'306',type:'1',
  //   position:'1F',
  //   left:300,top:100,imgName:'fan',
  //   list:[
  //     {name:'冷机运行状态',value:'停止'},
  //     {name:'压缩机状态',value:'停止'},
  //     {name:'故障报警',value:'停止'},
  //     {name:'运行模式',value:'准备启动'},
  //     {name:'电流百分比',value:'0%'},
  //   ]
  // },
  // {
  //   name:'组合式空调机组',id:'2',type:'2',
  //   left:350,top:150,imgName:'fan',
  //   list:[
  //     {name:'冷机运行状态',value:'停止'},
  //     {name:'压缩机状态',value:'停止'},
  //     {name:'故障报警',value:'停止2'},
  //     {name:'运行模式',value:'准备启动'},
  //     {name:'电流百分比',value:'0%'},
  //   ]
  // },
  // {
  //   name:'送风机',id:'3',type:'3',
  //   left:500,top:280,imgName:'fan',
  //   list:[
  //     {name:'冷机运行状态',value:'停止'},
  //     {name:'压缩机状态',value:'停止'},
  //     {name:'故障报警',value:'停止3'},
  //     {name:'运行模式',value:'准备启动'},
  //     {name:'电流百分比',value:'0%'},
  //   ]
  // },
  // {
  //   name:'排风机',id:'4',type:'4',
  //   left:500,top:400,imgName:'fan',
  //   list:[
  //     {name:'冷机运行状态',value:'停止'},
  //     {name:'压缩机状态',value:'停止'},
  //     {name:'故障报警',value:'停止4'},
  //     {name:'运行模式',value:'准备启动'},
  //     {name:'电流百分比',value:'0%'},
  //   ]
  // },
  // {
  //   name:'热风幕',id:'5',type:'5',
  //   left:600,top:400,imgName:'fan',
  //   list:[
  //     {name:'冷机运行状态',value:'停止'},
  //     {name:'压缩机状态',value:'停止'},
  //     {name:'故障报警',value:'停止5'},
  //     {name:'运行模式',value:'准备启动'},
  //     {name:'电流百分比',value:'0%'},
  //   ]
  // },
]);
const fun_getList = async () => {
  console.log('route.query:',route.query);
  if(!route.query.type)return;
  const { data } = await getDevicePositionList({sbdm:route.query.type,szld:buildingBtnValue.value,szlc:floorBtnValue.value,pageSize:1000});
  console.log('设备扎点列表:',data);
  dataList.value = data.list||[];
};

//鼠标单击和双击事件判断
// const clickCount = ref(0)
// const clickTimer = ref(null)
// const handleClickEvent = (node,key) => {
//   clickCount.value++
//   if (clickCount.value === 1) {
//     clickTimer.value = setTimeout(() => {
//       fun_handleClick(node,key)//单击事件
//       clickCount.value = 0
//     }, 250) // 250ms内没有第二次点击视为单击
//   } else if (clickCount.value === 2) {
//     clearTimeout(clickTimer.value)
//     fun_handleDblClick(node,key)//双击事件
//     clickCount.value = 0
//   }
// }

// 详情弹窗
const dataInfo = ref({list:[]})
const dataInfoVisibility = ref(false)
// 鼠标单击-显示详情弹窗
// const fun_handleClick = async (node,key) => {
//   console.log('鼠标单击:',node,key);
//   const { data } = await getDeviceAttributeList({equipmentId:node.gid,pageSize: 100});
//   console.log('设备属性详情:',data);
//   dataInfo.value.name=node.name;
//   dataInfo.value.list=data?.list.map(item => {return{name:item.attributeDesc,value:item.attributeValue,unit:item.attributeUnit}})||[];
//   dataInfoVisibility.value=true;
// }
// 鼠标移入-显示详情弹窗
const mouseEnterTimer = ref(null)
const fun_handleMouseEnter = async (node,key) => {
  if(mouseEnterTimer.value){
    clearTimeout(mouseEnterTimer.value);
  }
  mouseEnterTimer.value = setTimeout(async () => {
    console.log('鼠标移入:',node,key);
    const { data } = await getDeviceAttributeList({equipmentId:node.gid,pageSize: 100});
    console.log('设备属性详情:',data);
    dataInfo.value.name=node.sbmc;
    dataInfo.value.list=data?.list.map(item => {return{name:item.attributeDesc,value:item.attributeValue,unit:item.attributeUnit}})||[];
    dataInfoVisibility.value=true;
  }, 250) // 250ms内没有移动视为移入
}
// 鼠标移出-隐藏详情弹窗
const fun_handleMouseLeave = (node,key) => {
  console.log('鼠标移出:',node,key);
  if(mouseEnterTimer.value){
    clearTimeout(mouseEnterTimer.value);
  }
  // dataInfoVisibility.value=false;
}

// 启停控制弹窗
const dataControlVisibility = ref(false)
const dataInfoFormData = ref([])
const dataInfoSubmitData = ref([])
// 鼠标单击-显示启停控制弹窗
const fun_handleClick = async (node,key) => {
  console.log('鼠标单击:',node,key);
  const { data } = await getDeviceAttributeList({equipmentId:node.gid,status:1,pageSize: 100});
  console.log('设备属性详情:',data);
  formData.value.name=node.sbmc;
  // formData.value.id=node.gid;
  dataInfoSubmitData.value=data?.list||[];
  dataInfoFormData.value=data?.list.filter(item => item.attributeType!='sdqd')||[];//.map(item => {return{attributeDesc:item.attributeDesc,attributeType:item.attributeType,attributeValue:item.attributeValue,unit:item.attributeUnit,gid:item.gid}})
  // 控制属性值回显
  dataInfoFormData.value.forEach(item => {
    formData.value[item.attributeType]=item.attributeValue;
  })
  // 启停控制按钮值
  formData.value.sdqd = data?.list.find(item => item.attributeType=='sdqd')?.attributeValue||'';
  dataControlVisibility.value=true;
}

// 启用停用
const handleState = (type) => {
  ElMessageBox.confirm(`确定${type=='start'?'启动':'停止'}吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    formData.value.sdqd=type=='start'?'1':'0';
    for(let item of dataInfoSubmitData.value){
      if(item.attributeType=='sdqd'){
        item.attributeValue=formData.value.sdqd;
        break;
      }
    }
    const res = await updateDeviceAttribute({list:dataInfoSubmitData.value});
    ElMessage({
      type: "success",
      message: `${type=='start'?'启动':'停止'}成功`,
    });
    // fun_getList();
  })
};

// 表单提交数据
const formData = ref({})

// 表单验证
const ruleFormRef = ref();
const rules = reactive({
  // plsd: [
  //   { required: true, message: "请输入", trigger: ["blur","change"] },
  //   { type: 'number', message: '请输入数字类型' },
  // ],
  // cswdsd: [
  //   { required: true, message: "请输入", trigger: ["blur","change"] },
  //   { type: 'number', message: '请输入数字类型' },
  // ],
});

// 提交
const submitBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      ElMessageBox.confirm("确定提交吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        dataInfoSubmitData.value.forEach(item => {
          item.attributeValue=formData.value[item.attributeType];
        })
        const res = await updateDeviceAttribute({list:dataInfoSubmitData.value});
        ElMessage({
          message: res.message,
          type: "success",
        });
        dataControlVisibility.value=false;
        formData.value={};
        // fun_getList();
      })
    }
    else{
      console.log('表单验证失败:',fields);
    }
  })
};


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
.buildingBtnList{
  :deep(.el-select__wrapper){
    height: 100%;
  }
}
.floorBtn{
  .floorBtnActive,
  li .hover:hover{
    background-color: rgba(50, 117, 249, 0.16);
    color: #3275F9;font-weight: bold; 
  }
}
//详情弹窗
.info-dialog{
  position: absolute;
  right: 0;
  top: 60px;
  width: 350px;
  height: 600px;
  // background: url('/static/images/dialog-bg1.png') no-repeat;
  // background-size: 100% 100%;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  color: #333;
}
//启停弹窗
.control-dialog{
  position: absolute;
  left: 50%;
  margin-left: -180px;
  top: 50%;
  margin-top: -205px;
  width: 360px;
  height: 410px;
  // background-color: rgba(91, 115, 255, 0.4);
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  color: #333;
  // .input-bg-green,
  // .input-bg-green :deep(.el-input__wrapper){
  //     background-color: rgba(0, 255, 255, 0.1);
  // }
  :deep(.el-form-item__label){
    // color: #fff;
    font-size: 14px;
    line-height: 1;
    align-items: center;
  }
  // :deep(.el-input__inner){
  //   color: #fff;
  // }
  // :deep(.el-input__inner::placeholder){
  //   color: #fff;
  // }
  :deep(.el-form-item__label:before){
    display: none;
  }
}
</style>
