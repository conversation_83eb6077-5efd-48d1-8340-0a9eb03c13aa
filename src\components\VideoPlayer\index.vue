<template>
  <div style="position: relative" class="videoMain">
    <div :id="randomId"></div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from "vue";
const isControlling = ref(false);

const randomId = computed(
  () => "video_" + Math.random().toString(36).substr(2)
);

const player = ref(null);

const props = defineProps({
  options: {
    type: Object,
    default: () => {
      return {};
    },
  },
  // 摄像头信息
  videoInfo: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const getPlayer = () => {
  return player.value;
};
const addVideoMask = () => {
  const videoMask = document.createElement("div");
  videoMask.className = "video-mask";
  videoMask.onclick = (event) => handleVideoClick(event);
  videoMask.onmousemove = (event) => handleVideoMousemove(event);
  videoMask.onwheel = (event) => handleVideoWheel(event);

  videoMask.style.position = "absolute";
  videoMask.style.top = "0";
  videoMask.style.left = "0";
  videoMask.style.width = "100%";
  videoMask.style.height = "100%";

  document.querySelector(`#${randomId.value}`).appendChild(videoMask);
};
const createPlayer = () => {
  let container = document.querySelector(`#${randomId.value}`);

  let extraOptions = Object.assign(
    {
      videoBuffer: 0.2, // 缓存时长
      isResize: false,
      text: "",
      loadingText: "加载中",
      // debug: true,
      // debugLevel: 'debug',
      useMSE: true,
      isMulti: true,
      useSIMD: false,
      hasAudio: true,
      showBandwidth: false, // 显示网速
      showPerformance: false,
      operateBtns: {
        fullscreen: true,
        screenshot: true,
        play: true,
        audio: true,
        record: true,
        quality: true,
        performance: false,
      },
      watermarkConfig: {
        text: {
          content: props.videoInfo?.ptzFlag ? "支持云台控制" : "",
          fontSize: 60,
        },
        right: 20,
        top: 20,
      },
      playbackForwardMaxRateDecodeIFrame: 1,
      isWebrtcForOthers: true,
      demuxUseWorker: true,
      playFailedAndReplay: true,
      supportHls265: true,
      defaultStreamQuality: "普清",
      useWebGPU: true,
      autoResize: true,
    },
    props.options.extraOptions
  );

  player.value = new EasyPlayerPro({
    container,
    decoder: "./assets/js/EasyPlayer/decoder-pro.js",
    ...extraOptions,
  });
  player.value
    .play(props.options.url)
    .then(() => {})
    .catch((e) => {
      console.error(e);
    });

  setTimeout(() => {
    props.videoInfo?.ptzFlag && addVideoMask();
  }, 1000);
};
const handleVideoClick = (event, video) => {
  if (props.videoInfo?.ptzFlag) {
    const targetArea = event.currentTarget;
    const position = targetArea.getAttribute("data-position");

    handleSendOrder({
      command: position,
    });
  }
};
const handleVideoMousemove = (event, video) => {
  props.videoInfo?.ptzFlag && handleMousemove(event);
};
const handleVideoWheel = (event, video) => {
  if (props.videoInfo?.ptzFlag) {
    event.preventDefault();

    const delta = Math.sign(event.deltaY);

    handleSendOrder({
      command: delta > 0 ? "ZOOM_OUT" : "ZOOM_IN",
    });
  }
};
const handleSendOrder = ({ command }) => {
  if (!isControlling.value) {
    isControlling.value = true;
  }
};
const handleMousemove = (event) => {
  const targetArea = event.currentTarget;
  const rect = targetArea.getBoundingClientRect();
  const relativeX = event.clientX - rect.left;
  const relativeY = event.clientY - rect.top;

  const width = rect.width;
  const height = rect.height;

  targetArea.classList.remove(
    "cursor-top",
    "cursor-bottom",
    "cursor-left",
    "cursor-right",
    "cursor-wheel"
  );

  if (relativeY < height / 3) {
    targetArea.classList.add("cursor-top");
    targetArea.setAttribute("data-position", "UP");
    targetArea.setAttribute("title", "向上移动");
  } else if (relativeY > (2 * height) / 3) {
    targetArea.classList.add("cursor-bottom");
    targetArea.setAttribute("data-position", "DOWN");
    targetArea.setAttribute("title", "向下移动");
  } else if (relativeX < width / 3) {
    targetArea.classList.add("cursor-left");
    targetArea.setAttribute("data-position", "LEFT");
    targetArea.setAttribute("title", "向左移动");
  } else if (relativeX > (2 * width) / 3) {
    targetArea.classList.add("cursor-right");
    targetArea.setAttribute("data-position", "RIGHT");
    targetArea.setAttribute("title", "向右移动");
  } else {
    targetArea.classList.add("cursor-wheel");
    targetArea.setAttribute("title", "鼠标滚轮操作");
  }
};

onMounted(async () => {
  await nextTick();
  createPlayer();
});
onBeforeUnmount(() => {
  player.value.destroy();
});
</script>

<style lang="scss" scoped>
.video-mask {
  z-index: 5;
  position: absolute;
  width: 100%;
  height: 200px;
  background-color: rgba(0, 0, 0, 0.15);
}
:deep(.easyplayer-controls) {
  background-image: linear-gradient( 180deg, rgba(0,28,77,0) 0%, #001C4D 100%) !important;
}
</style>
