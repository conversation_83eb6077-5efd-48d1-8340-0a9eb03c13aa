<template>
  <div ref="refChart" class="chart-container"></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, watch, markRaw } from "vue";
import * as echarts from "echarts";
import { getTransferPx } from "@/utils/px2rem";

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

const refChart = ref<any>();
const chartInstance = ref();
const chartsData: any = ref({});

onMounted(() => {
  watch(
    () => props.data,
    (val) => {
      if (val) {
        chartsData.value = val;
        initChart();
      }
    },
    {
      deep: true,
      immediate: true,
    }
  );
});

onBeforeUnmount(() => {
  // 销毁图表
  chartInstance.value.dispose();
  chartInstance.value = null;
});

const initChart = () => {
  chartInstance.value = markRaw(echarts.init(refChart.value));

  const initOption = {
    color: chartsData.value.colorList,
    legend: {
      icon: "roundRect",
      orient: "horizontal", // 设置图例的方向
      left: "center", // 居中
      bottom: getTransferPx(10),
      data: chartsData.value.title,
      textStyle: {
        fontSize: getTransferPx(13),
        color: "#515A6E",
      },
    },
    tooltip: {
      confine: true,
      padding: [0, getTransferPx(10), getTransferPx(5), getTransferPx(10)],
      backgroundColor: "#fff",
      axisPointer: {
        type: "shadow",
      },
      formatter: (param: any) => `
          <div class="maintenanceChart-popup">
            <p class="top">
                <span>${param.seriesName} </span>
            </p>
            <p class="item">
                <i class="icon" style="background-color:${param.color}"></i>
                <span class="name">${param.name}</span>
                <span class="value"><b>${param.value}</b>个</span>
            </p>
          </div>
        `,
    },
    series: [
      {
        name: chartsData.value.tip,
        type: "pie",
        roseType: "radius",
        radius: ["10%", "68%"],
        center: ["50%", "45%"],
        // 处理数据，进行排序
        data: chartsData.value.list,
        labelLine: {
          smooth: true,
        },
        label: {
          formatter: (params: any) =>
            `{name|${params.name}:}{value|${params.value}}`,
          rich: {
            name: { fontSize: getTransferPx(15), color: "#515A6E" },
            value: { fontSize: getTransferPx(15), color: "#515A6E" },
          },
        },
      },
    ],
  };

  // 图表初始化配置
  chartInstance.value.setOption(initOption);

  setTimeout(function () {
    window.addEventListener("resize", () => {
      chartInstance.value?.resize();
    });
  }, 200);
};
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
