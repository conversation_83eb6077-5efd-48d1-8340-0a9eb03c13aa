import router from "./router";
import useUserStore from "@/store/modules/user";
import usePermissionStore from "@/store/modules/permission";
import { isHttp } from "@/utils/validate";

// 根据浏览器跳转地址参数，获取登录用户
const params: any = window.location.href.split("?");
let thirdToken = null;

export const autoLogin = async () => {
  if (!thirdToken) return;
  // 调用action的登录方法
  await useUserStore().getTokenByThirdPlatform({
    token: "Bearer " + thirdToken,
  });

  await useUserStore().getInfo();
  let accessRoutes: any = await usePermissionStore().generateRoutes();
  // 根据roles权限生成可访问的路由表
  accessRoutes.forEach((route: any) => {
    if (!isHttp(route.path)) {
      router.addRoute(route); // 动态添加可访问路由表
    }
  });
  router.push({ path: "/" });
};

function queryURLParams(url, name) {
  var pattern = new RegExp("[?&#]+" + name + "=([^?&#]+)");
  var res = pattern.exec(url);
  console.log(res);
  if (!res) return;
  if (!res[1]) return;
  return res[1];
}

export const getIsHaveToken = () => {
  if (params.length > 1) {
    thirdToken = queryURLParams(window.location.href, "tk");
    if (thirdToken) {
      localStorage.setItem('szToken', thirdToken)
      return true;
    }
  }
  return false;
};

export const setHideMenu = () => {
  if (params.length > 1) {
    const hideMenu:any = queryURLParams(window.location.href, "hideMenu");
    const isHide = localStorage.getItem('hideMenu')
    if(isHide != '1') {
      localStorage.setItem('hideMenu', hideMenu)
    }
  }
};
