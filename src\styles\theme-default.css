:root {
  /* 全局默认主题色 */
  --el-color-primary: #3399ff;
  /* 全局默认danger主题色 */
  --el-color-danger: #e54053;
  /* 全局默认success主题色 */
  --el-color-success: #67c23a;
  /* 全局默认warning主题色 */
  --el-color-warning: #ff8800;
}

/**
  * 输入框
  */
html .el-input {
  /* 鼠标悬停输入框的边框色 */
  --el-input-hover-border-color: #5cadff;
  /* 输入框禁用后的边框色 */
  --el-disabled-border-color: rgba(255, 255, 255, 0);
  /* 鼠标选中输入框的边框色 */
  --el-input-focus-border-color: #3399ff;
  /* 输入框的默认文字颜色 */
  --el-input-text-color: #363f51;
  /* 输入框的占位文字颜色 */
  --el-input-placeholder-color: #cfd3de;
}

/**
  * 按钮
  *  
  */
html .el-button {
  /* 鼠标点击背景色 */
  --el-button-active-bg-color: #2d89e5;
  /* 文字颜色 */
  --el-button-text-color: #000;
  /* 鼠标点击文字颜色 */
  --el-button-active-text-color: #fff;
}

/*主按钮 */
html .el-button--primary {
  /* 文字颜色 */
  --el-button-text-color: #fff;
  /* 鼠标悬停背景色 */
  --el-button-hover-bg-color: #5cadff;
  /* 鼠标点击背景色 */
  --el-button-active-bg-color: #2d89e5;
}

/* danger红色按钮 */
html .el-button--danger {
  /* 默认状态文字颜色 */
  --el-button-text-color: #ff485d;
  /* 默认状态背景色 */
  --el-button-bg-color: #fff;
  /* 鼠标点击背景色 */
  --el-button-active-bg-color: #e54053;
  /* 禁用状态背景色 */
  --el-button-disabled-bg-color: #fff;
  /* 禁用状态文字颜色 */
  --el-button-disabled-text-color: #ff919e;
  /* 鼠标悬停背景色 */
  --el-button-hover-bg-color: #fff;
  /* 鼠标悬停文字颜色 */
  --el-button-hover-text-color: #ff6d7d;
}
