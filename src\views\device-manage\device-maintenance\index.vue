<template>
  <div class="pointManage">
    <div class="formBox">
      <el-form :inline="true" :model="filter">
        <el-form-item label="设备编号:">
          <el-input v-model="filter.gid" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="设备名称:">
          <el-input v-model="filter.sbmc" placeholder="请输入" clearable />
        </el-form-item>
        <!-- <el-form-item label="安装时间:">
          <el-date-picker v-model="filter.datetime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" clearable placeholder="请选择" />
        </el-form-item> -->
      </el-form>

      <div class="btns">
        <el-button type="primary" @click="searchBtn">查询</el-button>
        <el-button @click="resetBtn">重置</el-button>
      </div>
    </div>

    <div class="main-contanier">
      <div class="main-btns">
        <!-- <el-button type="primary" @click="addBtn" :icon="Plus">新增</el-button> -->
        <!-- <el-button type="danger" @click="delAllBtn" :icon="Delete">批量删除</el-button> -->
      </div>
      <div class="tableBox">
        <Table :data="tableData" :columns="columns" row-key="gid" @listSelectChange="handleSelectionChange">
          <template #yhsx="{ row }">
            <div>
              <span v-if="row.yhsx">{{row.yhsx}} 天/次</span>
            </div>
          </template>
          <template #xcNum="{ row }">
            <div>
              <span class="color-blue hover" @click="handleLook(row,'inspect')">{{row.xcNum}}</span>
            </div>
          </template>
          <template #yhNum="{ row }">
            <div>
              <span class="color-blue hover" @click="handleLook(row,'conserve')">{{row.yhNum}}</span>
            </div>
          </template>
          <template #wxNum="{ row }">
            <div>
              <span class="color-blue hover" @click="handleLook(row,'maint')">{{row.wxNum}}</span>
            </div>
          </template>
          <!-- <template #operations="{ row }">
            <div class="buttons">
              <div class="btn" @click="handleLook(row)">查看</div>
              <div class="btn" @click="handleEdit(row)">编辑</div>
              <div class="btn" @click="handleDelete(row)">删除</div>
              <div class="btn" @click="handleEdit(row,'onlyEditDevice')">关联设备</div>
            </div>
          </template> -->
        </Table>
        <el-pagination class="pageBox" v-model:current-page="filter.pageNum" v-model:page-size="filter.pageSize"
          :page-sizes="[10, 50, 100]" layout="total, sizes, prev, pager, next,jumper" :total="total" background
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增-编辑 -->
    <!-- <addDialog :dialogVisible="addVisiblty" :title="addTitle" :data="addData" @onChangeVisible="addVisiblty = false" @refreshList="fun_getList()"></addDialog> -->
    <!-- 查看 -->
    <lookDialog :dialogVisible="lookVisiblty" :data="lookData" @onChangeVisible="lookVisiblty = false"></lookDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import { Columns } from "./table";
// import addDialog from "./components/add-dialog.vue";
import lookDialog from "./components/look-dialog.vue";

import { getDict } from "@/api/common";
import {
  getDeviceMaintenanceList
} from "@/api/device-manage/index";

// 搜索条件
const filter = ref({
  sbbm: '',
  sbmc: '',
  datetime: '',
  dateRange: '',
  startTime: '',
  endTime: '',
  pageSize: 10,
  pageNum: 1,
});

// 查询
const searchBtn = () => {
  filter.value.pageNum = 1;
  fun_getList();
};
// 重置
const resetBtn = () => {
  filter.value = {
    sbbm: "",
    sbmc: "",
    datetime: "",
    dateRange: "",
    startTime: "",
    endTime: "",
    pageSize: 10,
    pageNum: 1,
  };
  fun_getList();
};

//监测项
// const xjxOption = ref([]);

// 获取字典
const fun_getDicts = async () => {
  // const {data} = await getDict('BGLX')
  // bglxOption.value = data.list;
  // console.log('报告类型bglxOption:',bglxOption.value);
}

// 挂载
onMounted(async () => {
  // fun_getDicts();//获取字典
  fun_getList();
});

// 表格项
const headerColumns = new Columns();
const columns = ref(headerColumns.columns);
// 表格数据
const total = ref(0);
const tableData = ref([
  // {gid:1,sbbm:'sbbm0001',sbmc:'设备名称',sbll:'设备类型',azwz:'安装位置',yhsx:100,xcNum:100,yhNum:100,wxNum:100,
  //   inspectList:[{xjry:'张三',xjsj:'2021-01-01',sbwhx:1,xjzp:['https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg','https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg']}],
  //   conserveList:[{yhry:'李四',yhsj:'2021-01-01'}],
  //   maintList:[{wxry:'王五',wxsj:'2021-01-01',wxzt:1,wxcms:'故障描述',gzdj:1,gzms:'故障描述'}],
  // },
  // {gid:2,sbbm:'sbbm0002',sbmc:'设备名称',sbll:'设备类型',azwz:'安装位置',yhsx:100,xcNum:100,yhNum:100,wxNum:100,inspectList:[],conserveList:[],maintList:[]},
]);
const fun_getList = async () => {
  const { data } = await getDeviceMaintenanceList(filter.value);
  tableData.value = data.list;
  total.value = data.total;
};

// 分页
const handleSizeChange = (val) => {
  filter.value.pageNum = 1;
  filter.value.pageSize = val;
  fun_getList();
};
const handleCurrentChange = (val) => {
  filter.value.pageNum = val;
  fun_getList();
};


// 日期范围-切换
// const fun_dateChange = (val) => {
//   console.log('时间范围-时间选择:',val);
//   if (val) {
//     filter.value.startTime = val[0] + " 00:00:00";
//     filter.value.endTime = val[1] + " 23:59:59";
//   } else {
//     filter.value.startTime = "";
//     filter.value.endTime = "";
//   }
// };

// 新增
const addVisiblty = ref(false);
const addTitle = ref("新增");
const addData = ref([]);
const addBtn = () => {
  addData.value = {};
  addTitle.value = "新增";
  addVisiblty.value = true;
};
// 编辑
const handleEdit = (row,type) => {
  addData.value = JSON.parse(JSON.stringify(row));
  addTitle.value = "编辑";
  addVisiblty.value = true;
  if(type){
    addData.value.type = type;//关联设备
  }
};

// 查看弹窗
const lookVisiblty = ref(false);
const lookData = ref({});
const handleLook = async (row,type) => {
  lookVisiblty.value = true;
  lookData.value = row;
  lookData.value.type = type;
  // lookData.value = {}
  // const res = await getReportInfo(row.gid)
  // lookData.value = res.data;
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm("删除后不可恢复,确定吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    // const res = await delReport(row.gid);
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    fun_getList();
  })
};
// 批量删除
const delIds = ref("");
const handleSelectionChange = (data) => {
  delIds.value = data.map((item) => item.gid).join(",");
};
const delAllBtn = () => {
  if (delIds.value) {
    ElMessageBox.confirm("批量删除后不可恢复,确定吗?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        // const res = await delReport(delIds.value);
        if (res.code === 0) {
          fun_getList();
          ElMessage({
            type: "success",
            message: "删除成功",
          });
        } else {
          ElMessage({
            message: res.message,
            type: "warning",
          });
        }
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "取消删除",
        });
      });
  } else {
    ElMessage({
      message: "请选择需要删除的选项",
      type: "warning",
    });
  }
};

// 附件下载
// import { preImageUrl } from '@/utils/index'
// const handleDownload = (row) => {
//   console.log('附件下载:',preImageUrl,row);
//   if (row.fileList && row.fileList.length > 0) {
//     window.open( import.meta.env.VITE_APP_IMG_URL+preImageUrl+row.fileList[0].fileGid);
//   } else {
//     ElMessage({
//       message: "没有附件",
//       type: "warning",
//     });
//   }
// }


</script>

<style lang="scss" scoped>
.pointManage {
  width: 100%;
  height: 100%;
}
</style>
