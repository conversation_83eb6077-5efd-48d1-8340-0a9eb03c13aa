<template>
  <el-dialog v-model="dialogShow" width="720" class="dialog" title="上线">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ "上线" }}</p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form label-width="100" :inline="true" ref="ruleFormRef" :model="formData" :rules="rules" status-icon>
          <el-form-item label="步长:" prop="step">
            <el-select style="width: 150px;" v-model="formData.step" placeholder="请选择步长">
              <el-option key="0.01" label="0.01" value="0.01" />
              <el-option key="0.02" label="0.02" value="0.02" />
              <el-option key="0.05" label="0.05" value="0.05" />
              <el-option key="0.1" label="0.1" value="0.1" />
              <el-option key="0.2" label="0.2" value="0.2" />
              <el-option key="0.5" label="0.5" value="0.5" />
              <el-option key="1" label="1" value="1" />
              <el-option key="2" label="2" value="2" />
              <el-option key="5" label="5" value="5" />
              <el-option key="10" label="10" value="10" />
              <el-option key="20" label="20" value="20" />
              <el-option key="50" label="50" value="50" />
              <el-option key="100" label="100" value="100" />
              <el-option key="500" label="500" value="500" />
              <el-option key="1000" label="1000" value="1000" />
              <el-option key="5000" label="5000" value="5000" />
              <el-option key="10000" label="10000" value="10000" />
            </el-select>
          </el-form-item>
          <el-form-item label="数据精度:" prop="dataPrecision">
            <el-input-number v-model="formData.dataPrecision" type="number" />
          </el-form-item>
          <el-form-item label="最小值:" prop="minRange">
            <div class="width-all flex">
              <el-input-number v-model="formData.minRange" type="number" :step="formData.step" />
              <span style="margin-left: 8px">{{ formData.monitorItemMark }}</span>
            </div>
          </el-form-item>
          <el-form-item label="最大值:" prop="maxRange">
            <div class="width-all flex">
              <el-input-number v-model="formData.maxRange" type="number" :step="formData.step" />
              <span style="margin-left: 8px">{{ formData.monitorItemMark }}</span>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogShow = false">取消</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { reactive, computed, defineProps, defineEmits, markRaw, ref, watch } from 'vue';
  import { FormInstance, ElMessage, ElLoading } from 'element-plus';
  import { ResultEnum } from '@/enums/httpEnum';
  import { onlineDeviceItem } from '@/api/mockDeviceManager/deviceManager';

  const emits = defineEmits(['changeDialog', 'submitFormData']);

  const ruleFormRef = ref<any>();

  const props = defineProps({
    isVisible: {
      type: Boolean,
      default: false,
    },
    viewFormData: {
      type: Object,
      default: (): any => {
        return {};
      },
    },
  });

  const formData: any = computed(() => props.viewFormData);

  const dialogShow = computed({
    get: () => props.isVisible,
    set: (newValue) => {
      emits('changeDialog', { flag: newValue, mode: 'status' });
    },
  });

  const rules = markRaw({
    minRange: [
      {
        required: true,
        message: '请输入最小值',
        trigger: 'blur',
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (Number(formData.value.maxRange) <= Number(value)) {
            callback(new Error('输入最小值需小于最大值'));
          } else {
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
    maxRange: [
      {
        required: true,
        message: '请输入最大值',
        trigger: 'blur',
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (Number(formData.value.minRange) >= Number(value)) {
            callback(new Error('输入最大值需大于最小值'));
          } else {
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
    dataPrecision: [
      {
        required: true,
        message: '请输入数据精度',
        trigger: 'blur',
      },
    ],
  });

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    const valid: any = await formEl.validate();
    if (!valid) return;

    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(52, 101, 255, 0)',
    });
    const { code, message }: any = await onlineDeviceItem({
      ...formData.value,
      status: 1,
    });
    if (code === ResultEnum.SUCCESS) {
      ElMessage.success('上线成功');
      dialogShow.value = false;
      emits('submitFormData', true);
    } else {
      ElMessage.error(message);
    }
    loading.close();
  };

  watch(dialogShow, (n) => {
    if (!n) {
      ruleFormRef.value?.resetFields();
    }
  });
</script>
<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
        width: 200px;
      }
    }
  }
}

</style>
