<template>
  <el-dialog v-model="dialogShow" title="修改" width="860" class="dialog">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <p>{{ "修改" }}</p>
      </div>
    </template>
    <div class="dialog-main">
      <div class="left">
        <el-form label-width="112" :inline="true" ref="ruleFormRef" :model="formData" :rules="rules" status-icon>
          <el-form-item label="设备名称:" prop="deviceName">
            <el-input v-model="formData.deviceName" />
          </el-form-item>
          <el-form-item label="设备类型:" prop="deviceTypeName">
            <el-input v-model="formData.deviceTypeName" disabled />
          </el-form-item>
          <el-form-item label="监测项名称:" prop="monitorItemName">
            <el-input v-model="formData.monitorItemName" disabled />
          </el-form-item>
          <el-form-item label="监测项编码:" prop="monitorItemCode">
            <el-input v-model="formData.monitorItemCode" disabled />
          </el-form-item>
          <el-form-item label="监测项单位:" prop="monitorItemMark">
            <el-input v-model="formData.monitorItemMark" disabled />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogShow = false">取消</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import {
    reactive,
    computed,
    defineProps,
    defineEmits,
    markRaw,
    ref,
    defineExpose,
    watch,
    onMounted,
  } from 'vue';
  import { FormInstance, ElMessage, ElLoading } from 'element-plus';
  import { ResultEnum } from '@/enums/httpEnum';
  import { getMonitorItemByType, editDeviceItem } from '@/api/mockDeviceManager/deviceManager';

  const emits = defineEmits(['changeDialog', 'submitFormData']);

  const ruleFormRef = ref<FormInstance>();
  const typeList = ref<any>([]);

  const props = defineProps({
    isVisible: {
      type: Boolean,
      default: false,
    },
    viewFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const formData: any = computed(() => props.viewFormData);
  const dialogShow = computed({
    get: () => props.isVisible,
    set: (newValue) => {
      emits('changeDialog', { flag: newValue, mode: 'update' });
    },
  });

  const rules = markRaw({
    deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
    deviceType: [{ required: true, message: '请选择监测项', trigger: 'change' }],
  });

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    const valid: any = await formEl.validate();
    if (!valid) return;

    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(52, 101, 255, 0)',
    });
    const { code, message }: any = await editDeviceItem(formData.value);
    if (code === ResultEnum.SUCCESS) {
      ElMessage.success('修改成功');
      dialogShow.value = false;
      emits('submitFormData', true);
    } else {
      ElMessage.error(message);
    }
    loading.close();
  };

  watch(dialogShow, (n) => {
    if (!n) {
      ruleFormRef.value?.resetFields();
    }
  });

  onMounted(async () => {
    const { data } = await getMonitorItemByType({ type: 'SSLX,JCSBLX' });
    typeList.value = data.list;
  });

  defineExpose({
    ruleFormRef,
  });
</script>
<style lang="scss" scoped>
.dialog {
  .dialog-main {
    width: 100%;
    height: auto;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .left {
      width: 100%;
      ::v-deep(.el-form-item__content) {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
